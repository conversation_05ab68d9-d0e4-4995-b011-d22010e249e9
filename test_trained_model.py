#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学習済みモデルのテストスクリプト

データリーケージ修正版の学習済みモデルを使用して予測テストを実行します。
"""

import pandas as pd
import numpy as np
import joblib
import logging
from pathlib import Path
from typing import Dict, Any, List
import warnings
warnings.filterwarnings('ignore')

# プロジェクトモジュールのインポート
import sys
sys.path.append('.')
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TrainedModelTester:
    """学習済みモデルのテストクラス"""
    
    def __init__(self, model_timestamp: str = "20250608_212220"):
        """
        初期化
        
        Parameters
        ----------
        model_timestamp : str
            モデルのタイムスタンプ
        """
        self.model_timestamp = model_timestamp
        self.models_dir = Path("models")
        
        # モデルファイルパス
        self.model_path = self.models_dir / f"fixed_leakage_model_{model_timestamp}.pkl"
        self.scaler_path = self.models_dir / f"fixed_leakage_scaler_{model_timestamp}.pkl"
        self.features_path = self.models_dir / f"fixed_leakage_features_{model_timestamp}.pkl"
        self.encoders_path = self.models_dir / f"fixed_leakage_encoders_{model_timestamp}.pkl"
        
        # モデル関連オブジェクト
        self.model = None
        self.scaler = None
        self.feature_columns = None
        self.label_encoders = None
    
    def load_model(self) -> bool:
        """
        学習済みモデルと関連ファイルを読み込み
        
        Returns
        -------
        bool
            読み込み成功フラグ
        """
        try:
            logger.info("学習済みモデルの読み込み開始")
            
            # ファイル存在確認
            missing_files = []
            for file_path in [self.model_path, self.scaler_path, self.features_path, self.encoders_path]:
                if not file_path.exists():
                    missing_files.append(str(file_path))
            
            if missing_files:
                logger.error(f"必要なファイルが見つかりません: {missing_files}")
                return False
            
            # モデル読み込み
            self.model = joblib.load(self.model_path)
            self.scaler = joblib.load(self.scaler_path)
            self.feature_columns = joblib.load(self.features_path)
            self.label_encoders = joblib.load(self.encoders_path)
            
            logger.info(f"モデル読み込み完了:")
            logger.info(f"  - 特徴量数: {len(self.feature_columns)}")
            logger.info(f"  - エンコーダー数: {len(self.label_encoders)}")
            
            return True
            
        except Exception as e:
            logger.error(f"モデル読み込みエラー: {e}")
            return False
    
    def prepare_test_data(self, test_year: str = "2020") -> pd.DataFrame:
        """
        テストデータの準備
        
        Parameters
        ----------
        test_year : str
            テスト用年度
            
        Returns
        -------
        pd.DataFrame
            前処理済みテストデータ
        """
        logger.info(f"テストデータ準備開始: {test_year}年")
        
        # ComprehensiveDataIntegratorでデータ統合
        integrator = ComprehensiveDataIntegrator()
        
        test_data = integrator.generate_comprehensive_table(
            year=test_year,
            include_race_info=True,
            include_horse_info=True,
            include_past_performance=True,
            use_pickle_source=True
        )
        
        logger.info(f"テストデータ読み込み完了: {len(test_data):,}件")
        
        # ターゲット変数の作成
        if '着順' in test_data.columns:
            test_data['target'] = (pd.to_numeric(test_data['着順'], errors='coerce') <= 3).astype(int)
        
        # 無効なレコードを除外
        valid_mask = (
            test_data['target'].notna() &
            test_data['horse_id'].notna() &
            test_data['date'].notna()
        )
        test_data = test_data[valid_mask]
        logger.info(f"無効レコード除外後: {len(test_data):,}件")
        
        return test_data
    
    def prepare_features_for_prediction(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        予測用の特徴量準備
        
        Parameters
        ----------
        data : pd.DataFrame
            入力データ
            
        Returns
        -------
        pd.DataFrame
            予測用特徴量DataFrame
        """
        logger.info("予測用特徴量準備開始")
        
        # 学習時と同じ特徴量を抽出
        available_features = [col for col in self.feature_columns if col in data.columns]
        missing_features = [col for col in self.feature_columns if col not in data.columns]
        
        if missing_features:
            logger.warning(f"不足している特徴量: {missing_features}")
        
        feature_data = data[available_features].copy()
        
        # 不足している特徴量は0で埋める
        for col in missing_features:
            feature_data[col] = 0
            logger.info(f"不足特徴量を0で補完: {col}")
        
        # 特徴量の順序を学習時と合わせる
        feature_data = feature_data[self.feature_columns]
        
        # カテゴリカル特徴量のエンコーディング
        for col, encoder in self.label_encoders.items():
            if col in feature_data.columns:
                # 未知のカテゴリへの対処
                feature_data[col] = feature_data[col].fillna('unknown').astype(str)
                
                # 学習時に見たことのないラベルを 'unknown' に変換
                known_labels = set(encoder.classes_)
                mask = ~feature_data[col].isin(known_labels)
                feature_data.loc[mask, col] = 'unknown'
                
                # 'unknown' が学習時に存在しない場合の対処
                if 'unknown' not in known_labels:
                    # 最も頻度の高いラベルで代替
                    most_common = encoder.classes_[0]
                    feature_data.loc[mask, col] = most_common
                
                feature_data[col] = encoder.transform(feature_data[col])
        
        # 数値特徴量の欠損値処理
        numeric_cols = feature_data.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            feature_data[col] = feature_data[col].fillna(feature_data[col].median())
        
        # 無限値やNaNの最終チェック
        feature_data = feature_data.replace([np.inf, -np.inf], np.nan).fillna(0)
        
        logger.info(f"予測用特徴量準備完了: {feature_data.shape}")
        return feature_data
    
    def test_model(self, test_data: pd.DataFrame) -> Dict[str, Any]:
        """
        モデルのテスト実行
        
        Parameters
        ----------
        test_data : pd.DataFrame
            テストデータ
            
        Returns
        -------
        Dict[str, Any]
            テスト結果
        """
        logger.info("モデルテスト開始")
        
        # 特徴量準備
        X_test = self.prepare_features_for_prediction(test_data)
        y_test = test_data['target']
        
        # 特徴量スケーリング
        X_test_scaled = self.scaler.transform(X_test)
        
        # 予測実行
        logger.info("予測実行中...")
        y_pred_proba = self.model.predict(X_test_scaled, num_iteration=self.model.best_iteration)
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        # 評価指標計算
        from sklearn.metrics import accuracy_score, roc_auc_score, log_loss, classification_report
        
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba)
        logloss = log_loss(y_test, y_pred_proba)
        
        # 詳細な分析
        test_results = {
            'test_samples': len(X_test),
            'test_features': len(self.feature_columns),
            'accuracy': accuracy,
            'auc': auc,
            'logloss': logloss,
            'predictions': {
                'probabilities': y_pred_proba,
                'binary_predictions': y_pred,
                'actual': y_test.values
            },
            'target_distribution': y_test.value_counts().to_dict(),
            'prediction_distribution': pd.Series(y_pred).value_counts().to_dict()
        }
        
        logger.info(f"テスト完了:")
        logger.info(f"  - サンプル数: {len(X_test):,}")
        logger.info(f"  - 精度: {accuracy:.4f}")
        logger.info(f"  - AUC: {auc:.4f}")
        logger.info(f"  - LogLoss: {logloss:.4f}")
        
        return test_results
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """
        包括的なモデルテストの実行
        
        Returns
        -------
        Dict[str, Any]
            総合テスト結果
        """
        logger.info("=" * 60)
        logger.info("学習済みモデルの包括的テスト開始")
        logger.info("=" * 60)
        
        try:
            # 1. モデル読み込み
            if not self.load_model():
                raise RuntimeError("モデル読み込みに失敗しました")
            
            # 2. テストデータ準備
            test_data = self.prepare_test_data()
            
            # 3. モデルテスト実行
            test_results = self.test_model(test_data)
            
            # 4. 結果まとめ
            comprehensive_results = {
                'model_info': {
                    'timestamp': self.model_timestamp,
                    'model_path': str(self.model_path),
                    'feature_count': len(self.feature_columns)
                },
                'test_results': test_results
            }
            
            logger.info("=" * 60)
            logger.info("テスト結果サマリー")
            logger.info("=" * 60)
            logger.info(f"モデルタイムスタンプ: {self.model_timestamp}")
            logger.info(f"テストサンプル数: {test_results['test_samples']:,}")
            logger.info(f"使用特徴量数: {test_results['test_features']}")
            logger.info(f"テスト精度: {test_results['accuracy']:.4f}")
            logger.info(f"テストAUC: {test_results['auc']:.4f}")
            logger.info(f"テストLogLoss: {test_results['logloss']:.4f}")
            
            # ターゲット分布
            logger.info(f"\nターゲット分布: {test_results['target_distribution']}")
            logger.info(f"予測分布: {test_results['prediction_distribution']}")
            
            return comprehensive_results
            
        except Exception as e:
            logger.error(f"テスト中にエラーが発生: {e}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """メイン実行関数"""
    
    # 最新の学習済みモデルでテスト
    tester = TrainedModelTester()
    
    try:
        results = tester.run_comprehensive_test()
        
        print("\nモデルテストが正常に完了しました！")
        print(f"テスト精度: {results['test_results']['accuracy']:.4f}")
        print(f"テストAUC: {results['test_results']['auc']:.4f}")
        print(f"使用したモデル: {results['model_info']['model_path']}")
        
    except Exception as e:
        print(f"\nテスト中にエラーが発生しました: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()