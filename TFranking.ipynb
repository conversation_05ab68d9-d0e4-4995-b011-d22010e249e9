{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b83f09f8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["f:\\keiba__AI_2025\\numpy1.5\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import sys\n", "import os\n", "import logging\n", "from pathlib import Path\n", "from typing import Dict, List, Optional, Any\n", "import json\n", "from datetime import datetime\n", "\n", "# 最新のプロセッサクラスをインポート\n", "from module.race_batch_processor import process_race_bin_to_pickle_batch\n", "from module.race_horse_targeted_processor import RaceHorseTargetedProcessor\n", "from module.race_data_processor import RaceProcessor\n", "from module.data_merger import DataMerger\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from tqdm.notebook import tqdm"]}, {"cell_type": "code", "execution_count": 2, "id": "d7429bd9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27616\\1332656470.py:1: DtypeWarning: Columns (46) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  comprehensive_df = pd.read_csv('dataframe.csv')\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "着順", "rawType": "object", "type": "string"}, {"name": "枠番", "rawType": "int64", "type": "integer"}, {"name": "馬番", "rawType": "int64", "type": "integer"}, {"name": "馬名", "rawType": "object", "type": "string"}, {"name": "性齢", "rawType": "object", "type": "string"}, {"name": "斤量", "rawType": "float64", "type": "float"}, {"name": "騎手", "rawType": "object", "type": "string"}, {"name": "タイム", "rawType": "object", "type": "unknown"}, {"name": "着差", "rawType": "object", "type": "unknown"}, {"name": "ﾀｲﾑ指数", "rawType": "object", "type": "unknown"}, {"name": "通過", "rawType": "object", "type": "unknown"}, {"name": "上り", "rawType": "float64", "type": "float"}, {"name": "単勝", "rawType": "object", "type": "string"}, {"name": "人気", "rawType": "float64", "type": "float"}, {"name": "馬体重", "rawType": "object", "type": "string"}, {"name": "調教ﾀｲﾑ", "rawType": "float64", "type": "float"}, {"name": "厩舎ｺﾒﾝﾄ", "rawType": "float64", "type": "float"}, {"name": "備考", "rawType": "float64", "type": "float"}, {"name": "調教師", "rawType": "object", "type": "string"}, {"name": "馬主", "rawType": "object", "type": "string"}, {"name": "賞金(万円)", "rawType": "float64", "type": "float"}, {"name": "race_id", "rawType": "int64", "type": "integer"}, {"name": "horse_id", "rawType": "int64", "type": "integer"}, {"name": "jockey_id", "rawType": "int64", "type": "integer"}, {"name": "trainer_id", "rawType": "int64", "type": "integer"}, {"name": "開催", "rawType": "int64", "type": "integer"}, {"name": "レース名", "rawType": "object", "type": "string"}, {"name": "date", "rawType": "object", "type": "string"}, {"name": "course_len", "rawType": "int64", "type": "integer"}, {"name": "weather", "rawType": "object", "type": "string"}, {"name": "race_type", "rawType": "object", "type": "string"}, {"name": "ground_state", "rawType": "object", "type": "string"}, {"name": "around", "rawType": "float64", "type": "float"}, {"name": "生年月日", "rawType": "object", "type": "string"}, {"name": "調教師_horse_info", "rawType": "object", "type": "string"}, {"name": "馬主_horse_info", "rawType": "object", "type": "string"}, {"name": "生産者", "rawType": "object", "type": "unknown"}, {"name": "産地", "rawType": "object", "type": "string"}, {"name": "セリ取引価格", "rawType": "object", "type": "string"}, {"name": "獲得賞金", "rawType": "object", "type": "string"}, {"name": "通算成績", "rawType": "object", "type": "string"}, {"name": "主な勝鞍", "rawType": "object", "type": "unknown"}, {"name": "近親馬", "rawType": "object", "type": "unknown"}, {"name": "horse_name", "rawType": "object", "type": "string"}, {"name": "trainer_id_horse_info", "rawType": "object", "type": "string"}, {"name": "owner_id", "rawType": "object", "type": "string"}, {"name": "breeder_id", "rawType": "object", "type": "unknown"}, {"name": "father_name", "rawType": "object", "type": "string"}, {"name": "father_id", "rawType": "object", "type": "string"}, {"name": "mother_name", "rawType": "object", "type": "string"}, {"name": "mother_id", "rawType": "object", "type": "string"}, {"name": "mother_father_name", "rawType": "object", "type": "string"}, {"name": "mother_father_id", "rawType": "object", "type": "string"}, {"name": "sibling_ids", "rawType": "object", "type": "unknown"}, {"name": "募集情報", "rawType": "object", "type": "unknown"}, {"name": "着順_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "人気_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "オッズ_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "賞金_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "斤量_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "タイム_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "上り_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "体重_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "体重変化_last_3R_mean", "rawType": "float64", "type": "float"}, {"name": "着順_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "人気_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "オッズ_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "賞金_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "斤量_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "タイム_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "上り_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "体重_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "体重変化_last_5R_mean", "rawType": "float64", "type": "float"}, {"name": "着順_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "人気_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "オッズ_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "賞金_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "斤量_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "タイム_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "上り_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "体重_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "体重変化_last_10R_mean", "rawType": "float64", "type": "float"}, {"name": "着順_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "人気_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "オッズ_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "賞金_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "斤量_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "タイム_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "上り_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "体重_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "体重変化_all_R_mean", "rawType": "float64", "type": "float"}, {"name": "last_race_date", "rawType": "object", "type": "unknown"}, {"name": "interval_days", "rawType": "float64", "type": "float"}], "ref": "bd4032d9-4447-41f0-9065-052443b370bc", "rows": [["0", "1", "8", "15", "ニシノアナ", "牝3", "51.0", "横山琉人", "1:12.5", null, "**", "2-2", "37.6", "6.8", "4.0", "456(+4)", null, null, null, "[東] 相沢郁", "西山茂行", "520.0", "202206010101", "2019103610", "1192", "1020", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-03-03", "川島洋人 (北海道)", "西山茂行", "杵臼牧場", "浦河町", "-", "1,540万円 (中央) /60万円 (地方)", "29戦2勝 [2-1-2-24]", "22'3歳以上1勝クラス", "ブルベアモル, ポセイドンバローズ", "ニシノアナ (<PERSON><PERSON><PERSON>)", "05706", "897009", "400032", "ドレフォン", "000a013b82", "<PERSON><PERSON><PERSON><PERSON>", "000a013b81", "レッドセイリング", "2010104204", "2017105999, 2022102752", null, "9.666666666666666", "8.333333333333334", "35.36666666666667", "43.333333333333336", "53.0", null, "39.766666666666666", "454.0", "-5.333333333333333", "9.25", "7.0", "28.3", "32.5", "53.25", null, "38.85", "457.5", "-4.0", "9.25", "7.0", "28.3", "32.5", "53.25", null, "38.85", "457.5", "-4.0", "9.25", "7.0", "28.3", "32.5", "53.25", null, "38.85", "457.5", "-4.0", "2021-12-12", "24.0"], ["1", "2", "5", "10", "トラストパッキャオ", "牝3", "54.0", "菅原明良", "1:12.5", "クビ", "**", "1-1", "37.7", "57.2", "12.0", "458(+2)", null, null, null, "[東] 高木登", "菅波雅巳", "210.0", "202206010101", "2019100855", "1179", "1088", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-02-14", "田中守 (高知)", "菅波雅巳", "松浦牧場", "新冠町", "-", "1,578万円 (中央) /90万円 (地方)", "19戦3勝 [3-2-0-14]", "22'3歳以上1勝クラス", "サンナイト, ジュランビル", "トラストパッキャオ (Trust Pacquiao)", "05580", "125008", "430316", "ヘニーヒューズ", "000a011155", "Meadow Flyer", "000a01117d", "アリー", "000a011502", "2014100781, 2016100794", null, "11.0", "8.0", "24.75", "0.0", "54.0", null, "39.25", "455.0", "1.0", "11.0", "8.0", "24.75", "0.0", "54.0", null, "39.25", "455.0", "1.0", "11.0", "8.0", "24.75", "0.0", "54.0", null, "39.25", "455.0", "1.0", "11.0", "8.0", "24.75", "0.0", "54.0", null, "39.25", "455.0", "1.0", "2021-09-19", "108.0"], ["2", "3", "2", "4", "マイネルシトラス", "牡3", "56.0", "柴田大知", "1:12.5", "クビ", "**", "4-3", "37.3", "3.7", "1.0", "518(-2)", null, null, null, "[東] 武市康男", "サラブレッドクラブ・ラフィアン", "130.0", "202206010101", "2019103542", "1009", "1089", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-05-06", "打越勇児 (高知)", "（株）サラブレッドク", "金石牧場", "浦河町", "-", "3,129万円 (中央) /3,049万円 (地方)", "40戦10勝 [10-4-9-17]", "大高坂賞", "マイネルブリオン, マイネルサンタアナ", "マイネルシトラス (Meiner Citrus)", "a02b0", "546800", "10035", "ジョーカプチーノ", "2006100529", "ジョープシケ", "2000102239", "マイネオレンジ", "2010105940", "2022102681, 2016100734", "1口:15万円/100口", "3.333333333333333", "6.0", "25.066666666666663", "127.0", "55.0", null, "37.96666666666667", "518.0", "2.0", "4.6", "8.4", "52.08", "76.2", "55.0", null, "37.88", "516.0", "1.6", "4.6", "8.4", "52.08", "76.2", "55.0", null, "37.88", "516.0", "1.6", "4.6", "8.4", "52.08", "76.2", "55.0", null, "37.88", "516.0", "1.6", "2021-12-11", "25.0"], ["3", "4", "1", "2", "ピカリエ", "牝3", "54.0", "伊藤工真", "1:12.8", "1.1/2", "**", "10-7", "37.2", "16.0", "9.0", "486(+6)", null, null, null, "[東] 金成貴史", "リトルブルーファーム", "78.0", "202206010101", "2019104288", "1119", "1132", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-03-14", "金成貴史 (美浦)", "リトルブルーファーム", "リトルブルーファーム", "清水町", "-", "4,171万円 (中央)", "26戦2勝 [2-5-4-15]", "24'４歳上５００万下", "オヒナサマ, アマクサマンボ", "ピカリエ (Pi<PERSON><PERSON>)", "01132", "064803", "150100", "エイシンヒカリ", "2011101273", "キャタリナ", "000a00b4d3", "バンデリータ", "2003103432", "2014104165, 2010106217", null, "11.0", "15.333333333333334", "234.8", "17.0", "54.0", null, "38.0", "477.3333333333333", "-0.6666666666666666", "11.0", "15.333333333333334", "234.8", "17.0", "54.0", null, "38.0", "477.3333333333333", "-0.6666666666666666", "11.0", "15.333333333333334", "234.8", "17.0", "54.0", null, "38.0", "477.3333333333333", "-0.6666666666666666", "11.0", "15.333333333333334", "234.8", "17.0", "54.0", null, "38.0", "477.3333333333333", "-0.6666666666666666", "2021-12-12", "24.0"], ["4", "5", "8", "16", "ブラッドライン", "牡3", "56.0", "Ｍ．デム", "1:13.2", "2.1/2", "**", "4-5", "38.0", "10.0", "5.0", "478(-2)", null, null, null, "[東] 伊藤大士", "前原敏行", "52.0", "202206010101", "2019101003", "5212", "1109", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-03-31", "田島寿一 (川崎)", "神田雅行", "村田牧場", "新冠町", "1,705万円 (2021年 千葉サラブレッドセール)", "180万円 (中央) /908万円 (地方)", "23戦6勝 [6-5-2-10]", "馬い!八幡平バイオレット記念", "ハセノブライアン, ハセノエクスプレス", "ブラッドライン (Bloodline)", "05569", "x0905f", "400317", "ローレルゲレイロ", "2004100933", "ビッグテンビー", "1998100880", "フルオブスターズ", "2013102086", "2022107078, 2018102358", null, "6.333333333333333", "3.0", "7.0", "42.66666666666666", "54.66666666666666", null, "37.96666666666667", "474.0", "4.0", "6.333333333333333", "3.0", "7.0", "42.66666666666666", "54.66666666666666", null, "37.96666666666667", "474.0", "4.0", "6.333333333333333", "3.0", "7.0", "42.66666666666666", "54.66666666666666", null, "37.96666666666667", "474.0", "4.0", "6.333333333333333", "3.0", "7.0", "42.66666666666666", "54.66666666666666", null, "37.96666666666667", "474.0", "4.0", "2021-12-18", "18.0"], ["5", "6", "4", "8", "デザートサファリ", "牡3", "56.0", "丹内祐次", "1:13.4", "1", "**", "2-3", "38.4", "6.3", "3.0", "470(-4)", null, null, null, "[東] 岩戸孝樹", "田頭勇貴", null, "202206010101", "2019101217", "1091", "1051", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-02-24", "井樋一也 (金沢)", "小橋亮太", "船越伸也", "平取町", "495万円 (2020年 北海道サマーセール)", "200万円 (中央) /3万円 (地方)", "7戦0勝 [0-1-0-6]", null, "ジャングリオン, ケアロハ", "デザートサファリ (Desert Safari)", "05781", "673031", "940367", "エスポワールシチー", "2005102837", "エミネントシチー", "1998106179", "ビヨンドマイリーチ", "2006102087", "2016103994, 2013106023", null, "7.5", "5.5", "14.3", "100.0", "54.5", null, "40.400000000000006", "468.0", "6.0", "7.5", "5.5", "14.3", "100.0", "54.5", null, "40.400000000000006", "468.0", "6.0", "7.5", "5.5", "14.3", "100.0", "54.5", null, "40.400000000000006", "468.0", "6.0", "7.5", "5.5", "14.3", "100.0", "54.5", null, "40.400000000000006", "468.0", "6.0", "2021-12-12", "24.0"], ["6", "7", "5", "9", "ニシノスーベニア", "牡3", "56.0", "内田博幸", "1:13.7", "1.3/4", "**", "7-7", "38.4", "14.3", "8.0", "536(+28)", null, null, null, "[東] 上原博之", "西山茂行", null, "202206010101", "2019101408", "422", "423", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-03-18", "上原博之 (美浦)", "西山茂行", "西山牧場", "日高町", "-", "9,691万円 (中央)", "21戦4勝 [4-3-4-10]", "24'幕張S(3勝クラス)", "ニシノババンギタ, セイウンエンプレス", "ニシノスーベニア (Nishino Souvenir)", "00423", "897009", "1374", "ハービンジャー", "000a011996", "Penang Pearl", "000a011903", "リップル", "2010100715", "2022107024, 2018100849", null, "4.0", "11.0", "43.1", "110.0", "54.0", null, "35.2", "508.0", "0.0", "4.0", "11.0", "43.1", "110.0", "54.0", null, "35.2", "508.0", "0.0", "4.0", "11.0", "43.1", "110.0", "54.0", null, "35.2", "508.0", "0.0", "4.0", "11.0", "43.1", "110.0", "54.0", null, "35.2", "508.0", "0.0", "2021-06-06", "213.0"], ["7", "8", "6", "11", "ペルペテュエル", "牡3", "56.0", "戸崎圭太", "1:13.7", "クビ", "**", "7-7", "38.4", "6.2", "2.0", "472(0)", null, null, null, "[東] 武藤善則", "落合幸弘", null, "202206010101", "2019102271", "5386", "1064", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-03-22", "藤田輝信 (大井)", "落合幸弘", "モリナガファーム", "日高町", "1,210万円 (2020年 北海道サマーセール)", "174万円 (中央) /437万円 (地方)", "23戦1勝 [1-7-1-14]", "C2九 十11", "キンランドンス, ミテルテ", "ペルペテュエル (Perpetuel)", "05728", "252030", "213326", "キンシャサノキセキ", "2003110212", "ケルトシャーン", "000a006843", "ミルテ", "2009103108", "2016103726, 2015102045", null, "8.666666666666666", "9.333333333333334", "55.96666666666667", "23.33333333333333", "54.333333333333336", null, "36.93333333333333", "464.6666666666667", "4.666666666666667", "8.666666666666666", "9.333333333333334", "55.96666666666667", "23.33333333333333", "54.333333333333336", null, "36.93333333333333", "464.6666666666667", "4.666666666666667", "8.666666666666666", "9.333333333333334", "55.96666666666667", "23.33333333333333", "54.333333333333336", null, "36.93333333333333", "464.6666666666667", "4.666666666666667", "8.666666666666666", "9.333333333333334", "55.96666666666667", "23.33333333333333", "54.333333333333336", null, "36.93333333333333", "464.6666666666667", "4.666666666666667", "2021-10-23", "74.0"], ["8", "9", "4", "7", "アイアムラベンダー", "牝3", "54.0", "松岡正海", "1:13.9", "3/4", "**", "4-5", "38.7", "10.7", "6.0", "462(-2)", null, null, null, "[東] 奥平雅士", "堀紘一", null, "202206010101", "2019104028", "1085", "1074", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-02-25", "栗田裕光 (大井)", "堀紘一", "まるとみ冨岡牧場", "浦河町", "-", "287万円 (中央) /140万円 (地方)", "20戦1勝 [1-1-0-18]", "C2五 六", "アイアムユウシュン, アイアムハヤスギル", "アイアムラベンダー (<PERSON> <PERSON>)", "05508", "505006", "900470", "ドレフォン", "000a013b82", "<PERSON><PERSON><PERSON><PERSON>", "000a013b81", "アイアムネフライト", "2009105693", "2021103204, 2016102142", null, "6.0", "4.5", "15.45", "38.5", "54.0", null, "38.55", "468.0", "-4.0", "6.0", "4.5", "15.45", "38.5", "54.0", null, "38.55", "468.0", "-4.0", "6.0", "4.5", "15.45", "38.5", "54.0", null, "38.55", "468.0", "-4.0", "6.0", "4.5", "15.45", "38.5", "54.0", null, "38.55", "468.0", "-4.0", "2021-12-12", "24.0"], ["9", "10", "3", "6", "オールフラッグ", "牡3", "56.0", "横山武史", "1:13.9", "クビ", "**", "10-11", "38.3", "12.6", "7.0", "478(+10)", null, null, null, "[東] 奥村武", "キャロットファーム", null, "202206010101", "2019105170", "1170", "1145", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-05-10", "佐藤裕太 (船橋)", "（株）マフィン", "ノーザンファーム", "安平町", "-", "3,190万円 (中央) /744万円 (地方)", "22戦4勝 [4-4-2-12]", "23'4歳以上2勝クラス", "ナオミラフィネ, フジノタカネ", "オールフラッグ (All Flag)", "05719", "x0a2a1", "373126", "トゥザワールド", "2011103975", "トゥザヴィクトリー", "1996107386", "クルージンミジー", "2009110123", "2017105297, 2016104749", null, "5.5", "2.5", "5.0", "55.0", "54.5", null, "37.900000000000006", "469.0", "-1.0", "5.5", "2.5", "5.0", "55.0", "54.5", null, "37.900000000000006", "469.0", "-1.0", "5.5", "2.5", "5.0", "55.0", "54.5", null, "37.900000000000006", "469.0", "-1.0", "5.5", "2.5", "5.0", "55.0", "54.5", null, "37.900000000000006", "469.0", "-1.0", "2021-10-17", "80.0"], ["10", "11", "1", "1", "サノプリ", "牝3", "51.0", "小林凌大", "1:14.0", "1/2", "**", "9-7", "38.5", "258.1", "15.0", "424(+6)", null, null, null, "[東] 尾形和幸", "佐野信幸", null, "202206010101", "2019102173", "1177", "1141", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-04-15", "竹下直人 (愛知)", "（株）ファーストビジ", "豊洋牧場", "日高町", "-", "855万円 (地方)", "56戦7勝 [7-10-10-29]", "A5組", "サノレーヌ, サノノウォーリア", "サノプリ (Sano Pre)", "05270", "x06e26", "3325", "プリサイスエンド", "000a00fe9b", "Precisely", "000a00fefe", "アニマートホウヨウ", "2010101033", "2017100847, 2020102156", null, "10.5", "11.5", "160.45000000000002", "0.0", "52.5", null, "38.6", "422.0", "-4.0", "10.5", "11.5", "160.45000000000002", "0.0", "52.5", null, "38.6", "422.0", "-4.0", "10.5", "11.5", "160.45000000000002", "0.0", "52.5", null, "38.6", "422.0", "-4.0", "10.5", "11.5", "160.45000000000002", "0.0", "52.5", null, "38.6", "422.0", "-4.0", "2021-11-06", "60.0"], ["11", "12", "7", "14", "ヤマタケコーチャン", "牡3", "56.0", "丸田恭介", "1:14.2", "1.1/4", "**", "14-15", "38.3", "239.2", "14.0", "444(+2)", null, null, null, "[東] 萱野浩二", "山中和子", null, "202206010101", "2019102153", "1117", "1024", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-04-13", "水野貴史 (浦和)", "山中和子", "船越牧場", "日高町", "990万円 (2020年 北海道サマーセール)", "314万円 (中央) /238万円 (地方)", "40戦0勝 [0-3-2-35]", null, "セイウンブラスト, セイウンシヴァ", "ヤマタケコーチャン (<PERSON><PERSON><PERSON>)", "a02c6", "631002", "913367", "リーチザクラウン", "2006102923", "クラウンピース", "1997103429", "エコーズインザウインド", "000a01280c", "2016103433, 2015101770", null, "8.0", "12.0", "163.05", "0.0", "55.0", null, "39.45", "443.0", "-1.0", "8.0", "12.0", "163.05", "0.0", "55.0", null, "39.45", "443.0", "-1.0", "8.0", "12.0", "163.05", "0.0", "55.0", null, "39.45", "443.0", "-1.0", "8.0", "12.0", "163.05", "0.0", "55.0", null, "39.45", "443.0", "-1.0", "2021-11-14", "52.0"], ["12", "13", "3", "5", "キタノドーベル", "牝3", "54.0", "武士沢友", "1:14.4", "1", "**", "14-13", "38.5", "273.2", "16.0", "448(-2)", null, null, null, "[東] 深山雅史", "北所直人", null, "202206010101", "2019101943", "1029", "1174", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-03-10", "戸部尚実 (愛知)", "北所拓也", "戸川牧場", "日高町", "165万円 (2020年 北海道オータムセール)", "182万円 (中央) /269万円 (地方)", "54戦3勝 [3-5-4-42]", "C2組", "フェアチャイルドの2021, クリノチャイルド", "キタノドーベル (<PERSON><PERSON> Dober)", "a048d", "x0a72d", "833394", "エイシンフラッシュ", "2007102951", "ムーンレディ", "000a010eb7", "フェアチャイルド", "2014102890", "2021101789, 2020101923", null, "8.666666666666666", "11.333333333333334", "190.46666666666667", "0.0", "53.0", null, "38.66666666666666", "448.0", "3.333333333333333", "9.25", "11.5", "179.05", "0.0", "53.25", null, "38.125", "446.0", "2.5", "9.25", "11.5", "179.05", "0.0", "53.25", null, "38.125", "446.0", "2.5", "9.25", "11.5", "179.05", "0.0", "53.25", null, "38.125", "446.0", "2.5", "2021-10-23", "74.0"], ["13", "14", "7", "13", "アイスケイブ", "牝3", "54.0", "三浦皇成", "1:14.5", "3/4", "**", "16-15", "38.4", "25.3", "10.0", "432(+10)", null, null, null, "[東] 高柳瑞樹", "シルクレーシング", null, "202206010101", "2019105024", "1122", "1118", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-01-12", "宗綱泰彦 (金沢)", "金野博幸", "ノーザンファーム", "安平町", "-", "165万円 (地方)", "49戦2勝 [2-2-4-41]", "オリジナルコラボ下敷き配布記念", "アドマイヤデイトナ, レッドラグラス", "アイスケイブ (Ice Cave)", "05425", "x0948c", "373126", "モーリス", "2011100655", "メジロフランシス", "2001102948", "アイスパステル", "2014110112", "2022104584, 2020103183", null, "8.0", "7.0", "21.4", "0.0", "54.0", null, "35.1", "422.0", "0.0", "8.0", "7.0", "21.4", "0.0", "54.0", null, "35.1", "422.0", "0.0", "8.0", "7.0", "21.4", "0.0", "54.0", null, "35.1", "422.0", "0.0", "8.0", "7.0", "21.4", "0.0", "54.0", null, "35.1", "422.0", "0.0", "2021-10-17", "80.0"], ["14", "15", "6", "12", "ミュージアムピース", "牡3", "56.0", "黛弘人", "1:15.3", "5", "**", "12-13", "39.5", "67.9", "13.0", "464(+24)", null, null, null, "[東] 伊藤大士", "ミルファーム", null, "202206010101", "2019104143", "1109", "1109", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-02-14", "南田美知 (美浦)", "清水敏", "ミルファーム", "浦河町", "-", "970万円 (中央) /198万円 (地方)", "26戦5勝 [5-2-2-17]", "楽天競馬でカンタン投票!賞", "ムビョウソクサイ, エマウリオマレプ", "ミュージアムピース (Museum Piece)", "00437", "411033", "900515", "ディスクリートキャット", "000a0113a1", "<PERSON> Discreet", "000a0113bb", "コウギョウマシェリ", "2013103162", "2021103329, 2020106968", null, "7.0", "8.0", "55.23333333333333", "43.333333333333336", "54.333333333333336", null, "37.96666666666667", "436.0", "2.6666666666666665", "6.75", "7.75", "48.7", "32.5", "54.25", null, "37.625", "435.0", "2.0", "6.75", "7.75", "48.7", "32.5", "54.25", null, "37.625", "435.0", "2.0", "6.75", "7.75", "48.7", "32.5", "54.25", null, "37.625", "435.0", "2.0", "2021-10-09", "88.0"], ["15", "16", "2", "3", "コンセプシオン", "牝3", "54.0", "斎藤新", "1:16.0", "4", "**", "12-11", "40.2", "32.8", "11.0", "528(+4)", null, null, null, "[東] 水野貴広", "ヒダカ・ブリーダーズ・ユニオン", null, "202206010101", "2019106127", "1178", "1094", "6", "3歳未勝利", "2022-01-05", "1200", "晴", "ダート", "良", null, "2019-04-24", "九日俊光 (地方)", "山邉浩", "静内ファーム", "新ひだか町", "-", "3万円 (地方)", "9戦0勝 [0-0-0-9]", null, "コンフィテオール, テクノゴールド", "コンセプシオン (Concepcion)", "05005", "522030", "803080", "モーリス", "2011100655", "メジロフランシス", "2001102948", "コンフェッシオン", "2012104684", "2018102556, 2020104271", null, "12.0", "4.5", "8.6", "0.0", "54.0", null, "36.5", "519.0", "5.0", "12.0", "4.5", "8.6", "0.0", "54.0", null, "36.5", "519.0", "5.0", "12.0", "4.5", "8.6", "0.0", "54.0", null, "36.5", "519.0", "5.0", "12.0", "4.5", "8.6", "0.0", "54.0", null, "36.5", "519.0", "5.0", "2021-09-11", "116.0"], ["16", "1", "1", "1", "シンボリックレルム", "牝3", "54.0", "団野大成", "1:56.6", null, "**", "5-6-6-5", "39.4", "15.1", "4.0", "484(+6)", null, null, null, "[東] 水野貴広", "吉田千津", "520.0", "202206010102", "2019104958", "1180", "1094", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-02-04", "水野貴広 (美浦)", "吉田千津", "社台ファーム", "千歳市", "-", "3,182万円 (中央)", "24戦2勝 [2-3-4-15]", "24'4歳以上1勝クラス", "ナリノビスケッツ, ルージュカエラ", "シンボリックレルム (Symbolic Realm)", "01094", "310007", "393126", "エピファネイア", "2010104155", "シーザリオ", "2002100844", "レッツサッチャー", "2011104463", "2020102729, 2021104931", null, "6.333333333333333", "8.0", "68.66666666666667", "34.0", "54.0", null, "35.800000000000004", "477.3333333333333", "2.0", "8.75", "8.5", "66.3", "25.5", "54.0", null, "35.575", "476.0", "1.5", "8.75", "8.5", "66.3", "25.5", "54.0", null, "35.575", "476.0", "1.5", "8.75", "8.5", "66.3", "25.5", "54.0", null, "35.575", "476.0", "1.5", "2021-12-19", "17.0"], ["17", "2", "4", "8", "タマモタップダンス", "牝3", "51.0", "永野猛蔵", "1:56.6", "アタマ", "**", "3-3-3-3", "39.7", "1.5", "1.0", "490(-2)", null, null, null, "[東] 伊藤圭三", "タマモ", "210.0", "202206010102", "2019106690", "1188", "1023", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-03-23", "伊藤圭三 (美浦)", "タマモ", "前田牧場", "新ひだか町", "-", "4,484万円 (中央)", "24戦2勝 [2-8-2-12]", "23'3歳以上1勝クラス", "タマモアモーレ, タマモマズルカ", "タマモタップダンス (Tamamo Tap Dance)", "01023", "515800", "333302", "エスポワールシチー", "2005102837", "エミネントシチー", "1998106179", "チャームダンス", "2003105515", "2015106238, 2011102611", null, "2.0", "1.0", "2.1", "280.0", "51.0", null, "39.8", "492.0", "0.0", "2.0", "1.0", "2.1", "280.0", "51.0", null, "39.8", "492.0", "0.0", "2.0", "1.0", "2.1", "280.0", "51.0", null, "39.8", "492.0", "0.0", "2.0", "1.0", "2.1", "280.0", "51.0", null, "39.8", "492.0", "0.0", "2021-11-21", "45.0"], ["18", "3", "5", "10", "ハッピーバレー", "牝3", "54.0", "嶋田純次", "1:56.9", "2", "**", "3-3-3-3", "40.0", "8.5", "3.0", "462(0)", null, null, null, "[東] 手塚貴久", "馬場幸夫", "130.0", "202206010102", "2019105332", "1134", "1038", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-01-27", "佐々木仁 (川崎)", "馬場幸夫", "ノーザンファーム", "安平町", "-", "678万円 (中央) /923万円 (地方)", "29戦3勝 [3-3-3-20]", "やまなみ五湖「宮ヶ瀬湖」「奥相模湖」賞", "タイムマシン, ニューポート", "ハッピーバレー (Happy Valley)", "05388", "431002", "373126", "アジアエクスプレス", "2011110091", "ランニングボブキャッツ", "000a0121b7", "ハッピーパレード", "2006103394", "2017105466, 2016104564", null, "6.5", "7.5", "29.55", "0.0", "52.0", null, "34.95", "464.0", "-2.0", "6.5", "7.5", "29.55", "0.0", "52.0", null, "34.95", "464.0", "-2.0", "6.5", "7.5", "29.55", "0.0", "52.0", null, "34.95", "464.0", "-2.0", "6.5", "7.5", "29.55", "0.0", "52.0", null, "34.95", "464.0", "-2.0", "2021-07-03", "186.0"], ["19", "4", "6", "12", "フジシラユキヒメ", "牝3", "54.0", "菅原明良", "1:57.1", "1.1/4", "**", "1-1-1-1", "40.5", "17.1", "6.0", "474(-2)", null, null, null, "[東] 尾形和幸", "フジレーシング", "78.0", "202206010102", "2019107005", "1179", "1141", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-05-03", "佐藤博紀 (川崎)", "富士ファーム", "村上進治", "新冠町", "704万円 (2020年 北海道セプテンバーセール)", "277万円 (中央) /154万円 (地方)", "21戦0勝 [0-2-0-19]", null, "レジーナクィーン, マツリダスティール", "フジシラユキヒメ (<PERSON>)", "a02ea", "745800", "790543", "パイロ", "000a01176a", "Wild Vision", "000a011794", "シェーンブリッツ", "2011105042", "2017106638, 2018102309", null, "8.333333333333334", "4.666666666666667", "14.333333333333334", "17.0", "53.0", null, "39.76666666666667", "472.6666666666667", "1.3333333333333333", "7.8", "6.0", "36.58", "24.2", "52.2", null, "38.42", "471.2", "2.0", "7.8", "6.0", "36.58", "24.2", "52.2", null, "38.42", "471.2", "2.0", "7.8", "6.0", "36.58", "24.2", "52.2", null, "38.42", "471.2", "2.0", "2021-12-11", "25.0"], ["20", "5", "8", "16", "レオミネルバ", "牝3", "54.0", "津村明秀", "1:57.2", "クビ", "**", "2-2-2-2", "40.5", "92.4", "10.0", "482(+4)", null, null, null, "[東] 牧光二", "レオ", "52.0", "202206010102", "2019101445", "1092", "1106", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-28", "渡邊貴光 (船橋)", "田中博之", "川端正博", "日高町", "-", "416万円 (中央) /70万円 (地方)", "20戦0勝 [0-0-3-17]", null, "アストロブレイク, ホッコーハナミチ", "レオミネルバ (<PERSON>)", "a02af", "910008", "513353", "ストロングリターン", "2006102934", "コートアウト", "000a0105bc", "シーノットラブユー", "2011102728", "2017100195, 2018100220", null, "9.0", "5.0", "7.7", "0.0", "54.0", null, "41.1", "478.0", "0.0", "9.0", "5.0", "7.7", "0.0", "54.0", null, "41.1", "478.0", "0.0", "9.0", "5.0", "7.7", "0.0", "54.0", null, "41.1", "478.0", "0.0", "9.0", "5.0", "7.7", "0.0", "54.0", null, "41.1", "478.0", "0.0", "2021-12-25", "11.0"], ["21", "6", "7", "14", "ノーブルグレイシャ", "牝3", "51.0", "横山琉人", "1:57.6", "2.1/2", "**", "6-6-6-6", "40.3", "72.1", "9.0", "472(0)", null, null, null, "[東] 武藤善則", "吉木伸彦", null, "202206010102", "2019106415", "1192", "1064", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-05-10", "佐野謙二 (地方)", "吉木伸彦", "飛野牧場", "新ひだか町", "-", "286万円 (中央) /290万円 (地方)", "29戦1勝 [1-1-3-24]", "Ｃ２二　三四", "ノーブルスノー, ノーブルアース", "ノーブルグレイシャ (Noble Glacier)", "05746", "755030", "703082", "シニスターミニスター", "000a011099", "Sweet Minister", "000a01101c", "ピサノルビー", "2006101331", "2013104595, 2015105986", null, "10.5", "9.0", "49.1", "0.0", "52.5", null, "39.85", "477.0", "-5.0", "10.5", "9.0", "49.1", "0.0", "52.5", null, "39.85", "477.0", "-5.0", "10.5", "9.0", "49.1", "0.0", "52.5", null, "39.85", "477.0", "-5.0", "10.5", "9.0", "49.1", "0.0", "52.5", null, "39.85", "477.0", "-5.0", "2021-12-25", "11.0"], ["22", "7", "6", "11", "ニケテア", "牝3", "51.0", "小林凌大", "1:57.8", "1.1/2", "**", "8-9-10-8", "40.2", "198.1", "14.0", "456(+10)", null, null, null, "[東] 武井亮", "エースレーシング", null, "202206010102", "2019104335", "1177", "1147", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-02-27", "佐藤裕太 (船橋)", "川田篤", "阿部栄乃進", "厚真町", "1,650万円 (2021年 JRAブリーズアップセール)", "78万円 (中央) /245万円 (地方)", "20戦1勝 [1-0-1-18]", "3歳四", "レサイヤ, リーガルリリー", "ニケテア (<PERSON>)", "05719", "x0ab96", "3492", "ザファクター", "000a0130d0", "Greyciousness", "000a0130e4", "ファイナルリリー", "2007102337", "2016104058, 2015103904", null, "7.666666666666667", "12.0", "122.8", "0.0", "52.66666666666666", null, "41.1", "448.0", "-3.333333333333333", "9.4", "11.0", "98.04", "0.0", "52.6", null, "40.260000000000005", "450.8", "-1.6", "9.4", "11.0", "98.04", "0.0", "52.6", null, "40.260000000000005", "450.8", "-1.6", "9.4", "11.0", "98.04", "0.0", "52.6", null, "40.260000000000005", "450.8", "-1.6", "2021-10-16", "81.0"], ["23", "8", "1", "2", "ジョブックフィオリ", "牝3", "52.0", "山田敬士", "1:58.1", "1.1/2", "**", "8-9-10-8", "40.6", "207.1", "15.0", "434(+4)", null, null, null, "[東] 小桧山悟", "萩本企画", null, "202206010102", "2019105695", "1173", "1005", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-05-11", "栗本陽一 (笠松)", "吉田勝利", "池田スタッド", "新ひだか町", "-", "130万円 (中央) /188万円 (地方)", "28戦3勝 [3-1-2-22]", "短足はげしめじ君", "ジョブックビザーレ, ジョブックコメン", "ジョブックフィオリ (<PERSON>)", "a02e1", "554008", "610067", "ルーラーシップ", "2007103143", "エアグルーヴ", "1993109154", "カルトマリーヌ", "2007100433", "2016105410, 2015105352", null, "13.0", "13.0", "261.46666666666664", "0.0", "52.0", null, "35.86666666666667", "430.0", "-0.6666666666666666", "11.2", "11.2", "174.86", "0.0", "52.0", null, "36.2", "431.6", "-1.2", "11.2", "11.2", "174.86", "0.0", "52.0", null, "36.2", "431.6", "-1.2", "11.2", "11.2", "174.86", "0.0", "52.0", null, "36.2", "431.6", "-1.2", "2021-11-07", "59.0"], ["24", "9", "2", "3", "トゥザヒロイン", "牝3", "54.0", "北村宏司", "1:58.2", "クビ", "**", "13-11-10-11", "40.7", "7.6", "2.0", "522(+2)", null, null, null, "[東] 木村哲也", "ＤＭＭドリームクラブ", null, "202206010102", "2019105314", "1043", "1126", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-02-15", "土屋洋之 (兵庫)", "橘勝年", "ノーザンファーム", "安平町", "2,916万円 (2019年 セレクトセール)", "1,397万円 (中央)", "20戦1勝 [1-0-3-16]", "22'3歳未勝利", "クールファイブ, ミヤジユウダイ", "トゥザヒロイン (To the Heroine)", "05700", "x091aa", "373126", "ジャスタウェイ", "2009106461", "シビル", "1999106889", "トゥザハピネス", "2009106245", "2018105159, 2014106037", null, "7.0", "3.5", "7.6", "38.5", "54.0", null, "37.400000000000006", "517.0", "3.0", "7.0", "3.5", "7.6", "38.5", "54.0", null, "37.400000000000006", "517.0", "3.0", "7.0", "3.5", "7.6", "38.5", "54.0", null, "37.400000000000006", "517.0", "3.0", "7.0", "3.5", "7.6", "38.5", "54.0", null, "37.400000000000006", "517.0", "3.0", "2021-12-11", "25.0"], ["25", "10", "4", "7", "ニシノカケハシ", "牝3", "54.0", "丹内祐次", "1:58.6", "2.1/2", "**", "6-3-3-6", "41.7", "102.2", "11.0", "454(-4)", null, null, null, "[東] 松山将樹", "西山茂行", null, "202206010102", "2019101381", "1091", "1100", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-02-16", "広森久雄 (北海道)", "田村誠", "沖田牧場", "日高町", "-", "256万円 (地方)", "59戦2勝 [2-4-8-45]", "3歳以上C4ー4", "ニシノハナミズキ, ニシノレガシー", "ニシノカケハシ (<PERSON><PERSON><PERSON>)", "05233", "454031", "203347", "クリエイターII", "000a013630", "<PERSON><PERSON>", "000a01362f", "ニシノミチシルベ", "2011103729", "2017100151, 2018100151", null, "10.666666666666666", "11.0", "314.2333333333333", "0.0", "53.0", null, "36.66666666666666", "458.6666666666667", "0.6666666666666666", "9.8", "10.0", "248.78000000000003", "0.0", "53.4", null, "36.04", "457.2", "2.0", "9.166666666666666", "9.833333333333334", "224.45", "0.0", "53.5", null, "35.85", "455.6666666666667", "1.6666666666666667", "9.166666666666666", "9.833333333333334", "224.45", "0.0", "53.5", null, "35.85", "455.6666666666667", "1.6666666666666667", "2021-12-12", "24.0"], ["26", "11", "7", "13", "アメカテソーロ", "牝3", "54.0", "横山和生", "1:59.2", "3.1/2", "**", "14-13-9-12", "41.8", "21.3", "7.0", "470(+2)", null, null, null, "[東] 加藤士津", "了徳寺健二ホールディングス", null, "202206010102", "2019102387", "1140", "1169", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-03-17", "東眞市 (佐賀)", "了徳寺健二ホールディングス", "リョーケンファーム", "日高町", "-", "339万円 (中央) /7万円 (地方)", "13戦0勝 [0-1-0-12]", null, "アトリーテソーロ, チャイブテソーロ", "アメカテソーロ (Ameca <PERSON>)", "05601", "089803", "330237", "アドマイヤムーン", "2003102991", "マイケイティーズ", "1998101750", "メイドインリオ", "000a012f8d", "2020102382, 2018101054", null, "8.0", "8.333333333333334", "44.9", "17.0", "53.0", null, "34.53333333333333", "469.3333333333333", "0.0", "8.0", "8.333333333333334", "44.9", "17.0", "53.0", null, "34.53333333333333", "469.3333333333333", "0.0", "8.0", "8.333333333333334", "44.9", "17.0", "53.0", null, "34.53333333333333", "469.3333333333333", "0.0", "8.0", "8.333333333333334", "44.9", "17.0", "53.0", null, "34.53333333333333", "469.3333333333333", "0.0", "2021-12-11", "25.0"], ["27", "12", "5", "9", "アコークロー", "牝3", "54.0", "池添謙一", "1:59.2", "アタマ", "**", "16-15-14-14", "40.1", "24.2", "8.0", "484(+6)", null, null, null, "[東] 大竹正博", "Ｇ１レーシング", null, "202206010102", "2019104478", "1032", "1102", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-03-12", "大竹正博 (美浦)", "Ｇ１レーシング", "追分ファーム", "安平町", "-", "7,250万円 (中央)", "26戦3勝 [3-6-4-13]", "25'成田特別(2勝クラス)", "ギャラクシーソウル, ミッキーヌチバナ", "アコークロー (Akokuro)", "01102", "808800", "301513", "フェノーメノ", "2009106599", "ディラローシェ", "000a00fe54", "ヌチバナ", "2008102618", "2017105037, 2018105431", "1口:25万円/40口", "11.0", "6.0", "20.9", "0.0", "54.0", null, "36.9", "478.0", "0.0", "11.0", "6.0", "20.9", "0.0", "54.0", null, "36.9", "478.0", "0.0", "11.0", "6.0", "20.9", "0.0", "54.0", null, "36.9", "478.0", "0.0", "11.0", "6.0", "20.9", "0.0", "54.0", null, "36.9", "478.0", "0.0", "2021-12-18", "18.0"], ["28", "13", "8", "15", "サクセスソング", "牝3", "54.0", "木幡初也", "1:59.3", "1/2", "**", "8-8-6-10", "42.0", "216.0", "16.0", "442(+2)", null, null, null, "[東] 竹内正洋", "鈴木芳夫", null, "202206010102", "2019106725", "1153", "1152", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-03", "鋤田誠二 (金沢)", "組）玉川", "見上牧場", "新ひだか町", "-", "446万円 (地方)", "61戦5勝 [5-10-6-40]", "中日杯ライブ予想配信記念", "サクセスバラード, サクセスエース", "サクセスソング (Success Song)", "a027e", "x0aec8", "733306", "ダノンシャンティ", "2007105709", "シャンソネット", "000a01054d", "ミスベルツリー", "2006104843", "2020104898, 2018104460", null, "9.5", "8.0", "44.150000000000006", "0.0", "54.0", null, "37.45", "441.0", "-1.0", "9.5", "8.0", "44.150000000000006", "0.0", "54.0", null, "37.45", "441.0", "-1.0", "9.5", "8.0", "44.150000000000006", "0.0", "54.0", null, "37.45", "441.0", "-1.0", "9.5", "8.0", "44.150000000000006", "0.0", "54.0", null, "37.45", "441.0", "-1.0", "2021-12-18", "18.0"], ["29", "14", "3", "6", "カゼノカケハシ", "牝3", "52.0", "藤田菜七", "1:59.9", "3.1/2", "**", "15-14-13-12", "41.5", "121.2", "12.0", "398(-4)", null, null, null, "[東] 根本康広", "福田光博", null, "202206010102", "2019109057", "1164", "1029", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-03-15", "井樋明正 (佐賀)", "金野博幸", "野々宮牧場", "青森県", "88万円 (2020年 八戸市場)", "353万円 (地方)", "52戦3勝 [3-8-10-31]", "加藤真透☆友貴夫妻入籍1周年記念", "メイドインドリーム, ハンドリー", "カゼノカケハシ (<PERSON><PERSON><PERSON>)", "05759", "x0948c", "303143", "ウインバリアシオン", "2008103206", "スーパーバレリーナ", "1994109504", "タムロマンサーナ", "2001109258", "2014109034, 2015109031", null, "11.0", "12.0", "112.2", "0.0", "54.0", null, "34.9", "402.0", "0.0", "11.0", "12.0", "112.2", "0.0", "54.0", null, "34.9", "402.0", "0.0", "11.0", "12.0", "112.2", "0.0", "54.0", null, "34.9", "402.0", "0.0", "11.0", "12.0", "112.2", "0.0", "54.0", null, "34.9", "402.0", "0.0", "2021-10-23", "74.0"], ["30", "15", "3", "5", "ミニョン", "牝3", "51.0", "小林脩斗", "2:02.4", "大", "**", "11-11-14-15", "43.2", "16.3", "5.0", "446(-4)", null, null, null, "[東] 矢野英一", "落合幸弘", null, "202206010102", "2019103921", "1183", "1108", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-03", "田中譲二 (高知)", "今井高之", "富田牧場", "浦河町", "1,210万円 (2020年 北海道サマーセール)", "110万円 (中央) /2,873万円 (地方)", "42戦10勝 [10-5-2-25]", "レジーナディンヴェルノ賞", "グラブビクトリー, レッドサイオン", "ミニョン (Mignon)", "a00ed", "x0adf2", "700014", "ホッコータルマエ", "2009100921", "マダムチェロキー", "2001105650", "レッドベルフィーユ", "2010104316", "2022103064, 2016104672", null, "4.0", "9.0", "51.9", "110.0", "54.0", null, "41.9", "450.0", "0.0", "4.0", "9.0", "51.9", "110.0", "54.0", null, "41.9", "450.0", "0.0", "4.0", "9.0", "51.9", "110.0", "54.0", null, "41.9", "450.0", "0.0", "4.0", "9.0", "51.9", "110.0", "54.0", null, "41.9", "450.0", "0.0", "2021-12-18", "18.0"], ["31", "中", "2", "4", "フォーウィンズ", "牝3", "53.0", "秋山稔樹", null, null, "**", "11", null, "137.0", "13.0", "464(-2)", null, null, null, "[東] 田島俊明", "小原準一郎", null, "202206010102", "2019102503", "1181", "1112", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-18", "田島俊明 (美浦)", "小原準一郎", "上山牧場", "浦河町", "-", "0万円", "3戦0勝 [0-0-0-3]", null, "シーサンプーター, ナインゲーツ", "フォーウィンズ (Four Winds)", "01112", "853030", "600049", "ディスクリートキャット", "000a0113a1", "<PERSON> Discreet", "000a0113bb", "ヌーヴォモンド", "2013101270", "2017100908, 2018101085", null, "7.5", "7.0", "30.25", "0.0", "52.5", null, "37.7", "468.0", "-2.0", "7.5", "7.0", "30.25", "0.0", "52.5", null, "37.7", "468.0", "-2.0", "7.5", "7.0", "30.25", "0.0", "52.5", null, "37.7", "468.0", "-2.0", "7.5", "7.0", "30.25", "0.0", "52.5", null, "37.7", "468.0", "-2.0", "2021-12-25", "11.0"], ["32", "1", "3", "5", "クアトロマジコ", "牡3", "56.0", "横山和生", "1:56.2", null, "**", "2-2-2-1", "38.8", "4.3", "2.0", "522(+4)", null, null, null, "[東] 宮田敬介", "大塚亮一", "520.0", "202206010103", "2019105458", "1140", "1175", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-14", "堀内岳志 (美浦)", "大塚亮一", "ノーザンファーム", "安平町", "6,600万円 (2020年 セレクトセール)", "1,789万円 (中央)", "17戦2勝 [2-0-2-13]", "22'3歳1勝クラス", "レーツェル, ジョディー", "クアトロマジコ (Quattro Magico)", "01189", "858800", "373126", "サトノアラジン", "2011104063", "マジックストーム", "000a01116e", "ミスティークII", "000a0120f6", "2015105025, 2016104858", null, "3.6666666666666665", "3.333333333333333", "7.466666666666666", "110.0", "54.66666666666666", null, "37.46666666666667", "518.6666666666666", "0.0", "3.6666666666666665", "3.333333333333333", "7.466666666666666", "110.0", "54.66666666666666", null, "37.46666666666667", "518.6666666666666", "0.0", "3.6666666666666665", "3.333333333333333", "7.466666666666666", "110.0", "54.66666666666666", null, "37.46666666666667", "518.6666666666666", "0.0", "3.6666666666666665", "3.333333333333333", "7.466666666666666", "110.0", "54.66666666666666", null, "37.46666666666667", "518.6666666666666", "0.0", "2021-12-11", "25.0"], ["33", "2", "4", "7", "ケイティマジック", "牡3", "56.0", "菊沢一樹", "1:56.5", "1.3/4", "**", "4-3-4-3", "38.9", "278.8", "12.0", "482(+8)", null, null, null, "[東] 清水英克", "瀧本和義", "210.0", "202206010103", "2019102785", "1161", "1080", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-03-29", "保利幸作 (兵庫)", "瀧本和義", "フクオカファーム", "浦河町", "990万円 (2020年 北海道サマーセール)", "1,100万円 (中央) /15万円 (地方)", "21戦1勝 [1-1-2-17]", "22'3歳未勝利", "チョウレンチャン, ラシェーラ", "ケイティマジック (K T Magic)", "a0221", "083006", "110055", "ロゴタイプ", "2010103783", "ステレオタイプ", "2002100573", "レンヌルシャトー", "2008103065", "2014104009, 2016102636", null, "13.0", "12.666666666666666", "139.53333333333333", "0.0", "55.0", null, "37.7", "466.0", "3.333333333333333", "13.0", "12.666666666666666", "139.53333333333333", "0.0", "55.0", null, "37.7", "466.0", "3.333333333333333", "13.0", "12.666666666666666", "139.53333333333333", "0.0", "55.0", null, "37.7", "466.0", "3.333333333333333", "13.0", "12.666666666666666", "139.53333333333333", "0.0", "55.0", null, "37.7", "466.0", "3.333333333333333", "2021-12-18", "18.0"], ["34", "3", "6", "11", "インペラートル", "牡3", "56.0", "戸崎圭太", "1:56.5", "ハナ", "**", "6-6-5-5", "38.7", "1.7", "1.0", "516(+6)", null, null, null, "[東] 金成貴史", "藤沼利夫", "130.0", "202206010103", "2019104997", "5386", "1132", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-02-25", "金成貴史 (美浦)", "藤沼利夫", "青藍牧場", "登別市", "-", "780万円 (中央) /690万円 (地方)", "18戦3勝 [3-1-3-11]", "初富賞", "エーデル, コルデアニル", "インペラートル (Imperator)", "01132", "326030", "100383", "ヘニーヒューズ", "000a011155", "Meadow Flyer", "000a01117d", "ブルザンインディゴ", "2011102788", "2021105321, 2020103154", null, "2.5", "5.0", "14.0", "190.0", "54.0", null, "39.25", "501.0", "9.0", "2.5", "5.0", "14.0", "190.0", "54.0", null, "39.25", "501.0", "9.0", "2.5", "5.0", "14.0", "190.0", "54.0", null, "39.25", "501.0", "9.0", "2.5", "5.0", "14.0", "190.0", "54.0", null, "39.25", "501.0", "9.0", "2021-12-11", "25.0"], ["35", "4", "8", "15", "オセアレジェンド", "牡3", "56.0", "団野大成", "1:56.6", "1/2", "**", "1-1-1-2", "39.3", "15.9", "5.0", "484(+4)", null, null, null, "[東] 田村康仁", "ＩＨＲ", "78.0", "202206010103", "2019104371", "1180", "1027", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-03-16", "松木啓助 (高知)", "蕭敬意", "上水牧場", "むかわ町", "1,045万円 (2020年 北海道セプテンバーセール)", "396万円 (中央) /150万円 (地方)", "30戦1勝 [1-2-2-25]", "ファイナルレース", "スワンオーキッド, ミラクルダマスク", "オセアレジェンド (<PERSON>cea Legend)", "a0045", "x02efd", "343374", "パイロ", "000a01176a", "Wild Vision", "000a011794", "バンダ", "2008102349", "2020102447, 2015103931", null, "4.0", "1.0", "3.4", "110.0", "55.0", null, "41.2", "480.0", "0.0", "4.0", "1.0", "3.4", "110.0", "55.0", null, "41.2", "480.0", "0.0", "4.0", "1.0", "3.4", "110.0", "55.0", null, "41.2", "480.0", "0.0", "4.0", "1.0", "3.4", "110.0", "55.0", null, "41.2", "480.0", "0.0", "2021-12-11", "25.0"], ["36", "5", "5", "8", "キージュピター", "牡3", "56.0", "丹内祐次", "1:57.1", "3.1/2", "**", "6-6-5-5", "39.2", "37.2", "7.0", "502(+8)", null, null, null, "[東] 青木孝文", "北前孔一郎", "52.0", "202206010103", "2019100482", "1091", "1156", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-30", "川西毅 (愛知)", "北前孔一郎", "中本牧場", "新冠町", "-", "514万円 (中央) /1,520万円 (地方)", "45戦10勝 [10-7-9-19]", "B4組", "キーフェイス, キースカーフ", "キージュピター (<PERSON> Jupiter)", "05604", "338008", "800311", "ディープスカイ", "2005101358", "アビ", "000a00fb01", "キーレター", "2003104182", "2016100484, 2012106110", null, "5.0", "11.0", "113.5", "70.0", "55.0", null, "38.5", "494.0", "0.0", "5.0", "11.0", "113.5", "70.0", "55.0", null, "38.5", "494.0", "0.0", "5.0", "11.0", "113.5", "70.0", "55.0", null, "38.5", "494.0", "0.0", "5.0", "11.0", "113.5", "70.0", "55.0", null, "38.5", "494.0", "0.0", "2021-12-11", "25.0"], ["37", "6", "7", "12", "セイウンフィーバー", "牡3", "53.0", "小林凌大", "1:57.6", "2.1/2", "**", "9-8-8-9", "39.5", "54.0", "9.0", "436(+4)", null, null, null, "[東] 尾形和幸", "西山茂行", null, "202206010103", "2019100489", "1177", "1141", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-02-10", "宇都英樹 (愛知)", "和氣竜彦", "中山高鹿康", "新冠町", "-", "259万円 (中央) /138万円 (地方)", "21戦2勝 [2-1-1-17]", "飛華くん郁ちゃん結婚超記念", "ニシノオールマイト, ニシノジーク", "セイウンフィーバー (Seiun Fever)", "a03ea", "x07a35", "610317", "エイシンヒカリ", "2011101273", "キャタリナ", "000a00b4d3", "エテ", "2013110072", "2020105473, 2021107062", null, "8.333333333333334", "9.666666666666666", "140.29999999999998", "25.666666666666668", "52.66666666666666", null, "37.13333333333333", "426.6666666666667", "2.0", "9.0", "9.5", "116.2", "19.25", "53.0", null, "37.125", "426.5", "1.5", "9.0", "9.5", "116.2", "19.25", "53.0", null, "37.125", "426.5", "1.5", "9.0", "9.5", "116.2", "19.25", "53.0", null, "37.125", "426.5", "1.5", "2021-12-11", "25.0"], ["38", "7", "7", "13", "ウロボン", "牡3", "56.0", "菅原明良", "1:57.6", "アタマ", "**", "2-3-2-3", "40.1", "7.2", "3.0", "460(+2)", null, null, null, "[東] 武井亮", "井山登", null, "202206010103", "2019104352", "1179", "1147", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-02-08", "武井亮 (美浦)", "井山登", "市川牧場", "むかわ町", "2,090万円 (2020年 北海道セレクションセール)", "1,800万円 (中央)", "16戦0勝 [0-5-3-8]", null, "ドンア<PERSON><PERSON>, ドナキアーロ", "ウロボン (Urobon)", "01147", "149002", "13370", "シルバーステート", "2013105903", "シルヴァースカヤ", "000a0117fe", "アンソニカ", "2007110110", "2021104658, 2017105202", null, "3.333333333333333", "3.333333333333333", "6.8", "118.0", "55.0", null, "36.96666666666667", "455.3333333333333", "2.0", "3.0", "2.6", "5.32", "136.8", "54.6", null, "36.34", "454.0", "2.0", "2.833333333333333", "3.1666666666666665", "6.483333333333333", "160.66666666666666", "54.333333333333336", null, "36.25", "453.0", "1.6666666666666667", "2.833333333333333", "3.1666666666666665", "6.483333333333333", "160.66666666666666", "54.333333333333336", null, "36.25", "453.0", "1.6666666666666667", "2021-12-19", "17.0"], ["39", "8", "4", "6", "パワーエンブレム", "牡3", "56.0", "宮崎北斗", "1:57.7", "クビ", "**", "10-10-8-7", "39.6", "18.3", "6.0", "502(-4)", null, null, null, "[東] 中川公成", "京都ホースレーシング", null, "202206010103", "2019106702", "1118", "1081", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-11", "秋田大助 (北海道)", "北原大史", "増本牧場", "新ひだか町", "484万円 (2020年 北海道サマーセール)", "753万円 (中央) /99万円 (地方)", "31戦1勝 [1-1-1-28]", "22'3歳未勝利", "サーカスタウン, サウザンドエース", "パワーエンブレム (Power Emblem)", "a0373", "x097fd", "603302", "イスラボニータ", "2011103565", "イスラコジーン", "000a011566", "パワーオブフェイス", "2001104702", "2009104250, 2011102621", null, "7.333333333333333", "9.666666666666666", "90.7", "25.666666666666668", "54.66666666666666", null, "40.0", "506.0", "0.0", "7.333333333333333", "9.666666666666666", "90.7", "25.666666666666668", "54.66666666666666", null, "40.0", "506.0", "0.0", "7.333333333333333", "9.666666666666666", "90.7", "25.666666666666668", "54.66666666666666", null, "40.0", "506.0", "0.0", "7.333333333333333", "9.666666666666666", "90.7", "25.666666666666668", "54.66666666666666", null, "40.0", "506.0", "0.0", "2021-12-19", "17.0"], ["40", "9", "6", "10", "インパチェンス", "牡3", "53.0", "永野猛蔵", "1:58.2", "3", "**", "12-12-10-10", "39.6", "11.7", "4.0", "492(+4)", null, null, null, "[東] 小笠倫弘", "吉田千津", null, "202206010103", "2019104696", "1188", "1076", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-02-17", "笹野博司 (笠松)", "（同）ＭＩＲＡＩ", "社台ファーム", "千歳市", "-", "145万円 (地方)", "26戦3勝 [3-2-1-20]", "C13組", "オータムライラック, デルマアートマン", "インパチェンス (I<PERSON>ati<PERSON>)", "05675", "x09066", "393126", "イスラボニータ", "2011103565", "イスラコジーン", "000a011566", "オータムフラワー", "000a013dab", "2022105277, 2021104992", null, "9.0", "10.5", "63.8", "0.0", "53.5", null, "36.7", "485.0", "3.0", "9.0", "10.5", "63.8", "0.0", "53.5", null, "36.7", "485.0", "3.0", "9.0", "10.5", "63.8", "0.0", "53.5", null, "36.7", "485.0", "3.0", "9.0", "10.5", "63.8", "0.0", "53.5", null, "36.7", "485.0", "3.0", "2021-12-18", "18.0"], ["41", "10", "1", "1", "パフオブウインド", "牡3", "54.0", "山田敬士", "1:58.3", "3/4", "**", "14-15-15-11", "39.3", "42.9", "8.0", "474(+4)", null, null, null, "[東] 小桧山悟", "西森功", null, "202206010103", "2019104222", "1173", "1005", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-02", "北野真弘 (兵庫)", "ウエスト．フォレスト．ステイブル", "清水スタッド", "様似町", "-", "1,518万円 (中央) /758万円 (地方)", "38戦6勝 [6-9-6-17]", "C3", "アエノワールド, モジャータルマエ", "パフオブウインド (Puff of Wind)", "05715", "368803", "230005", "ディスクリートキャット", "000a0113a1", "<PERSON> Discreet", "000a0113bb", "ウイニングティアラ", "2008101389", "2014103118, 2020106155", null, "9.0", "12.666666666666666", "219.83333333333331", "0.0", "52.333333333333336", null, "35.0", "469.3333333333333", "1.3333333333333333", "9.0", "12.666666666666666", "219.83333333333331", "0.0", "52.333333333333336", null, "35.0", "469.3333333333333", "1.3333333333333333", "9.0", "12.666666666666666", "219.83333333333331", "0.0", "52.333333333333336", null, "35.0", "469.3333333333333", "1.3333333333333333", "9.0", "12.666666666666666", "219.83333333333331", "0.0", "52.333333333333336", null, "35.0", "469.3333333333333", "1.3333333333333333", "2021-11-13", "53.0"], ["42", "11", "8", "14", "テンオーケオー", "牡3", "56.0", "松岡正海", "1:58.8", "3", "**", "5-5-5-7", "40.9", "56.7", "10.0", "530(+2)", null, null, null, "[東] 中舘英二", "天白泰司", null, "202206010103", "2019104787", "1085", "1153", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-02-08", "佐藤雅彦 (地方)", "天白泰司", "社台ファーム", "千歳市", "-", "468万円 (中央) /371万円 (地方)", "27戦1勝 [1-4-2-20]", "しし座特別", "デルマアミダ, ディーエストッキー", "テンオーケオー (Ten Okay O)", "05417", "075006", "393126", "ローエングリン", "1999106738", "カーリング", "1992190002", "セデュウシング", "2010103759", "2018104655, 2021105098", null, "7.0", "7.0", "15.7", "34.0", "52.66666666666666", null, "39.53333333333333", "530.0", "-2.6666666666666665", "7.0", "7.0", "15.7", "34.0", "52.66666666666666", null, "39.53333333333333", "530.0", "-2.6666666666666665", "7.0", "7.0", "15.7", "34.0", "52.66666666666666", null, "39.53333333333333", "530.0", "-2.6666666666666665", "7.0", "7.0", "15.7", "34.0", "52.66666666666666", null, "39.53333333333333", "530.0", "-2.6666666666666665", "2021-10-31", "66.0"], ["43", "12", "2", "2", "テイエムクロマル", "牡3", "53.0", "小林脩斗", "1:59.2", "2.1/2", "**", "12-12-13-13", "40.3", "310.2", "13.0", "492(+10)", null, null, null, "[東] 石栗龍彦", "竹園正繼", null, "202206010103", "2019102399", "1183", "1043", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-05", "石栗龍彦 (美浦)", "竹園正繼", "若林順一", "日高町", "-", "594万円 (中央)", "22戦0勝 [0-0-1-21]", null, "サチノマリアージュ, アサヤケ", "テイエムクロマル (T <PERSON> Kuro Mar<PERSON>)", "01043", "356002", "230361", "オーシャンブルー", "2008102978", "プアプー", "000a00fe55", "ヒシアリアケ", "2005109305", "2012106009, 2011103294", null, "10.666666666666666", "13.333333333333334", "241.33333333333331", "0.0", "54.0", null, "37.333333333333336", "480.0", "0.6666666666666666", "10.0", "12.0", "188.44", "0.0", "53.4", null, "37.68", "479.6", "0.8", "10.166666666666666", "12.5", "194.66666666666663", "0.0", "53.5", null, "37.48333333333333", "479.3333333333333", "0.6666666666666666", "10.166666666666666", "12.5", "194.66666666666663", "0.0", "53.5", null, "37.48333333333333", "479.3333333333333", "0.6666666666666666", "2021-12-18", "18.0"], ["44", "13", "5", "9", "シゲルキリン", "牡3", "53.0", "横山琉人", "2:00.4", "7", "**", "10-10-10-11", "41.9", "164.4", "11.0", "526(+2)", null, null, null, "[東] 田中清隆", "森中蕃", null, "202206010103", "2019102996", "1192", "408", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-13", "九日俊光 (佐賀)", "森中蕃", "グローリーファーム", "新ひだか町", "-", "22万円 (地方)", "11戦0勝 [0-1-1-9]", null, "シゲルスピネル, ブレーザー", "シゲルキリン (<PERSON><PERSON><PERSON>)", "05005", "674004", "440543", "ラニ", "2013110045", "ヘヴンリーロマンス", "2000105086", "シゲルアンドロメダ", "2010110118", "2016101139, 2021100114", null, "12.0", "13.0", "174.0", "0.0", "55.0", null, "37.1", "524.0", "0.0", "12.0", "13.0", "174.0", "0.0", "55.0", null, "37.1", "524.0", "0.0", "12.0", "13.0", "174.0", "0.0", "55.0", null, "37.1", "524.0", "0.0", "12.0", "13.0", "174.0", "0.0", "55.0", null, "37.1", "524.0", "0.0", "2021-12-18", "18.0"], ["45", "14", "2", "3", "ハウドベルク", "牡3", "56.0", "武士沢友", "2:00.9", "3", "**", "14-12-13-13", "42.1", "330.3", "14.0", "480(0)", null, null, null, "[東] 古賀史生", "西森功", null, "202206010103", "2019107054", "1029", "392", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-18", "田中譲二 (高知)", "ウエスト．フォレスト．ステイブル", "宝寄山拓樹", "日高町", "-", "497万円 (地方)", "51戦7勝 [7-3-3-38]", "3歳ー2", "トイトイトイ, スペルマロン", "ハウドベルク (<PERSON><PERSON>)", "a00ed", "368803", "360232", "マジェスティックウォリアー", "000a011e9c", "Dream Supreme", "000a011eae", "チャンピオンダイヤ", "2008106569", "2013103645, 2014103425", null, "11.333333333333334", "12.666666666666666", "188.96666666666667", "0.0", "55.0", null, "38.93333333333334", "482.6666666666667", "4.0", "10.75", "11.25", "148.425", "0.0", "54.75", null, "38.775", "479.0", "3.0", "10.75", "11.25", "148.425", "0.0", "54.75", null, "38.775", "479.0", "3.0", "10.75", "11.25", "148.425", "0.0", "54.75", null, "38.775", "479.0", "3.0", "2021-12-18", "18.0"], ["46", "15", "3", "4", "パラチェーン", "牡3", "56.0", "木幡初也", "2:01.2", "2", "**", "6-8-12-13", "42.5", "559.8", "15.0", "444(+8)", null, null, null, "[東] 佐藤吉勝", "田頭勇貴", null, "202206010103", "2019101138", "1153", "1035", "6", "3歳未勝利", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-28", "井樋明正 (佐賀)", "大黒富美子", "中川隆", "平取町", "242万円 (2020年 北海道オータムセール)", "118万円 (地方)", "74戦1勝 [1-2-1-70]", "C2ー19組", "サイモンパルフェの2017, シールドメイデン", "パラチェーン (Parachain)", "05759", "a00021", "230362", "アンライバルド", "2006103308", "バレークイーン", "000a006412", "サイモンパルフェ", "2010106547", "2017101137, 2020101654", null, "10.0", "11.333333333333334", "247.53333333333333", "0.0", "53.0", null, "37.46666666666667", "436.0", "-0.6666666666666666", "9.2", "10.0", "169.94", "0.0", "53.2", null, "37.42", "436.0", "0.4", "9.2", "10.0", "169.94", "0.0", "53.2", null, "37.42", "436.0", "0.4", "9.2", "10.0", "169.94", "0.0", "53.2", null, "37.42", "436.0", "0.4", "2021-09-26", "101.0"], ["47", "1", "5", "5", "エクセスリターン", "牡3", "56.0", "藤井勘一", "1:58.9", null, "**", "6-5-3-1", "39.1", "6.3", "3.0", "542(0)", null, null, null, "[西] 野中賢二", "今村明浩", "600.0", "202206010104", "2019101274", "5525", "1098", "6", "3歳新馬", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-16", "坂井英光 (大井)", "今村明浩", "天羽禮治", "日高町", "-", "6,497万円 (中央) /798万円 (地方)", "23戦4勝 [4-2-3-14]", "24'観月橋S(3勝クラス)", "プレストルーチェ, クレイジーアクセル", "エクセスリターン (Excess Return)", "a03d7", "466033", "103341", "ストロングリターン", "2006102934", "コートアウト", "000a0105bc", "ベアフルート", "2009105736", "2017103375, 2015100160", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], ["48", "2", "6", "6", "ディーノエナジー", "牡3", "56.0", "横山武史", "1:59.3", "2.1/2", "**", "2-2-1-2", "39.7", "1.7", "1.0", "490(0)", null, null, null, "[西] 清水久詞", "坂本守孝", "240.0", "202206010104", "2019102810", "1170", "1110", "6", "3歳新馬", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-05-22", "堀千亜樹 (大井)", "垣本栄一", "丸村村下ファーム", "浦河町", "748万円 (2020年 北海道サマーセール)", "370万円 (中央) /222万円 (地方)", "47戦1勝 [1-2-1-43]", "C2二 三", "シエルラビクトア, サイレントギフト", "ディーノエナジー (Dino Energy)", "05607", "760033", "640052", "マジェスティックウォリアー", "000a011e9c", "Dream Supreme", "000a011eae", "ジュヴァンクル", "2011103378", "2020100961, 2018103359", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], ["49", "3", "8", "10", "ゲティスバーグ", "牡3", "56.0", "戸崎圭太", "1:59.7", "2.1/2", "**", "4-3-5-4", "39.7", "31.3", "9.0", "518(0)", null, null, null, "[東] 金成貴史", "ゴドルフィン", "150.0", "202206010104", "2019101900", "5386", "1132", "6", "3歳新馬", "2022-01-05", "1800", "晴", "ダート", "良", null, "2019-04-01", "別府真司 (高知)", "（株）ファーストビジ", "ダーレー・ジャパン・ファーム", "日高町", "-", "202万円 (中央) /223万円 (地方)", "46戦2勝 [2-2-3-39]", "ファイナルレース", "ミストラヴィアータ", "ゲティスバーグ (Gettysburg)", "a0044", "x06e26", "811540", "ヴィクトワールピサ", "2007102923", "ホワイトウォーターアフェア", "000a00039f", "トラヴィアータ", "2015101589", "2020101831", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]], "shape": {"columns": 93, "rows": 47220}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>着順</th>\n", "      <th>枠番</th>\n", "      <th>馬番</th>\n", "      <th>馬名</th>\n", "      <th>性齢</th>\n", "      <th>斤量</th>\n", "      <th>騎手</th>\n", "      <th>タイム</th>\n", "      <th>着差</th>\n", "      <th>ﾀｲﾑ指数</th>\n", "      <th>...</th>\n", "      <th>人気_all_R_mean</th>\n", "      <th>オッズ_all_R_mean</th>\n", "      <th>賞金_all_R_mean</th>\n", "      <th>斤量_all_R_mean</th>\n", "      <th>タイム_all_R_mean</th>\n", "      <th>上り_all_R_mean</th>\n", "      <th>体重_all_R_mean</th>\n", "      <th>体重変化_all_R_mean</th>\n", "      <th>last_race_date</th>\n", "      <th>interval_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>15</td>\n", "      <td>ニシノアナ</td>\n", "      <td>牝3</td>\n", "      <td>51.0</td>\n", "      <td>横山琉人</td>\n", "      <td>1:12.5</td>\n", "      <td>NaN</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>7.000000</td>\n", "      <td>28.300000</td>\n", "      <td>32.500000</td>\n", "      <td>53.250000</td>\n", "      <td>NaN</td>\n", "      <td>38.850000</td>\n", "      <td>457.500000</td>\n", "      <td>-4.000000</td>\n", "      <td>2021-12-12</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>10</td>\n", "      <td>トラストパッキャオ</td>\n", "      <td>牝3</td>\n", "      <td>54.0</td>\n", "      <td>菅原明良</td>\n", "      <td>1:12.5</td>\n", "      <td>クビ</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>8.000000</td>\n", "      <td>24.750000</td>\n", "      <td>0.000000</td>\n", "      <td>54.000000</td>\n", "      <td>NaN</td>\n", "      <td>39.250000</td>\n", "      <td>455.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2021-09-19</td>\n", "      <td>108.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>マイネルシトラス</td>\n", "      <td>牡3</td>\n", "      <td>56.0</td>\n", "      <td>柴田大知</td>\n", "      <td>1:12.5</td>\n", "      <td>クビ</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>8.400000</td>\n", "      <td>52.080000</td>\n", "      <td>76.200000</td>\n", "      <td>55.000000</td>\n", "      <td>NaN</td>\n", "      <td>37.880000</td>\n", "      <td>516.000000</td>\n", "      <td>1.600000</td>\n", "      <td>2021-12-11</td>\n", "      <td>25.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>ピカリエ</td>\n", "      <td>牝3</td>\n", "      <td>54.0</td>\n", "      <td>伊藤工真</td>\n", "      <td>1:12.8</td>\n", "      <td>1.1/2</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>15.333333</td>\n", "      <td>234.800000</td>\n", "      <td>17.000000</td>\n", "      <td>54.000000</td>\n", "      <td>NaN</td>\n", "      <td>38.000000</td>\n", "      <td>477.333333</td>\n", "      <td>-0.666667</td>\n", "      <td>2021-12-12</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>8</td>\n", "      <td>16</td>\n", "      <td>ブラッドライン</td>\n", "      <td>牡3</td>\n", "      <td>56.0</td>\n", "      <td>Ｍ．デム</td>\n", "      <td>1:13.2</td>\n", "      <td>2.1/2</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>3.000000</td>\n", "      <td>7.000000</td>\n", "      <td>42.666667</td>\n", "      <td>54.666667</td>\n", "      <td>NaN</td>\n", "      <td>37.966667</td>\n", "      <td>474.000000</td>\n", "      <td>4.000000</td>\n", "      <td>2021-12-18</td>\n", "      <td>18.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47215</th>\n", "      <td>12</td>\n", "      <td>6</td>\n", "      <td>12</td>\n", "      <td>モンファボリ</td>\n", "      <td>牝4</td>\n", "      <td>53.0</td>\n", "      <td>吉田隼人</td>\n", "      <td>1:09.5</td>\n", "      <td>1/2</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>2.200000</td>\n", "      <td>4.845000</td>\n", "      <td>409.250000</td>\n", "      <td>53.900000</td>\n", "      <td>NaN</td>\n", "      <td>34.650000</td>\n", "      <td>439.300000</td>\n", "      <td>2.000000</td>\n", "      <td>2022-11-06</td>\n", "      <td>52.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47216</th>\n", "      <td>13</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>テイエムイダテン</td>\n", "      <td>牡5</td>\n", "      <td>54.0</td>\n", "      <td>松田大作</td>\n", "      <td>1:09.8</td>\n", "      <td>1.3/4</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>6.900000</td>\n", "      <td>25.035000</td>\n", "      <td>190.650000</td>\n", "      <td>55.950000</td>\n", "      <td>NaN</td>\n", "      <td>36.140000</td>\n", "      <td>475.700000</td>\n", "      <td>0.500000</td>\n", "      <td>2022-12-11</td>\n", "      <td>17.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47217</th>\n", "      <td>14</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>ショウナンアリアナ</td>\n", "      <td>牝6</td>\n", "      <td>52.0</td>\n", "      <td>西村淳也</td>\n", "      <td>1:09.8</td>\n", "      <td>アタマ</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>7.975000</td>\n", "      <td>33.885000</td>\n", "      <td>213.675000</td>\n", "      <td>53.700000</td>\n", "      <td>NaN</td>\n", "      <td>34.730000</td>\n", "      <td>429.000000</td>\n", "      <td>0.900000</td>\n", "      <td>2022-12-11</td>\n", "      <td>17.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47218</th>\n", "      <td>15</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>アールラプチャー</td>\n", "      <td>牝4</td>\n", "      <td>52.0</td>\n", "      <td>竹之下智</td>\n", "      <td>1:10.0</td>\n", "      <td>1</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>5.200000</td>\n", "      <td>15.450000</td>\n", "      <td>352.000000</td>\n", "      <td>53.600000</td>\n", "      <td>NaN</td>\n", "      <td>36.000000</td>\n", "      <td>455.400000</td>\n", "      <td>-1.400000</td>\n", "      <td>2022-09-03</td>\n", "      <td>116.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47219</th>\n", "      <td>16</td>\n", "      <td>7</td>\n", "      <td>13</td>\n", "      <td>グレイトゲイナー</td>\n", "      <td>牡5</td>\n", "      <td>56.0</td>\n", "      <td>松本大輝</td>\n", "      <td>1:10.1</td>\n", "      <td>1</td>\n", "      <td>**</td>\n", "      <td>...</td>\n", "      <td>5.730769</td>\n", "      <td>16.946154</td>\n", "      <td>233.576923</td>\n", "      <td>55.884615</td>\n", "      <td>NaN</td>\n", "      <td>35.384615</td>\n", "      <td>463.923077</td>\n", "      <td>1.307692</td>\n", "      <td>2022-12-11</td>\n", "      <td>17.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>47220 rows × 93 columns</p>\n", "</div>"], "text/plain": ["       着順  枠番  馬番         馬名  性齢    斤量    騎手     タイム     着差 ﾀｲﾑ指数  ...  \\\n", "0       1   8  15      ニシノアナ  牝3  51.0  横山琉人  1:12.5    NaN    **  ...   \n", "1       2   5  10  トラストパッキャオ  牝3  54.0  菅原明良  1:12.5     クビ    **  ...   \n", "2       3   2   4   マイネルシトラス  牡3  56.0  柴田大知  1:12.5     クビ    **  ...   \n", "3       4   1   2       ピカリエ  牝3  54.0  伊藤工真  1:12.8  1.1/2    **  ...   \n", "4       5   8  16    ブラッドライン  牡3  56.0  Ｍ．デム  1:13.2  2.1/2    **  ...   \n", "...    ..  ..  ..        ...  ..   ...   ...     ...    ...   ...  ...   \n", "47215  12   6  12     モンファボリ  牝4  53.0  吉田隼人  1:09.5    1/2    **  ...   \n", "47216  13   3   6   テイエムイダテン  牡5  54.0  松田大作  1:09.8  1.3/4    **  ...   \n", "47217  14   4   8  ショウナンアリアナ  牝6  52.0  西村淳也  1:09.8    アタマ    **  ...   \n", "47218  15   5   9   アールラプチャー  牝4  52.0  竹之下智  1:10.0      1    **  ...   \n", "47219  16   7  13   グレイトゲイナー  牡5  56.0  松本大輝  1:10.1      1    **  ...   \n", "\n", "      人気_all_R_mean  オッズ_all_R_mean 賞金_all_R_mean  斤量_all_R_mean  \\\n", "0          7.000000       28.300000     32.500000      53.250000   \n", "1          8.000000       24.750000      0.000000      54.000000   \n", "2          8.400000       52.080000     76.200000      55.000000   \n", "3         15.333333      234.800000     17.000000      54.000000   \n", "4          3.000000        7.000000     42.666667      54.666667   \n", "...             ...             ...           ...            ...   \n", "47215      2.200000        4.845000    409.250000      53.900000   \n", "47216      6.900000       25.035000    190.650000      55.950000   \n", "47217      7.975000       33.885000    213.675000      53.700000   \n", "47218      5.200000       15.450000    352.000000      53.600000   \n", "47219      5.730769       16.946154    233.576923      55.884615   \n", "\n", "      タイム_all_R_mean  上り_all_R_mean  体重_all_R_mean  体重変化_all_R_mean  \\\n", "0                NaN      38.850000     457.500000        -4.000000   \n", "1                NaN      39.250000     455.000000         1.000000   \n", "2                NaN      37.880000     516.000000         1.600000   \n", "3                NaN      38.000000     477.333333        -0.666667   \n", "4                NaN      37.966667     474.000000         4.000000   \n", "...              ...            ...            ...              ...   \n", "47215            NaN      34.650000     439.300000         2.000000   \n", "47216            NaN      36.140000     475.700000         0.500000   \n", "47217            NaN      34.730000     429.000000         0.900000   \n", "47218            NaN      36.000000     455.400000        -1.400000   \n", "47219            NaN      35.384615     463.923077         1.307692   \n", "\n", "      last_race_date interval_days  \n", "0         2021-12-12          24.0  \n", "1         2021-09-19         108.0  \n", "2         2021-12-11          25.0  \n", "3         2021-12-12          24.0  \n", "4         2021-12-18          18.0  \n", "...              ...           ...  \n", "47215     2022-11-06          52.0  \n", "47216     2022-12-11          17.0  \n", "47217     2022-12-11          17.0  \n", "47218     2022-09-03         116.0  \n", "47219     2022-12-11          17.0  \n", "\n", "[47220 rows x 93 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["comprehensive_df = pd.read_csv('dataframe.csv')\n", "comprehensive_df"]}, {"cell_type": "code", "execution_count": 9, "id": "021c401d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Warning: Column '着順' contains non-numeric values that were converted to NaN. Handle these NaNs as appropriate (e.g., imputation, exclusion).\n", "--- After relevance score generation ---\n", "        race_id    horse_id   着順  num_starters_in_race  relevance_score\n", "0  202206010101  2019103610  1.0                  16.0             16.0\n", "1  202206010101  2019100855  2.0                  16.0             15.0\n", "2  202206010101  2019103542  3.0                  16.0             14.0\n", "3  202206010101  2019104288  4.0                  16.0             13.0\n", "4  202206010101  2019101003  5.0                  16.0             12.0\n", "Features to be used: ['枠番', '馬番', '馬名', '性齢', '斤量', '騎手', 'タイム', '着差', 'ﾀｲﾑ指数', '通過', '上り', '単勝', '人気', '馬体重', '調教ﾀｲﾑ', '厩舎ｺﾒﾝﾄ', '備考', '調教師', '馬主', '賞金(万円)', 'jockey_id', 'trainer_id', '開催', 'レース名', 'course_len', 'weather', 'race_type', 'ground_state', 'around', '調教師_horse_info', '馬主_horse_info', '生産者', '産地', 'セリ取引価格', '獲得賞金', '通算成績', '主な勝鞍', '近親馬', 'horse_name', 'trainer_id_horse_info', 'owner_id', 'breeder_id', 'father_name', 'father_id', 'mother_name', 'mother_id', 'mother_father_name', 'mother_father_id', 'sibling_ids', '募集情報', '着順_last_3R_mean', '人気_last_3R_mean', 'オッズ_last_3R_mean', '賞金_last_3R_mean', '斤量_last_3R_mean', 'タイム_last_3R_mean', '上り_last_3R_mean', '体重_last_3R_mean', '体重変化_last_3R_mean', '着順_last_5R_mean', '人気_last_5R_mean', 'オッズ_last_5R_mean', '賞金_last_5R_mean', '斤量_last_5R_mean', 'タイム_last_5R_mean', '上り_last_5R_mean', '体重_last_5R_mean', '体重変化_last_5R_mean', '着順_last_10R_mean', '人気_last_10R_mean', 'オッズ_last_10R_mean', '賞金_last_10R_mean', '斤量_last_10R_mean', 'タイム_last_10R_mean', '上り_last_10R_mean', '体重_last_10R_mean', '体重変化_last_10R_mean', '着順_all_R_mean', '人気_all_R_mean', 'オッズ_all_R_mean', '賞金_all_R_mean', '斤量_all_R_mean', 'タイム_all_R_mean', '上り_all_R_mean', '体重_all_R_mean', '体重変化_all_R_mean', 'interval_days', 'num_starters_in_race']\n", "Final split date (data before this for train, on/after for test): 2022-11-28\n", "Training data: X_train (43104, 88), y_train (43104,)\n", "  LightGBM groups: 3168 groups, TF Ranking group IDs: 43104 samples\n", "  Categorical features in training data: []\n", "Test data: X_test (4116, 88), y_test (4116,)\n", "  LightGBM groups: 288 groups, TF Ranking group IDs: 4116 samples\n", "  Categorical features in test data: []\n", "\n", "Sample of test data with predicted scores and ranks:\n", "            race_id    horse_id  predicted_score  predicted_rank   着順\n", "43104  202206050101  2020100258         0.374540             9.0  1.0\n", "43105  202206050101  2020103079         0.950714             2.0  2.0\n", "43106  202206050101  2020100656         0.731994             5.0  3.0\n", "43107  202206050101  2020100206         0.598658             8.0  4.0\n", "43108  202206050101  2020105525         0.156019            13.0  5.0\n"]}], "source": ["from datetime import timedelta\n", "import pandas as pd # pandas をインポート\n", "import numpy as np # numpy をインポート (ダミーデータ用)\n", "from module.constants import HorseInfoCols # Assuming HorseInfoCols.BIRTHDAY is defined\n", "\n", "# Define column names as constants for better maintainability\n", "ACTUAL_RANK_COLUMN_DEFAULT = '着順' # Default Japanese column name\n", "ACTUAL_RANK_COLUMN_FALLBACK = 'rank' # Fallback English column name\n", "RACE_ID_COLUMN = 'race_id'\n", "HORSE_ID_COLUMN = 'horse_id'\n", "DATE_COLUMN = 'date'\n", "\n", "# Determine the actual rank column name to be used\n", "actual_rank_column_name = ACTUAL_RANK_COLUMN_DEFAULT\n", "if actual_rank_column_name not in comprehensive_df.columns:\n", "    if ACTUAL_RANK_COLUMN_FALLBACK in comprehensive_df.columns:\n", "        actual_rank_column_name = ACTUAL_RANK_COLUMN_FALLBACK\n", "        print(f\"Warning: Column '{ACTUAL_RANK_COLUMN_DEFAULT}' not found. Using '{actual_rank_column_name}' column instead.\")\n", "    else:\n", "        raise KeyError(f\"Required rank column ('{ACTUAL_RANK_COLUMN_DEFAULT}' or '{ACTUAL_RANK_COLUMN_FALLBACK}') for relevance score generation not found in DataFrame.\")\n", "\n", "# 1. Convert rank column to numeric\n", "comprehensive_df[actual_rank_column_name] = pd.to_numeric(comprehensive_df[actual_rank_column_name], errors='coerce')\n", "\n", "# Handle NaNs after conversion (e.g., fill with a large number, or drop rows)\n", "if comprehensive_df[actual_rank_column_name].isnull().any():\n", "    print(f\"Warning: Column '{actual_rank_column_name}' contains non-numeric values that were converted to NaN. Handle these NaNs as appropriate (e.g., imputation, exclusion).\")\n", "    # Example: comprehensive_df[actual_rank_column_name] = comprehensive_df[actual_rank_column_name].fillna(99) # Fill with a large rank\n", "\n", "# 2. Calculate relevance score\n", "NUM_STARTERS_COLUMN = 'num_starters_in_race'\n", "RELEVANCE_SCORE_COLUMN = 'relevance_score'\n", "\n", "comprehensive_df[NUM_STARTERS_COLUMN] = comprehensive_df.groupby(RACE_ID_COLUMN)[actual_rank_column_name].transform('max')\n", "comprehensive_df[RELEVANCE_SCORE_COLUMN] = comprehensive_df[NUM_STARTERS_COLUMN] - comprehensive_df[actual_rank_column_name] + 1\n", "y_label_column_name = RELEVANCE_SCORE_COLUMN # This will be the target variable for ranking\n", "\n", "print(\"--- After relevance score generation ---\")\n", "print(comprehensive_df[[RACE_ID_COLUMN, HORSE_ID_COLUMN, actual_rank_column_name, NUM_STARTERS_COLUMN, y_label_column_name]].head())\n", "\n", "# Feature selection\n", "columns_to_exclude = [\n", "    RACE_ID_COLUMN, HORSE_ID_COLUMN, DATE_COLUMN, y_label_column_name, actual_rank_column_name,\n", "    'last_race_date',      # Exclude datetime64 (assuming interval_days is used)\n", "    HorseInfoCols.BIRTHDAY # Exclude datetime64 (assuming age column is used)\n", "]\n", "# If 'rank' (ACTUAL_RANK_COLUMN_FALLBACK) exists and is different from actual_rank_column_name, exclude it too.\n", "if ACTUAL_RANK_COLUMN_FALLBACK in comprehensive_df.columns and ACTUAL_RANK_COLUMN_FALLBACK != actual_rank_column_name:\n", "    columns_to_exclude.append(ACTUAL_RANK_COLUMN_FALLBACK)\n", "\n", "# Consider excluding NUM_STARTERS_COLUMN to prevent target leakage,\n", "# though it might be a useful feature indicating race scale.\n", "# If needed, add: columns_to_exclude.append(NUM_STARTERS_COLUMN)\n", "\n", "feature_columns = [\n", "    col for col in comprehensive_df.columns\n", "        if col not in columns_to_exclude\n", "]\n", "print(f\"Features to be used: {feature_columns}\")\n", "\n", "# Missing value imputation (handle based on data type)\n", "# Imputation strategies:\n", "#  - Constant value for numeric NaNs (ensure it's outside the typical range)\n", "#  - Indicator value for strings (or consider separate unknown category handling)\n", "MISSING_NUMERIC_FILL = 999.0  # Use a value larger than any reasonable rank\n", "MISSING_CATEGORY_FILL = \"Unknown\"  # Or perhaps \"\" (empty string), depending on the context\n", "MISSING_OBJECT_FILL = \"Unknown\" # For object types that fail numeric conversion\n", "\n", "\n", "for col in feature_columns:\n", "    col_dtype = comprehensive_df[col].dtype # Store dtype for re-use\n", "    # Check if it's a string\n", "    # Note: Python 3.8 compatibility: Using isinstance instead of == pd.StringDtype() to avoid TypeErrors\n", "\n", "    if isinstance(col_dtype, pd.StringDtype):\n", "        comprehensive_df[col] = comprehensive_df[col].fillna(MISSING_CATEGORY_FILL)\n", "\n", "    elif pd.api.types.is_numeric_dtype(comprehensive_df[col].dtype):\n", "        comprehensive_df[col] = comprehensive_df[col].fillna(MISSING_NUMERIC_FILL)\n", "    elif col_dtype == 'object':\n", "        # For object type, attempt numeric conversion first\n", "\n", "        try:\n", "            comprehensive_df[col] = pd.to_numeric(comprehensive_df[col], errors='coerce').fillna(MISSING_NUMERIC_FILL)\n", "            # Check if it's categorical if the number of unique values is small\n", "            # This is only a simple check, and depending on the data, may need to be more robust\n", "            if comprehensive_df[col].nunique() < 10:\n", "                print(f\"Info: Column '{col}' originally of type 'object' was converted to numeric and might be categorical. Consider one-hot encoding.\")\n", "\n", "        except Exception:\n", "            # If not convertible to numeric, treat as categorical string\n", "            comprehensive_df[col] = comprehensive_df[col].fillna(MISSING_OBJECT_FILL)\n", "    # Handle other dtypes (e.g., datetime if not already excluded) as needed\n", "\n", "# --- 2. Data Splitting (time-series aware, automatic split date determination) ---\n", "if DATE_COLUMN not in comprehensive_df.columns:\n", "    raise KeyError(f\"Required column '{DATE_COLUMN}' for data splitting not found in DataFrame.\")\n", "if not pd.api.types.is_datetime64_any_dtype(comprehensive_df[DATE_COLUMN]): # Handle possible non-datetime type\n", "    comprehensive_df[DATE_COLUMN] = pd.to_datetime(comprehensive_df[DATE_COLUMN], errors='coerce')\n", "    if comprehensive_df[DATE_COLUMN].isnull().any():\n", "        print(f\"Warning: Column '{DATE_COLUMN}' contains values that could not be converted to datetime and resulted in NaT.\")\n", "        # Depending on the use case, either raise an error or drop/impute these NaTs\n", "        # comprehensive_df.dropna(subset=[DATE_COLUMN], inplace=True)\n", "\n", "if comprehensive_df.empty or comprehensive_df[DATE_COLUMN].isnull().all():\n", "    raise ValueError(\"DataFrame is empty or contains no valid date data for splitting.\")\n", "\n", "# 利用可能なデータの最大日付を取得\n", "max_data_date = comprehensive_df[DATE_COLUMN].max()\n", "\n", "# 分割戦略: 例えば、最新のN日間をテストデータとする\n", "TEST_PERIOD_DAYS = 30  # Example: last 30 days for testing\n", "MIN_TRAIN_RACES = 50 # Increased minimum number of races for training\n", "MIN_TEST_RACES = 10  # Increased minimum number of races for testing\n", "\n", "if pd.isna(max_data_date):\n", "    raise ValueError(\"最大日付が取得できませんでした。\")\n", "\n", "# split_date をここで定義\n", "split_date = max_data_date - timedelta(days=TEST_PERIOD_DAYS)\n", "\n", "# 全レース数を取得\n", "total_unique_races = comprehensive_df[RACE_ID_COLUMN].nunique()\n", "\n", "# 学習データとテストデータのレース数を計算\n", "potential_train_races = comprehensive_df[comprehensive_df['date'] < split_date]['race_id'].nunique()\n", "potential_test_races = comprehensive_df[comprehensive_df['date'] >= split_date]['race_id'].nunique()\n", "\n", "potential_train_races = comprehensive_df[comprehensive_df[DATE_COLUMN] < split_date][RACE_ID_COLUMN].nunique()\n", "potential_test_races = comprehensive_df[comprehensive_df[DATE_COLUMN] >= split_date][RACE_ID_COLUMN].nunique()\n", "\n", "unique_dates = sorted(comprehensive_df[DATE_COLUMN].dropna().unique())\n", "\n", "if len(unique_dates) <= 1:\n", "    print(\"Warning: Only one unique date found in data. Cannot split into train/test. Using all data for training.\")\n", "    split_date = max_data_date + timedelta(days=1) # Ensure all data goes to train\n", "elif potential_train_races < MIN_TRAIN_RACES and total_unique_races > (MIN_TRAIN_RACES + MIN_TEST_RACES):\n", "    print(f\"Warning: Initial split_date ({split_date.strftime('%Y-%m-%d')}) results in too few training races ({potential_train_races}). Adjusting split_date.\")\n", "    # Try to adjust split_date to ensure MIN_TRAIN_RACES, while keeping at least MIN_TEST_RACES\n", "    # This can be complex; a simpler approach is to iterate dates backwards from max_data_date\n", "    # until MIN_TEST_RACES are in the test set, then check if MIN_TRAIN_RACES remain.\n", "    # For now, we'll keep the warning and proceed, or use a simpler adjustment.\n", "    # Simplified adjustment: if train is too small, try to make test smaller if possible\n", "    if len(unique_dates) > MIN_TEST_RACES : # Ensure we can even form a minimal test set\n", "        # Find a date that gives at least MIN_TEST_RACES for testing\n", "        for i in range(len(unique_dates) - MIN_TEST_RACES, 0, -1):\n", "            temp_split_date = pd.to_datetime(unique_dates[i])\n", "            if comprehensive_df[comprehensive_df[DATE_COLUMN] < temp_split_date][RACE_ID_COLUMN].nunique() >= MIN_TRAIN_RACES:\n", "                split_date = temp_split_date\n", "                print(f\"Adjusted split_date to {split_date.strftime('%Y-%m-%d')} to secure more training data.\")\n", "                break\n", "elif potential_test_races < MIN_TEST_RACES and total_unique_races > (MIN_TRAIN_RACES + MIN_TEST_RACES):\n", "    print(f\"Warning: Initial split_date ({split_date.strftime('%Y-%m-%d')}) results in too few test races ({potential_test_races}). Adjusting split_date.\")\n", "    # Try to adjust split_date to ensure MIN_TEST_RACES\n", "    if len(unique_dates) > MIN_TRAIN_RACES :\n", "         for i in range(MIN_TRAIN_RACES, len(unique_dates) - MIN_TEST_RACES +1):\n", "            temp_split_date = pd.to_datetime(unique_dates[i])\n", "            if comprehensive_df[comprehensive_df[DATE_COLUMN] >= temp_split_date][RACE_ID_COLUMN].nunique() >= MIN_TEST_RACES:\n", "                split_date = temp_split_date\n", "                print(f\"Adjusted split_date to {split_date.strftime('%Y-%m-%d')} to secure more test data.\")\n", "                break\n", "elif comprehensive_df[DATE_COLUMN].min() >= split_date and len(unique_dates) > 1 :\n", "    # All data falls into test period, and there's more than one date\n", "    if len(unique_dates) // 2 > 0 :\n", "        split_point_index = len(unique_dates) // 2\n", "        split_date = pd.to_datetime(unique_dates[split_point_index])\n", "        print(f\"Warning: Initial split_date results in no training data. Splitting at midpoint: {split_date.strftime('%Y-%m-%d')}.\")\n", "    else: # Only two unique dates, use the first for training\n", "        split_date = pd.to_datetime(unique_dates[1]) # Second date becomes start of test\n", "        print(f\"Warning: Initial split_date results in no training data. Using first date for training, split at: {split_date.strftime('%Y-%m-%d')}.\")\n", "\n", "\n", "print(f\"Final split date (data before this for train, on/after for test): {split_date.strftime('%Y-%m-%d')}\")\n", "\n", "\n", "# データを分割\n", "train_df = comprehensive_df[comprehensive_df[DATE_COLUMN] < split_date].copy()\n", "test_df = comprehensive_df[comprehensive_df[DATE_COLUMN] >= split_date].copy()\n", "\n", "# --- 3. Prepare features, labels, and group information ----\n", "X_train, y_train, group_train = pd.DataFrame(), pd.Series(dtype='float64'), []\n", "X_test, y_test, group_test = pd.DataFrame(), pd.Series(dtype='float64'), []\n", "group_ids_train, group_ids_test = np.array([]), np.array([]) # For TensorFlow Ranking\n", "categorical_feature_names = []\n", "\n", "if not train_df.empty:\n", "    categorical_feature_names = [col for col in feature_columns if train_df[col].dtype.name == 'object' or isinstance(train_df[col].dtype, pd.StringDtype) or isinstance(train_df[col].dtype, pd.CategoricalDtype)]\n", "    for col in categorical_feature_names:\n", "        train_df[col] = train_df[col].astype('category')\n", "        if not test_df.empty and col in test_df.columns:\n", "             test_df[col] = test_df[col].astype('category')\n", "\n", "    X_train = train_df[feature_columns]\n", "    y_train = train_df[y_label_column_name]\n", "    # For LightGBM Ranker:\n", "    group_train_lgbm = train_df.groupby(RACE_ID_COLUMN).size().to_list()\n", "    # For TensorFlow Ranking (array of group_ids for each sample):\n", "    group_ids_train = train_df[RACE_ID_COLUMN].values\n", "\n", "    print(f\"Training data: X_train {X_train.shape}, y_train {y_train.shape}\")\n", "    print(f\"  LightGBM groups: {len(group_train_lgbm)} groups, TF Ranking group IDs: {len(group_ids_train)} samples\")\n", "    print(f\"  Categorical features in training data: {[col for col in X_train.columns if X_train[col].dtype.name == 'category']}\")\n", "\n", "if not test_df.empty:\n", "    X_test = test_df[feature_columns]\n", "    y_test = test_df[y_label_column_name]\n", "    # For LightGBM Ranker:\n", "    group_test_lgbm = test_df.groupby(RACE_ID_COLUMN).size().to_list()\n", "    # For TensorFlow Ranking:\n", "    group_ids_test = test_df[RACE_ID_COLUMN].values\n", "\n", "    print(f\"Test data: X_test {X_test.shape}, y_test {y_test.shape}\")\n", "    print(f\"  LightGBM groups: {len(group_test_lgbm)} groups, TF Ranking group IDs: {len(group_ids_test)} samples\")\n", "    print(f\"  Categorical features in test data: {[col for col in X_test.columns if X_test[col].dtype.name == 'category']}\")\n", "\n", "if train_df.empty:\n", "    print(f\"Warning: Training data is empty. Check the split_date ({split_date.strftime('%Y-%m-%d')}) and data source.\")\n", "\n", "# Dummy prediction part for context, assuming model is trained and predicts.\n", "# This section should be replaced with actual model prediction logic.\n", "if not test_df.empty:\n", "    # Create a copy for storing predictions\n", "    test_data_with_predictions_df = test_df.copy()\n", "\n", "    # Placeholder for actual model predictions\n", "    # Ensure 'predicted_score' column is populated by your model\n", "    if not test_data_with_predictions_df.empty: # Check if test_df was not empty to begin with\n", "        # This is where your model's prediction would go.\n", "        # For now, using random scores as a placeholder.\n", "        test_data_with_predictions_df['predicted_score'] = np.random.rand(len(test_data_with_predictions_df))\n", "\n", "    if 'predicted_score' in test_data_with_predictions_df.columns:\n", "        test_data_with_predictions_df['predicted_rank'] = test_data_with_predictions_df.groupby(RACE_ID_COLUMN)['predicted_score'].rank(ascending=False, method='first')\n", "        print(\"\\nSample of test data with predicted scores and ranks:\")\n", "        print(test_data_with_predictions_df[[RACE_ID_COLUMN, HORSE_ID_COLUMN, 'predicted_score', 'predicted_rank', actual_rank_column_name]].head())\n", "\n", "    else:\n", "        print(\"\\n'predicted_score' column is missing in test_data_with_predictions_df. Cannot calculate predicted_rank.\")\n", "elif not X_test.empty:\n", "    print(\"\\nTest features (X_test) exist, but no predictions were made (e.g., model not trained or test_df was empty initially).\")\n", "    print(\"\\nモデルが学習されていない、またはテストデータの特徴量が空のため、テストデータの予測はスキップされました。\")\n", "else:\n", "    print(\"\\nNo test data available, skipping prediction display.\")"]}, {"cell_type": "code", "execution_count": 11, "id": "744a61c0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X_train shape: (43104, 88)\n", "y_train shape: (43104,)\n", "group_ids_train shape: (43104,)\n", "First 5 elements of group_ids_train: [202206010101 202206010101 202206010101 202206010101 202206010101]\n", "Unique groups in group_ids_train: 3168\n"]}], "source": ["# (train_df, test_df から X_train, y_train, group_ids_train などを生成した後)\n", "\n", "if 'X_train' in locals():\n", "    print(f\"X_train shape: {X_train.shape}\")\n", "else:\n", "    print(\"X_train is not defined.\")\n", "\n", "if 'y_train' in locals():\n", "    print(f\"y_train shape: {y_train.shape}\")\n", "else:\n", "    print(\"y_train is not defined.\")\n", "\n", "if 'group_ids_train' in locals() and hasattr(group_ids_train, 'shape'): # NumPy配列を想定\n", "    print(f\"group_ids_train shape: {group_ids_train.shape}\")\n", "    if len(group_ids_train) > 0:\n", "        print(f\"First 5 elements of group_ids_train: {group_ids_train[:5]}\")\n", "        print(f\"Unique groups in group_ids_train: {np.unique(group_ids_train).size}\")\n", "elif 'group_ids_train' in locals():\n", "    print(f\"group_ids_train length: {len(group_ids_train)} (type: {type(group_ids_train)})\")\n", "else:\n", "    print(\"group_ids_train is not defined.\")\n", "\n", "# 同様に X_test, y_test, group_ids_test についても確認\n"]}, {"cell_type": "code", "execution_count": 12, "id": "c65cf6a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Test Data Check ---\n", "X_test shape: (4116, 88)\n", "<class 'pandas.core.frame.DataFrame'>\n", "Index: 4116 entries, 43104 to 47219\n", "Data columns (total 88 columns):\n", " #   Column                 Non-Null Count  Dtype  \n", "---  ------                 --------------  -----  \n", " 0   枠番                     4116 non-null   int64  \n", " 1   馬番                     4116 non-null   int64  \n", " 2   馬名                     4116 non-null   float64\n", " 3   性齢                     4116 non-null   float64\n", " 4   斤量                     4116 non-null   float64\n", " 5   騎手                     4116 non-null   float64\n", " 6   タイム                    4116 non-null   float64\n", " 7   着差                     4116 non-null   float64\n", " 8   ﾀｲﾑ指数                  4116 non-null   float64\n", " 9   通過                     4116 non-null   float64\n", " 10  上り                     4116 non-null   float64\n", " 11  単勝                     4116 non-null   float64\n", " 12  人気                     4116 non-null   float64\n", " 13  馬体重                    4116 non-null   float64\n", " 14  調教ﾀｲﾑ                  4116 non-null   float64\n", " 15  厩舎ｺﾒﾝﾄ                 4116 non-null   float64\n", " 16  備考                     4116 non-null   float64\n", " 17  調教師                    4116 non-null   float64\n", " 18  馬主                     4116 non-null   float64\n", " 19  賞金(万円)                 4116 non-null   float64\n", " 20  jockey_id              4116 non-null   int64  \n", " 21  trainer_id             4116 non-null   int64  \n", " 22  開催                     4116 non-null   int64  \n", " 23  レース名                   4116 non-null   float64\n", " 24  course_len             4116 non-null   int64  \n", " 25  weather                4116 non-null   float64\n", " 26  race_type              4116 non-null   float64\n", " 27  ground_state           4116 non-null   float64\n", " 28  around                 4116 non-null   float64\n", " 29  調教師_horse_info         4116 non-null   float64\n", " 30  馬主_horse_info          4116 non-null   float64\n", " 31  生産者                    4116 non-null   float64\n", " 32  産地                     4116 non-null   float64\n", " 33  セリ取引価格                 4116 non-null   float64\n", " 34  獲得賞金                   4116 non-null   float64\n", " 35  通算成績                   4116 non-null   float64\n", " 36  主な勝鞍                   4116 non-null   float64\n", " 37  近親馬                    4116 non-null   float64\n", " 38  horse_name             4116 non-null   float64\n", " 39  trainer_id_horse_info  4116 non-null   float64\n", " 40  owner_id               4116 non-null   float64\n", " 41  breeder_id             4116 non-null   float64\n", " 42  father_name            4116 non-null   float64\n", " 43  father_id              4116 non-null   float64\n", " 44  mother_name            4116 non-null   float64\n", " 45  mother_id              4116 non-null   float64\n", " 46  mother_father_name     4116 non-null   float64\n", " 47  mother_father_id       4116 non-null   float64\n", " 48  sibling_ids            4116 non-null   float64\n", " 49  募集情報                   4116 non-null   float64\n", " 50  着順_last_3R_mean        4116 non-null   float64\n", " 51  人気_last_3R_mean        4116 non-null   float64\n", " 52  オッズ_last_3R_mean       4116 non-null   float64\n", " 53  賞金_last_3R_mean        4116 non-null   float64\n", " 54  斤量_last_3R_mean        4116 non-null   float64\n", " 55  タイム_last_3R_mean       4116 non-null   float64\n", " 56  上り_last_3R_mean        4116 non-null   float64\n", " 57  体重_last_3R_mean        4116 non-null   float64\n", " 58  体重変化_last_3R_mean      4116 non-null   float64\n", " 59  着順_last_5R_mean        4116 non-null   float64\n", " 60  人気_last_5R_mean        4116 non-null   float64\n", " 61  オッズ_last_5R_mean       4116 non-null   float64\n", " 62  賞金_last_5R_mean        4116 non-null   float64\n", " 63  斤量_last_5R_mean        4116 non-null   float64\n", " 64  タイム_last_5R_mean       4116 non-null   float64\n", " 65  上り_last_5R_mean        4116 non-null   float64\n", " 66  体重_last_5R_mean        4116 non-null   float64\n", " 67  体重変化_last_5R_mean      4116 non-null   float64\n", " 68  着順_last_10R_mean       4116 non-null   float64\n", " 69  人気_last_10R_mean       4116 non-null   float64\n", " 70  オッズ_last_10R_mean      4116 non-null   float64\n", " 71  賞金_last_10R_mean       4116 non-null   float64\n", " 72  斤量_last_10R_mean       4116 non-null   float64\n", " 73  タイム_last_10R_mean      4116 non-null   float64\n", " 74  上り_last_10R_mean       4116 non-null   float64\n", " 75  体重_last_10R_mean       4116 non-null   float64\n", " 76  体重変化_last_10R_mean     4116 non-null   float64\n", " 77  着順_all_R_mean          4116 non-null   float64\n", " 78  人気_all_R_mean          4116 non-null   float64\n", " 79  オッズ_all_R_mean         4116 non-null   float64\n", " 80  賞金_all_R_mean          4116 non-null   float64\n", " 81  斤量_all_R_mean          4116 non-null   float64\n", " 82  タイム_all_R_mean         4116 non-null   float64\n", " 83  上り_all_R_mean          4116 non-null   float64\n", " 84  体重_all_R_mean          4116 non-null   float64\n", " 85  体重変化_all_R_mean        4116 non-null   float64\n", " 86  interval_days          4116 non-null   float64\n", " 87  num_starters_in_race   4116 non-null   float64\n", "dtypes: float64(82), int64(6)\n", "memory usage: 2.8 MB\n", "X_test dtypes:\n", "None\n", "X_test NaNs per column (if DataFrame):\n", "Series([], dtype: int64)\n", "y_test shape: (4116,)\n", "y_test dtype: float64\n", "y_test NaNs (if Series): 26\n", "group_ids_test shape: (4116,)\n", "group_ids_test dtype: int64\n", "First 5 elements of group_ids_test: [202206050101 202206050101 202206050101 202206050101 202206050101]\n", "Unique groups in group_ids_test: 288\n", "Test data row counts match: 4116\n"]}], "source": ["# (test_df から X_test, y_test, group_ids_test などを生成した後)\n", "\n", "print(\"\\n--- Test Data Check ---\")\n", "if 'X_test' in locals() and hasattr(X_test, 'shape'):\n", "    print(f\"X_test shape: {X_test.shape}\")\n", "    if X_test.shape[0] > 0:\n", "        print(f\"X_test dtypes:\\n{pd.DataFrame(X_test).info()}\") # DataFrameに変換してinfo()を呼ぶか、NumPyならX_test.dtype\n", "        # print(f\"X_test NaNs: {np.isnan(X_test).any()}\") # NumPyの場合\n", "        print(f\"X_test NaNs per column (if DataFrame):\\n{pd.DataFrame(X_test).isnull().sum()[pd.DataFrame(X_test).isnull().sum() > 0]}\")\n", "\n", "else:\n", "    print(\"X_test is not defined or has no shape attribute.\")\n", "\n", "if 'y_test' in locals() and hasattr(y_test, 'shape'):\n", "    print(f\"y_test shape: {y_test.shape}\")\n", "    if y_test.shape[0] > 0:\n", "        print(f\"y_test dtype: {y_test.dtype}\")\n", "        # print(f\"y_test NaNs: {np.isnan(y_test).any()}\") # NumPyの場合\n", "        print(f\"y_test NaNs (if Series): {pd.Series(y_test).isnull().sum()}\")\n", "else:\n", "    print(\"y_test is not defined or has no shape attribute.\")\n", "\n", "if 'group_ids_test' in locals() and hasattr(group_ids_test, 'shape'):\n", "    print(f\"group_ids_test shape: {group_ids_test.shape}\")\n", "    if group_ids_test.shape[0] > 0:\n", "        print(f\"group_ids_test dtype: {group_ids_test.dtype}\")\n", "        print(f\"First 5 elements of group_ids_test: {group_ids_test[:5]}\")\n", "        print(f\"Unique groups in group_ids_test: {np.unique(group_ids_test).size}\")\n", "else:\n", "    print(\"group_ids_test is not defined or has no shape attribute.\")\n", "\n", "# 行数の一致確認\n", "if 'X_test' in locals() and 'y_test' in locals() and 'group_ids_test' in locals() and \\\n", "   hasattr(X_test, 'shape') and hasattr(y_test, 'shape') and hasattr(group_ids_test, 'shape'):\n", "    if X_test.shape[0] == y_test.shape[0] == group_ids_test.shape[0]:\n", "        print(f\"Test data row counts match: {X_test.shape[0]}\")\n", "    else:\n", "        print(f\"ERROR: Test data row counts MISMATCH! \"\n", "              f\"X_test: {X_test.shape[0]}, y_test: {y_test.shape[0]}, group_ids_test: {group_ids_test.shape[0]}\")\n", "else:\n", "    print(\"Could not perform row count match for test data due to missing variables.\")\n"]}, {"cell_type": "code", "execution_count": 13, "id": "d482e68a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Before NaN removal - X_test shape: (4116, 88), y_test shape: (4116,), group_ids_test shape: (4116,)\n", "Number of NaNs in y_test before removal: 26\n", "Removing 26 samples due to NaNs in y_test.\n", "After NaN removal - X_test shape: (4090, 88), y_test shape: (4090,), group_ids_test shape: (4090,)\n", "Number of NaNs in y_test after removal: 0\n"]}], "source": ["# (X_test, y_test, group_ids_test が NumPy 配列として定義された後)\n", "\n", "print(f\"Before NaN removal - X_test shape: {X_test.shape}, y_test shape: {y_test.shape}, group_ids_test shape: {group_ids_test.shape}\")\n", "print(f\"Number of NaNs in y_test before removal: {np.sum(np.isnan(y_test))}\") # NumPy配列の場合\n", "\n", "# y_test が NaN でない行のインデックスを取得\n", "valid_indices = ~np.isnan(y_test)\n", "\n", "if np.sum(~valid_indices) > 0: # NaNが存在する場合のみ処理\n", "    print(f\"Removing {np.sum(~valid_indices)} samples due to NaNs in y_test.\")\n", "    X_test = X_test[valid_indices]\n", "    y_test = y_test[valid_indices]\n", "    group_ids_test = group_ids_test[valid_indices]\n", "\n", "    print(f\"After NaN removal - X_test shape: {X_test.shape}, y_test shape: {y_test.shape}, group_ids_test shape: {group_ids_test.shape}\")\n", "    print(f\"Number of NaNs in y_test after removal: {np.sum(np.isnan(y_test))}\")\n", "else:\n", "    print(\"No NaNs found in y_test. No removal needed.\")\n", "\n", "# この後、クリーンになった X_test, y_test, group_ids_test を使って\n", "# create_ranking_dataset やモデルの評価を行います。\n"]}, {"cell_type": "code", "execution_count": 24, "id": "40360e8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Training Data Check (before setting data_is_ready) ---\n", "--- Characteristics for X_train ---\n", "X_train shape: (43104, 88)\n", "X_train info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "Index: 43104 entries, 0 to 43103\n", "Data columns (total 88 columns):\n", " #   Column                 Non-Null Count  Dtype  \n", "---  ------                 --------------  -----  \n", " 0   枠番                     43104 non-null  int64  \n", " 1   馬番                     43104 non-null  int64  \n", " 2   馬名                     43104 non-null  float64\n", " 3   性齢                     43104 non-null  float64\n", " 4   斤量                     43104 non-null  float64\n", " 5   騎手                     43104 non-null  float64\n", " 6   タイム                    43104 non-null  float64\n", " 7   着差                     43104 non-null  float64\n", " 8   ﾀｲﾑ指数                  43104 non-null  float64\n", " 9   通過                     43104 non-null  float64\n", " 10  上り                     43104 non-null  float64\n", " 11  単勝                     43104 non-null  float64\n", " 12  人気                     43104 non-null  float64\n", " 13  馬体重                    43104 non-null  float64\n", " 14  調教ﾀｲﾑ                  43104 non-null  float64\n", " 15  厩舎ｺﾒﾝﾄ                 43104 non-null  float64\n", " 16  備考                     43104 non-null  float64\n", " 17  調教師                    43104 non-null  float64\n", " 18  馬主                     43104 non-null  float64\n", " 19  賞金(万円)                 43104 non-null  float64\n", " 20  jockey_id              43104 non-null  int64  \n", " 21  trainer_id             43104 non-null  int64  \n", " 22  開催                     43104 non-null  int64  \n", " 23  レース名                   43104 non-null  float64\n", " 24  course_len             43104 non-null  int64  \n", " 25  weather                43104 non-null  float64\n", " 26  race_type              43104 non-null  float64\n", " 27  ground_state           43104 non-null  float64\n", " 28  around                 43104 non-null  float64\n", " 29  調教師_horse_info         43104 non-null  float64\n", " 30  馬主_horse_info          43104 non-null  float64\n", " 31  生産者                    43104 non-null  float64\n", " 32  産地                     43104 non-null  float64\n", " 33  セリ取引価格                 43104 non-null  float64\n", " 34  獲得賞金                   43104 non-null  float64\n", " 35  通算成績                   43104 non-null  float64\n", " 36  主な勝鞍                   43104 non-null  float64\n", " 37  近親馬                    43104 non-null  float64\n", " 38  horse_name             43104 non-null  float64\n", " 39  trainer_id_horse_info  43104 non-null  float64\n", " 40  owner_id               43104 non-null  float64\n", " 41  breeder_id             43104 non-null  float64\n", " 42  father_name            43104 non-null  float64\n", " 43  father_id              43104 non-null  float64\n", " 44  mother_name            43104 non-null  float64\n", " 45  mother_id              43104 non-null  float64\n", " 46  mother_father_name     43104 non-null  float64\n", " 47  mother_father_id       43104 non-null  float64\n", " 48  sibling_ids            43104 non-null  float64\n", " 49  募集情報                   43104 non-null  float64\n", " 50  着順_last_3R_mean        43104 non-null  float64\n", " 51  人気_last_3R_mean        43104 non-null  float64\n", " 52  オッズ_last_3R_mean       43104 non-null  float64\n", " 53  賞金_last_3R_mean        43104 non-null  float64\n", " 54  斤量_last_3R_mean        43104 non-null  float64\n", " 55  タイム_last_3R_mean       43104 non-null  float64\n", " 56  上り_last_3R_mean        43104 non-null  float64\n", " 57  体重_last_3R_mean        43104 non-null  float64\n", " 58  体重変化_last_3R_mean      43104 non-null  float64\n", " 59  着順_last_5R_mean        43104 non-null  float64\n", " 60  人気_last_5R_mean        43104 non-null  float64\n", " 61  オッズ_last_5R_mean       43104 non-null  float64\n", " 62  賞金_last_5R_mean        43104 non-null  float64\n", " 63  斤量_last_5R_mean        43104 non-null  float64\n", " 64  タイム_last_5R_mean       43104 non-null  float64\n", " 65  上り_last_5R_mean        43104 non-null  float64\n", " 66  体重_last_5R_mean        43104 non-null  float64\n", " 67  体重変化_last_5R_mean      43104 non-null  float64\n", " 68  着順_last_10R_mean       43104 non-null  float64\n", " 69  人気_last_10R_mean       43104 non-null  float64\n", " 70  オッズ_last_10R_mean      43104 non-null  float64\n", " 71  賞金_last_10R_mean       43104 non-null  float64\n", " 72  斤量_last_10R_mean       43104 non-null  float64\n", " 73  タイム_last_10R_mean      43104 non-null  float64\n", " 74  上り_last_10R_mean       43104 non-null  float64\n", " 75  体重_last_10R_mean       43104 non-null  float64\n", " 76  体重変化_last_10R_mean     43104 non-null  float64\n", " 77  着順_all_R_mean          43104 non-null  float64\n", " 78  人気_all_R_mean          43104 non-null  float64\n", " 79  オッズ_all_R_mean         43104 non-null  float64\n", " 80  賞金_all_R_mean          43104 non-null  float64\n", " 81  斤量_all_R_mean          43104 non-null  float64\n", " 82  タイム_all_R_mean         43104 non-null  float64\n", " 83  上り_all_R_mean          43104 non-null  float64\n", " 84  体重_all_R_mean          43104 non-null  float64\n", " 85  体重変化_all_R_mean        43104 non-null  float64\n", " 86  interval_days          43104 non-null  float64\n", " 87  num_starters_in_race   43104 non-null  float64\n", "dtypes: float64(82), int64(6)\n", "memory usage: 29.3 MB\n", "X_train has no NaN values in any column.\n", "--- Characteristics for y_train ---\n", "y_train shape: (43104,)\n", "y_train dtype: float64\n", "y_train NaN count: 354\n", "--- Characteristics for group_ids_train ---\n", "group_ids_train shape: (43104,)\n", "group_ids_train dtype: int64\n", "group_ids_train NaN count: 0\n", "First 5 elements of group_ids_train: [202206010101 202206010101 202206010101 202206010101 202206010101]\n", "Unique groups in group_ids_train: 3168\n", "\n", "Found 354 NaNs in y_train. Filtering training data...\n", "Attempting to filter X_train...\n", "Attempting to filter y_train...\n", "Attempting to filter group_ids_train...\n", "\n", "--- Data Characteristics After Filtering NaNs from y_train ---\n", "--- Characteristics for X_train (filtered) ---\n", "X_train (filtered) shape: (42750, 88)\n", "X_train (filtered) info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "Index: 42750 entries, 0 to 43103\n", "Data columns (total 88 columns):\n", " #   Column                 Non-Null Count  Dtype  \n", "---  ------                 --------------  -----  \n", " 0   枠番                     42750 non-null  int64  \n", " 1   馬番                     42750 non-null  int64  \n", " 2   馬名                     42750 non-null  float64\n", " 3   性齢                     42750 non-null  float64\n", " 4   斤量                     42750 non-null  float64\n", " 5   騎手                     42750 non-null  float64\n", " 6   タイム                    42750 non-null  float64\n", " 7   着差                     42750 non-null  float64\n", " 8   ﾀｲﾑ指数                  42750 non-null  float64\n", " 9   通過                     42750 non-null  float64\n", " 10  上り                     42750 non-null  float64\n", " 11  単勝                     42750 non-null  float64\n", " 12  人気                     42750 non-null  float64\n", " 13  馬体重                    42750 non-null  float64\n", " 14  調教ﾀｲﾑ                  42750 non-null  float64\n", " 15  厩舎ｺﾒﾝﾄ                 42750 non-null  float64\n", " 16  備考                     42750 non-null  float64\n", " 17  調教師                    42750 non-null  float64\n", " 18  馬主                     42750 non-null  float64\n", " 19  賞金(万円)                 42750 non-null  float64\n", " 20  jockey_id              42750 non-null  int64  \n", " 21  trainer_id             42750 non-null  int64  \n", " 22  開催                     42750 non-null  int64  \n", " 23  レース名                   42750 non-null  float64\n", " 24  course_len             42750 non-null  int64  \n", " 25  weather                42750 non-null  float64\n", " 26  race_type              42750 non-null  float64\n", " 27  ground_state           42750 non-null  float64\n", " 28  around                 42750 non-null  float64\n", " 29  調教師_horse_info         42750 non-null  float64\n", " 30  馬主_horse_info          42750 non-null  float64\n", " 31  生産者                    42750 non-null  float64\n", " 32  産地                     42750 non-null  float64\n", " 33  セリ取引価格                 42750 non-null  float64\n", " 34  獲得賞金                   42750 non-null  float64\n", " 35  通算成績                   42750 non-null  float64\n", " 36  主な勝鞍                   42750 non-null  float64\n", " 37  近親馬                    42750 non-null  float64\n", " 38  horse_name             42750 non-null  float64\n", " 39  trainer_id_horse_info  42750 non-null  float64\n", " 40  owner_id               42750 non-null  float64\n", " 41  breeder_id             42750 non-null  float64\n", " 42  father_name            42750 non-null  float64\n", " 43  father_id              42750 non-null  float64\n", " 44  mother_name            42750 non-null  float64\n", " 45  mother_id              42750 non-null  float64\n", " 46  mother_father_name     42750 non-null  float64\n", " 47  mother_father_id       42750 non-null  float64\n", " 48  sibling_ids            42750 non-null  float64\n", " 49  募集情報                   42750 non-null  float64\n", " 50  着順_last_3R_mean        42750 non-null  float64\n", " 51  人気_last_3R_mean        42750 non-null  float64\n", " 52  オッズ_last_3R_mean       42750 non-null  float64\n", " 53  賞金_last_3R_mean        42750 non-null  float64\n", " 54  斤量_last_3R_mean        42750 non-null  float64\n", " 55  タイム_last_3R_mean       42750 non-null  float64\n", " 56  上り_last_3R_mean        42750 non-null  float64\n", " 57  体重_last_3R_mean        42750 non-null  float64\n", " 58  体重変化_last_3R_mean      42750 non-null  float64\n", " 59  着順_last_5R_mean        42750 non-null  float64\n", " 60  人気_last_5R_mean        42750 non-null  float64\n", " 61  オッズ_last_5R_mean       42750 non-null  float64\n", " 62  賞金_last_5R_mean        42750 non-null  float64\n", " 63  斤量_last_5R_mean        42750 non-null  float64\n", " 64  タイム_last_5R_mean       42750 non-null  float64\n", " 65  上り_last_5R_mean        42750 non-null  float64\n", " 66  体重_last_5R_mean        42750 non-null  float64\n", " 67  体重変化_last_5R_mean      42750 non-null  float64\n", " 68  着順_last_10R_mean       42750 non-null  float64\n", " 69  人気_last_10R_mean       42750 non-null  float64\n", " 70  オッズ_last_10R_mean      42750 non-null  float64\n", " 71  賞金_last_10R_mean       42750 non-null  float64\n", " 72  斤量_last_10R_mean       42750 non-null  float64\n", " 73  タイム_last_10R_mean      42750 non-null  float64\n", " 74  上り_last_10R_mean       42750 non-null  float64\n", " 75  体重_last_10R_mean       42750 non-null  float64\n", " 76  体重変化_last_10R_mean     42750 non-null  float64\n", " 77  着順_all_R_mean          42750 non-null  float64\n", " 78  人気_all_R_mean          42750 non-null  float64\n", " 79  オッズ_all_R_mean         42750 non-null  float64\n", " 80  賞金_all_R_mean          42750 non-null  float64\n", " 81  斤量_all_R_mean          42750 non-null  float64\n", " 82  タイム_all_R_mean         42750 non-null  float64\n", " 83  上り_all_R_mean          42750 non-null  float64\n", " 84  体重_all_R_mean          42750 non-null  float64\n", " 85  体重変化_all_R_mean        42750 non-null  float64\n", " 86  interval_days          42750 non-null  float64\n", " 87  num_starters_in_race   42750 non-null  float64\n", "dtypes: float64(82), int64(6)\n", "memory usage: 29.0 MB\n", "X_train (filtered) has no NaN values in any column.\n", "--- Characteristics for y_train (filtered) ---\n", "y_train (filtered) shape: (42750,)\n", "y_train (filtered) dtype: float64\n", "y_train (filtered) NaN count: 0\n", "--- Characteristics for group_ids_train (filtered) ---\n", "group_ids_train (filtered) shape: (42750,)\n", "group_ids_train (filtered) dtype: int64\n", "group_ids_train (filtered) NaN count: 0\n", "\n", "--- Row Count Consistency Check ---\n", "Training data row counts MATCH and are non-zero: 42750\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import sys # For X_train.info() output capture if needed, though direct print is fine.\n", "\n", "def display_data_characteristics(data, data_name):\n", "    \"\"\"\n", "    指定されたデータセットの特性（形状、型、NaN数など）を表示します。\n", "    \"\"\"\n", "    print(f\"--- Characteristics for {data_name} ---\")\n", "    if data is None:\n", "        print(f\"{data_name} is not defined (None).\")\n", "        return\n", "\n", "    if not hasattr(data, 'shape'):\n", "        print(f\"{data_name} does not have a 'shape' attribute (not an array/DataFrame/Series).\")\n", "        return\n", "\n", "    print(f\"{data_name} shape: {data.shape}\")\n", "\n", "    if isinstance(data, pd.DataFrame):\n", "        print(f\"{data_name} info:\")\n", "        data.info() # Prints directly to stdout\n", "        nan_counts = data.isnull().sum()\n", "        nan_counts_filtered = nan_counts[nan_counts > 0]\n", "        if not nan_counts_filtered.empty:\n", "            print(f\"{data_name} NaN counts per column:\\n{nan_counts_filtered}\")\n", "        else:\n", "            print(f\"{data_name} has no NaN values in any column.\")\n", "    elif isinstance(data, pd.Series):\n", "        print(f\"{data_name} dtype: {data.dtype}\")\n", "        nan_count = data.isnull().sum()\n", "        print(f\"{data_name} NaN count: {nan_count}\")\n", "    elif isinstance(data, np.ndarray):\n", "        print(f\"{data_name} dtype: {data.dtype}\")\n", "        nan_count = 0\n", "        if np.issubdtype(data.dtype, np.number): # For numeric types\n", "            nan_count = np.isnan(data).sum()\n", "        elif data.dtype == 'object': # For object types, count None or np.nan\n", "            # Using pd.Series.isnull() is safer for object arrays that might contain non-floatable NaNs\n", "            nan_count = pd.Series(data).isnull().sum()\n", "        print(f\"{data_name} NaN count: {nan_count}\")\n", "\n", "        # Original specific logic for group_ids_train when it's an ndarray\n", "        if data_name == \"group_ids_train\" and len(data) > 0:\n", "             print(f\"First 5 elements of {data_name}: {data[:5]}\")\n", "             try:\n", "                 # Exclude NaNs before counting unique values if data can contain NaNs\n", "                 # pd.isna is a general way to check for NaN-like values\n", "                 is_nan_mask = pd.isna(data)\n", "                 if np.any(is_nan_mask):\n", "                     unique_elements = np.unique(data[~is_nan_mask])\n", "                 else:\n", "                     unique_elements = np.unique(data)\n", "                 print(f\"Unique groups in {data_name}: {unique_elements.size}\")\n", "             except TypeError:\n", "                 print(f\"Could not determine unique groups in {data_name} due to uncomparable elements.\")\n", "    else:\n", "        print(f\"{data_name} is of an unrecognized type for detailed check: {type(data)}\")\n", "\n", "def filter_dataset_by_indices(data, valid_indices, data_name):\n", "    \"\"\"\n", "    ブール型のインデックスを使用してデータセット (DataFrame, Series, or ndarray) をフィルタリングします。\n", "    valid_indices は pandas Series または NumPy boolean 配列であることを期待します。\n", "    \"\"\"\n", "    if data is None:\n", "        # print(f\"Info: {data_name} is None. Skipping filtering.\") # Optional info message\n", "        return None\n", "\n", "    filter_indices_for_apply = valid_indices\n", "    if isinstance(data, np.ndarray) and isinstance(valid_indices, pd.Series):\n", "        filter_indices_for_apply = valid_indices.values\n", "    \n", "    # Ensure filter_indices_for_apply is a 1D boolean array/Series of correct length for row-wise filtering\n", "    if not (hasattr(filter_indices_for_apply, 'ndim') and filter_indices_for_apply.ndim == 1 and \n", "            hasattr(filter_indices_for_apply, 'dtype') and filter_indices_for_apply.dtype == bool and\n", "            len(filter_indices_for_apply) == data.shape[0]):\n", "        print(f\"Warning: Invalid or mismatched indices for {data_name}. Data shape: {data.shape}, Indices length: {len(filter_indices_for_apply) if hasattr(filter_indices_for_apply, '__len__') else 'N/A'}. Skipping filtering.\")\n", "        return data\n", "\n", "\n", "    if isinstance(data, (pd.DataFrame, pd.Series)):\n", "        try:\n", "            return data[filter_indices_for_apply]\n", "        except Exception as e:\n", "            print(f\"Warning: Could not filter {data_name} (type: {type(data)}). Error: {e}. Returning original.\")\n", "            return data\n", "    elif isinstance(data, np.ndarray):\n", "        try:\n", "            return data[filter_indices_for_apply]\n", "        except Exception as e:\n", "            print(f\"Warning: Could not filter {data_name} (ndarray). Error: {e}. Returning original.\")\n", "            return data\n", "    else:\n", "        print(f\"Warning: {data_name} is not a DataFrame, Series, or ndarray (type: {type(data)}). Skipping filtering.\")\n", "        return data\n", "\n", "# --- Training Data Check (before setting data_is_ready) ---\n", "print(\"--- Training Data Check (before setting data_is_ready) ---\")\n", "\n", "# `locals().get()` を使用して、変数が未定義の場合でも安全に None を取得\n", "# 実際のスクリプトでは、これらの変数はこのコードブロックの前に定義されていることを想定\n", "x_train = locals().get('X_train')\n", "y_train = locals().get('y_train')\n", "group_ids_train = locals().get('group_ids_train')\n", "\n", "# 1. 各データセットの初期状態表示\n", "display_data_characteristics(x_train, \"X_train\")\n", "display_data_characteristics(y_train, \"y_train\")\n", "display_data_characteristics(group_ids_train, \"group_ids_train\")\n", "\n", "# 2. y_train の NaN に基づくフィルタリング\n", "y_train_is_filterable_series = y_train is not None and isinstance(y_train, pd.Series)\n", "\n", "if y_train_is_filterable_series and y_train.isnull().any():\n", "    num_nans_y = y_train.isnull().sum()\n", "    print(f\"\\nFound {num_nans_y} NaNs in y_train. Filtering training data...\")\n", "    valid_train_indices = y_train.notna() # boolean Series\n", "\n", "    if x_train is not None:\n", "        print(\"Attempting to filter X_train...\")\n", "        x_train = filter_dataset_by_indices(x_train, valid_train_indices, \"X_train\")\n", "    else:\n", "        print(\"X_train is not defined, skipping its filtering based on y_train NaNs.\")\n", "\n", "    # y_train 自身をフィルタリング (pd.Series であることは確認済み)\n", "    print(\"Attempting to filter y_train...\")\n", "    y_train = y_train[valid_train_indices]\n", "\n", "    if group_ids_train is not None:\n", "        print(\"Attempting to filter group_ids_train...\")\n", "        group_ids_train = filter_dataset_by_indices(group_ids_train, valid_train_indices, \"group_ids_train\")\n", "    else:\n", "        print(\"group_ids_train is not defined, skipping its filtering based on y_train NaNs.\")\n", "\n", "    print(f\"\\n--- Data Characteristics After Filtering NaNs from y_train ---\")\n", "    display_data_characteristics(x_train, \"X_train (filtered)\")\n", "    display_data_characteristics(y_train, \"y_train (filtered)\")\n", "    display_data_characteristics(group_ids_train, \"group_ids_train (filtered)\")\n", "elif y_train_is_filterable_series: # No NaNs in y_train (and it's a Series)\n", "    print(\"\\nNo NaNs found in y_train. No filtering based on y_train NaNs performed.\")\n", "else: # y_train is None or not a pd.Series\n", "    if y_train is None:\n", "        print(\"\\ny_train is not defined. Skipping NaN filtering based on y_train.\")\n", "    else: # Not a pd.Series\n", "        print(f\"\\ny_train is not a pandas Series (type: {type(y_train)}). Skipping NaN filtering based on y_train.\")\n", "\n", "\n", "# 3. 行数の一致確認\n", "print(\"\\n--- Row Count Consistency Check ---\")\n", "# チェック対象の変数がすべて適切に定義されているか確認\n", "can_check_rows = (\n", "    x_train is not None and hasattr(x_train, 'shape') and\n", "    y_train is not None and hasattr(y_train, 'shape') and\n", "    group_ids_train is not None and hasattr(group_ids_train, 'shape')\n", ")\n", "\n", "if can_check_rows:\n", "    shape_x = x_train.shape[0]\n", "    shape_y = y_train.shape[0]\n", "    shape_group = group_ids_train.shape[0]\n", "\n", "    if shape_x == shape_y == shape_group:\n", "        if shape_x > 0:\n", "            print(f\"Training data row counts MATCH and are non-zero: {shape_x}\")\n", "            # ここで data_is_ready = True としても良いか検討\n", "        else: # shape_x == 0\n", "            print(f\"WARNING: Training data row counts MATCH but are zero: {shape_x}\")\n", "    else:\n", "        print(f\"ERROR: Training data row counts MISMATCH. \"\n", "              f\"X_train: {shape_x}, y_train: {shape_y}, group_ids_train: {shape_group}\")\n", "else:\n", "    print(\"Could not perform row count match for training data due to one or more variables being undefined, None, or not having a shape attribute.\")\n", "    # 問題のある変数を特定しやすくするための追加情報\n", "    if x_train is None or not hasattr(x_train, 'shape'):\n", "        print(\"  - X_train is missing or not an array/DataFrame.\")\n", "    if y_train is None or not hasattr(y_train, 'shape'):\n", "        print(\"  - y_train is missing or not an array/Series.\")\n", "    if group_ids_train is None or not hasattr(group_ids_train, 'shape'):\n", "        print(\"  - group_ids_train is missing or not an array.\")\n", "\n", "# 同様のチェックをテストデータに対しても実施\n", "# print(\"\\n--- Test Data Check (before setting test_data_is_ready) ---\")\n", "# X_test = locals().get('X_test')\n", "# y_test = locals().get('y_test')\n", "# group_ids_test = locals().get('group_ids_test')\n", "# display_data_characteristics(X_test, \"X_test\")\n", "# ... (テストデータ用の同様の処理) ...\n"]}, {"cell_type": "code", "execution_count": null, "id": "07df7a22", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "id": "820ac286", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学習データが不十分なため、モデル学習は実行されませんでした。\n", "\n", "テストデータがないため、予測はスキップされました。\n", "\n", "TensorFlow Rankingを使用した競馬予測システムの実行が完了しました。\n"]}], "source": ["import tensorflow as tf\n", "import tensorflow_ranking as tfr\n", "import tensorflow.keras.backend as K # NDCG計算の安定性向上のために追加 (オプション)\n", "import optuna\n", "import pandas as pd\n", "import numpy as np\n", "import functools\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "# X_train, y_train, group_train, X_test, y_test, group_test, categorical_features, feature_columns, actual_rank_col, y_label_column_name, test_df は事前に定義されていると仮定します。\n", "\n", "# 再現性のためのシード設定\n", "tf.random.set_seed(42)\n", "np.random.seed(42)\n", "\n", "# --- データ前処理用関数 ---\n", "def create_ranking_dataset(X, y, groups, list_size=10):\n", "    \"\"\"ランキング用のデータセットを作成する関数\"\"\"\n", "    # グループごとにデータを整理\n", "    unique_groups = np.unique(groups)\n", "    X_list = []\n", "    y_list = []\n", "    \n", "    for group in unique_groups:\n", "        group_mask = groups == group\n", "        group_X = X[group_mask]\n", "        group_y = y[group_mask]\n", "        \n", "        # パディング処理（list_sizeに満たない場合）\n", "        current_list_size = len(group_X) # 変数名を変更して明確化\n", "        if current_list_size < list_size:\n", "            # ゼロパディング\n", "            pad_size = list_size - current_list_size\n", "            pad_X = np.zeros((pad_size, group_X.shape[1]))\n", "            pad_y = np.zeros(pad_size)\n", "            \n", "            group_X = np.vstack([group_X, pad_X])\n", "            group_y = np.concatenate([group_y, pad_y])\n", "        elif current_list_size > list_size:\n", "            # 上位list_size個に制限\n", "            top_indices = np.argsort(group_y)[::-1][:list_size]\n", "            group_X = group_X[top_indices]\n", "            group_y = group_y[top_indices]\n", "        \n", "        X_list.append(group_X)\n", "        y_list.append(group_y)\n", "    \n", "    return np.array(X_list), np.array(y_list)\n", "\n", "def create_tf_dataset(X_list, y_list, batch_size=32):\n", "    \"\"\"TensorFlowデータセットを作成\"\"\"\n", "    dataset = tf.data.Dataset.from_tensor_slices({\n", "        'features': X_list.astype(np.float32),\n", "        'labels': y_list.astype(np.float32)\n", "    })\n", "    return dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)\n", "\n", "# --- TF-Rankingモデル定義 ---\n", "def create_ranking_model(input_shape, hidden_units=[64, 32], dropout_rate=0.2):\n", "    \"\"\"ランキングモデルを作成\"\"\"\n", "    inputs = tf.keras.Input(shape=input_shape, name='features')\n", "    \n", "    # DNNレイヤー\n", "    x = inputs\n", "    for units in hidden_units:\n", "        x = tf.keras.layers.Dense(units, activation='relu')(x)\n", "        x = tf.keras.layers.Dropout(dropout_rate)(x)\n", "    \n", "    # 出力層（スコア予測）\n", "    outputs = tf.keras.layers.Dense(1, name='scores')(x)\n", "    \n", "    model = tf.keras.Model(inputs=inputs, outputs=outputs)\n", "    return model\n", "\n", "# --- Optunaによるハイパーパラメータ最適化 ---\n", "def objective_for_tf_ranking(trial, x_train_list, y_train_list, x_test_list, y_test_list, \n", "                           input_shape, list_size):\n", "    \"\"\"Optunaの目的関数。TF-Rankingのハイパーパラメータを最適化します。\"\"\"\n", "    \n", "    # ハイパーパラメータの定義\n", "    params = {\n", "        'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),\n", "        'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64]),\n", "        'hidden_units_1': trial.suggest_int('hidden_units_1', 32, 128, step=16),\n", "        'hidden_units_2': trial.suggest_int('hidden_units_2', 16, 64, step=8),\n", "        'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5, step=0.1),\n", "        'epochs': trial.suggest_int('epochs', 10, 50, step=5)\n", "    }\n", "    \n", "    # モデル作成\n", "    model = create_ranking_model(\n", "        input_shape=input_shape,\n", "        hidden_units=[params['hidden_units_1'], params['hidden_units_2']],\n", "        dropout_rate=params['dropout_rate']\n", "    )\n", "    \n", "    # ランキング損失関数の設定\n", "    ranking_loss = tfr.keras.losses.ListMLELoss()\n", "    \n", "    # メトリクスの設定\n", "    ranking_metrics = [\n", "        tfr.keras.metrics.NDCGMetric(topk=3, name='ndcg_3'),\n", "        tfr.keras.metrics.NDCGMetric(topk=5, name='ndcg_5')\n", "    ]\n", "    \n", "    # モデルコンパイル\n", "    model.compile(\n", "        optimizer=tf.keras.optimizers.<PERSON>(learning_rate=params['learning_rate']),\n", "        loss=ranking_loss,\n", "        metrics=ranking_metrics\n", "    )\n", "    \n", "    # データセット作成\n", "    train_dataset = create_tf_dataset(x_train_list, y_train_list, params['batch_size'])\n", "    \n", "    if x_test_list is not None and y_test_list is not None:\n", "        test_dataset = create_tf_dataset(x_test_list, y_test_list, params['batch_size'])\n", "        validation_data = test_dataset\n", "    else:\n", "        validation_data = None\n", "        # テストデータがない場合、<PERSON><PERSON><PERSON><PERSON>この試行を評価できない。\n", "        # 警告を出し、この試行が無効であることを示す値を返す。\n", "        # あるいは、ここではエラーやTrialPrunedを発生させても良い。\n", "        print(\"警告: Optunaの評価にテストデータが提供されていません。この試行の評価はスキップされます。\")\n", "        return -float('inf')\n", "    \n", "    # 早期停止の設定\n", "    early_stopping = tf.keras.callbacks.EarlyStopping(\n", "        monitor='val_ndcg_3',\n", "        patience=5,\n", "        restore_best_weights=True,\n", "        mode='max'\n", "    )\n", "    \n", "    # モデル学習\n", "    try:\n", "        history = model.fit(\n", "            train_dataset,\n", "            validation_data=validation_data,\n", "            epochs=params['epochs'],\n", "            callbacks=[early_stopping],\n", "            verbose=0\n", "        )\n", "        \n", "        # 最高のNDCG@3スコアを返す\n", "        if validation_data:\n", "            best_ndcg = max(history.history.get('val_ndcg_3', [-float('inf')])) # 検証NDCGがない場合は最小値を返す\n", "        else: # 通常ここには到達しないはず\n", "            best_ndcg = -float('inf')\n", "        return best_ndcg\n", "    except Exception as e:\n", "        print(f\"モデル学習中にエラーが発生しました: {e}\")\n", "        return -float('inf')\n", "\n", "# --- データ準備とOptuna実行制御 ---\n", "# グローバルスコープの変数が存在するかどうかを確認\n", "data_is_ready = (\n", "    'X_train' in globals() and not X_train.empty and\n", "    'y_train' in globals() and not y_train.empty and\n", "    'group_train' in globals() and len(group_train) > 0 and\n", "    'categorical_features' in globals() and\n", "    'feature_columns' in globals()\n", ")\n", "\n", "# テストデータは任意とするが、存在すればOptunaや最終評価で使用\n", "test_data_is_ready = (\n", "    'X_test' in globals() and not X_test.empty and\n", "    'y_test' in globals() and not y_test.empty and\n", "    'group_test' in globals() and len(group_test) > 0\n", ")\n", "\n", "USE_OPTUNA = True  # Optunaによる最適化を実行するかどうか\n", "optimized_tf_ranker = None\n", "best_params_from_optuna = {}\n", "scaler = StandardScaler()\n", "\n", "if data_is_ready:\n", "    print(\"データの前処理を開始します...\")\n", "    \n", "    # 特徴量の標準化\n", "    X_train_scaled = scaler.fit_transform(X_train)\n", "    if test_data_is_ready:\n", "        X_test_scaled = scaler.transform(X_test)\n", "    \n", "    # ランキング用データセットの作成\n", "    list_size = 10  # 各レースの最大馬数\n", "    X_train_list, y_train_list = create_ranking_dataset(X_train_scaled, y_train, group_train, list_size)\n", "    \n", "    if test_data_is_ready:\n", "        X_test_list, y_test_list = create_ranking_dataset(X_test_scaled, y_test, group_test, list_size)\n", "    else:\n", "        X_test_list, y_test_list = None, None\n", "    \n", "    input_shape = (list_size, X_train_scaled.shape[1])\n", "    \n", "    print(f\"学習データの形状: {X_train_list.shape}\")\n", "    if test_data_is_ready:\n", "        print(f\"テストデータの形状: {X_test_list.shape}\")\n", "\n", "if USE_OPTUNA and data_is_ready:\n", "    print(\"Optunaによるハイパーパラメータ最適化を開始します...\")\n", "    \n", "    # functools.partial を使って目的関数に必要なデータを渡す\n", "    objective_with_data = functools.partial(\n", "        objective_for_tf_ranking,\n", "        x_train_list=X_train_list,\n", "        y_train_list=y_train_list,\n", "        x_test_list=X_test_list if test_data_is_ready else None,\n", "        y_test_list=y_test_list if test_data_is_ready else None,\n", "        input_shape=input_shape,\n", "        list_size=list_size\n", "    )\n", "    \n", "    study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=42))\n", "    study.optimize(objective_with_data, n_trials=20, timeout=1800)\n", "    \n", "    print(\"\\nOptunaによるハイパーパラメータ最適化が完了しました。\")\n", "    print(\"最適な試行:\")\n", "    best_trial_result = study.best_trial\n", "    print(f\"  Value (NDCG@3): {best_trial_result.value}\")\n", "    print(\"  Params: \")\n", "    for key, value in best_trial_result.params.items():\n", "        print(f\"    {key}: {value}\")\n", "    best_params_from_optuna = best_trial_result.params\n", "\n", "# --- 最終モデルの学習 ---\n", "if data_is_ready:\n", "    print(\"\\n最終モデルを学習します...\")\n", "    \n", "    if USE_OPTUNA and best_params_from_optuna:\n", "        print(\"Optunaで見つかった最適なパラメータを使用します。\")\n", "        final_params = best_params_from_optuna.copy()\n", "    else:\n", "        print(\"Optunaを使用しないか、最適なパラメータが見つからなかったため、デフォルトパラメータを使用します。\")\n", "        final_params = {\n", "            'learning_rate': 0.001,\n", "            'batch_size': 32,\n", "            'hidden_units_1': 64,\n", "            'hidden_units_2': 32,\n", "            'dropout_rate': 0.2,\n", "            'epochs': 30\n", "        }\n", "    \n", "    # 最終モデル作成\n", "    optimized_tf_ranker = create_ranking_model(\n", "        input_shape=input_shape,\n", "        hidden_units=[final_params['hidden_units_1'], final_params['hidden_units_2']],\n", "        dropout_rate=final_params['dropout_rate']\n", "    )\n", "    \n", "    # ランキング損失関数とメトリクスの設定\n", "    ranking_loss = tfr.keras.losses.ListMLELoss()\n", "    ranking_metrics = [\n", "        tfr.keras.metrics.NDCGMetric(topk=1, name='ndcg_1'),\n", "        tfr.keras.metrics.NDCGMetric(topk=3, name='ndcg_3'),\n", "        tfr.keras.metrics.NDCGMetric(topk=5, name='ndcg_5')\n", "    ]\n", "    \n", "    # モデルコンパイル\n", "    optimized_tf_ranker.compile(\n", "        optimizer=tf.keras.optimizers.<PERSON>(learning_rate=final_params['learning_rate']),\n", "        loss=ranking_loss,\n", "        metrics=ranking_metrics\n", "    )\n", "    \n", "    # データセット作成\n", "    train_dataset = create_tf_dataset(X_train_list, y_train_list, final_params['batch_size'])\n", "    \n", "    callbacks = []\n", "    validation_data = None\n", "    \n", "    if test_data_is_ready:\n", "        test_dataset = create_tf_dataset(X_test_list, y_test_list, final_params['batch_size'])\n", "        validation_data = test_dataset\n", "        callbacks.append(tf.keras.callbacks.EarlyStopping(\n", "            monitor='val_ndcg_3',\n", "            patience=10,\n", "            restore_best_weights=True,\n", "            mode='max',\n", "            verbose=1\n", "        ))\n", "    \n", "    # モデル学習\n", "    history = optimized_tf_ranker.fit(\n", "        train_dataset,\n", "        validation_data=validation_data,\n", "        epochs=final_params['epochs'],\n", "        callbacks=callbacks,\n", "        verbose=1\n", "    )\n", "    \n", "    print(\"最終モデルの学習が完了しました。\")\n", "else:\n", "    print(\"学習データが不十分なため、モデル学習は実行されませんでした。\")\n", "\n", "# --- 学習済み最終モデルでテストデータの予測 ---\n", "if optimized_tf_ranker and test_data_is_ready:\n", "    print(\"\\n最終モデルでテストデータの予測を行います...\")\n", "    \n", "    # test_data_with_predictions の準備\n", "    if 'test_df' in globals() and not test_df.empty:\n", "        test_data_with_predictions = test_df.copy()\n", "    else:\n", "        print(\"警告: test_df が存在しないか空のため、予測結果を格納するDataFrameを作成できません。\")\n", "        test_data_with_predictions = pd.DataFrame()\n", "    \n", "    if not test_data_with_predictions.empty:\n", "        # テストデータセットで予測\n", "        test_dataset = create_tf_dataset(X_test_list, y_test_list, 32)\n", "        predictions = optimized_tf_ranker.predict(test_dataset)\n", "        \n", "        # 予測結果を元のデータフレームの各行に対応付ける\n", "        # test_data_with_predictions (test_dfのコピー) と同じ長さのNaN配列を準備\n", "        predicted_scores_for_df = np.full(len(test_data_with_predictions), np.nan)\n", "\n", "        unique_test_groups = np.unique(group_test)\n", "        for i, group in enumerate(unique_test_groups):\n", "            original_group_mask = (group_test == group)\n", "            original_group_indices = np.where(original_group_mask)[0] # test_dfにおける元のインデックス\n", "            original_group_y = y_test[original_group_mask] # 元のラベル (ソート用)\n", "            num_original_items_in_group = len(original_group_indices)\n", "            group_scores_from_model = predictions[i].flatten() # (list_size,) の形状\n", "\n", "            if num_original_items_in_group == 0:\n", "                continue\n", "\n", "            if num_original_items_in_group <= list_size:\n", "                # パディングされたケース：元のアイテム数だけスコアを割り当てる\n", "                predicted_scores_for_df[original_group_indices] = group_scores_from_model[:num_original_items_in_group]\n", "            else:\n", "                # 切り捨てられたケース：create_ranking_dataset と同じロジックで上位 list_size 個の元のインデックスを特定\n", "                top_indices_within_group = np.argsort(original_group_y)[::-1][:list_size]\n", "                original_indices_for_top_items = original_group_indices[top_indices_within_group]\n", "                predicted_scores_for_df[original_indices_for_top_items] = group_scores_from_model\n", "\n", "        # 予測結果をDataFrameに追加\n", "        if len(predicted_scores_for_df) == len(test_data_with_predictions):\n", "            test_data_with_predictions['predicted_score'] = predicted_scores_for_df\n", "            test_data_with_predictions['predicted_rank'] = test_data_with_predictions.groupby('race_id')['predicted_score'].rank(ascending=False, method='first')\n", "\n", "            print(\"\\n予測結果（テストデータ）:\")\n", "            display_cols = ['race_id', 'horse_id', 'predicted_score', 'predicted_rank']\n", "            if 'y_label_column_name' in globals() and y_label_column_name in test_data_with_predictions.columns:\n", "                display_cols.append(y_label_column_name)\n", "            if 'actual_rank_col' in globals() and actual_rank_col in test_data_with_predictions.columns:\n", "                display_cols.append(actual_rank_col)\n", "            \n", "            # 実際に存在するカラムのみを選択して表示\n", "            valid_display_cols = [col for col in display_cols if col in test_data_with_predictions.columns]\n", "            print(test_data_with_predictions[valid_display_cols].head())\n", "        else:\n", "            print(f\"致命的エラー: 予測スコアの割り当てに失敗しました。予測数({len(predicted_scores_for_df)})とテストデータ数({len(test_data_with_predictions)})が一致しません。\")\n", " \n", "\n", "elif not test_data_is_ready:\n", "    print(\"\\nテストデータがないため、予測はスキップされました。\")\n", "else:\n", "    print(\"\\nモデルが学習されていないため、テストデータの予測はスキップされました。\")\n", "\n", "# --- 評価 ---\n", "if optimized_tf_ranker and test_data_is_ready:\n", "    print(\"\\nモデル評価 (検証データ):\")\n", "    test_dataset = create_tf_dataset(X_test_list, y_test_list, 32)\n", "    evaluation_results = optimized_tf_ranker.evaluate(test_dataset, verbose=0)\n", "    \n", "    metric_names = optimized_tf_ranker.metrics_names\n", "    for metric_name, value in zip(metric_names, evaluation_results):\n", "        print(f\"  {metric_name}: {value:.4f}\")\n", "\n", "# モデルサマリー表示\n", "if optimized_tf_ranker:\n", "    print(\"\\nモデル構造:\")\n", "    optimized_tf_ranker.summary()\n", "\n", "print(\"\\nTensorFlow Rankingを使用した競馬予測システムの実行が完了しました。\")"]}, {"cell_type": "code", "execution_count": 25, "id": "f16c9ca7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学習データが不十分または不整合なため、モデル学習は実行されませんでした。\n", "X_train, y_train, group_train の存在と、それらの行数が一致しているか確認してください。\n", "\n", "テストデータがないか不整合なため、予測はスキップされました。\n", "\n", "TensorFlow Rankingを使用した競馬予測システムの実行が完了しました。\n"]}], "source": ["import tensorflow as tf\n", "import tensorflow_ranking as tfr\n", "import optuna\n", "import pandas as pd\n", "import numpy as np\n", "import functools\n", "from sklearn.preprocessing import StandardScaler\n", "from typing import List, Tuple, Dict, Any, Optional, Union\n", "\n", "# X_train, y_train, group_train, X_test, y_test, group_test, categorical_features, feature_columns, actual_rank_col, y_label_column_name, test_df は事前に定義されていると仮定します。\n", "# 重要:\n", "# - X_train, y_train, group_train は同じ行数で、各行が対応している必要があります。\n", "# - group_train の各要素は、X_train の対応する行が属するグループ (例: レースID) を示します。\n", "# - テストデータ (X_test, y_test, group_test) についても同様です。\n", "# - y_train, y_test, group_train, group_test が pandas Series の場合、.values で NumPy 配列に変換して関数に渡すことを推奨します。\n", "\n", "# --- グローバル設定値 ---\n", "RANDOM_SEED = 42\n", "DEFAULT_LIST_SIZE = 10  # 各レースの最大馬数 (ランキングリストのサイズ)\n", "OPTUNA_N_TRIALS = 20    # Optunaの試行回数\n", "OPTUNA_TIMEOUT = 1800   # Optunaのタイムアウト時間 (秒)\n", "\n", "# Optunaを使用するかどうか\n", "USE_OPTUNA = True\n", "\n", "# デフォルトのハイパーパラメータ (Optunaを使用しない場合や、最適なパラメータが見つからなかった場合)\n", "DEFAULT_HYPERPARAMETERS = {\n", "    'learning_rate': 0.001,\n", "    'batch_size': 32,\n", "    'hidden_units_1': 64,\n", "    'hidden_units_2': 32,\n", "    'dropout_rate': 0.2,\n", "    'epochs': 30 # Optunaの探索対象外だった場合、ここで定義\n", "}\n", "\n", "# 再現性のためのシード設定\n", "tf.random.set_seed(RANDOM_SEED)\n", "np.random.seed(RANDOM_SEED)\n", "\n", "# --- データ前処理用関数 ---\n", "def create_ranking_dataset(X: np.ndarray,\n", "                           y: np.n<PERSON><PERSON>,\n", "                           groups: np.n<PERSON><PERSON>,\n", "                           list_size: int = DEFAULT_LIST_SIZE) -> Tuple[np.ndarray, np.ndarray]:\n", "    \"\"\"\n", "    ランキング学習用のデータセットをリスト形式で作成します。\n", "    各グループ（例: レース）内のアイテムを固定長 (list_size) のリストに整形します。\n", "    不足分はゼロパディングし、超過分はyの値に基づいて上位アイテムを保持します。\n", "\n", "    Args:\n", "        X (np.ndarray): 特徴量データ (サンプル数 x 特徴量数)。\n", "        y (np.ndarray): ラベルデータ (サンプル数)。ランキングの基準となるスコアや順位。\n", "                        高いほど関連性が高い（または上位）と解釈されます。\n", "        groups (np.ndarray): 各サンプルが属するグループID (サンプル数)。\n", "        list_size (int): 各グループのリストの固定長。\n", "\n", "    Returns:\n", "        <PERSON><PERSON>[np.n<PERSON><PERSON>, np.n<PERSON><PERSON>]:\n", "            - X_list (np.ndarray): (グループ数 x list_size x 特徴量数) の形状の整形済み特徴量データ。\n", "            - y_list (np.ndarray): (グループ数 x list_size) の形状の整形済みラベルデータ。\n", "    \"\"\"\n", "    unique_groups = np.unique(groups)\n", "    X_list_collector = []\n", "    y_list_collector = []\n", "    \n", "    for group_id in unique_groups:\n", "        group_mask = (groups == group_id)\n", "        group_X = X[group_mask]\n", "        group_y = y[group_mask]\n", "        \n", "        current_items_in_group = len(group_X)\n", "        if current_items_in_group == 0:\n", "            continue\n", "\n", "        if current_items_in_group < list_size:\n", "            # パディング処理\n", "            pad_size = list_size - current_items_in_group\n", "            # 特徴量の次元数 X.shape[1] を使用\n", "            pad_X = np.zeros((pad_size, X.shape[1]))\n", "            pad_y = np.zeros(pad_size) # ラベルは通常0でパディング\n", "            \n", "            processed_X = np.vstack([group_X, pad_X])\n", "            processed_y = np.concatenate([group_y, pad_y])\n", "        elif current_items_in_group > list_size:\n", "            # 上位list_size個に制限 (group_y の値が大きいものを優先)\n", "            # argsortは昇順のインデックスを返すため、[::-1]で降順にする\n", "            top_indices = np.argsort(group_y)[::-1][:list_size]\n", "            processed_X = group_X[top_indices]\n", "            processed_y = group_y[top_indices]\n", "        else: # current_items_in_group == list_size\n", "            processed_X = group_X\n", "            processed_y = group_y\n", "        \n", "        X_list_collector.append(processed_X)\n", "        y_list_collector.append(processed_y)\n", "    \n", "    if not X_list_collector: # 有効なグループが一つもなかった場合\n", "        # 特徴量の次元数を X.shape[1] から取得\n", "        # Xが空の可能性も考慮するが、通常は学習データがある前提\n", "        num_features = X.shape[1] if X.ndim == 2 and X.shape[1] > 0 else 1\n", "        return np.empty((0, list_size, num_features), dtype=np.float32), np.empty((0, list_size), dtype=np.float32)\n", "\n", "    return np.array(X_list_collector), np.array(y_list_collector)\n", "\n", "def create_tf_dataset(X_list: np.ndarray,\n", "                      y_list: np.n<PERSON><PERSON>,\n", "                      batch_size: int = 32) -> tf.data.Dataset:\n", "    \"\"\"\n", "    整形済みのリスト形式データからTensorFlowデータセットを作成します。\n", "\n", "    Args:\n", "        X_list (np.ndarray): (グループ数 x list_size x 特徴量数) の特徴量データ。\n", "        y_list (np.ndarray): (グループ数 x list_size) のラベルデータ。\n", "        batch_size (int): データセットのバッチサイズ。\n", "\n", "    Returns:\n", "        tf.data.Dataset: バッチ化され、プリフェッチが設定されたTensorFlowデータセット。\n", "    \"\"\"\n", "    dataset = tf.data.Dataset.from_tensor_slices({\n", "        'features': X_list.astype(np.float32),\n", "        'labels': y_list.astype(np.float32) # TF-Rankingはラベルもfloat32を期待することが多い\n", "    })\n", "    return dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)\n", "\n", "# --- TF-Rankingモデル定義 ---\n", "def create_ranking_model(input_shape: Tuple[int, int],\n", "                         hidden_units: List[int] = [64, 32],\n", "                         dropout_rate: float = 0.2) -> tf.keras.Model:\n", "    \"\"\"\n", "    TensorFlow Ranking用のDNNベースのスコアリングモデルを作成します。\n", "    このモデルは各アイテムのスコアを予測します。\n", "\n", "    Args:\n", "        input_shape (Tuple[int, int]): モデルへの入力形状 (list_size, num_features)。\n", "        hidden_units (List[int]): DNNの隠れ層のユニット数のリスト。\n", "        dropout_rate (float): ドロップアウト率。\n", "\n", "    Returns:\n", "        tf.keras.Model: コンパイル前のランキングモデル。\n", "    \"\"\"\n", "    inputs = tf.keras.Input(shape=input_shape, name='features') # (batch_size, list_size, num_features)\n", "    \n", "    # DNNレイヤー: TimeDistributed を使ってリスト内の各アイテムに同じDNNを適用\n", "    # または、入力形状を (list_size * num_features) にフラット化して処理することも考えられるが、\n", "    # ここでは各アイテムの特徴ベクトルを独立して処理するアプローチを示す。\n", "    # TF-Ranking の損失関数は通常 (batch_size, list_size) のスコアを期待する。\n", "    # このモデルは (batch_size, list_size, 1) のスコアを出力し、損失関数側で調整される。\n", "    \n", "    x = inputs # (batch_size, list_size, num_features)\n", "    for units_count in hidden_units:\n", "        # Denseレイヤーはデフォルトで最後の次元に作用する。\n", "        # (batch_size, list_size, num_features) -> (batch_size, list_size, units_count)\n", "        x = tf.keras.layers.Dense(units_count, activation='relu')(x)\n", "        x = tf.keras.layers.Dropout(dropout_rate)(x)\n", "    \n", "    # 出力層（各アイテムのスコア予測）\n", "    # (batch_size, list_size, units_count) -> (batch_size, list_size, 1)\n", "    outputs = tf.keras.layers.Dense(1, name='scores')(x)\n", "    \n", "    model = tf.keras.Model(inputs=inputs, outputs=outputs)\n", "    return model\n", "\n", "# --- Optunaによるハイパーパラメータ最適化 ---\n", "def objective_for_tf_ranking(trial: optuna.trial.Trial,\n", "                             x_train_list_opt: np.n<PERSON><PERSON>, # 変数名を変更 (グローバルと区別)\n", "                             y_train_list_opt: np.n<PERSON><PERSON>, # 変数名を変更\n", "                             input_shape_opt: <PERSON><PERSON>[int, int], # 変数名を変更\n", "                             x_test_list_opt: Optional[np.ndarray] = None, # 変数名を変更\n", "                             y_test_list_opt: Optional[np.ndarray] = None) -> float: # 変数名を変更\n", "    \"\"\"\n", "    Optunaの目的関数。TF-Rankingモデルのハイパーパラメータを最適化し、\n", "    検証データセットでのNDCG@3を最大化します。\n", "\n", "    Args:\n", "        trial (optuna.trial.Trial): OptunaのTrialオブジェクト。\n", "        x_train_list_opt, y_train_list_opt: 学習用の整形済みデータ。\n", "        input_shape_opt: モデルの入力形状。\n", "        x_test_list_opt, y_test_list_opt: (オプション) 検証用の整形済みデータ。\n", "\n", "    Returns:\n", "        float: 検証データセットでの最高のNDCG@3スコア。テストデータがない場合は -float('inf')。\n", "    \"\"\"\n", "    params = {\n", "        'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),\n", "        'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64]),\n", "        'hidden_units_1': trial.suggest_int('hidden_units_1', 32, 128, step=16),\n", "        'hidden_units_2': trial.suggest_int('hidden_units_2', 16, 64, step=8),\n", "        'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5, step=0.1),\n", "        'epochs': trial.suggest_int('epochs', 10, 50, step=5) # epochsも最適化対象に\n", "    }\n", "    \n", "    model = create_ranking_model(\n", "        input_shape=input_shape_opt,\n", "        hidden_units=[params['hidden_units_1'], params['hidden_units_2']],\n", "        dropout_rate=params['dropout_rate']\n", "    )\n", "    \n", "    ranking_loss_fn = tfr.keras.losses.ListMLELoss() # TF Rankingは (batch_size, list_size) のラベルを期待\n", "    \n", "    ranking_metrics_fns = [\n", "        tfr.keras.metrics.NDCGMetric(topk=3, name='ndcg_3'),\n", "        tfr.keras.metrics.NDCGMetric(topk=5, name='ndcg_5')\n", "    ]\n", "    \n", "    model.compile(\n", "        optimizer=tf.keras.optimizers.<PERSON>(learning_rate=params['learning_rate']),\n", "        loss=ranking_loss_fn,\n", "        metrics=ranking_metrics_fns\n", "    )\n", "    \n", "    train_dataset_opt = create_tf_dataset(x_train_list_opt, y_train_list_opt, params['batch_size'])\n", "    \n", "    validation_data_opt = None\n", "    if x_test_list_opt is not None and y_test_list_opt is not None and len(x_test_list_opt) > 0:\n", "        test_dataset_opt = create_tf_dataset(x_test_list_opt, y_test_list_opt, params['batch_size'])\n", "        validation_data_opt = test_dataset_opt\n", "    else:\n", "        print(\"警告: <PERSON>tunaの評価に有効なテストデータが提供されていません。この試行の評価はスキップされます。\")\n", "        # Optunaにこの試行を枝刈りするよう促すこともできる\n", "        # raise optuna.exceptions.TrialPruned(\"Validation data not available.\")\n", "        return -float('inf') \n", "    \n", "    early_stopping_cb = tf.keras.callbacks.EarlyStopping(\n", "        monitor='val_ndcg_3', # 監視するメトリクス\n", "        patience=5,           # 改善が見られない場合に待つエポック数\n", "        restore_best_weights=True, # 最良の重みを復元\n", "        mode='max'            # ndcgは大きい方が良い\n", "    )\n", "    \n", "    try:\n", "        history = model.fit(\n", "            train_dataset_opt,\n", "            validation_data=validation_data_opt,\n", "            epochs=params['epochs'],\n", "            callbacks=[early_stopping_cb],\n", "            verbose=0 # Optuna実行中はログを抑制\n", "        )\n", "        \n", "        # history.history['val_ndcg_3'] がリストであることを確認\n", "        val_ndcg_3_scores = history.history.get('val_ndcg_3', [])\n", "        if not val_ndcg_3_scores: # リストが空の場合 (学習が1エポックも完了しなかった等)\n", "             best_ndcg_val = -float('inf')\n", "        else:\n", "             best_ndcg_val = max(val_ndcg_3_scores)\n", "        return best_ndcg_val\n", "    except Exception as e:\n", "        print(f\"Optuna試行中にモデル学習エラーが発生しました: {e}\")\n", "        return -float('inf') # エラー発生時は低いスコアを返す\n", "\n", "# --- データ準備とOptuna実行制御 ---\n", "# グローバルスコープの変数が存在するかどうかを確認 (Jupyter Notebook等での実行を想定)\n", "# これらの変数はスクリプト実行前に定義されている必要がある\n", "# 例:\n", "# X_train = pd.DataFrame(...)\n", "# y_train = pd.Series(...)\n", "# group_train = pd.Series(...) (レースIDなど)\n", "# ... testデータも同様 ...\n", "# feature_columns = [...] (特徴量のカラム名リスト)\n", "# test_df = pd.DataFrame(...) (予測結果をマージするためのテストデータフレーム)\n", "\n", "data_is_ready = (\n", "    'X_train' in globals() and isinstance(X_train, pd.DataFrame) and not X_train.empty and\n", "    'y_train' in globals() and isinstance(y_train, pd.Series) and not y_train.empty and\n", "    'group_train' in globals() and isinstance(group_train, pd.Series) and not group_train.empty and\n", "    len(X_train) == len(y_train) == len(group_train) # 行数が一致しているか\n", ")\n", "\n", "test_data_is_ready = (\n", "    'X_test' in globals() and isinstance(X_test, pd.DataFrame) and not X_test.empty and\n", "    'y_test' in globals() and isinstance(y_test, pd.Series) and not y_test.empty and\n", "    'group_test' in globals() and isinstance(group_test, pd.Series) and not group_test.empty and\n", "    len(X_test) == len(y_test) == len(group_test) # 行数が一致しているか\n", ")\n", "\n", "optimized_tf_ranker: Optional[tf.keras.Model] = None\n", "best_params_from_optuna: Dict[str, Any] = {}\n", "scaler = StandardScaler()\n", "\n", "if data_is_ready:\n", "    print(\"データの前処理を開始します...\")\n", "    \n", "    # 特徴量の標準化 (X_train, X_test は DataFrame を想定)\n", "    # feature_columns が定義されている前提\n", "    if 'feature_columns' not in globals():\n", "        print(\"エラー: 特徴量カラムリスト 'feature_columns' が定義されていません。\")\n", "        # 処理を中断するか、適切なデフォルト処理を行う\n", "        feature_columns = X_train.columns.tolist() # フォールバックとして全カラムを使用 (非推奨)\n", "\n", "    X_train_processed = X_train[feature_columns].copy()\n", "    X_train_scaled_np: np.ndarray = scaler.fit_transform(X_train_processed)\n", "    \n", "    y_train_np: np.ndarray = y_train.values\n", "    group_train_np: np.ndarray = group_train.values\n", "\n", "    if test_data_is_ready:\n", "        X_test_processed = X_test[feature_columns].copy()\n", "        X_test_scaled_np: np.ndarray = scaler.transform(X_test_processed)\n", "        y_test_np: np.ndarray = y_test.values\n", "        group_test_np: np.ndarray = group_test.values\n", "    \n", "    # ランキング用データセットの作成\n", "    # ここで渡す group_train_np は X_train_scaled_np の各行に対応するグループID\n", "    X_train_list, y_train_list = create_ranking_dataset(\n", "        X_train_scaled_np, y_train_np, group_train_np, DEFAULT_LIST_SIZE\n", "    )\n", "    \n", "    X_test_list_main: Optional[np.ndarray] = None # mainスコープでの変数名\n", "    y_test_list_main: Optional[np.ndarray] = None # mainスコープでの変数名\n", "    if test_data_is_ready:\n", "        X_test_list_main, y_test_list_main = create_ranking_dataset(\n", "            X_test_scaled_np, y_test_np, group_test_np, DEFAULT_LIST_SIZE\n", "        )\n", "        if X_test_list_main is not None and len(X_test_list_main) == 0: # 有効なテストグループがなかった場合\n", "            print(\"警告: テストデータから有効なランキングリストが作成できませんでした。テストはスキップされます。\")\n", "            X_test_list_main, y_test_list_main = None, None # Optunaや学習で使われないようにする\n", "            test_data_is_ready = False # 後続の処理でテストデータがないものとして扱う\n", "    \n", "    # 入力形状は (list_size, num_features)\n", "    input_shape_main: Tuple[int, int] = (DEFAULT_LIST_SIZE, X_train_scaled_np.shape[1])\n", "    \n", "    print(f\"学習データの形状 (X_list): {X_train_list.shape}, (y_list): {y_train_list.shape}\")\n", "    if test_data_is_ready and X_test_list_main is not None:\n", "        print(f\"テストデータの形状 (X_list): {X_test_list_main.shape}, (y_list): {y_test_list_main.shape}\")\n", "    elif not test_data_is_ready:\n", "        print(\"テストデータは利用できません。\")\n", "\n", "\n", "    if USE_OPTUNA and data_is_ready: # data_is_ready は学習データがあることを保証\n", "        print(\"Optunaによるハイパーパラメータ最適化を開始します...\")\n", "        \n", "        # Optunaの目的関数に渡すデータ。テストデータがない場合はNoneを渡す。\n", "        objective_with_data = functools.partial(\n", "            objective_for_tf_ranking,\n", "            x_train_list_opt=X_train_list,\n", "            y_train_list_opt=y_train_list,\n", "            input_shape_opt=input_shape_main,\n", "            x_test_list_opt=X_test_list_main if test_data_is_ready else None,\n", "            y_test_list_opt=y_test_list_main if test_data_is_ready else None\n", "        )\n", "        \n", "        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=RANDOM_SEED))\n", "        study.optimize(objective_with_data, n_trials=OPTUNA_N_TRIALS, timeout=OPTUNA_TIMEOUT)\n", "        \n", "        print(\"\\nOptunaによるハイパーパラメータ最適化が完了しました。\")\n", "        if study.best_trial:\n", "            print(\"最適な試行:\")\n", "            best_trial_result = study.best_trial\n", "            print(f\"  Value (NDCG@3): {best_trial_result.value}\")\n", "            print(\"  Params: \")\n", "            for key, value in best_trial_result.params.items():\n", "                print(f\"    {key}: {value}\")\n", "            best_params_from_optuna = best_trial_result.params\n", "        else:\n", "            print(\"Optunaで有効な試行が見つかりませんでした。\")\n", "\n", "    # --- 最終モデルの学習 ---\n", "    if data_is_ready: # data_is_ready は学習データがあることを保証\n", "        print(\"\\n最終モデルを学習します...\")\n", "        \n", "        final_params_main = DEFAULT_HYPERPARAMETERS.copy() # デフォルト値で初期化\n", "        if USE_OPTUNA and best_params_from_optuna:\n", "            print(\"Optunaで見つかった最適なパラメータを使用します。\")\n", "            final_params_main.update(best_params_from_optuna) # Optunaの結果で上書き\n", "        else:\n", "            print(\"Optunaを使用しないか、最適なパラメータが見つからなかったため、デフォルトパラメータを使用します。\")\n", "        \n", "        optimized_tf_ranker = create_ranking_model(\n", "            input_shape=input_shape_main,\n", "            hidden_units=[final_params_main['hidden_units_1'], final_params_main['hidden_units_2']],\n", "            dropout_rate=final_params_main['dropout_rate']\n", "        )\n", "        \n", "        final_ranking_loss_fn = tfr.keras.losses.ListMLELoss()\n", "        final_ranking_metrics_fns = [\n", "            tfr.keras.metrics.NDCGMetric(topk=1, name='ndcg_1'),\n", "            tfr.keras.metrics.NDCGMetric(topk=3, name='ndcg_3'),\n", "            tfr.keras.metrics.NDCGMetric(topk=5, name='ndcg_5')\n", "        ]\n", "        \n", "        optimized_tf_ranker.compile(\n", "            optimizer=tf.keras.optimizers.<PERSON>(learning_rate=final_params_main['learning_rate']),\n", "            loss=final_ranking_loss_fn,\n", "            metrics=final_ranking_metrics_fns\n", "        )\n", "        \n", "        final_train_dataset = create_tf_dataset(X_train_list, y_train_list, final_params_main['batch_size'])\n", "        \n", "        callbacks_main = []\n", "        validation_data_main = None\n", "        \n", "        if test_data_is_ready and X_test_list_main is not None and y_test_list_main is not None:\n", "            final_test_dataset = create_tf_dataset(X_test_list_main, y_test_list_main, final_params_main['batch_size'])\n", "            validation_data_main = final_test_dataset\n", "            callbacks_main.append(tf.keras.callbacks.EarlyStopping(\n", "                monitor='val_ndcg_3',\n", "                patience=10, # <PERSON><PERSON><PERSON>より少し長めのpatience\n", "                restore_best_weights=True,\n", "                mode='max',\n", "                verbose=1\n", "            ))\n", "        \n", "        print(f\"最終モデルの学習を開始します... Epochs: {final_params_main['epochs']}\")\n", "        history_final = optimized_tf_ranker.fit(\n", "            final_train_dataset,\n", "            validation_data=validation_data_main,\n", "            epochs=final_params_main['epochs'],\n", "            callbacks=callbacks_main,\n", "            verbose=1\n", "        )\n", "        print(\"最終モデルの学習が完了しました。\")\n", "\n", "else: # data_is_ready が False の場合\n", "    print(\"学習データが不十分または不整合なため、モデル学習は実行されませんでした。\")\n", "    print(\"X_train, y_train, group_train の存在と、それらの行数が一致しているか確認してください。\")\n", "\n", "# --- 学習済み最終モデルでテストデータの予測 ---\n", "# test_df は予測結果を格納・表示するために使用\n", "if optimized_tf_ranker and test_data_is_ready and 'test_df' in globals() and isinstance(test_df, pd.DataFrame) and not test_df.empty:\n", "    print(\"\\n最終モデルでテストデータの予測を行います...\")\n", "    \n", "    # X_test_list_main, y_test_list_main が None でないことを確認\n", "    if X_test_list_main is None or y_test_list_main is None:\n", "        print(\"警告: 予測に必要なテストデータリスト (X_test_list_main, y_test_list_main) がありません。予測をスキップします。\")\n", "    else:\n", "        test_data_with_predictions = test_df.copy()\n", "        \n", "        # バッチサイズは学習時と同じか、推論に適した値を使用\n", "        # final_params_main が定義されている前提\n", "        predict_batch_size = final_params_main.get('batch_size', 32)\n", "        predict_dataset = create_tf_dataset(X_test_list_main, y_test_list_main, predict_batch_size)\n", "        \n", "        # predictions_raw の形状は (num_test_groups, list_size, 1)\n", "        predictions_raw = optimized_tf_ranker.predict(predict_dataset)\n", "        \n", "        # 予測結果を元の test_df の各行に対応付ける\n", "        # group_test_np, y_test_np は元のテストデータに対応\n", "        predicted_scores_flat = np.full(len(test_data_with_predictions), np.nan)\n", "        \n", "        unique_test_groups = np.unique(group_test_np) # group_test_np を使用\n", "        \n", "        current_pred_idx = 0 # predictions_raw のインデックス\n", "        for group_val in unique_test_groups:\n", "            if current_pred_idx >= len(predictions_raw):\n", "                print(f\"警告: グループ {group_val} の予測データがありません。スキップします。\")\n", "                break\n", "\n", "            # 元の test_df/X_test における、このグループのアイテムのインデックス\n", "            original_indices_in_df = test_df[group_train == group_val].index # group_train は Series を想定\n", "            \n", "            # create_ranking_dataset と同じロジックで処理されたアイテム数を特定\n", "            # X_test_scaled_np, y_test_np, group_test_np を使用\n", "            original_group_mask_np = (group_test_np == group_val)\n", "            num_original_items_in_group = np.sum(original_group_mask_np)\n", "            \n", "            # モデルからの予測スコア (このグループのリストに対応)\n", "            # predictions_raw[current_pred_idx] の形状は (list_size, 1) なので flatten()\n", "            group_scores_from_model = predictions_raw[current_pred_idx].flatten()\n", "            \n", "            if num_original_items_in_group == 0:\n", "                current_pred_idx +=1\n", "                continue\n", "\n", "            if num_original_items_in_group <= DEFAULT_LIST_SIZE:\n", "                # パディングされたケース: 元のアイテム数だけスコアを割り当てる\n", "                # original_indices_in_df の順序と group_scores_from_model の先頭部分が対応\n", "                # ただし、create_ranking_dataset でソートされていない場合\n", "                # ここでは、元の順序が保たれていると仮定して、先頭から割り当てる\n", "                # より堅牢にするには、create_ranking_datasetでアイテムIDも保持し、突合する\n", "                target_indices = original_indices_in_df[:num_original_items_in_group]\n", "                predicted_scores_flat[target_indices] = group_scores_from_model[:num_original_items_in_group]\n", "\n", "            else: # num_original_items_in_group > DEFAULT_LIST_SIZE\n", "                # 切り捨てられたケース: create_ranking_dataset と同じロジックで\n", "                # y_test_np に基づいて上位 DEFAULT_LIST_SIZE 個が選ばれたはず。\n", "                # それらの元のインデックスを特定する。\n", "                original_y_for_group = y_test_np[original_group_mask_np]\n", "                \n", "                # y_test_np の中で、このグループの上位アイテムのインデックス (グループ内での相対インデックス)\n", "                top_indices_within_original_group = np.argsort(original_y_for_group)[::-1][:DEFAULT_LIST_SIZE]\n", "                \n", "                # test_df全体での絶対インデックスを取得\n", "                # original_indices_in_df は DataFrame のインデックスなので、これを使って NumPy 配列のインデックスに変換\n", "                # original_indices_in_df.take(top_indices_within_original_group)\n", "                # または、X_test_scaled_np でのインデックスを元に DataFrame のインデックスを取得\n", "                \n", "                # X_test_scaled_np での絶対インデックス\n", "                absolute_indices_in_X_test = np.where(original_group_mask_np)[0]\n", "                # その中で上位だったものの絶対インデックス\n", "                target_absolute_indices = absolute_indices_in_X_test[top_indices_within_original_group]\n", "                \n", "                # DataFrame のインデックスに変換 (X_test と test_df のインデックスが一致している前提)\n", "                target_df_indices = test_df.iloc[target_absolute_indices].index\n", "                predicted_scores_flat[target_df_indices] = group_scores_from_model # 長さが一致するはず\n", "\n", "            current_pred_idx += 1\n", "\n", "        if len(predicted_scores_flat) == len(test_data_with_predictions):\n", "            test_data_with_predictions['predicted_score'] = predicted_scores_flat\n", "            # 'race_id' カラムが存在する前提でランク付け\n", "            if 'race_id' in test_data_with_predictions.columns:\n", "                 test_data_with_predictions['predicted_rank'] = test_data_with_predictions.groupby('race_id')['predicted_score'].rank(ascending=False, method='first')\n", "            else:\n", "                 print(\"警告: 'race_id' カラムが test_df に見つからないため、predicted_rank は計算されません。\")\n", "\n", "\n", "            print(\"\\n予測結果（テストデータの一部）:\")\n", "            display_cols = ['predicted_score'] # 必須\n", "            if 'race_id' in test_data_with_predictions.columns: display_cols.insert(0, 'race_id')\n", "            if 'horse_id' in test_data_with_predictions.columns: display_cols.insert(1, 'horse_id') # 例\n", "            if 'predicted_rank' in test_data_with_predictions.columns: display_cols.append('predicted_rank')\n", "\n", "            # y_label_column_name と actual_rank_col はグローバルスコープで定義されている想定\n", "            if 'y_label_column_name' in globals() and y_label_column_name in test_data_with_predictions.columns:\n", "                display_cols.append(y_label_column_name)\n", "            if 'actual_rank_col' in globals() and actual_rank_col in test_data_with_predictions.columns:\n", "                display_cols.append(actual_rank_col)\n", "            \n", "            valid_display_cols = [col for col in display_cols if col in test_data_with_predictions.columns]\n", "            print(test_data_with_predictions[valid_display_cols].head())\n", "        else:\n", "            print(f\"致命的エラー: 予測スコアの割り当てに失敗しました。予測数({len(predicted_scores_flat)})とテストデータ数({len(test_data_with_predictions)})が一致しません。\")\n", "elif not test_data_is_ready:\n", "    print(\"\\nテストデータがないか不整合なため、予測はスキップされました。\")\n", "elif not optimized_tf_ranker:\n", "     print(\"\\nモデルが学習されていないため、テストデータの予測はスキップされました。\")\n", "else: # test_df がない場合など\n", "    print(\"\\n予測結果を格納する test_df がないため、予測詳細はスキップされました。\")\n", "\n", "\n", "# --- 評価 ---\n", "if optimized_tf_ranker and test_data_is_ready and X_test_list_main is not None and y_test_list_main is not None:\n", "    print(\"\\nモデル評価 (テストデータ):\")\n", "    # final_params_main が定義されている前提\n", "    eval_batch_size = final_params_main.get('batch_size', 32)\n", "    eval_dataset = create_tf_dataset(X_test_list_main, y_test_list_main, eval_batch_size)\n", "    evaluation_results = optimized_tf_ranker.evaluate(eval_dataset, verbose=0)\n", "    \n", "    metric_names = optimized_tf_ranker.metrics_names\n", "    for metric_name, value in zip(metric_names, evaluation_results):\n", "        print(f\"  {metric_name}: {value:.4f}\")\n", "\n", "# モデルサマリー表示\n", "if optimized_tf_ranker:\n", "    print(\"\\n最終モデル構造:\")\n", "    optimized_tf_ranker.summary()\n", "\n", "print(\"\\nTensorFlow Rankingを使用した競馬予測システムの実行が完了しました。\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "59dedf11", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "numpy1.5", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.9"}}, "nbformat": 4, "nbformat_minor": 5}