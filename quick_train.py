#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
クイック訓練ツール - 1コマンドでモデル訓練

使用方法:
    python quick_train.py                    # 基本訓練
    python quick_train.py --enhanced         # 拡張訓練
    python quick_train.py --fast             # 高速訓練
    python quick_train.py --age              # 年齢特化訓練
"""

import sys
import argparse
import subprocess
from datetime import datetime

# Windows環境でのUnicode文字対応
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

def run_training(training_type: str):
    """指定されたタイプの訓練を実行"""
    training_scripts = {
        'basic': 'simple_train.py',
        'enhanced': 'enhanced_train.py', 
        'fast': 'quick_enhanced_train.py',
        'age': 'age_enhanced_train.py'
    }
    
    script = training_scripts.get(training_type)
    if not script:
        print(f"❌ 無効な訓練タイプ: {training_type}")
        return False
    
    print(f"🤖 {training_type}モデル訓練を開始します...")
    print(f"📜 実行スクリプト: {script}")
    print(f"🕐 開始時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 訓練スクリプト実行
        result = subprocess.run(
            [sys.executable, script], 
            capture_output=False,  # リアルタイム出力
            text=True
        )
        
        print("=" * 60)
        if result.returncode == 0:
            print(f"✅ {training_type}モデル訓練完了！")
            print(f"🕐 終了時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print(f"❌ {training_type}モデル訓練でエラーが発生しました")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 訓練実行エラー: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='クイック訓練ツール')
    
    # 訓練タイプの選択（排他的）
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--basic', action='store_true', help='基本モデル訓練')
    group.add_argument('--enhanced', action='store_true', help='拡張モデル訓練')
    group.add_argument('--fast', action='store_true', help='高速訓練')
    group.add_argument('--age', action='store_true', help='年齢特化訓練')
    
    args = parser.parse_args()
    
    # 訓練タイプの決定
    if args.enhanced:
        training_type = 'enhanced'
    elif args.fast:
        training_type = 'fast'
    elif args.age:
        training_type = 'age'
    else:
        training_type = 'basic'  # デフォルト
    
    print(f"🚀 {training_type}モードで訓練を開始します")
    
    # 訓練実行
    success = run_training(training_type)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()