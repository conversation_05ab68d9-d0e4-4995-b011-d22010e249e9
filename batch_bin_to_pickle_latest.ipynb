{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 改善版：競馬データ一括処理→学習前準備Notebook\n", "\n", "このNotebookは、複数年分のbinファイルをDataFrameに変換し、レース情報と馬情報を統合処理して、機械学習用の訓練データまで準備します。\n", "\n", "## 主な改善点と機能\n", "- **既存coreモジュールの完全活用**: ComprehensiveDataIntegrator + FeatureEngineeringManager\n", "- **包括的特徴量エンジニアリング**: 7カテゴリ、268項目の特徴量設定\n", "- **データリーケージ完全防止**: 時間軸フィルタリングとリスク列除外機能\n", "- **学習前データ準備**: 特徴量選択、エンコーディング、前処理まで完了\n", "- **チェックポイント機能**: 中断した処理を再開可能\n", "- **詳細なログ記録**: 処理状況を完全に追跡\n", "- **設定駆動処理**: YAML設定ファイルで柔軟な制御"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 環境設定とインポート"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["プロジェクトルート: h:\\AI\\keiba_ai_system\n"]}], "source": ["# 基本ライブラリのインポート\n", "import os\n", "import sys\n", "from pathlib import Path\n", "from datetime import datetime\n", "import pandas as pd\n", "import numpy as np\n", "from tqdm.notebook import tqdm\n", "import logging\n", "import json\n", "from typing import List, Dict, Tuple, Optional\n", "import gc\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# プロジェクトルートの設定\n", "PROJECT_ROOT = Path.cwd()\n", "sys.path.insert(0, str(PROJECT_ROOT))\n", "\n", "print(f\"プロジェクトルート: {PROJECT_ROOT}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-09 17:27:57,888 - __main__ - INFO - ログ設定完了\n"]}], "source": ["# ログ設定\n", "def setup_logging(log_dir: Path = Path('logs')):\n", "    \"\"\"ログ設定のセットアップ\"\"\"\n", "    log_dir.mkdir(exist_ok=True)\n", "    log_file = log_dir / f'batch_processing_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.log'\n", "    \n", "    logging.basicConfig(\n", "        level=logging.INFO,\n", "        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n", "        handlers=[\n", "            logging.FileHandler(log_file),\n", "            logging.StreamHandler()\n", "        ]\n", "    )\n", "    return logging.getLogger(__name__)\n", "\n", "logger = setup_logging()\n", "logger.info(\"ログ設定完了\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-09 17:28:00,769 - __main__ - INFO - モジュールのインポートに成功しました\n"]}], "source": ["# 必要なモジュールのインポート\n", "try:\n", "    from core.processors.race_processor import RaceProcessor\n", "    from core.processors.race_file_handler import RaceFileHandler\n", "    from core.processors.race_html_parser import RaceHtmlParser\n", "    from core.processors.corner_analyzer import CornerAnalyzer\n", "    from core.processors.horse_processor import HorseProcessor\n", "    from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator\n", "    \n", "    # 特徴量エンジニアリング関連\n", "    from core.features.manager import FeatureEngineeringManager\n", "    from core.features.calculators import FeatureCalculators\n", "    \n", "    # 学習前処理\n", "    from sklearn.preprocessing import LabelEncoder, StandardScaler\n", "    from sklearn.model_selection import train_test_split\n", "    import yaml\n", "    import pickle\n", "    \n", "    logger.info(\"モジュールのインポートに成功しました\")\n", "except ImportError as e:\n", "    logger.error(f\"モジュールのインポートに失敗しました: {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. BatchProcessorクラスの定義"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class BatchProcessor:\n", "    \"\"\"既存のcoreモジュールを活用したバッチ処理管理クラス\"\"\"\n", "    \n", "    def __init__(self, config: Dict):\n", "        self.config = config\n", "        self.setup_directories()\n", "        self.setup_processors()\n", "        self.processing_stats = {\n", "            'total_files': 0,\n", "            'processed_files': 0,\n", "            'failed_files': 0,\n", "            'start_time': None,\n", "            'end_time': None\n", "        }\n", "    \n", "    def setup_directories(self):\n", "        \"\"\"ディレクトリの設定\"\"\"\n", "        self.data_dir = Path(self.config['data_dir'])\n", "        self.output_dir = Path(self.config['output_dir'])\n", "        self.output_dir.mkdir(parents=True, exist_ok=True)\n", "        \n", "        # サブディレクトリの作成\n", "        self.pickle_dir = self.output_dir / 'pickle'\n", "        self.stats_dir = self.output_dir / 'stats'\n", "        self.checkpoint_dir = self.output_dir / 'checkpoints'\n", "        \n", "        for dir_path in [self.pickle_dir, self.stats_dir, self.checkpoint_dir]:\n", "            dir_path.mkdir(exist_ok=True)\n", "    \n", "    def setup_processors(self):\n", "        \"\"\"既存のcoreモジュールのプロセッサーを初期化\"\"\"\n", "        # ComprehensiveDataIntegratorを中心とした統合処理\n", "        integrator_config = {\n", "            'use_pickle_source': self.config.get('use_pickle_source', False),\n", "            'parallel': self.config.get('parallel', True),\n", "            'max_workers': self.config.get('max_workers', 4),\n", "            'include_corner_features': self.config.get('include_corner_features', True),\n", "            'include_race_info': True,\n", "            'include_horse_info': self.config.get('include_horse_data', True),\n", "            'include_past_performance': self.config.get('include_horse_data', True)\n", "        }\n", "        \n", "        self.comprehensive_integrator = ComprehensiveDataIntegrator(config=integrator_config)\n", "        logger.info(\"ComprehensiveDataIntegratorを初期化しました\")\n", "    \n", "    def process_years(self, years: List[str]):\n", "        \"\"\"複数年のデータを処理（ComprehensiveDataIntegratorを使用）\"\"\"\n", "        self.processing_stats['start_time'] = datetime.now()\n", "        logger.info(f\"処理開始: {len(years)}年分のデータを処理します\")\n", "        \n", "        all_results = []\n", "        \n", "        for year in tqdm(years, desc=\"年次処理\", unit=\"年\"):\n", "            try:\n", "                year_results = self.process_single_year(year)\n", "                \n", "                if year_results is not None and not year_results.empty:\n", "                    all_results.append(year_results)\n", "                    logger.info(f\"{year}年: {len(year_results)}行のデータを処理\")\n", "                \n", "                # メモリ解放\n", "                gc.collect()\n", "                \n", "            except Exception as e:\n", "                logger.error(f\"{year}年の処理でエラーが発生: {e}\")\n", "                self.save_checkpoint(year, status='failed', error=str(e))\n", "                continue\n", "        \n", "        # 全体の結果を結合\n", "        if all_results:\n", "            final_results = pd.concat(all_results, ignore_index=True)\n", "            logger.info(f\"全{len(years)}年分のデータを結合: {len(final_results)}行\")\n", "        else:\n", "            final_results = pd.DataFrame()\n", "            logger.warning(\"処理されたデータがありません\")\n", "        \n", "        self.processing_stats['end_time'] = datetime.now()\n", "        self.save_processing_stats()\n", "        \n", "        return final_results\n", "    \n", "    def process_single_year(self, year: str) -> Optional[pd.DataFrame]:\n", "        \"\"\"単一年のデータを処理（ComprehensiveDataIntegratorを使用）\"\"\"\n", "        logger.info(f\"{year}年のデータ処理を開始\")\n", "        \n", "        # チェックポイントの確認\n", "        checkpoint = self.load_checkpoint(year)\n", "        if checkpoint and checkpoint.get('status') == 'completed':\n", "            logger.info(f\"{year}年は既に処理済みです\")\n", "            return self.load_year_results(year)\n", "        \n", "        try:\n", "            # ComprehensiveDataIntegratorで包括的なデータ統合\n", "            comprehensive_data = self.comprehensive_integrator.generate_comprehensive_table(\n", "                year=year,\n", "                include_race_info=True,\n", "                include_horse_info=self.config.get('include_horse_data', True),\n", "                include_past_performance=self.config.get('include_horse_data', True),\n", "                include_corner_features=self.config.get('include_corner_features', True)\n", "            )\n", "            \n", "            if comprehensive_data is not None and not comprehensive_data.empty:\n", "                # 年次データの保存\n", "                self.save_year_data(year, comprehensive_data)\n", "                \n", "                # 統計情報の保存\n", "                self.save_year_stats(year, comprehensive_data)\n", "                \n", "                # チェックポイントの保存\n", "                self.save_checkpoint(year, status='completed')\n", "                \n", "                return comprehensive_data\n", "            else:\n", "                logger.warning(f\"{year}年のデータが空です\")\n", "                return None\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"{year}年の処理中にエラーが発生: {e}\")\n", "            self.save_checkpoint(year, status='failed', error=str(e))\n", "            raise\n", "    \n", "    def save_year_data(self, year: str, data: pd.DataFrame):\n", "        \"\"\"年次データの保存\"\"\"\n", "        output_file = self.pickle_dir / f'comprehensive_data_{year}.pkl'\n", "        data.to_pickle(output_file)\n", "        logger.info(f\"{year}年のデータを保存: {output_file}\")\n", "    \n", "    def save_year_stats(self, year: str, data: pd.DataFrame):\n", "        \"\"\"年次統計の保存\"\"\"\n", "        stats = {\n", "            'year': year,\n", "            'total_rows': len(data),\n", "            'total_columns': len(data.columns),\n", "            'unique_races': data['race_id'].nunique() if 'race_id' in data.columns else 0,\n", "            'unique_horses': data['horse_id'].nunique() if 'horse_id' in data.columns else 0,\n", "            'memory_usage_mb': data.memory_usage(deep=True).sum() / 1024 / 1024,\n", "            'processing_time': datetime.now().isoformat(),\n", "            'column_names': list(data.columns)\n", "        }\n", "        \n", "        stats_file = self.stats_dir / f'stats_{year}.json'\n", "        with open(stats_file, 'w', encoding='utf-8') as f:\n", "            json.dump(stats, f, ensure_ascii=False, indent=2)\n", "    \n", "    def save_checkpoint(self, year: str, status: str, error: Optional[str] = None):\n", "        \"\"\"チェックポイントの保存\"\"\"\n", "        checkpoint = {\n", "            'year': year,\n", "            'status': status,\n", "            'timestamp': datetime.now().isoformat(),\n", "            'error': error\n", "        }\n", "        \n", "        checkpoint_file = self.checkpoint_dir / f'checkpoint_{year}.json'\n", "        with open(checkpoint_file, 'w', encoding='utf-8') as f:\n", "            json.dump(checkpoint, f, ensure_ascii=False, indent=2)\n", "    \n", "    def load_checkpoint(self, year: str) -> Optional[Dict]:\n", "        \"\"\"チェックポイントの読み込み\"\"\"\n", "        checkpoint_file = self.checkpoint_dir / f'checkpoint_{year}.json'\n", "        if checkpoint_file.exists():\n", "            with open(checkpoint_file, 'r', encoding='utf-8') as f:\n", "                return json.load(f)\n", "        return None\n", "    \n", "    def load_year_results(self, year: str) -> Optional[pd.DataFrame]:\n", "        \"\"\"年次結果の読み込み\"\"\"\n", "        pickle_file = self.pickle_dir / f'comprehensive_data_{year}.pkl'\n", "        if pickle_file.exists():\n", "            return pd.read_pickle(pickle_file)\n", "        return None\n", "    \n", "    def save_processing_stats(self):\n", "        \"\"\"処理統計の保存\"\"\"\n", "        if self.processing_stats['start_time'] and self.processing_stats['end_time']:\n", "            duration = self.processing_stats['end_time'] - self.processing_stats['start_time']\n", "            self.processing_stats['duration_seconds'] = duration.total_seconds()\n", "        \n", "        stats_file = self.stats_dir / 'overall_stats.json'\n", "        with open(stats_file, 'w', encoding='utf-8') as f:\n", "            json.dump(self.processing_stats, f, default=str, ensure_ascii=False, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. ユーティリティ関数"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def validate_results(results: pd.DataFrame) -> Dict:\n", "    \"\"\"結果の検証（単一のDataFrameに対応）\"\"\"\n", "    if results is None or results.empty:\n", "        return {'status': 'empty', 'message': 'データが空です'}\n", "    \n", "    validation = {\n", "        'status': 'success',\n", "        'rows': len(results),\n", "        'columns': len(results.columns),\n", "        'memory_usage_mb': results.memory_usage(deep=True).sum() / 1024 / 1024,\n", "        'unique_races': results['race_id'].nunique() if 'race_id' in results.columns else 0,\n", "        'unique_horses': results['horse_id'].nunique() if 'horse_id' in results.columns else 0,\n", "        'column_categories': {}\n", "    }\n", "    \n", "    # 列をカテゴリ別に分類\n", "    columns = results.columns.tolist()\n", "    validation['column_categories'] = {\n", "        'race_info': [col for col in columns if col.startswith('race_') or col in ['date', 'venue', 'distance']],\n", "        'horse_info': [col for col in columns if col.startswith('horse_') and 'past_' not in col],\n", "        'past_performance': [col for col in columns if 'past_' in col or 'avg_' in col],\n", "        'corner_features': [col for col in columns if 'corner_' in col],\n", "        'other': [col for col in columns if not any(cat in col for cat in ['race_', 'horse_', 'past_', 'corner_', 'avg_'])]\n", "    }\n", "    \n", "    return validation\n", "\n", "def display_summary(results: pd.DataFrame, validation: Dict):\n", "    \"\"\"処理結果のサマリー表示（単一のDataFrameに対応）\"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"包括的データ処理結果サマリー\")\n", "    print(\"=\"*60)\n", "    \n", "    if validation['status'] == 'empty':\n", "        print(f\"ステータス: {validation['message']}\")\n", "        return\n", "    \n", "    print(f\"総行数: {validation['rows']:,}\")\n", "    print(f\"総列数: {validation['columns']}\")\n", "    print(f\"メモリ使用量: {validation['memory_usage_mb']:.2f} MB\")\n", "    print(f\"ユニークなレース数: {validation['unique_races']:,}\")\n", "    print(f\"ユニークな馬数: {validation['unique_horses']:,}\")\n", "    \n", "    print(\"\\n【データカテゴリ別列数】\")\n", "    for category, cols in validation['column_categories'].items():\n", "        if cols:\n", "            print(f\"  {category}: {len(cols)}列\")\n", "    \n", "    print(\"\\n【データ品質チェック】\")\n", "    if 'race_id' in results.columns:\n", "        print(f\"  - レースID欠損: {results['race_id'].isnull().sum()}件\")\n", "    if 'horse_id' in results.columns:\n", "        print(f\"  - 馬ID欠損: {results['horse_id'].isnull().sum()}件\")\n", "    \n", "    # サンプルデータの表示\n", "    print(\"\\n【サンプルデータ（最初の3行）】\")\n", "    try:\n", "        # 重要な列のみ表示\n", "        important_cols = ['race_id', 'horse_id', 'horse_name', 'rank', 'date']\n", "        available_cols = [col for col in important_cols if col in results.columns]\n", "        if available_cols:\n", "            print(results[available_cols].head(3).to_string())\n", "        else:\n", "            print(results.head(3).to_string())\n", "    except Exception as e:\n", "        print(f\"サンプルデータ表示エラー: {e}\")\n", "    \n", "    print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 設定の定義"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["処理対象年: ['2020', '2021', '2022', '2023', '2024']\n", "データディレクトリ: H:/AI/keiba_ai_system/data/html/race/race_by_year\n", "馬データディレクトリ: H:/AI/keiba_ai_system/data/html/horse\n", "出力ディレクトリ: output\n", "馬データ処理: 有効\n", "特徴量エンジニアリング: 有効\n", "学習前データ準備: 有効\n"]}], "source": ["# 設定\n", "config = {\n", "    # データディレクトリ\n", "    'data_dir': 'H:/AI/keiba_ai_system/data/html/race/race_by_year',\n", "    'output_dir': 'output',\n", "    \n", "    # 馬データディレクトリ\n", "    'horse_data_dir': 'H:/AI/keiba_ai_system/data/html/horse',\n", "    \n", "    # 処理対象年\n", "    # 個別指定の場合\n", "    # 'years': ['2020', '2021', '2022'],\n", "    \n", "    # 範囲指定の場合\n", "    'year_range': (2020, 2024),\n", "    \n", "    # 処理設定\n", "    'parallel': True,\n", "    'max_workers': 4,\n", "    'max_files_per_year': None,  # Noneで全ファイル処理、テスト時は100など指定\n", "    'include_corner_features': True,\n", "    'include_horse_data': True,  # 馬の固有データを含める\n", "    'use_pickle_source': False,  # HTMLから処理（True: 高速、False: 最新データ）\n", "    \n", "    # 特徴量エンジニアリング設定\n", "    'apply_feature_engineering': True,  # 特徴量エンジニアリングを適用\n", "    'feature_config_path': 'core/features/config.yaml',\n", "    'training_config_path': 'training_config.yaml',\n", "    \n", "    # 学習前準備設定\n", "    'prepare_training_data': True,  # 学習用データの準備まで実行\n", "    'target_column': 'rank',  # ターゲット列名\n", "    'target_threshold': 3,    # 3着以内を正例とする\n", "    'test_size': 0.2,        # テストデータの割合\n", "    'random_state': 42,      # 再現性のための固定シード\n", "    \n", "    # 保存設定\n", "    'save_final_results': True,\n", "    'save_training_data': True\n", "}\n", "\n", "# 年次リストの生成\n", "if config.get('year_range'):\n", "    start_year, end_year = config['year_range']\n", "    years = [str(year) for year in range(start_year, end_year + 1)]\n", "else:\n", "    years = config.get('years', [])\n", "\n", "print(f\"処理対象年: {years}\")\n", "print(f\"データディレクトリ: {config['data_dir']}\")\n", "print(f\"馬データディレクトリ: {config['horse_data_dir']}\")\n", "print(f\"出力ディレクトリ: {config['output_dir']}\")\n", "print(f\"馬データ処理: {'有効' if config['include_horse_data'] else '無効'}\")\n", "print(f\"特徴量エンジニアリング: {'有効' if config['apply_feature_engineering'] else '無効'}\")\n", "print(f\"学習前データ準備: {'有効' if config['prepare_training_data'] else '無効'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. メイン処理の実行"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# BatchProcessorの初期化\n", "processor = BatchProcessor(config)\n", "logger.info(f\"処理対象年: {years}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# データ処理の実行\n", "results = processor.process_years(years)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 結果の検証\n", "validation = validate_results(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# サマリー表示\n", "display_summary(results, validation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 最終結果の保存（オプション）\n", "if config.get('save_final_results', True) and results is not None and not results.empty:\n", "    output_file = processor.output_dir / 'comprehensive_data_all_years.pkl'\n", "    results.to_pickle(output_file)\n", "    logger.info(f\"包括的データを保存: {output_file}\")\n", "    print(f\"包括的データを保存しました: {output_file}\")\n", "    print(f\"データ形状: {results.shape}\")\n", "else:\n", "    print(\"保存するデータがありません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 特徴量エンジニアリングと学習前準備\n", "if config.get('apply_feature_engineering', False) and results is not None and not results.empty:\n", "    logger.info(\"特徴量エンジニアリングを開始...\")\n", "    \n", "    try:\n", "        # FeatureEngineeringManagerの初期化\n", "        feature_manager = FeatureEngineeringManager(\n", "            config_path=config.get('feature_config_path', 'core/features/config.yaml')\n", "        )\n", "        \n", "        # 特徴量エンジニアリングの適用\n", "        enhanced_results = feature_manager.calculate_features(\n", "            data=results,\n", "            dependencies={'race_date': 'date' if 'date' in results.columns else 'race_date'}\n", "        )\n", "        \n", "        logger.info(f\"特徴量エンジニアリング完了: {enhanced_results.shape}\")\n", "        print(f\"特徴量エンジニアリング完了: {enhanced_results.shape}\")\n", "        \n", "        # 特徴量統計の表示\n", "        feature_summary = feature_manager.get_feature_summary()\n", "        if feature_summary:\n", "            print(\"特徴量カテゴリ別統計:\")\n", "            for category, count in feature_summary.items():\n", "                print(f\"  {category}: {count}個\")\n", "        \n", "        # 強化されたデータを保存\n", "        if config.get('save_final_results', True):\n", "            enhanced_output_file = processor.output_dir / 'enhanced_comprehensive_data_all_years.pkl'\n", "            enhanced_results.to_pickle(enhanced_output_file)\n", "            logger.info(f\"特徴量エンジニアリング済みデータを保存: {enhanced_output_file}\")\n", "            print(f\"特徴量エンジニアリング済みデータを保存: {enhanced_output_file}\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"特徴量エンジニアリングでエラーが発生: {e}\")\n", "        print(f\"特徴量エンジニアリングでエラーが発生: {e}\")\n", "        enhanced_results = results  # エラー時は元データを使用\n", "        \n", "else:\n", "    enhanced_results = results\n", "    print(\"特徴量エンジニアリングをスキップしました\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 学習前データ準備\n", "if config.get('prepare_training_data', False) and enhanced_results is not None and not enhanced_results.empty:\n", "    logger.info(\"学習前データ準備を開始...\")\n", "    \n", "    try:\n", "        # training_config.yamlの読み込み\n", "        training_config_path = config.get('training_config_path', 'training_config.yaml')\n", "        if Path(training_config_path).exists():\n", "            with open(training_config_path, 'r', encoding='utf-8') as f:\n", "                training_config = yaml.safe_load(f)\n", "        else:\n", "            logger.warning(f\"training_config.yamlが見つかりません: {training_config_path}\")\n", "            training_config = {}\n", "        \n", "        # データリーケージリスクのある列を除外\n", "        leakage_risk_columns = training_config.get('leakage_risk_columns', [])\n", "        logger.info(f\"データリーケージリスク列を除外: {len(leakage_risk_columns)}列\")\n", "        \n", "        # ターゲット変数の作成\n", "        target_col = config.get('target_column', 'rank')\n", "        threshold = config.get('target_threshold', 3)\n", "        \n", "        if target_col in enhanced_results.columns:\n", "            enhanced_results['target'] = (pd.to_numeric(enhanced_results[target_col], errors='coerce') <= threshold).astype(int)\n", "        elif '着順' in enhanced_results.columns:\n", "            enhanced_results['target'] = (pd.to_numeric(enhanced_results['着順'], errors='coerce') <= threshold).astype(int)\n", "        else:\n", "            raise ValueError(f\"ターゲット列が見つかりません: {target_col}\")\n", "        \n", "        # 特徴量の選択\n", "        feature_columns = []\n", "        \n", "        # 数値特徴量\n", "        numeric_cols = enhanced_results.select_dtypes(include=[np.number]).columns.tolist()\n", "        feature_columns.extend([col for col in numeric_cols \n", "                               if col not in leakage_risk_columns + ['target'] + [target_col]])\n", "        \n", "        # カテゴリカル特徴量のエンコーディング\n", "        safe_categorical_cols = training_config.get('safe_categorical_cols', ['venue', 'weather', 'track_condition'])\n", "        le_dict = {}\\n        \\n        for col in safe_categorical_cols:\\n            if col in enhanced_results.columns:\\n                le = LabelEncoder()\\n                enhanced_results[f'{col}_encoded'] = le.fit_transform(\\n                    enhanced_results[col].astype(str)\\n                )\\n                feature_columns.append(f'{col}_encoded')\\n                le_dict[col] = le\\n        \\n        # 欠損値処理\\n        for col in feature_columns:\\n            if col in enhanced_results.columns:\\n                enhanced_results[col] = enhanced_results[col].fillna(0)\\n        \\n        # 最終データの準備\\n        available_features = [col for col in feature_columns if col in enhanced_results.columns]\\n        X = enhanced_results[available_features]\\n        y = enhanced_results['target']\\n        \\n        # データの分割\\n        X_train, X_test, y_train, y_test = train_test_split(\\n            X, y, \\n            test_size=config.get('test_size', 0.2),\\n            random_state=config.get('random_state', 42),\\n            stratify=y\\n        )\\n        \\n        # 標準化\\n        scaler = StandardScaler()\\n        X_train_scaled = scaler.fit_transform(X_train)\\n        X_test_scaled = scaler.transform(X_test)\\n        \\n        # データ統計の表示\\n        print(f\\\"学習用データ準備完了:\\\")\\n        print(f\\\"  特徴量数: {len(available_features)}\\\")\\n        print(f\\\"  学習データ: {X_train.shape}\\\")\\n        print(f\\\"  テストデータ: {X_test.shape}\\\")\\n        print(f\\\"  正例率 (全体): {y.mean():.3f}\\\")\\n        print(f\\\"  正例率 (学習): {y_train.mean():.3f}\\\")\\n        print(f\\\"  正例率 (テスト): {y_test.mean():.3f}\\\")\\n        \\n        # 学習用データの保存\\n        if config.get('save_training_data', True):\\n            training_data = {\\n                'X_train': X_train,\\n                'X_test': X_test,\\n                'y_train': y_train,\\n                'y_test': y_test,\\n                'X_train_scaled': X_train_scaled,\\n                'X_test_scaled': X_test_scaled,\\n                'feature_names': available_features,\\n                'scaler': scaler,\\n                'encoders': le_dict,\\n                'full_data': enhanced_results,\\n                'config': config,\\n                'training_config': training_config\\n            }\\n            \\n            training_output_path = processor.output_dir / 'training_ready_data.pkl'\\n            with open(training_output_path, 'wb') as f:\\n                pickle.dump(training_data, f)\\n            \\n            logger.info(f\\\"学習用データを保存: {training_output_path}\\\")\\n            print(f\\\"学習用データを保存: {training_output_path}\\\")\\n            \\n            # メタデータの保存\\n            metadata = {\\n                'feature_count': len(available_features),\\n                'train_samples': len(X_train),\\n                'test_samples': len(X_test),\\n                'positive_rate': float(y.mean()),\\n                'target_threshold': threshold,\\n                'processing_date': datetime.now().isoformat(),\\n                'excluded_columns': leakage_risk_columns\\n            }\\n            \\n            metadata_path = processor.output_dir / 'training_metadata.json'\\n            with open(metadata_path, 'w', encoding='utf-8') as f:\\n                json.dump(metadata, f, ensure_ascii=False, indent=2)\\n            \\n            print(f\\\"メタデータを保存: {metadata_path}\\\")\\n        \\n    except Exception as e:\\n        logger.error(f\\\"学習前データ準備でエラーが発生: {e}\\\")\\n        print(f\\\"学習前データ準備でエラーが発生: {e}\\\")\\n        \\nelse:\\n    print(\\\"学習前データ準備をスキップしました\\\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 特徴量エンジニアリング済みデータの確認\n", "if 'enhanced_results' in locals() and enhanced_results is not None and not enhanced_results.empty:\n", "    print(\"=== 特徴量エンジニアリング済みデータ Sample ===\")\n", "    \n", "    # 重要な列を選択して表示\n", "    important_cols = ['race_id', 'horse_id', 'horse_name', 'rank', 'date', 'venue']\n", "    available_cols = [col for col in important_cols if col in enhanced_results.columns]\n", "    \n", "    if available_cols:\n", "        display(enhanced_results[available_cols].head())\n", "    else:\n", "        display(enhanced_results.head())\n", "    \n", "    print(f\"\\n特徴量エンジニアリング後の総列数: {len(enhanced_results.columns)}\")\n", "    \n", "    # 新しく追加された特徴量を確認\n", "    if 'results' in locals() and results is not None:\n", "        original_cols = set(results.columns)\n", "        new_cols = set(enhanced_results.columns) - original_cols\n", "        if new_cols:\n", "            print(f\"\\n追加された特徴量数: {len(new_cols)}\")\n", "            print(\"追加された特徴量の例:\")\n", "            for i, col in enumerate(sorted(new_cols), 1):\n", "                print(f\"  {i:3d}. {col}\")\n", "                if i >= 20:  # 最初の20個まで表示\n", "                    print(f\"  ... 他{len(new_cols)-20}個\")\n", "                    break\n", "    \n", "else:\n", "    print(\"特徴量エンジニアリング済みデータがありません\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 学習用データの詳細確認\n", "if config.get('prepare_training_data', False) and 'X_train' in locals():\n", "    print(\"=== 学習用データ詳細統計 ===\")\n", "    \n", "    # 基本統計\n", "    print(f\"学習データ形状: {X_train.shape}\")\n", "    print(f\"テストデータ形状: {X_test.shape}\")\n", "    print(f\"特徴量数: {len(available_features)}\")\n", "    \n", "    # ターゲット分布\n", "    print(f\"\\nターゲット分布:\")\n", "    print(f\"  全体 - 正例: {y.sum():,} ({y.mean():.1%}), 負例: {(~y.astype(bool)).sum():,}\")\n", "    print(f\"  学習 - 正例: {y_train.sum():,} ({y_train.mean():.1%}), 負例: {(~y_train.astype(bool)).sum():,}\")\n", "    print(f\"  テスト - 正例: {y_test.sum():,} ({y_test.mean():.1%}), 負例: {(~y_test.astype(bool)).sum():,}\")\n", "    \n", "    # 特徴量の統計\n", "    print(f\"\\n特徴量統計:\")\n", "    print(f\"  欠損値がある特徴量: {X_train.isnull().any().sum()}個\")\n", "    print(f\"  数値特徴量: {X_train.select_dtypes(include=[np.number]).shape[1]}個\")\n", "    \n", "    # 重要そうな特徴量の例を表示\n", "    print(f\"\\n特徴量の例（最初の20個）:\")\n", "    for i, feature in enumerate(available_features[:20], 1):\n", "        non_zero_rate = (X_train[feature] != 0).mean()\n", "        print(f\"  {i:2d}. {feature} (非ゼロ率: {non_zero_rate:.1%})\")\n", "    \n", "    if len(available_features) > 20:\n", "        print(f\"  ... 他{len(available_features)-20}個の特徴量\")\n", "    \n", "    # データ品質チェック\n", "    print(f\"\\nデータ品質チェック:\")\n", "    infinite_cols = [col for col in X_train.columns if np.isinf(X_train[col]).any()]\n", "    if infinite_cols:\n", "        print(f\"  無限値を含む列: {len(infinite_cols)}個\")\n", "    else:\n", "        print(f\"  無限値: なし\")\n", "    \n", "    print(f\"  学習データ準備完了時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "    \n", "else:\n", "    print(\"学習用データが準備されていません\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 処理完了サマリー\n", "print(\"=\" * 80)\n", "print(\"🎯 バッチ処理完了サマリー\")\n", "print(\"=\" * 80)\n", "\n", "# 基本処理の結果\n", "if 'results' in locals() and results is not None:\n", "    print(f\"✅ データ統合処理: 完了\")\n", "    print(f\"   - 処理対象年: {', '.join(years)}\")\n", "    print(f\"   - 統合データ: {results.shape}\")\n", "    \n", "    if 'enhanced_results' in locals() and enhanced_results is not None:\n", "        print(f\"✅ 特徴量エンジニアリング: 完了\")\n", "        print(f\"   - 強化後データ: {enhanced_results.shape}\")\n", "        original_cols = len(results.columns) if results is not None else 0\n", "        new_cols = len(enhanced_results.columns) - original_cols\n", "        print(f\"   - 追加特徴量: {new_cols}個\")\n", "    \n", "    if 'X_train' in locals():\n", "        print(f\"✅ 学習前データ準備: 完了\")\n", "        print(f\"   - 学習データ: {X_train.shape}\")\n", "        print(f\"   - テストデータ: {X_test.shape}\")\n", "        print(f\"   - 正例率: {y.mean():.1%}\")\n", "else:\n", "    print(\"❌ データ処理が完了していません\")\n", "\n", "# 保存されたファイル\n", "print(f\"\\n📁 保存されたファイル:\")\n", "saved_files = []\n", "\n", "if config.get('save_final_results', True):\n", "    comprehensive_file = processor.output_dir / 'comprehensive_data_all_years.pkl'\n", "    if comprehensive_file.exists():\n", "        saved_files.append(f\"   - 統合データ: {comprehensive_file}\")\n", "    \n", "    enhanced_file = processor.output_dir / 'enhanced_comprehensive_data_all_years.pkl'\n", "    if enhanced_file.exists():\n", "        saved_files.append(f\"   - 特徴量強化データ: {enhanced_file}\")\n", "\n", "if config.get('save_training_data', True):\n", "    training_file = processor.output_dir / 'training_ready_data.pkl'\n", "    if training_file.exists():\n", "        saved_files.append(f\"   - 学習用データ: {training_file}\")\n", "    \n", "    metadata_file = processor.output_dir / 'training_metadata.json'\n", "    if metadata_file.exists():\n", "        saved_files.append(f\"   - メタデータ: {metadata_file}\")\n", "\n", "if saved_files:\n", "    for file_info in saved_files:\n", "        print(file_info)\n", "else:\n", "    print(\"   - 保存されたファイルはありません\")\n", "\n", "# 処理時間\n", "if hasattr(processor, 'processing_stats') and processor.processing_stats.get('end_time'):\n", "    duration = processor.processing_stats['end_time'] - processor.processing_stats['start_time']\n", "    print(f\"\\n⏱️  総処理時間: {duration}\")\n", "\n", "print(\"\\n🎉 処理が正常に完了しました！\")\n", "\n", "# 次のステップの案内\n", "if 'X_train' in locals():\n", "    print(\"\\n📝 次のステップ:\")\n", "    print(\"   1. 学習用データ(training_ready_data.pkl)を使用してモデル訓練\")\n", "    print(\"   2. LightGBM、XGBoost、ニューラルネットワークなどで学習実行\")\n", "    print(\"   3. 予測精度の評価とハイパーパラメータ調整\")\n", "    print(\"\\n   学習スクリプト例:\")\n", "    print(\"   python enhanced_train.py --use-prepared-data output/training_ready_data.pkl\")\n", "\n", "print(\"=\" * 80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 保存されたデータの読み込み・確認機能"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["データ読み込み関数を定義しました。以下のように使用できます:\n", "\n", "# 利用可能なファイルの確認\n", "available_files = list_available_data('output')\n", "\n", "# 学習用データの読み込み\n", "training_data = load_training_data('output')\n", "X_train = training_data['X_train']\n", "y_train = training_data['y_train']\n", "\n", "# 特徴量強化データの読み込み\n", "enhanced_data = load_enhanced_data('output')\n", "\n", "# 統合データの読み込み\n", "comprehensive_data = load_comprehensive_data('output')\n", "\n", "# メタデータの読み込み\n", "metadata = load_metadata('output')\n"]}], "source": ["def load_training_data(output_dir: str = 'output') -> dict:\n", "    \"\"\"\n", "    保存された学習用データを読み込む\n", "    \n", "    Parameters\n", "    ----------\n", "    output_dir : str\n", "        出力ディレクトリのパス\n", "        \n", "    Returns\n", "    -------\n", "    dict\n", "        学習用データの辞書\n", "    \"\"\"\n", "    output_path = Path(output_dir)\n", "    training_file = output_path / 'training_ready_data.pkl'\n", "    \n", "    if not training_file.exists():\n", "        raise FileNotFoundError(f\"学習用データが見つかりません: {training_file}\")\n", "    \n", "    with open(training_file, 'rb') as f:\n", "        training_data = pickle.load(f)\n", "    \n", "    print(f\"学習用データを読み込みました: {training_file}\")\n", "    print(f\"学習データ形状: {training_data['X_train'].shape}\")\n", "    print(f\"テストデータ形状: {training_data['X_test'].shape}\")\n", "    print(f\"特徴量数: {len(training_data['feature_names'])}\")\n", "    \n", "    return training_data\n", "\n", "def load_enhanced_data(output_dir: str = 'output') -> pd.DataFrame:\n", "    \"\"\"\n", "    特徴量エンジニアリング済みデータを読み込む\n", "    \n", "    Parameters\n", "    ----------\n", "    output_dir : str\n", "        出力ディレクトリのパス\n", "        \n", "    Returns\n", "    -------\n", "    pd.DataFrame\n", "        特徴量エンジニアリング済みデータ\n", "    \"\"\"\n", "    output_path = Path(output_dir)\n", "    enhanced_file = output_path / 'enhanced_comprehensive_data_all_years.pkl'\n", "    \n", "    if not enhanced_file.exists():\n", "        raise FileNotFoundError(f\"特徴量強化データが見つかりません: {enhanced_file}\")\n", "    \n", "    enhanced_data = pd.read_pickle(enhanced_file)\n", "    \n", "    print(f\"特徴量強化データを読み込みました: {enhanced_file}\")\n", "    print(f\"データ形状: {enhanced_data.shape}\")\n", "    \n", "    return enhanced_data\n", "\n", "def load_comprehensive_data(output_dir: str = 'output') -> pd.DataFrame:\n", "    \"\"\"\n", "    統合データを読み込む\n", "    \n", "    Parameters\n", "    ----------\n", "    output_dir : str\n", "        出力ディレクトリのパス\n", "        \n", "    Returns\n", "    -------\n", "    pd.DataFrame\n", "        統合データ\n", "    \"\"\"\n", "    output_path = Path(output_dir)\n", "    comprehensive_file = output_path / 'comprehensive_data_all_years.pkl'\n", "    \n", "    if not comprehensive_file.exists():\n", "        raise FileNotFoundError(f\"統合データが見つかりません: {comprehensive_file}\")\n", "    \n", "    comprehensive_data = pd.read_pickle(comprehensive_file)\n", "    \n", "    print(f\"統合データを読み込みました: {comprehensive_file}\")\n", "    print(f\"データ形状: {comprehensive_data.shape}\")\n", "    \n", "    return comprehensive_data\n", "\n", "def load_metadata(output_dir: str = 'output') -> dict:\n", "    \"\"\"\n", "    メタデータを読み込む\n", "    \n", "    Parameters\n", "    ----------\n", "    output_dir : str\n", "        出力ディレクトリのパス\n", "        \n", "    Returns\n", "    -------\n", "    dict\n", "        メタデータ\n", "    \"\"\"\n", "    output_path = Path(output_dir)\n", "    metadata_file = output_path / 'training_metadata.json'\n", "    \n", "    if not metadata_file.exists():\n", "        raise FileNotFoundError(f\"メタデータが見つかりません: {metadata_file}\")\n", "    \n", "    with open(metadata_file, 'r', encoding='utf-8') as f:\n", "        metadata = json.load(f)\n", "    \n", "    print(f\"メタデータを読み込みました: {metadata_file}\")\n", "    \n", "    return metadata\n", "\n", "def list_available_data(output_dir: str = 'output') -> dict:\n", "    \"\"\"\n", "    利用可能なデータファイルをリストアップ\n", "    \n", "    Parameters\n", "    ----------\n", "    output_dir : str\n", "        出力ディレクトリのパス\n", "        \n", "    Returns\n", "    -------\n", "    dict\n", "        利用可能なファイルの情報\n", "    \"\"\"\n", "    output_path = Path(output_dir)\n", "    \n", "    files_info = {\n", "        'comprehensive_data': output_path / 'comprehensive_data_all_years.pkl',\n", "        'enhanced_data': output_path / 'enhanced_comprehensive_data_all_years.pkl',\n", "        'training_data': output_path / 'training_ready_data.pkl',\n", "        'metadata': output_path / 'training_metadata.json'\n", "    }\n", "    \n", "    available_files = {}\n", "    \n", "    print(\"=== 利用可能なデータファイル ===\")\n", "    \n", "    for data_type, file_path in files_info.items():\n", "        if file_path.exists():\n", "            file_size = file_path.stat().st_size / (1024 * 1024)  # MB\n", "            modified_time = datetime.fromtimestamp(file_path.stat().st_mtime)\n", "            \n", "            available_files[data_type] = {\n", "                'path': file_path,\n", "                'size_mb': file_size,\n", "                'modified': modified_time\n", "            }\n", "            \n", "            print(f\"✅ {data_type}: {file_path}\")\n", "            print(f\"   サイズ: {file_size:.1f} MB, 更新: {modified_time.strftime('%Y-%m-%d %H:%M:%S')}\")\n", "        else:\n", "            print(f\"❌ {data_type}: ファイルが見つかりません\")\n", "    \n", "    return available_files\n", "\n", "# 使用例の表示\n", "print(\"データ読み込み関数を定義しました。以下のように使用できます:\")\n", "print()\n", "print(\"# 利用可能なファイルの確認\")\n", "print(\"available_files = list_available_data('output')\")\n", "print()\n", "print(\"# 学習用データの読み込み\")\n", "print(\"training_data = load_training_data('output')\")\n", "print(\"X_train = training_data['X_train']\")\n", "print(\"y_train = training_data['y_train']\")\n", "print()\n", "print(\"# 特徴量強化データの読み込み\")\n", "print(\"enhanced_data = load_enhanced_data('output')\")\n", "print()\n", "print(\"# 統合データの読み込み\")\n", "print(\"comprehensive_data = load_comprehensive_data('output')\")\n", "print()\n", "print(\"# メタデータの読み込み\")\n", "print(\"metadata = load_metadata('output')\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 既存データファイルの確認...\n", "=== 利用可能なデータファイル ===\n", "❌ comprehensive_data: ファイルが見つかりません\n", "❌ enhanced_data: ファイルが見つかりません\n", "❌ training_data: ファイルが見つかりません\n", "❌ metadata: ファイルが見つかりません\n"]}], "source": ["# 既存データの確認・読み込みテスト\n", "print(\"🔍 既存データファイルの確認...\")\n", "available_files = list_available_data(config['output_dir'])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 データ読み込みのサンプル実行...\n", "読み込み可能なデータファイルがありません。\n", "先にデータ処理を実行してください。\n"]}], "source": ["# データ読み込みのサンプル実行\n", "print(\"\\n📊 データ読み込みのサンプル実行...\")\n", "\n", "# 学習用データが存在する場合の読み込みテスト\n", "if 'training_data' in available_files:\n", "    try:\n", "        print(\"\\n--- 学習用データの読み込みテスト ---\")\n", "        loaded_training_data = load_training_data(config['output_dir'])\n", "        \n", "        print(\"学習用データの内容:\")\n", "        for key in loaded_training_data.keys():\n", "            if hasattr(loaded_training_data[key], 'shape'):\n", "                print(f\"  {key}: {loaded_training_data[key].shape}\")\n", "            elif isinstance(loaded_training_data[key], list):\n", "                print(f\"  {key}: {len(loaded_training_data[key])}個のアイテム\")\n", "            else:\n", "                print(f\"  {key}: {type(loaded_training_data[key])}\")\n", "        \n", "        # 特徴量名の確認\n", "        if 'feature_names' in loaded_training_data:\n", "            feature_names = loaded_training_data['feature_names']\n", "            print(f\"\\n特徴量名（最初の10個）:\")\n", "            for i, name in enumerate(feature_names[:10], 1):\n", "                print(f\"  {i:2d}. {name}\")\n", "            if len(feature_names) > 10:\n", "                print(f\"  ... 他{len(feature_names)-10}個\")\n", "        \n", "    except Exception as e:\n", "        print(f\"学習用データの読み込みでエラー: {e}\")\n", "\n", "# 特徴量強化データが存在する場合の読み込みテスト\n", "if 'enhanced_data' in available_files:\n", "    try:\n", "        print(\"\\n--- 特徴量強化データの読み込みテスト ---\")\n", "        loaded_enhanced_data = load_enhanced_data(config['output_dir'])\n", "        \n", "        print(f\"列数: {len(loaded_enhanced_data.columns)}\")\n", "        print(f\"行数: {len(loaded_enhanced_data)}\")\n", "        \n", "        # 列名のサンプル表示\n", "        print(\"列名のサンプル（最初の10個）:\")\n", "        for i, col in enumerate(loaded_enhanced_data.columns[:10], 1):\n", "            print(f\"  {i:2d}. {col}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"特徴量強化データの読み込みでエラー: {e}\")\n", "\n", "# メタデータが存在する場合の読み込みテスト\n", "if 'metadata' in available_files:\n", "    try:\n", "        print(\"\\n--- メタデータの読み込みテスト ---\")\n", "        loaded_metadata = load_metadata(config['output_dir'])\n", "        \n", "        print(\"メタデータの内容:\")\n", "        for key, value in loaded_metadata.items():\n", "            print(f\"  {key}: {value}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"メタデータの読み込みでエラー: {e}\")\n", "\n", "if not available_files:\n", "    print(\"読み込み可能なデータファイルがありません。\")\n", "    print(\"先にデータ処理を実行してください。\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 高度なデータ分析・可視化機能\n", "def analyze_training_data(training_data: dict) -> None:\n", "    \"\"\"\n", "    学習用データの詳細分析\n", "    \n", "    Parameters\n", "    ----------\n", "    training_data : dict\n", "        学習用データ\n", "    \"\"\"\n", "    print(\"=== 学習用データ詳細分析 ===\")\n", "    \n", "    X_train = training_data['X_train']\n", "    y_train = training_data['y_train']\n", "    feature_names = training_data['feature_names']\n", "    \n", "    # 基本統計\n", "    print(f\"特徴量数: {len(feature_names)}\")\n", "    print(f\"学習サンプル数: {len(X_train)}\")\n", "    print(f\"正例率: {y_train.mean():.1%}\")\n", "    \n", "    # 特徴量の分布分析\n", "    print(f\"\\n【特徴量分布分析】\")\n", "    \n", "    # 欠損値がある特徴量\n", "    missing_features = X_train.columns[X_train.isnull().any()].tolist()\n", "    print(f\"欠損値がある特徴量: {len(missing_features)}個\")\n", "    \n", "    # ゼロが多い特徴量\n", "    zero_heavy_features = []\n", "    for col in X_train.columns:\n", "        zero_rate = (X_train[col] == 0).mean()\n", "        if zero_rate > 0.8:  # 80%以上がゼロ\n", "            zero_heavy_features.append((col, zero_rate))\n", "    \n", "    print(f\"ゼロが80%以上の特徴量: {len(zero_heavy_features)}個\")\n", "    if zero_heavy_features:\n", "        print(\"  例:\")\n", "        for col, rate in zero_heavy_features[:5]:\n", "            print(f\"    {col}: {rate:.1%}\")\n", "    \n", "    # 分散が小さい特徴量\n", "    low_var_features = []\n", "    for col in X_train.select_dtypes(include=[np.number]).columns:\n", "        var = X_train[col].var()\n", "        if var < 0.01:\n", "            low_var_features.append((col, var))\n", "    \n", "    print(f\"分散が小さい特徴量（<0.01）: {len(low_var_features)}個\")\n", "    \n", "    # カテゴリ別特徴量の確認\n", "    print(f\"\\n【特徴量カテゴリ分析】\")\n", "    categories = {\n", "        '過去成績': [col for col in feature_names if 'past_' in col or 'avg_' in col],\n", "        'レース条件': [col for col in feature_names if any(x in col for x in ['distance', 'venue', 'weather', 'track'])],\n", "        '馬情報': [col for col in feature_names if 'horse_' in col],\n", "        '騎手情報': [col for col in feature_names if 'jockey_' in col],\n", "        'コーナー': [col for col in feature_names if 'corner_' in col],\n", "        'その他': []\n", "    }\n", "    \n", "    # その他カテゴリの計算\n", "    categorized = set()\n", "    for cat_features in categories.values():\n", "        categorized.update(cat_features)\n", "    categories['その他'] = [col for col in feature_names if col not in categorized]\n", "    \n", "    for category, features in categories.items():\n", "        if features:\n", "            print(f\"  {category}: {len(features)}個\")\n", "\n", "def compare_data_versions(output_dir: str = 'output') -> None:\n", "    \"\"\"\n", "    異なるバージョンのデータを比較\n", "    \n", "    Parameters\n", "    ----------\n", "    output_dir : str\n", "        出力ディレクトリ\n", "    \"\"\"\n", "    print(\"=== データバージョン比較 ===\")\n", "    \n", "    try:\n", "        # 統合データ\n", "        comprehensive_data = load_comprehensive_data(output_dir)\n", "        print(f\"統合データ: {comprehensive_data.shape}\")\n", "        \n", "        # 特徴量強化データ\n", "        enhanced_data = load_enhanced_data(output_dir)\n", "        print(f\"特徴量強化データ: {enhanced_data.shape}\")\n", "        \n", "        # 追加された特徴量\n", "        original_cols = set(comprehensive_data.columns)\n", "        enhanced_cols = set(enhanced_data.columns)\n", "        added_features = enhanced_cols - original_cols\n", "        \n", "        print(f\"追加された特徴量: {len(added_features)}個\")\n", "        \n", "        if added_features:\n", "            print(\"追加特徴量の例:\")\n", "            for i, feature in enumerate(sorted(added_features)[:10], 1):\n", "                print(f\"  {i:2d}. {feature}\")\n", "            if len(added_features) > 10:\n", "                print(f\"  ... 他{len(added_features)-10}個\")\n", "        \n", "    except FileNotFoundError as e:\n", "        print(f\"データが見つかりません: {e}\")\n", "\n", "# 使用例\n", "print(\"高度なデータ分析機能を定義しました。\")\n", "print(\"\\n以下の関数が利用可能です:\")\n", "print(\"- analyze_training_data(training_data): 学習データの詳細分析\")\n", "print(\"- compare_data_versions('output'): データバージョンの比較\")\n", "print(\"\\n実行例:\")\n", "print(\"# 学習データが読み込まれている場合\")\n", "print(\"if 'loaded_training_data' in locals():\")\n", "print(\"    analyze_training_data(loaded_training_data)\")\n", "print()\n", "print(\"# データバージョン比較\")\n", "print(\"compare_data_versions(config['output_dir'])\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 実際のデータ分析実行\n", "if 'loaded_training_data' in locals() and loaded_training_data:\n", "    print(\"🔬 学習用データの詳細分析を実行...\")\n", "    analyze_training_data(loaded_training_data)\n", "\n", "print(\"\\n🔍 データバージョン比較を実行...\")\n", "compare_data_versions(config['output_dir'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 機械学習用のデータエクスポート機能"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_for_ml_framework(training_data: dict, output_dir: str = 'output', \n", "                            framework: str = 'lightgbm') -> str:\n", "    \"\"\"\n", "    機械学習フレームワーク用にデータをエクスポート\n", "    \n", "    Parameters\n", "    ----------\n", "    training_data : dict\n", "        学習用データ\n", "    output_dir : str\n", "        出力ディレクトリ\n", "    framework : str\n", "        対象フレームワーク ('lightgbm', 'xgboost', 'tensorflow', 'sklearn')\n", "        \n", "    Returns\n", "    -------\n", "    str\n", "        エクスポートされたファイルのパス\n", "    \"\"\"\n", "    output_path = Path(output_dir)\n", "    output_path.mkdir(exist_ok=True)\n", "    \n", "    X_train = training_data['X_train']\n", "    X_test = training_data['X_test']\n", "    y_train = training_data['y_train'] \n", "    y_test = training_data['y_test']\n", "    \n", "    if framework.lower() == 'lightgbm':\n", "        import lightgbm as lgb\n", "        \n", "        # LightGBM Dataset形式で保存\n", "        train_data = lgb.Dataset(X_train, label=y_train)\n", "        test_data = lgb.Dataset(X_test, label=y_test, reference=train_data)\n", "        \n", "        train_file = output_path / 'lgb_train.bin'\n", "        test_file = output_path / 'lgb_test.bin'\n", "        \n", "        train_data.save_binary(str(train_file))\n", "        test_data.save_binary(str(test_file))\n", "        \n", "        print(f\"LightGBM形式で保存:\")\n", "        print(f\"  学習データ: {train_file}\")\n", "        print(f\"  テストデータ: {test_file}\")\n", "        \n", "        return str(train_file)\n", "        \n", "    elif framework.lower() == 'tensorflow':\n", "        import tensorflow as tf\n", "        \n", "        # TFRecord形式で保存\n", "        train_file = output_path / 'tf_train.tfrecord'\n", "        test_file = output_path / 'tf_test.tfrecord'\n", "        \n", "        def _create_tfrecord(X, y, filename):\n", "            with tf.io.TFRecordWriter(str(filename)) as writer:\n", "                for i in range(len(X)):\n", "                    features = {\n", "                        'features': tf.train.Feature(\n", "                            float_list=tf.train.FloatList(value=X.iloc[i].values)\n", "                        ),\n", "                        'label': tf.train.Feature(\n", "                            int64_list=tf.train.Int64List(value=[int(y.iloc[i])])\n", "                        )\n", "                    }\\n                    example = tf.train.Example(\\n                        features=tf.train.Features(feature=features)\\n                    )\\n                    writer.write(example.SerializeToString())\\n        \\n        _create_tfrecord(X_train, y_train, train_file)\\n        _create_tfrecord(X_test, y_test, test_file)\\n        \\n        print(f\\\"TensorFlow TFRecord形式で保存:\\\")\\n        print(f\\\"  学習データ: {train_file}\\\")\\n        print(f\\\"  テストデータ: {test_file}\\\")\\n        \\n        return str(train_file)\\n        \\n    elif framework.lower() == 'csv':\\n        # CSV形式で保存（汎用的）\\n        train_file = output_path / 'train_data.csv'\\n        test_file = output_path / 'test_data.csv'\\n        \\n        # 学習データ\\n        train_df = X_train.copy()\\n        train_df['target'] = y_train\\n        train_df.to_csv(train_file, index=False)\\n        \\n        # テストデータ\\n        test_df = X_test.copy()\\n        test_df['target'] = y_test\\n        test_df.to_csv(test_file, index=False)\\n        \\n        print(f\\\"CSV形式で保存:\\\")\\n        print(f\\\"  学習データ: {train_file}\\\")\\n        print(f\\\"  テストデータ: {test_file}\\\")\\n        \\n        return str(train_file)\\n    \\n    else:\\n        raise ValueError(f\\\"サポートされていないフレームワーク: {framework}\\\")\\n\\ndef create_feature_info_file(training_data: dict, output_dir: str = 'output') -> str:\\n    \\\"\\\"\\\"\\n    特徴量情報ファイルを作成\\n    \\n    Parameters\\n    ----------\\n    training_data : dict\\n        学習用データ\\n    output_dir : str\\n        出力ディレクトリ\\n        \\n    Returns\\n    -------\\n    str\\n        特徴量情報ファイルのパス\\n    \\\"\\\"\\\"\\n    output_path = Path(output_dir)\\n    \\n    X_train = training_data['X_train']\\n    feature_names = training_data['feature_names']\\n    \\n    # 特徴量の詳細情報を収集\\n    feature_info = []\\n    \\n    for i, feature_name in enumerate(feature_names):\\n        if feature_name in X_train.columns:\\n            col_data = X_train[feature_name]\\n            \\n            info = {\\n                'index': i,\\n                'name': feature_name,\\n                'dtype': str(col_data.dtype),\\n                'non_null_count': int(col_data.count()),\\n                'null_count': int(col_data.isnull().sum()),\\n                'mean': float(col_data.mean()) if pd.api.types.is_numeric_dtype(col_data) else None,\\n                'std': float(col_data.std()) if pd.api.types.is_numeric_dtype(col_data) else None,\\n                'min': float(col_data.min()) if pd.api.types.is_numeric_dtype(col_data) else None,\\n                'max': float(col_data.max()) if pd.api.types.is_numeric_dtype(col_data) else None,\\n                'zero_rate': float((col_data == 0).mean()),\\n                'unique_count': int(col_data.nunique())\\n            }\\n            \\n            feature_info.append(info)\\n    \\n    # JSONファイルとして保存\\n    feature_info_file = output_path / 'feature_info.json'\\n    with open(feature_info_file, 'w', encoding='utf-8') as f:\\n        json.dump(feature_info, f, ensure_ascii=False, indent=2)\\n    \\n    # CSVファイルとしても保存\\n    feature_info_csv = output_path / 'feature_info.csv'\\n    pd.DataFrame(feature_info).to_csv(feature_info_csv, index=False)\\n    \\n    print(f\\\"特徴量情報を保存:\\\")\\n    print(f\\\"  JSON: {feature_info_file}\\\")\\n    print(f\\\"  CSV: {feature_info_csv}\\\")\\n    \\n    return str(feature_info_file)\\n\\ndef create_model_config_template(training_data: dict, output_dir: str = 'output') -> str:\\n    \\\"\\\"\\\"\\n    モデル設定テンプレートを作成\\n    \\n    Parameters\\n    ----------\\n    training_data : dict\\n        学習用データ\\n    output_dir : str\\n        出力ディレクトリ\\n        \\n    Returns\\n    -------\\n    str\\n        設定ファイルのパス\\n    \\\"\\\"\\\"\\n    output_path = Path(output_dir)\\n    \\n    feature_count = len(training_data['feature_names'])\\n    sample_count = len(training_data['X_train'])\\n    positive_rate = float(training_data['y_train'].mean())\\n    \\n    # LightGBM設定テンプレート\\n    lgb_config = {\\n        'objective': 'binary',\\n        'metric': 'binary_logloss',\\n        'boosting_type': 'gbdt',\\n        'num_leaves': min(31, max(10, feature_count // 10)),\\n        'learning_rate': 0.1,\\n        'feature_fraction': 0.8,\\n        'bagging_fraction': 0.8,\\n        'bagging_freq': 5,\\n        'verbose': 0,\\n        'num_boost_round': 1000,\\n        'early_stopping_rounds': 100,\\n        'scale_pos_weight': (1 - positive_rate) / positive_rate if positive_rate > 0 else 1\\n    }\\n    \\n    # TensorFlow設定テンプレート\\n    tf_config = {\\n        'model_type': 'dense_neural_network',\\n        'layers': [\\n            {'units': max(64, feature_count // 2), 'activation': 'relu', 'dropout': 0.3},\\n            {'units': max(32, feature_count // 4), 'activation': 'relu', 'dropout': 0.3},\\n            {'units': 1, 'activation': 'sigmoid'}\\n        ],\\n        'optimizer': 'adam',\\n        'learning_rate': 0.001,\\n        'batch_size': min(512, sample_count // 100),\\n        'epochs': 100,\\n        'validation_split': 0.2,\\n        'early_stopping_patience': 10\\n    }\\n    \\n    # 設定ファイルを保存\\n    lgb_config_file = output_path / 'lightgbm_config.json'\\n    tf_config_file = output_path / 'tensorflow_config.json'\\n    \\n    with open(lgb_config_file, 'w', encoding='utf-8') as f:\\n        json.dump(lgb_config, f, ensure_ascii=False, indent=2)\\n    \\n    with open(tf_config_file, 'w', encoding='utf-8') as f:\\n        json.dump(tf_config, f, ensure_ascii=False, indent=2)\\n    \\n    print(f\\\"モデル設定テンプレートを作成:\\\")\\n    print(f\\\"  LightGBM: {lgb_config_file}\\\")\\n    print(f\\\"  TensorFlow: {tf_config_file}\\\")\\n    \\n    return str(lgb_config_file)\\n\\nprint(\\\"機械学習フレームワーク用エクスポート機能を定義しました。\\\")\\nprint(\\\"\\\\n利用可能な関数:\\\")\\nprint(\\\"- export_for_ml_framework(): LightGBM、TensorFlow、CSV形式でエクスポート\\\")\\nprint(\\\"- create_feature_info_file(): 特徴量の詳細情報ファイル作成\\\")\\nprint(\\\"- create_model_config_template(): モデル設定テンプレート作成\\\")\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# エクスポート機能の実行（学習データが存在する場合）\n", "if 'loaded_training_data' in locals() and loaded_training_data:\n", "    print(\"📤 機械学習フレームワーク用データエクスポートを実行...\")\n", "    \n", "    try:\\n        # CSV形式でエクスポート（汎用的）\\n        print(\\\"\\\\n--- CSV形式エクスポート ---\\\")\\n        csv_file = export_for_ml_framework(loaded_training_data, config['output_dir'], 'csv')\\n        \\n        # 特徴量情報ファイル作成\\n        print(\\\"\\\\n--- 特徴量情報ファイル作成 ---\\\")\\n        feature_info_file = create_feature_info_file(loaded_training_data, config['output_dir'])\\n        \\n        # モデル設定テンプレート作成\\n        print(\\\"\\\\n--- モデル設定テンプレート作成 ---\\\")\\n        model_config_file = create_model_config_template(loaded_training_data, config['output_dir'])\\n        \\n        # LightGBM形式エクスポート（LightGBMが利用可能な場合）\\n        try:\\n            print(\\\"\\\\n--- LightGBM形式エクスポート ---\\\")\\n            lgb_file = export_for_ml_framework(loaded_training_data, config['output_dir'], 'lightgbm')\\n        except ImportError:\\n            print(\\\"LightGBMがインストールされていません。CSV形式を使用してください。\\\")\\n            \\n    except Exception as e:\\n        print(f\\\"エクスポート処理でエラーが発生: {e}\\\")\\n        \\nelse:\\n    print(\\\"学習用データが読み込まれていません。先にデータ処理を実行してください。\\\")\""]}, {"cell_type": "markdown", "source": "## 10. 特徴量の詳細検証・確認機能", "metadata": {}}, {"cell_type": "code", "source": "def verify_feature_categories(data: pd.DataFrame, detailed: bool = True) -> dict:\n    \"\"\"\n    特徴量カテゴリの詳細検証\n    \n    Parameters\n    ----------\n    data : pd.DataFrame\n        検証対象のデータフレーム\n    detailed : bool\n        詳細分析を実行するかどうか\n        \n    Returns\n    -------\n    dict\n        カテゴリ別特徴量の詳細情報\n    \"\"\"\n    print(\"🔍 特徴量カテゴリ詳細検証\")\n    print(\"=\" * 60)\n    \n    columns = data.columns.tolist()\n    \n    # カテゴリ別特徴量の定義\n    categories = {\n        'レース基本情報': {\n            'patterns': ['race_', 'date', 'venue', 'distance', 'weather', 'track_condition', 'grade', 'surface'],\n            'features': []\n        },\n        '馬基本情報': {\n            'patterns': ['horse_name', 'horse_id', 'age', 'sex', 'weight', 'trainer', 'owner', 'breeder'],\n            'features': []\n        },\n        '馬過去戦績': {\n            'patterns': ['past_', 'avg_', 'win_rate', 'place_rate', 'show_rate', 'recent_', 'career_'],\n            'features': []\n        },\n        '血統情報': {\n            'patterns': ['father', 'mother', 'pedigree', 'lineage'],\n            'features': []\n        },\n        '騎手情報': {\n            'patterns': ['jockey_', 'rider_'],\n            'features': []\n        },\n        'コーナー特徴量': {\n            'patterns': ['corner_', 'position_'],\n            'features': []\n        },\n        'レース条件': {\n            'patterns': ['class_', 'condition_', 'handicap_', 'allowance_'],\n            'features': []\n        },\n        'その他特徴量': {\n            'patterns': [],\n            'features': []\n        }\n    }\n    \n    # 特徴量をカテゴリに分類\n    categorized_features = set()\n    \n    for category, info in categories.items():\n        if category == 'その他特徴量':\n            continue\n            \n        for col in columns:\n            if any(pattern in col.lower() for pattern in info['patterns']):\n                info['features'].append(col)\n                categorized_features.add(col)\n    \n    # その他カテゴリに未分類の特徴量を追加\n    categories['その他特徴量']['features'] = [\n        col for col in columns if col not in categorized_features\n    ]\n    \n    # 結果の表示\n    total_features = 0\n    verification_results = {}\n    \n    for category, info in categories.items():\n        features = info['features']\n        count = len(features)\n        total_features += count\n        \n        verification_results[category] = {\n            'count': count,\n            'features': features,\n            'coverage': count / len(columns) * 100 if columns else 0\n        }\n        \n        print(f\\\"\\\\n【{category}】: {count}個 ({count/len(columns)*100:.1f}%)\\\")\\n        \\n        if detailed and features:\\n            # サンプル特徴量の表示\\n            sample_features = features[:10]\\n            for i, feature in enumerate(sample_features, 1):\\n                # データの基本統計\\n                if feature in data.columns:\\n                    col_data = data[feature]\\n                    non_null_count = col_data.count()\\n                    null_rate = col_data.isnull().mean()\\n                    \\n                    if pd.api.types.is_numeric_dtype(col_data):\\n                        stats = f\\\"平均: {col_data.mean():.3f}, 非ゼロ率: {(col_data != 0).mean():.1%}\\\"\\n                    else:\\n                        unique_count = col_data.nunique()\\n                        stats = f\\\"ユニーク値: {unique_count}個\\\"\\n                    \\n                    print(f\\\"  {i:2d}. {feature}\\\")\\n                    print(f\\\"      欠損率: {null_rate:.1%}, {stats}\\\")\\n                else:\\n                    print(f\\\"  {i:2d}. {feature} (データなし)\\\")\\n            \\n            if len(features) > 10:\\n                print(f\\\"  ... 他{len(features)-10}個\\\")\\n    \\n    print(f\\\"\\\\n📊 総特徴量数: {total_features}個\\\")\\n    print(f\\\"📊 分類カバー率: {len(categorized_features)/len(columns)*100:.1f}%\\\")\\n    \\n    return verification_results\\n\\ndef check_core_features_presence(data: pd.DataFrame) -> dict:\\n    \\\"\\\"\\\"\\n    重要な基本特徴量の存在確認\\n    \\n    Parameters\\n    ----------\\n    data : pd.DataFrame\\n        検証対象のデータフレーム\\n        \\n    Returns\\n    -------\\n    dict\\n        重要特徴量の存在確認結果\\n    \\\"\\\"\\\"\\n    print(\\\"\\\\n🎯 重要特徴量の存在確認\\\")\\n    print(\\\"=\\\" * 40)\\n    \\n    # 重要特徴量のチェックリスト\\n    core_features = {\\n        'レース基本': {\\n            'race_id': ['race_id'],\\n            'レース日付': ['date', 'race_date'],\\n            '競馬場': ['venue', 'course', 'track'],\\n            '距離': ['distance'],\\n            '天気': ['weather'],\\n            '馬場状態': ['track_condition', 'condition'],\\n            'グレード': ['grade', 'class']\\n        },\\n        '馬基本': {\\n            '馬ID': ['horse_id'],\\n            '馬名': ['horse_name', 'name'],\\n            '年齢': ['age'],\\n            '性別': ['sex', 'gender'],\\n            '斤量': ['weight', 'impost'],\\n            '調教師': ['trainer'],\\n            '馬主': ['owner']\\n        },\\n        '馬過去戦績': {\\n            '過去平均着順': ['past_avg_rank', 'avg_rank'],\\n            '勝率': ['win_rate', 'past_win_rate'],\\n            '連対率': ['place_rate', 'past_place_rate'],\\n            '複勝率': ['show_rate', 'past_show_rate'],\\n            '過去レース数': ['past_races', 'race_count'],\\n            '最近の成績': ['recent_avg', 'recent_performance']\\n        },\\n        '血統': {\\n            '父馬': ['father', 'sire'],\\n            '母馬': ['mother', 'dam'],\\n            '母父': ['mother_father', 'broodmare_sire']\\n        }\\n    }\\n    \\n    results = {}\\n    \\n    for category, feature_groups in core_features.items():\\n        print(f\\\"\\\\n【{category}】\\\")\\n        category_results = {}\\n        \\n        for feature_name, possible_columns in feature_groups.items():\\n            found_columns = []\\n            for possible_col in possible_columns:\\n                # 完全一致\\n                exact_matches = [col for col in data.columns if col == possible_col]\\n                # 部分一致\\n                partial_matches = [col for col in data.columns if possible_col in col.lower()]\\n                \\n                found_columns.extend(exact_matches)\\n                found_columns.extend(partial_matches)\\n            \\n            # 重複を除去\\n            found_columns = list(set(found_columns))\\n            \\n            category_results[feature_name] = {\\n                'found': len(found_columns) > 0,\\n                'columns': found_columns\\n            }\\n            \\n            if found_columns:\\n                print(f\\\"  ✅ {feature_name}: {', '.join(found_columns[:3])}\\\")\\n                if len(found_columns) > 3:\\n                    print(f\\\"      ... 他{len(found_columns)-3}個\\\")\\n            else:\\n                print(f\\\"  ❌ {feature_name}: 見つかりません\\\")\\n        \\n        results[category] = category_results\\n    \\n    return results\\n\\ndef analyze_feature_completeness(data: pd.DataFrame) -> dict:\\n    \\\"\\\"\\\"\\n    特徴量の完全性分析\\n    \\n    Parameters\\n    ----------\\n    data : pd.DataFrame\\n        分析対象のデータフレーム\\n        \\n    Returns\\n    -------\\n    dict\\n        完全性分析結果\\n    \\\"\\\"\\\"\\n    print(\\\"\\\\n📈 特徴量完全性分析\\\")\\n    print(\\\"=\\\" * 30)\\n    \\n    total_columns = len(data.columns)\\n    total_rows = len(data)\\n    \\n    completeness_stats = {\\n        'total_features': total_columns,\\n        'total_samples': total_rows,\\n        'missing_analysis': {},\\n        'data_quality': {}\\n    }\\n    \\n    # 欠損値分析\\n    print(f\\\"\\\\n【欠損値分析】\\\")\\n    missing_counts = data.isnull().sum()\\n    missing_rates = data.isnull().mean()\\n    \\n    # 欠損値が多い特徴量\\n    high_missing = missing_rates[missing_rates > 0.5]\\n    medium_missing = missing_rates[(missing_rates > 0.1) & (missing_rates <= 0.5)]\\n    low_missing = missing_rates[(missing_rates > 0) & (missing_rates <= 0.1)]\\n    no_missing = missing_rates[missing_rates == 0]\\n    \\n    print(f\\\"  欠損値なし: {len(no_missing)}個 ({len(no_missing)/total_columns*100:.1f}%)\\\")\\n    print(f\\\"  低欠損(~10%): {len(low_missing)}個 ({len(low_missing)/total_columns*100:.1f}%)\\\")\\n    print(f\\\"  中欠損(10~50%): {len(medium_missing)}個 ({len(medium_missing)/total_columns*100:.1f}%)\\\")\\n    print(f\\\"  高欠損(50%~): {len(high_missing)}個 ({len(high_missing)/total_columns*100:.1f}%)\\\")\\n    \\n    if len(high_missing) > 0:\\n        print(f\\\"\\\\n  高欠損特徴量の例:\\\")\\n        for i, (col, rate) in enumerate(high_missing.head().items(), 1):\\n            print(f\\\"    {i}. {col}: {rate:.1%}\\\")\\n    \\n    completeness_stats['missing_analysis'] = {\\n        'no_missing': len(no_missing),\\n        'low_missing': len(low_missing),\\n        'medium_missing': len(medium_missing),\\n        'high_missing': len(high_missing)\\n    }\\n    \\n    # データ型分析\\n    print(f\\\"\\\\n【データ型分析】\\\")\\n    numeric_cols = data.select_dtypes(include=[np.number]).columns\\n    object_cols = data.select_dtypes(include=['object']).columns\\n    datetime_cols = data.select_dtypes(include=['datetime']).columns\\n    \\n    print(f\\\"  数値型: {len(numeric_cols)}個 ({len(numeric_cols)/total_columns*100:.1f}%)\\\")\\n    print(f\\\"  文字列型: {len(object_cols)}個 ({len(object_cols)/total_columns*100:.1f}%)\\\")\\n    print(f\\\"  日時型: {len(datetime_cols)}個 ({len(datetime_cols)/total_columns*100:.1f}%)\\\")\\n    \\n    completeness_stats['data_quality'] = {\\n        'numeric_features': len(numeric_cols),\\n        'object_features': len(object_cols),\\n        'datetime_features': len(datetime_cols)\\n    }\\n    \\n    return completeness_stats\\n\\nprint(\\\"特徴量詳細検証機能を定義しました。\\\")\\nprint(\\\"\\\\n利用可能な関数:\\\")\\nprint(\\\"- verify_feature_categories(): カテゴリ別特徴量詳細検証\\\")\\nprint(\\\"- check_core_features_presence(): 重要特徴量存在確認\\\")\\nprint(\\\"- analyze_feature_completeness(): 特徴量完全性分析\\\")", "metadata": {}, "outputs": []}, {"cell_type": "code", "source": "# 包括的特徴量検証の実行\nprint(\"🎯 包括的特徴量検証を実行します...\\\")\\nprint(\\\"=\\\" * 80)\\n\\n# 使用するデータの決定\\ndata_to_verify = None\\ndata_source = \\\"\\\"\\n\\nif 'enhanced_results' in locals() and enhanced_results is not None and not enhanced_results.empty:\\n    data_to_verify = enhanced_results\\n    data_source = \\\"特徴量エンジニアリング済みデータ\\\"\\nelif 'results' in locals() and results is not None and not results.empty:\\n    data_to_verify = results\\n    data_source = \\\"統合データ\\\"\\nelif 'loaded_enhanced_data' in locals():\\n    data_to_verify = loaded_enhanced_data\\n    data_source = \\\"読み込み済み特徴量強化データ\\\"\\nelse:\\n    # outputディレクトリから読み込みを試行\\n    try:\\n        data_to_verify = load_enhanced_data(config['output_dir'])\\n        data_source = \\\"outputディレクトリの特徴量強化データ\\\"\\n    except:\\n        try:\\n            data_to_verify = load_comprehensive_data(config['output_dir'])\\n            data_source = \\\"outputディレクトリの統合データ\\\"\\n        except:\\n            pass\\n\\nif data_to_verify is not None:\\n    print(f\\\"📊 検証対象: {data_source}\\\")\\n    print(f\\\"📊 データ形状: {data_to_verify.shape}\\\")\\n    print(f\\\"📊 対象期間: {data_to_verify['date'].min() if 'date' in data_to_verify.columns else '不明'} ~ {data_to_verify['date'].max() if 'date' in data_to_verify.columns else '不明'}\\\")\\n    \\n    # 1. カテゴリ別特徴量詳細検証\\n    print(\\\"\\\\n\\\" + \\\"=\\\"*80)\\n    verification_results = verify_feature_categories(data_to_verify, detailed=True)\\n    \\n    # 2. 重要特徴量存在確認\\n    print(\\\"\\\\n\\\" + \\\"=\\\"*80)\\n    core_features_results = check_core_features_presence(data_to_verify)\\n    \\n    # 3. 特徴量完全性分析\\n    print(\\\"\\\\n\\\" + \\\"=\\\"*80)\\n    completeness_results = analyze_feature_completeness(data_to_verify)\\n    \\nelse:\\n    print(\\\"❌ 検証対象のデータが見つかりません。\\\")\\n    print(\\\"先にデータ処理を実行するか、outputディレクトリにデータが存在することを確認してください。\\\")\"", "metadata": {}, "outputs": []}, {"cell_type": "code", "source": "def generate_feature_validation_report(data: pd.DataFrame, output_dir: str = 'output') -> str:\\n    \\\"\\\"\\\"\\n    特徴量検証レポートを生成\\n    \\n    Parameters\\n    ----------\\n    data : pd.DataFrame\\n        検証対象データ\\n    output_dir : str\\n        出力ディレクトリ\\n        \\n    Returns\\n    -------\\n    str\\n        レポートファイルのパス\\n    \\\"\\\"\\\"\\n    output_path = Path(output_dir)\\n    report_file = output_path / f'feature_validation_report_{datetime.now().strftime(\\\"%Y%m%d_%H%M%S\\\")}.txt'\\n    \\n    with open(report_file, 'w', encoding='utf-8') as f:\\n        f.write(\\\"=\\\"*80 + \\\"\\\\n\\\")\\n        f.write(\\\"特徴量検証レポート\\\\n\\\")\\n        f.write(\\\"=\\\"*80 + \\\"\\\\n\\\")\\n        f.write(f\\\"生成日時: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\\\n\\\")\\n        f.write(f\\\"データ形状: {data.shape}\\\\n\\\")\\n        f.write(f\\\"対象期間: {data['date'].min() if 'date' in data.columns else '不明'} ~ {data['date'].max() if 'date' in data.columns else '不明'}\\\\n\\\")\\n        f.write(\\\"\\\\n\\\")\\n        \\n        # カテゴリ別特徴量分析\\n        f.write(\\\"【カテゴリ別特徴量分析】\\\\n\\\")\\n        f.write(\\\"-\\\"*40 + \\\"\\\\n\\\")\\n        \\n        categories = {\\n            'レース基本情報': ['race_', 'date', 'venue', 'distance', 'weather', 'track_condition', 'grade'],\\n            '馬基本情報': ['horse_name', 'horse_id', 'age', 'sex', 'weight', 'trainer', 'owner'],\\n            '馬過去戦績': ['past_', 'avg_', 'win_rate', 'place_rate', 'show_rate', 'recent_'],\\n            '血統情報': ['father', 'mother', 'pedigree'],\\n            '騎手情報': ['jockey_', 'rider_'],\\n            'コーナー特徴量': ['corner_', 'position_']\\n        }\\n        \\n        total_categorized = 0\\n        for category, patterns in categories.items():\\n            matching_cols = []\\n            for col in data.columns:\\n                if any(pattern in col.lower() for pattern in patterns):\\n                    matching_cols.append(col)\\n            \\n            total_categorized += len(matching_cols)\\n            f.write(f\\\"{category}: {len(matching_cols)}個\\\\n\\\")\\n            \\n            # サンプル特徴量\\n            if matching_cols:\\n                for i, col in enumerate(matching_cols[:5], 1):\\n                    null_rate = data[col].isnull().mean()\\n                    f.write(f\\\"  {i}. {col} (欠損率: {null_rate:.1%})\\\\n\\\")\\n                if len(matching_cols) > 5:\\n                    f.write(f\\\"  ... 他{len(matching_cols)-5}個\\\\n\\\")\\n            f.write(\\\"\\\\n\\\")\\n        \\n        uncategorized = len(data.columns) - total_categorized\\n        f.write(f\\\"未分類特徴量: {uncategorized}個\\\\n\\\")\\n        f.write(f\\\"分類率: {total_categorized/len(data.columns)*100:.1f}%\\\\n\\\")\\n        f.write(\\\"\\\\n\\\")\\n        \\n        # 重要特徴量チェック\\n        f.write(\\\"【重要特徴量チェック】\\\\n\\\")\\n        f.write(\\\"-\\\"*40 + \\\"\\\\n\\\")\\n        \\n        required_features = {\\n            'race_id': 'レースID',\\n            'horse_id': '馬ID', \\n            'date': 'レース日付',\\n            'venue': '競馬場',\\n            'distance': '距離',\\n            'rank': '着順'\\n        }\\n        \\n        for feature, description in required_features.items():\\n            found_cols = [col for col in data.columns if feature in col.lower()]\\n            if found_cols:\\n                f.write(f\\\"✅ {description}: {', '.join(found_cols[:3])}\\\\n\\\")\\n            else:\\n                f.write(f\\\"❌ {description}: 見つかりません\\\\n\\\")\\n        \\n        f.write(\\\"\\\\n\\\")\\n        \\n        # データ品質分析\\n        f.write(\\\"【データ品質分析】\\\\n\\\")\\n        f.write(\\\"-\\\"*40 + \\\"\\\\n\\\")\\n        \\n        # 欠損値統計\\n        missing_rates = data.isnull().mean()\\n        f.write(f\\\"欠損値なし: {(missing_rates == 0).sum()}個\\\\n\\\")\\n        f.write(f\\\"低欠損(~10%): {((missing_rates > 0) & (missing_rates <= 0.1)).sum()}個\\\\n\\\")\\n        f.write(f\\\"中欠損(10~50%): {((missing_rates > 0.1) & (missing_rates <= 0.5)).sum()}個\\\\n\\\")\\n        f.write(f\\\"高欠損(50%~): {(missing_rates > 0.5).sum()}個\\\\n\\\")\\n        f.write(\\\"\\\\n\\\")\\n        \\n        # データ型統計\\n        numeric_cols = data.select_dtypes(include=[np.number]).columns\\n        object_cols = data.select_dtypes(include=['object']).columns\\n        \\n        f.write(f\\\"数値型特徴量: {len(numeric_cols)}個 ({len(numeric_cols)/len(data.columns)*100:.1f}%)\\\\n\\\")\\n        f.write(f\\\"文字列型特徴量: {len(object_cols)}個 ({len(object_cols)/len(data.columns)*100:.1f}%)\\\\n\\\")\\n        f.write(\\\"\\\\n\\\")\\n        \\n        # 推奨事項\\n        f.write(\\\"【推奨事項】\\\\n\\\")\\n        f.write(\\\"-\\\"*40 + \\\"\\\\n\\\")\\n        \\n        high_missing = missing_rates[missing_rates > 0.5]\\n        if len(high_missing) > 0:\\n            f.write(f\\\"1. 高欠損特徴量({len(high_missing)}個)の除去を検討\\\\n\\\")\\n        \\n        if uncategorized > len(data.columns) * 0.1:\\n            f.write(f\\\"2. 未分類特徴量({uncategorized}個)の内容確認\\\\n\\\")\\n        \\n        if len(object_cols) > 0:\\n            f.write(f\\\"3. 文字列型特徴量({len(object_cols)}個)のエンコーディング確認\\\\n\\\")\\n        \\n        f.write(f\\\"4. 特徴量選択による次元削減の検討\\\\n\\\")\\n        \\n    print(f\\\"特徴量検証レポートを保存: {report_file}\\\")\\n    return str(report_file)\\n\\n# 検証対象データが存在する場合のレポート生成\\nif data_to_verify is not None:\\n    print(\\\"\\\\n\\\" + \\\"=\\\"*80)\\n    print(\\\"📝 特徴量検証レポート生成\\\")\\n    report_path = generate_feature_validation_report(data_to_verify, config['output_dir'])\\n    \\n    # 検証結果のサマリー\\n    print(\\\"\\\\n\\\" + \\\"=\\\"*80)\\n    print(\\\"🎯 特徴量検証結果サマリー\\\")\\n    print(\\\"=\\\"*80)\\n    \\n    if 'verification_results' in locals():\\n        print(\\\"\\\\n【カテゴリ別特徴量数】\\\")\\n        for category, results in verification_results.items():\\n            if results['count'] > 0:\\n                print(f\\\"  {category}: {results['count']}個 ({results['coverage']:.1f}%)\\\")\\n    \\n    if 'core_features_results' in locals():\\n        print(\\\"\\\\n【重要特徴量の充足度】\\\")\\n        for category, features in core_features_results.items():\\n            found_count = sum(1 for feature_info in features.values() if feature_info['found'])\\n            total_count = len(features)\\n            print(f\\\"  {category}: {found_count}/{total_count} ({found_count/total_count*100:.0f}%)\\\")\\n    \\n    if 'completeness_results' in locals():\\n        print(\\\"\\\\n【データ品質】\\\")\\n        missing_stats = completeness_results['missing_analysis']\\n        print(f\\\"  欠損値なし特徴量: {missing_stats['no_missing']}個\\\")\\n        print(f\\\"  数値型特徴量: {completeness_results['data_quality']['numeric_features']}個\\\")\\n        \\n        # 総合評価\\n        total_features = completeness_results['total_features']\\n        quality_score = (\\n            missing_stats['no_missing'] * 1.0 +\\n            missing_stats['low_missing'] * 0.8 +\\n            missing_stats['medium_missing'] * 0.5 +\\n            missing_stats['high_missing'] * 0.2\\n        ) / total_features * 100\\n        \\n        print(f\\\"\\\\n📊 データ品質スコア: {quality_score:.1f}/100\\\")\\n        \\n        if quality_score >= 80:\\n            print(\\\"✅ 高品質: 機械学習に適したデータです\\\")\\n        elif quality_score >= 60:\\n            print(\\\"⚠️  中品質: 一部特徴量の見直しを推奨\\\")\\n        else:\\n            print(\\\"❌ 低品質: 大幅な前処理が必要\\\")\\nelse:\\n    print(\\\"\\\\n❌ 検証対象データが見つかりませんでした。\\\")\"", "metadata": {}, "outputs": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.9"}}, "nbformat": 4, "nbformat_minor": 4}