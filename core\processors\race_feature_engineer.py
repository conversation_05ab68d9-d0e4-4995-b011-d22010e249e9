#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
レースデータから特徴量をエンジニアリングするモジュール
"""

import logging
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from tqdm.auto import tqdm

from core.utils.constants import Master, RaceInfoCols, ResultsCols, HorseResultsCols, HorseInfoCols
from core.processors.race_html_parser import RaceHtmlParser
from core.processors.race_data_preprocessor import RaceDataPreprocessor

class RaceFeatureEngineer:
    """
    レースデータから特徴量を生成・加工するクラス
    """
    def __init__(self, html_parser: RaceHtmlParser, data_preprocessor: RaceDataPreprocessor, config: Optional[Dict[str, Any]] = None):
        """
        初期化

        Parameters
        ----------
        html_parser : RaceHtmlParser
            HTMLパースを行うインスタンス
        data_preprocessor : RaceDataPreprocessor
            データ前処理を行うインスタンス
        config : Dict[str, Any], optional
            処理の設定情報
        """
        self._config = config or {}
        self.logger = logging.getLogger(__name__)
        self._html_parser = html_parser
        self._preprocessor = data_preprocessor

    def merge_past_horse_performance(self,
                                     current_race_results_df: pd.DataFrame,
                                     all_horse_past_results_df: pd.DataFrame,
                                     target_cols: List[str],
                                     n_races_list: List[int] = [5, 9],
                                     group_cols: Optional[List[str]] = None
                                     ) -> pd.DataFrame:
        """
        レース結果データに、各出走馬のそのレース時点での過去戦績をマージする。

        Parameters
        ----------
        current_race_results_df : pd.DataFrame
            マージ対象の現在のレース結果DataFrame。'horse_id'と'date'(datetime型)列が必要。
        all_horse_past_results_df : pd.DataFrame
            全馬の過去の全レース結果を含むDataFrame。
            'horse_id', 'date'(datetime型), および target_cols に指定する成績列が必要。
        target_cols : List[str]
            all_horse_past_results_df の中で集計対象とする列名のリスト (例: ['着順', '賞金'])。
        n_races_list : List[int], optional
            直近Nレースとして集計するレース数のリスト。デフォルトは [5, 9]。
        group_cols : Optional[List[str]], optional
            追加のグルーピング列。指定された場合、これらの列ごとにも集計。デフォルトは None。

        Returns
        -------
        pd.DataFrame
            過去戦績がマージされたDataFrame。
        """
        if current_race_results_df.empty:
            self.logger.warning("マージ対象の現在のレース結果データが空です。")
            return pd.DataFrame()
        if all_horse_past_results_df.empty:
            self.logger.warning("過去戦績の計算に必要な全馬の過去成績データが空です。")
            return current_race_results_df

        # 'date' 列の型チェックと変換 (all_horse_past_results_df)
        if 'date' not in all_horse_past_results_df.columns or \
           not pd.api.types.is_datetime64_any_dtype(all_horse_past_results_df['date']):
            date_col_name_past = 'date' # デフォルト
            if '日付' in all_horse_past_results_df.columns: date_col_name_past = '日付'
            if date_col_name_past in all_horse_past_results_df:
                 all_horse_past_results_df['date'] = pd.to_datetime(all_horse_past_results_df[date_col_name_past], errors='coerce')
            if 'date' not in all_horse_past_results_df.columns or not pd.api.types.is_datetime64_any_dtype(all_horse_past_results_df['date']):
                self.logger.error("全馬の過去成績データに有効な 'date' 列 (datetime型) が見つかりません。")
                return current_race_results_df

        # 'date' 列の型チェックと変換 (current_race_results_df)
        if 'date' not in current_race_results_df.columns or \
           not pd.api.types.is_datetime64_any_dtype(current_race_results_df['date']):
            date_col_name_current = RaceInfoCols.DATE # scraping_constants から
            if date_col_name_current not in current_race_results_df.columns and '日付' in current_race_results_df.columns:
                date_col_name_current = '日付'

            if date_col_name_current in current_race_results_df:
                current_race_results_df['date'] = pd.to_datetime(current_race_results_df[date_col_name_current], errors='coerce')
            if 'date' not in current_race_results_df.columns or not pd.api.types.is_datetime64_any_dtype(current_race_results_df['date']):
                self.logger.error("現在のレース結果データに有効な 'date' 列 (datetime型) が見つかりません。")
                return current_race_results_df

        if 'horse_id' not in current_race_results_df.columns:
            self.logger.error("現在のレース結果データに 'horse_id' 列が見つかりません。")
            return current_race_results_df
        if 'horse_id' not in all_horse_past_results_df.columns:
            self.logger.error("全馬の過去成績データに 'horse_id' 列が見つかりません。")
            return current_race_results_df

        # horse_id の型を文字列に統一
        current_race_results_df['horse_id'] = current_race_results_df['horse_id'].astype(str)
        all_horse_past_results_df['horse_id'] = all_horse_past_results_df['horse_id'].astype(str)
        self.logger.debug("horse_idカラムを文字列型に統一しました。")

        processed_results_list = []
        unique_dates = sorted(current_race_results_df['date'].unique())

        for race_date in tqdm(unique_dates, desc="過去戦績マージ中"):
            results_on_date_df = current_race_results_df[current_race_results_df['date'] == race_date].copy()
            if results_on_date_df.empty:
                continue

            horse_id_list_on_date = results_on_date_df['horse_id'].unique()

            past_horse_results_for_date = all_horse_past_results_df[
                (all_horse_past_results_df['horse_id'].isin(horse_id_list_on_date)) &
                (all_horse_past_results_df['date'] < race_date)
            ].copy()

            if past_horse_results_for_date.empty:
                processed_results_list.append(results_on_date_df)
                continue

            # 集計対象となる有効な target_cols をフィルタリング
            valid_target_cols = [col for col in target_cols if col in past_horse_results_for_date.columns]
            if not valid_target_cols:
                self.logger.warning(f"レース日 {race_date}: 有効な集計対象カラム (target_cols) が過去成績データに存在しません。この日付の集計をスキップします。")
                processed_results_list.append(results_on_date_df)
                continue
            if len(valid_target_cols) < len(target_cols):
                self.logger.debug(f"レース日 {race_date}: target_colsの一部が存在しません。存在するカラムのみ使用: {valid_target_cols}")

            # 直近Nレースの集計
            for n_races in n_races_list:
                n_race_filtered_results = past_horse_results_for_date.sort_values('date', ascending=False)\
                                                                    .groupby('horse_id').head(n_races)
                if not n_race_filtered_results.empty:
                    summarized_n = n_race_filtered_results.groupby('horse_id')[valid_target_cols].mean()\
                                                        .add_suffix(f'_last_{n_races}R_mean')
                    results_on_date_df = results_on_date_df.merge(summarized_n, on='horse_id', how='left')

                    if group_cols:
                        valid_group_cols_n = [
                            gc for gc in group_cols
                            if gc in n_race_filtered_results.columns and gc in results_on_date_df.columns
                        ]
                        if not valid_group_cols_n and group_cols:
                             self.logger.debug(f"レース日 {race_date}, 直近{n_races}R: 指定されたgroup_cols ({group_cols}) のいずれもデータに存在しません。")
                        for group_col_valid in valid_group_cols_n:
                                summarized_n_with = n_race_filtered_results.groupby(['horse_id', group_col_valid])[valid_target_cols].mean()\
                                                                        .add_suffix(f'_{group_col}_last_{n_races}R_mean')
                                results_on_date_df = results_on_date_df.merge(summarized_n_with, on=['horse_id', group_col_valid], how='left', suffixes=('', f'_gcol_{group_col_valid}'))

            # 全期間の集計
            if not past_horse_results_for_date.empty:
                summarized_all = past_horse_results_for_date.groupby('horse_id')[valid_target_cols].mean()\
                                                            .add_suffix('_all_R_mean')
                results_on_date_df = results_on_date_df.merge(summarized_all, on='horse_id', how='left')

                if group_cols:
                    valid_group_cols_all = [
                        gc for gc in group_cols
                        if gc in past_horse_results_for_date.columns and gc in results_on_date_df.columns
                    ]
                    if not valid_group_cols_all and group_cols:
                        self.logger.debug(f"レース日 {race_date}, 全期間: 指定されたgroup_cols ({group_cols}) のいずれもデータに存在しません。")
                    for group_col_valid in valid_group_cols_all:
                            summarized_all_with = past_horse_results_for_date.groupby(['horse_id', group_col_valid])[valid_target_cols].mean()\
                                                                            .add_suffix(f'_{group_col}_all_R_mean')
                            results_on_date_df = results_on_date_df.merge(summarized_all_with, on=['horse_id', group_col_valid], how='left', suffixes=('', f'_gcol_{group_col_valid}'))

            # 前走の日付と経過日数
            if not past_horse_results_for_date.empty:
                latest_race_date_series = past_horse_results_for_date.groupby('horse_id')['date'].max().rename('last_race_date')
                results_on_date_df = results_on_date_df.merge(latest_race_date_series, on='horse_id', how='left')

                # 'interval_days' の計算を安全に行う
                if 'last_race_date' in results_on_date_df.columns:
                    # 'last_race_date' が NaT でない行のみ計算対象とする
                    valid_last_race_dates = results_on_date_df['last_race_date'].notna() & \
                                            pd.api.types.is_datetime64_any_dtype(results_on_date_df['last_race_date'])

                    if valid_last_race_dates.any(): # 有効な日付が一つでもあれば
                        # 'date' も datetime型であることを確認
                        if pd.api.types.is_datetime64_any_dtype(results_on_date_df['date']):
                            results_on_date_df.loc[valid_last_race_dates, 'interval_days'] = \
                                (results_on_date_df.loc[valid_last_race_dates, 'date'] - results_on_date_df.loc[valid_last_race_dates, 'last_race_date']).dt.days
                        else:
                            results_on_date_df['interval_days'] = pd.NA
                    else: # 有効な last_race_date がない場合
                        results_on_date_df['interval_days'] = pd.NA
                else:
                    results_on_date_df['interval_days'] = pd.NA # 前走がない、または日付型でない場合

            processed_results_list.append(results_on_date_df)

        if not processed_results_list:
            self.logger.info("過去戦績をマージする対象レースがありませんでした。")
            return current_race_results_df

        final_merged_df = pd.concat(processed_results_list, ignore_index=True)
        self.logger.info(f"過去戦績のマージが完了しました。処理後の行数: {len(final_merged_df)}")
        return final_merged_df

    def extract_horse_sex_age_mapping(self, year: Optional[int] = None, race_id: Optional[str] = None,
                                     html_path_list: Optional[List[str]] = None) -> pd.DataFrame:
        """
        レース結果から馬IDと性齢情報のマッピングを抽出する

        Parameters
        ----------
        year : int, optional
            対象年度。html_path_listがNoneの場合に使用
        race_id : str, optional
            特定のレースIDを指定する場合
        html_path_list : List[str], optional
            処理対象のHTMLファイルパスのリスト

        Returns
        -------
        pd.DataFrame
            馬IDと性齢情報のマッピング（列: horse_id, 性齢, 性, 年齢, race_id, 日付）
        """
        self.logger.info("馬IDと性齢情報のマッピング抽出を開始")

        # HTMLファイルパスのリストを取得
        if html_path_list is None:
            import glob
            html_path_list = []
            if year:
                # 年からファイルリストを取得
                pattern = os.path.join(LocalPaths.HTML_RACE_DIR, "race_by_year", str(year), "*.bin")
                html_path_list.extend(glob.glob(pattern))
                if not html_path_list:
                    # フォールバック
                    pattern_fallback = os.path.join(LocalPaths.HTML_RACE_DIR, f"*{year}*.bin")
                    html_path_list.extend(glob.glob(pattern_fallback))
            elif race_id:
                # race_idから年を抽出
                year_from_id = race_id[:4]
                path = os.path.join(LocalPaths.HTML_RACE_DIR, "race_by_year", year_from_id, f"{race_id}.bin")
                if os.path.exists(path):
                    html_path_list.append(path)

        if not html_path_list:
            self.logger.warning("処理対象のHTMLファイルが見つかりません")
            return pd.DataFrame()

        mapping_list = []

        # 並列処理のためのワーカー数を決定
        num_workers = self._config.get('max_workers', os.cpu_count() or 1) if self._config else (os.cpu_count() or 1)

        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = {
                executor.submit(self._extract_sex_age_from_single_html, html_path): html_path
                for html_path in html_path_list
            }
            for future in tqdm(as_completed(futures), total=len(html_path_list), desc="性齢情報抽出(並列)"):
                html_path_completed = futures[future]
                try:
                    mapping_df = future.result()
                    if mapping_df is not None and not mapping_df.empty:
                        mapping_list.append(mapping_df)
                except Exception as e:
                    self.logger.error(f"ファイル {html_path_completed} の性齢情報抽出中にエラー: {e}", exc_info=True)

        if not mapping_list:
            self.logger.warning("性齢情報を抽出できませんでした")
            return pd.DataFrame()

        # 全てのマッピングを結合
        final_mapping_df = pd.concat(mapping_list, ignore_index=True)

        # 重複を除去（同じ馬が複数のレースに出走している場合）
        # 最新の情報を優先（日付順でソートして重複除去）
        if '日付' in final_mapping_df.columns:
            final_mapping_df = final_mapping_df.sort_values('日付', ascending=False)

        # horse_idごとに最新の性齢情報を保持
        final_mapping_df = final_mapping_df.drop_duplicates(subset=['horse_id'], keep='first')

        self.logger.info(f"性齢情報マッピング抽出完了: {len(final_mapping_df)}件")
        return final_mapping_df

    def _extract_sex_age_from_single_html(self, html_path: str) -> Optional[pd.DataFrame]:
        """単一のHTMLファイルから性齢情報を抽出するヘルパー関数"""
        try:
            # レース結果をパース (RaceHtmlParserを使用)
            race_info_df, results_df = self._html_parser.parse_race_html(html_path)
            if results_df is None or results_df.empty:
                return None

            race_id_current = os.path.basename(html_path).replace('.bin', '')
            race_date = None
            if not race_info_df.empty and RaceInfoCols.DATE in race_info_df.columns:
                race_date = race_info_df[RaceInfoCols.DATE].iloc[0] if len(race_info_df) > 0 else None

            # 性齢情報が含まれているかチェック
            if ResultsCols.SEX_AGE not in results_df.columns:
                self.logger.debug(f"性齢情報が見つかりません: {html_path}")
                return None

            # horse_idが含まれているかチェック
            if ResultsCols.HORSE_ID not in results_df.columns:
                self.logger.debug(f"馬ID情報が見つかりません: {html_path}")
                return None

            # 必要な列のみ抽出
            mapping_df = results_df[[ResultsCols.HORSE_ID, ResultsCols.SEX_AGE]].copy()
            mapping_df = mapping_df.dropna(subset=[ResultsCols.HORSE_ID, ResultsCols.SEX_AGE])

            if mapping_df.empty:
                return None

            # 統合された性齢分離メソッドを使用
            mapping_df = self.process_sex_age_separation(mapping_df, ResultsCols.SEX_AGE)
            
            # データ型標準化を適用
            mapping_df = self.standardize_data_types(mapping_df)

            # レース情報を追加
            mapping_df['race_id'] = race_id_current
            mapping_df['日付'] = race_date

            return mapping_df.rename(columns={ResultsCols.HORSE_ID: 'horse_id', ResultsCols.SEX_AGE: '性齢'})
        except Exception as e: # pragma: no cover
            self.logger.error(f"単一HTMLからの性齢情報抽出エラー ({html_path}): {e}", exc_info=True)
            return None
    
    def process_sex_age_separation(self, df: pd.DataFrame, sex_age_col: str = None) -> pd.DataFrame:
        """
        データフレーム内の性齢情報を性と年齢に分離する統合メソッド
        
        Parameters
        ----------
        df : pd.DataFrame
            処理対象のデータフレーム
        sex_age_col : str, optional
            性齢情報が含まれる列名。Noneの場合は自動検出
            
        Returns
        -------
        pd.DataFrame
            性齢が分離されたデータフレーム（'性'、'年齢'列が追加）
        """
        if df.empty:
            return df
        
        # 性齢列の自動検出
        if sex_age_col is None:
            if ResultsCols.SEX_AGE in df.columns:
                sex_age_col = ResultsCols.SEX_AGE
            elif HorseResultsCols.SEX_AGE in df.columns:
                sex_age_col = HorseResultsCols.SEX_AGE
            elif '性齢' in df.columns:
                sex_age_col = '性齢'
            else:
                self.logger.warning("性齢情報の列が見つかりません。データをそのまま返します。")
                return df
        
        try:
            # 性齢を性と年齢に分ける
            if sex_age_col in df.columns:
                sex_age_series = df[sex_age_col].astype(str)
                # str.extract を使用して性別と年齢を抽出
                # 例: "牡2" -> extract_result[0] = "牡", extract_result[1] = "2"
                extract_result = sex_age_series.str.extract(r'^([^\d]+)(\d+)$', expand=True)
                if not extract_result.empty:
                    df["性"] = extract_result[0]
                    df["年齢"] = pd.to_numeric(extract_result[1], errors='coerce').astype('Int64')

                    # 性別が期待される文字でない場合はNAにするバリデーション
                    valid_sexes = Master.SEX_LIST if hasattr(Master, 'SEX_LIST') else ['牡', '牝', 'セ']
                    if "性" in df.columns:  # "性" カラムが実際に作成されたか確認
                        df.loc[~df["性"].isin(valid_sexes), "性"] = pd.NA
                else:
                    df["性"] = pd.NA
                    df["年齢"] = pd.NA
                    
                self.logger.info(f"性齢分離処理完了: {len(df)}件のレコードを処理")
            else:
                self.logger.warning(f"指定された性齢列 '{sex_age_col}' が見つかりません。")
                
        except Exception as e:
            self.logger.error(f"性齢分離処理エラー: {e}")
            df["性"] = pd.NA
            df["年齢"] = pd.NA
            
        return df
    
    def standardize_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        データ型を統一する統合メソッド
        
        Parameters
        ----------
        df : pd.DataFrame
            型標準化するデータフレーム
            
        Returns
        -------
        pd.DataFrame
            型標準化されたデータフレーム
        """
        if df.empty:
            return df

        # ID関連カラムの統一処理
        id_columns = ['horse_id', 'race_id', 'jockey_id', 'trainer_id',
                      HorseInfoCols.FATHER_ID, HorseInfoCols.MOTHER_ID, HorseInfoCols.MOTHER_FATHER_ID,
                      HorseInfoCols.SIBLING_IDS]

        for col in id_columns:
            if col in df.columns:
                try:
                    # pd.StringDtype() を使用して、欠損値を pd.NA として扱う
                    df[col] = df[col].astype(pd.StringDtype())
                except TypeError:
                    # 古いPandasバージョンなど StringDtype が使えない場合のフォールバック
                    df[col] = df[col].astype(object).fillna(pd.NA).astype(str).replace({
                        '<NA>': pd.NA, 'nan': pd.NA, 'None': pd.NA, 'NA': pd.NA
                    })

        # 日付関連カラムの統一処理
        if 'date' in df.columns:
            df['date'] = df['date'].apply(self._normalize_date)
            df['date'] = pd.to_datetime(df['date'], errors='coerce')

        # 馬の生年月日の処理
        birthday_col = HorseInfoCols.BIRTHDAY
        if birthday_col in df.columns:
            df[birthday_col] = df[birthday_col].apply(self._normalize_date)
            df[birthday_col] = pd.to_datetime(df[birthday_col], errors='coerce')

        self.logger.info(f"データ型標準化完了: {len(df)}件のレコードを処理")
        return df
    
    def _normalize_date(self, date_str):
        """
        日付文字列を正規化するヘルパーメソッド
        
        Parameters
        ----------
        date_str : str
            日付文字列
            
        Returns
        -------
        str or pd.NaType
            正規化された日付文字列またはNA
        """
        if pd.isna(date_str) or date_str == '' or str(date_str).lower() in ['none', 'nan', 'na']:
            return pd.NA
        
        date_str = str(date_str).strip()
        
        # 既に正しい形式の場合はそのまま返す
        import re
        if re.match(r'^\d{4}-\d{2}-\d{2}', date_str):
            return date_str
            
        # 日本語形式 (例: "2023年2月5日") を YYYY-MM-DD に変換
        japanese_pattern = r'(\d{4})年(\d{1,2})月(\d{1,2})日'
        match = re.match(japanese_pattern, date_str)
        if match:
            year, month, day = match.groups()
            return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        
        # その他の形式はそのまま返してpandasにto_datetimeで処理させる
        return date_str