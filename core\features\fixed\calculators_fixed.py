#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特徴量計算関数（データリーケージ対策版）

過去のデータのみを使用して特徴量を計算する関数群
"""

import logging
import re
from typing import Dict, List, Any, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.preprocessing import LabelEncoder, StandardScaler

logger = logging.getLogger(__name__)


class FeatureCalculatorsFixed:
    """
    データリーケージを防ぐ特徴量計算関数を提供するクラス
    """
    
    def __init__(self):
        self.label_encoders: Dict[str, LabelEncoder] = {}
        self.scalers: Dict[str, StandardScaler] = {}
    
    # ===== データフィルタリング関数 =====
    
    def filter_past_data(self, horse_results_df: pd.DataFrame, 
                        current_race_date: Union[str, pd.Timestamp],
                        horse_id: Optional[str] = None,
                        exclude_current_date: bool = True) -> pd.DataFrame:
        """
        現在のレース日より前のデータのみをフィルタリング
        
        Parameters
        ----------
        horse_results_df : pd.DataFrame
            馬の過去成績データ
        current_race_date : str or pd.Timestamp
            現在のレース日
        horse_id : str, optional
            特定の馬IDでフィルタリング
        exclude_current_date : bool
            当日のレースを除外するか
            
        Returns
        -------
        pd.DataFrame
            フィルタリングされたデータ
        """
        if horse_results_df.empty:
            return horse_results_df
            
        # 日付列の確認
        date_column = None
        for col in ['日付', 'date', 'race_date']:
            if col in horse_results_df.columns:
                date_column = col
                break
                
        if date_column is None:
            logger.warning("日付列が見つかりません。データリーケージの可能性があります。")
            return horse_results_df
            
        # 日付を標準化
        current_date = pd.to_datetime(current_race_date)
        filtered_df = horse_results_df.copy()
        filtered_df[date_column] = pd.to_datetime(filtered_df[date_column], errors='coerce')
        
        # 過去データのみをフィルタリング
        if exclude_current_date:
            filtered_df = filtered_df[filtered_df[date_column] < current_date]
        else:
            filtered_df = filtered_df[filtered_df[date_column] <= current_date]
            
        # 特定の馬でフィルタリング
        if horse_id is not None and 'horse_id' in filtered_df.columns:
            filtered_df = filtered_df[filtered_df['horse_id'] == horse_id]
            
        return filtered_df
    
    # ===== 過去成績特徴量計算関数（修正版） =====
    
    def calculate_race_count(self, data: pd.DataFrame, horse_results_df: pd.DataFrame, 
                           column: str = 'horse_id', race_date_column: str = 'date', **kwargs) -> pd.Series:
        """
        過去の出走回数を計算（データリーケージ対策版）
        
        Parameters
        ----------
        data : pd.DataFrame
            メインデータ（現在のレース情報を含む）
        horse_results_df : pd.DataFrame
            馬の過去成績データ
        column : str
            馬IDカラム名
        race_date_column : str
            レース日カラム名
            
        Returns
        -------
        pd.Series
            出走回数
        """
        if column not in data.columns:
            logger.warning(f"カラム '{column}' が見つかりません")
            return pd.Series(index=data.index, dtype=float)
        
        if horse_results_df.empty:
            return pd.Series(0, index=data.index)
            
        # レース日が存在しない場合は警告
        if race_date_column not in data.columns:
            logger.error(f"レース日カラム '{race_date_column}' が見つかりません。データリーケージの可能性があります。")
            return pd.Series(0, index=data.index)
        
        # 各レースの日付で過去データをフィルタリングして出走回数を計算
        race_counts = pd.Series(index=data.index, dtype=float)
        
        for idx, row in data.iterrows():
            horse_id = row[column]
            race_date = row[race_date_column]
            
            # 過去データのみをフィルタリング
            past_data = self.filter_past_data(
                horse_results_df, 
                race_date, 
                horse_id=horse_id,
                exclude_current_date=True
            )
            
            # 出走回数をカウント
            race_counts[idx] = len(past_data)
            
        return race_counts
    
    def calculate_win_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                          column: str = 'horse_id', race_date_column: str = 'date', **kwargs) -> pd.Series:
        """
        過去の勝率（1着率）を計算（データリーケージ対策版）
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
            
        if race_date_column not in data.columns:
            logger.error(f"レース日カラム '{race_date_column}' が見つかりません。データリーケージの可能性があります。")
            return pd.Series(0, index=data.index)
        
        win_rates = pd.Series(index=data.index, dtype=float)
        
        for idx, row in data.iterrows():
            horse_id = row[column]
            race_date = row[race_date_column]
            
            # 過去データのみをフィルタリング
            past_data = self.filter_past_data(
                horse_results_df, 
                race_date, 
                horse_id=horse_id,
                exclude_current_date=True
            )
            
            if len(past_data) == 0:
                win_rates[idx] = 0.0
            else:
                # 1着の回数と総出走回数を計算
                wins = (pd.to_numeric(past_data['着順'], errors='coerce') == 1).sum()
                total_races = len(past_data)
                win_rates[idx] = wins / total_races if total_races > 0 else 0.0
                
        return win_rates
    
    def calculate_avg_rank(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                          column: str = 'horse_id', race_date_column: str = 'date', 
                          lookback_races: Optional[int] = None, **kwargs) -> pd.Series:
        """
        過去の平均着順を計算（データリーケージ対策版）
        
        Parameters
        ----------
        data : pd.DataFrame
            メインデータ
        horse_results_df : pd.DataFrame
            馬の過去成績データ
        column : str
            馬IDカラム名
        race_date_column : str
            レース日カラム名
        lookback_races : int, optional
            直近何レースを見るか（Noneの場合は全過去レース）
            
        Returns
        -------
        pd.Series
            平均着順
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
            
        if race_date_column not in data.columns:
            logger.error(f"レース日カラム '{race_date_column}' が見つかりません。データリーケージの可能性があります。")
            return pd.Series(np.nan, index=data.index)
        
        avg_ranks = pd.Series(index=data.index, dtype=float)
        
        for idx, row in data.iterrows():
            horse_id = row[column]
            race_date = row[race_date_column]
            
            # 過去データのみをフィルタリング
            past_data = self.filter_past_data(
                horse_results_df, 
                race_date, 
                horse_id=horse_id,
                exclude_current_date=True
            )
            
            # 直近N件のみを使用
            if lookback_races is not None and len(past_data) > lookback_races:
                # 日付でソートして直近N件を取得
                date_col = None
                for col in ['日付', 'date', 'race_date']:
                    if col in past_data.columns:
                        date_col = col
                        break
                if date_col:
                    past_data = past_data.sort_values(date_col, ascending=False).head(lookback_races)
            
            if len(past_data) == 0:
                avg_ranks[idx] = np.nan
            else:
                # 着順を数値に変換して平均を計算
                numeric_ranks = pd.to_numeric(past_data['着順'], errors='coerce')
                avg_ranks[idx] = numeric_ranks.mean()
                
        return avg_ranks
    
    def calculate_last_rank(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                          column: str = 'horse_id', race_date_column: str = 'date', **kwargs) -> pd.Series:
        """
        前走の着順を計算（データリーケージ対策版）
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
            
        if race_date_column not in data.columns:
            logger.error(f"レース日カラム '{race_date_column}' が見つかりません。データリーケージの可能性があります。")
            return pd.Series(np.nan, index=data.index)
        
        last_ranks = pd.Series(index=data.index, dtype=float)
        
        for idx, row in data.iterrows():
            horse_id = row[column]
            race_date = row[race_date_column]
            
            # 過去データのみをフィルタリング
            past_data = self.filter_past_data(
                horse_results_df, 
                race_date, 
                horse_id=horse_id,
                exclude_current_date=True
            )
            
            if len(past_data) == 0:
                last_ranks[idx] = np.nan
            else:
                # 日付でソートして最新のレースの着順を取得
                date_col = None
                for col in ['日付', 'date', 'race_date']:
                    if col in past_data.columns:
                        date_col = col
                        break
                        
                if date_col:
                    past_data = past_data.sort_values(date_col, ascending=False)
                    
                last_rank = past_data.iloc[0]['着順']
                last_ranks[idx] = pd.to_numeric(last_rank, errors='coerce')
                
        return last_ranks
    
    def calculate_place_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                           column: str = 'horse_id', race_date_column: str = 'date', **kwargs) -> pd.Series:
        """
        過去の連対率（1-2着率）を計算（データリーケージ対策版）
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
            
        if race_date_column not in data.columns:
            logger.error(f"レース日カラム '{race_date_column}' が見つかりません。データリーケージの可能性があります。")
            return pd.Series(0, index=data.index)
        
        place_rates = pd.Series(index=data.index, dtype=float)
        
        for idx, row in data.iterrows():
            horse_id = row[column]
            race_date = row[race_date_column]
            
            # 過去データのみをフィルタリング
            past_data = self.filter_past_data(
                horse_results_df, 
                race_date, 
                horse_id=horse_id,
                exclude_current_date=True
            )
            
            if len(past_data) == 0:
                place_rates[idx] = 0.0
            else:
                places = (pd.to_numeric(past_data['着順'], errors='coerce') <= 2).sum()
                total_races = len(past_data)
                place_rates[idx] = places / total_races if total_races > 0 else 0.0
                
        return place_rates
    
    def calculate_show_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                          column: str = 'horse_id', race_date_column: str = 'date', **kwargs) -> pd.Series:
        """
        過去の複勝率（1-3着率）を計算（データリーケージ対策版）
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
            
        if race_date_column not in data.columns:
            logger.error(f"レース日カラム '{race_date_column}' が見つかりません。データリーケージの可能性があります。")
            return pd.Series(0, index=data.index)
        
        show_rates = pd.Series(index=data.index, dtype=float)
        
        for idx, row in data.iterrows():
            horse_id = row[column]
            race_date = row[race_date_column]
            
            # 過去データのみをフィルタリング
            past_data = self.filter_past_data(
                horse_results_df, 
                race_date, 
                horse_id=horse_id,
                exclude_current_date=True
            )
            
            if len(past_data) == 0:
                show_rates[idx] = 0.0
            else:
                shows = (pd.to_numeric(past_data['着順'], errors='coerce') <= 3).sum()
                total_races = len(past_data)
                show_rates[idx] = shows / total_races if total_races > 0 else 0.0
                
        return show_rates
    
    def calculate_jockey_win_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                                column: str = 'jockey_id', race_date_column: str = 'date', 
                                statistics_period_days: int = 365, **kwargs) -> pd.Series:
        """
        騎手の勝率を計算（データリーケージ対策版）
        
        Parameters
        ----------
        statistics_period_days : int
            統計期間（日数）
        """
        if column not in data.columns or horse_results_df.empty:
            return pd.Series(index=data.index, dtype=float)
        
        if 'jockey_id' not in horse_results_df.columns or '着順' not in horse_results_df.columns:
            return pd.Series(index=data.index, dtype=float)
            
        if race_date_column not in data.columns:
            logger.error(f"レース日カラム '{race_date_column}' が見つかりません。データリーケージの可能性があります。")
            return pd.Series(0, index=data.index)
        
        jockey_win_rates = pd.Series(index=data.index, dtype=float)
        
        for idx, row in data.iterrows():
            jockey_id = row[column]
            race_date = pd.to_datetime(row[race_date_column])
            
            # 統計期間の開始日を計算
            start_date = race_date - timedelta(days=statistics_period_days)
            
            # 該当期間のデータをフィルタリング
            date_col = None
            for col in ['日付', 'date', 'race_date']:
                if col in horse_results_df.columns:
                    date_col = col
                    break
                    
            if date_col:
                temp_df = horse_results_df.copy()
                temp_df[date_col] = pd.to_datetime(temp_df[date_col], errors='coerce')
                
                # 騎手の該当期間のデータ
                jockey_data = temp_df[
                    (temp_df['jockey_id'] == jockey_id) & 
                    (temp_df[date_col] >= start_date) & 
                    (temp_df[date_col] < race_date)
                ]
                
                if len(jockey_data) == 0:
                    jockey_win_rates[idx] = 0.0
                else:
                    wins = (pd.to_numeric(jockey_data['着順'], errors='coerce') == 1).sum()
                    total_races = len(jockey_data)
                    jockey_win_rates[idx] = wins / total_races if total_races > 0 else 0.0
            else:
                jockey_win_rates[idx] = 0.0
                
        return jockey_win_rates
    
    # 既存の基本特徴量計算関数は変更なし（データリーケージの心配がないため）
    def identity(self, data: pd.DataFrame, column: str, **kwargs) -> pd.Series:
        """そのまま返す（恒等関数）"""
        if column in data.columns:
            return data[column].copy()
        else:
            logger.warning(f"カラム '{column}' が見つかりません")
            return pd.Series(index=data.index, dtype=float)
    
    def encode_sex(self, data: pd.DataFrame, column: str = '性齢', **kwargs) -> pd.Series:
        """性別を数値化（牡=1, 牝=2, セ=3）"""
        if column not in data.columns:
            logger.warning(f"カラム '{column}' が見つかりません")
            return pd.Series(index=data.index, dtype=float)
        
        sex_map = {'牡': 1, '牝': 2, 'セ': 3}
        
        def extract_sex(seirei):
            if pd.isna(seirei):
                return np.nan
            seirei_str = str(seirei)
            for sex, code in sex_map.items():
                if sex in seirei_str:
                    return code
            return np.nan
        
        return data[column].apply(extract_sex)
    
    def extract_age(self, data: pd.DataFrame, column: str = '性齢', **kwargs) -> pd.Series:
        """性齢から年齢を抽出"""
        if column not in data.columns:
            logger.warning(f"カラム '{column}' が見つかりません")
            return pd.Series(index=data.index, dtype=float)
        
        def extract_age_value(seirei):
            if pd.isna(seirei):
                return np.nan
            # 数字を抽出
            match = re.search(r'\d+', str(seirei))
            if match:
                return int(match.group())
            return np.nan
        
        return data[column].apply(extract_age_value)
