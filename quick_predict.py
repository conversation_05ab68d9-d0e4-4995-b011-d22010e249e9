#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
クイック予測ツール - 1コマンドでレース予測

使用方法:
    python quick_predict.py 202406010101
    python quick_predict.py 202406010101 --model models/lgb_model.pkl
"""

import sys
import argparse
from pathlib import Path

# Windows環境でのUnicode文字対応
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

def quick_predict(race_id: str, model_path: str = None, scaler_path: str = None):
    """クイック予測実行"""
    try:
        print(f"🎯 レース{race_id}の予測を開始します...")
        
        # 既存のライブ予測システムを使用
        from prediction.live_predictor import LiveRacePredictor
        
        # 予測器の初期化
        predictor = LiveRacePredictor(
            model_path=model_path,
            scaler_path=scaler_path
        )
        
        # 予測実行
        result = predictor.predict_race_complete(
            race_id=race_id,
            skip_existing=True,
            save_results=True
        )
        
        if not result.empty:
            print("\n🏆 予測結果 TOP 10:")
            print("=" * 60)
            display_columns = ['馬名', '予測順位', '勝率予測']
            if 'オッズ' in result.columns:
                display_columns.append('オッズ')
            if '人気' in result.columns:
                display_columns.append('人気')
            
            print(result[display_columns].head(10).to_string(index=False))
            print("=" * 60)
            print(f"✅ 予測完了！結果を保存しました")
            
            # 簡易分析
            if '勝率予測' in result.columns:
                top_horse = result.iloc[0]
                print(f"\n🥇 最有力候補: {top_horse['馬名']} (勝率: {top_horse['勝率予測']:.3f})")
        else:
            print("❌ 予測結果を取得できませんでした")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 予測エラー: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='クイック予測ツール')
    parser.add_argument('race_id', help='レースID (例: 202406010101)')
    parser.add_argument('--model', help='使用するモデルファイルパス')
    parser.add_argument('--scaler', help='使用するスケーラーファイルパス')
    
    args = parser.parse_args()
    
    # レースIDの検証
    if len(args.race_id) != 12 or not args.race_id.isdigit():
        print("❌ 無効なレースID形式です。12桁の数字で入力してください。")
        print("例: 202406010101")
        sys.exit(1)
    
    # クイック予測実行
    success = quick_predict(args.race_id, args.model, args.scaler)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()