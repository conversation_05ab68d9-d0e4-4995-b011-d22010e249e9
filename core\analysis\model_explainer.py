#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
機械学習モデル解釈・説明システム

このモジュールはAI予想の根拠を解釈・説明します。
- SHAP値による特徴量重要度分析
- 予想根拠の可視化
- モデルの判断理由説明
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import shap
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import pickle
import warnings
import japanize_matplotlib

warnings.filterwarnings('ignore')

class ModelExplainer:
    """機械学習モデル解釈・説明クラス"""
    
    def __init__(self, model=None, features=None, output_dir: str = "explanation_output"):
        """
        初期化
        
        Parameters
        ----------
        model : object
            学習済みモデル
        features : list
            特徴量リスト
        output_dir : str
            説明結果出力ディレクトリ
        """
        self.model = model
        self.features = features
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
        self.explainer = None
        
        if model and features:
            self._initialize_explainer()
    
    def _initialize_explainer(self):
        """SHAP Explainerの初期化"""
        try:
            if hasattr(self.model, 'predict'):
                # LightGBMの場合（Boosterオブジェクトも含む）
                if hasattr(self.model, 'booster_') or str(type(self.model)).find('lightgbm') != -1:
                    self.explainer = shap.TreeExplainer(self.model)
                    self.logger.info("SHAP TreeExplainer（LightGBM最適化）初期化成功")
                else:
                    # その他のモデル用のExplainer
                    self.explainer = shap.Explainer(self.model)
                    self.logger.info("SHAP Explainer（汎用）初期化成功")
            else:
                self.logger.warning("モデルにpredict属性がありません")
        except Exception as e:
            self.logger.error(f"SHAP Explainer初期化エラー: {e}")
    
    def explain_predictions(self, X: pd.DataFrame, 
                          horse_names: List[str] = None) -> Dict[str, Any]:
        """
        予想結果の詳細説明
        
        Parameters
        ----------
        X : pd.DataFrame
            予想対象の特徴量データ
        horse_names : List[str], optional
            馬名リスト
            
        Returns
        -------
        Dict[str, Any]
            説明結果
        """
        if self.explainer is None:
            self.logger.error("Explainerが初期化されていません")
            return {}
        
        try:
            # SHAP値計算
            shap_values = self.explainer.shap_values(X)
            
            # 基本統計
            explanation_result = {
                'feature_importance': self._calculate_feature_importance(shap_values, X),
                'individual_explanations': self._explain_individual_predictions(
                    shap_values, X, horse_names),
                'model_insights': self._generate_model_insights(shap_values, X),
                'decision_factors': self._analyze_decision_factors(shap_values, X)
            }
            
            return explanation_result
            
        except Exception as e:
            self.logger.error(f"予想説明エラー: {e}")
            return {}
    
    def _calculate_feature_importance(self, shap_values: np.ndarray, 
                                    X: pd.DataFrame) -> Dict[str, Any]:
        """特徴量重要度計算"""
        
        # SHAP値の絶対値平均で重要度を計算
        if len(shap_values.shape) == 2:
            importance_scores = np.abs(shap_values).mean(axis=0)
        else:
            importance_scores = np.abs(shap_values)
        
        feature_names = X.columns.tolist()
        
        # 重要度でソート
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importance_scores
        }).sort_values('importance', ascending=False)
        
        return {
            'top_features': importance_df.head(10).to_dict('records'),
            'all_features': importance_df.to_dict('records'),
            'importance_scores': importance_scores.tolist()
        }
    
    def _explain_individual_predictions(self, shap_values: np.ndarray,
                                      X: pd.DataFrame,
                                      horse_names: List[str] = None) -> List[Dict[str, Any]]:
        """個別予想の説明"""
        
        explanations = []
        feature_names = X.columns.tolist()
        
        for i in range(len(X)):
            if len(shap_values.shape) == 2:
                horse_shap = shap_values[i]
            else:
                horse_shap = shap_values
                
            horse_features = X.iloc[i]
            
            # 影響が大きい特徴量トップ5
            feature_impact = [(feature_names[j], horse_shap[j], horse_features.iloc[j]) 
                            for j in range(len(feature_names))]
            feature_impact.sort(key=lambda x: abs(x[1]), reverse=True)
            
            explanation = {
                'horse_index': i,
                'horse_name': horse_names[i] if horse_names and i < len(horse_names) else f"Horse_{i+1}",
                'top_positive_factors': [
                    {'feature': f[0], 'shap_value': f[1], 'feature_value': f[2]}
                    for f in feature_impact if f[1] > 0
                ][:3],
                'top_negative_factors': [
                    {'feature': f[0], 'shap_value': f[1], 'feature_value': f[2]}
                    for f in feature_impact if f[1] < 0
                ][:3],
                'overall_prediction_reason': self._generate_prediction_reason(feature_impact[:5])
            }
            
            explanations.append(explanation)
        
        return explanations
    
    def _generate_prediction_reason(self, top_features: List[Tuple]) -> str:
        """予想理由の生成"""
        
        positive_factors = [f for f in top_features if f[1] > 0]
        negative_factors = [f for f in top_features if f[1] < 0]
        
        reason_parts = []
        
        if positive_factors:
            reason_parts.append("有利要因: " + 
                              ", ".join([self._translate_feature_name(f[0]) 
                                       for f in positive_factors[:2]]))
        
        if negative_factors:
            reason_parts.append("不利要因: " + 
                              ", ".join([self._translate_feature_name(f[0]) 
                                       for f in negative_factors[:2]]))
        
        return "; ".join(reason_parts)
    
    def _translate_feature_name(self, feature_name: str) -> str:
        """特徴量名の日本語変換"""
        
        translations = {
            'age': '年齢',
            'weight': '斤量',
            'popularity': '人気',
            'odds': 'オッズ',
            'course_len': '距離',
            'avg_rank': '平均着順',
            'win_rate': '勝率',
            'race_count': 'レース数',
            'recent_form': '最近の調子',
            'jockey_win_rate': '騎手勝率',
            'trainer_win_rate': '調教師勝率',
            'track_record': 'コース実績',
            'distance_aptitude': '距離適性',
            'ground_aptitude': '馬場適性'
        }
        
        for eng, jp in translations.items():
            if eng in feature_name.lower():
                return jp
        
        return feature_name
    
    def _generate_model_insights(self, shap_values: np.ndarray, 
                               X: pd.DataFrame) -> Dict[str, Any]:
        """モデル全体の洞察"""
        
        if len(shap_values.shape) == 2:
            global_importance = np.abs(shap_values).mean(axis=0)
        else:
            global_importance = np.abs(shap_values)
        
        feature_names = X.columns.tolist()
        
        insights = {
            'most_influential_feature': feature_names[np.argmax(global_importance)],
            'model_complexity': len([x for x in global_importance if x > 0.01]),
            'prediction_stability': np.std(global_importance),
            'feature_correlation_insights': self._analyze_feature_correlations(X)
        }
        
        return insights
    
    def _analyze_feature_correlations(self, X: pd.DataFrame) -> Dict[str, Any]:
        """特徴量相関分析"""
        
        correlation_matrix = X.corr()
        
        # 高相関ペアを抽出
        high_corr_pairs = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_value = correlation_matrix.iloc[i, j]
                if abs(corr_value) > 0.7:
                    high_corr_pairs.append({
                        'feature1': correlation_matrix.columns[i],
                        'feature2': correlation_matrix.columns[j],
                        'correlation': corr_value
                    })
        
        return {
            'high_correlation_pairs': high_corr_pairs,
            'correlation_summary': {
                'max_correlation': correlation_matrix.abs().max().max(),
                'avg_correlation': correlation_matrix.abs().mean().mean()
            }
        }
    
    def _analyze_decision_factors(self, shap_values: np.ndarray,
                                X: pd.DataFrame) -> Dict[str, Any]:
        """判断要因分析"""
        
        feature_names = X.columns.tolist()
        
        if len(shap_values.shape) == 2:
            # 各特徴量の正負の影響度分析
            positive_impact = (shap_values > 0).sum(axis=0)
            negative_impact = (shap_values < 0).sum(axis=0)
        else:
            positive_impact = np.array([1 if sv > 0 else 0 for sv in shap_values])
            negative_impact = np.array([1 if sv < 0 else 0 for sv in shap_values])
        
        decision_factors = {
            'consistently_positive_features': [
                feature_names[i] for i in range(len(feature_names))
                if positive_impact[i] > len(X) * 0.7
            ],
            'consistently_negative_features': [
                feature_names[i] for i in range(len(feature_names))
                if negative_impact[i] > len(X) * 0.7
            ],
            'variable_impact_features': [
                feature_names[i] for i in range(len(feature_names))
                if 0.3 < positive_impact[i] / len(X) < 0.7
            ]
        }
        
        return decision_factors
    
    def create_explanation_visualizations(self, explanation_result: Dict[str, Any],
                                        X: pd.DataFrame) -> Dict[str, str]:
        """説明結果の可視化"""
        
        visualization_files = {}
        
        try:
            # 1. 特徴量重要度チャート
            fig1_path = self.create_feature_importance_chart(explanation_result['feature_importance'])
            if fig1_path:
                visualization_files['feature_importance'] = fig1_path
            
            # 2. 個別説明チャート
            fig2_path = self.create_individual_explanation_chart(
                explanation_result['individual_explanations'][:5])  # 上位5頭
            if fig2_path:
                visualization_files['individual_explanations'] = fig2_path
            
            # 3. 相関分析チャート  
            if 'feature_correlation_insights' in explanation_result['model_insights']:
                fig3_path = self.create_correlation_heatmap(X)
                if fig3_path:
                    visualization_files['correlation_analysis'] = fig3_path
            
        except Exception as e:
            self.logger.error(f"可視化作成エラー: {e}")
        
        return visualization_files
    
    def create_feature_importance_chart(self, importance_data: Dict[str, Any]) -> Optional[str]:
        """特徴量重要度チャート作成"""
        
        try:
            top_features = importance_data['top_features']
            
            if not top_features:
                return None
            
            fig, ax = plt.subplots(figsize=(12, 8))
            
            features = [self._translate_feature_name(f['feature']) for f in top_features]
            importances = [f['importance'] for f in top_features]
            
            bars = ax.barh(range(len(features)), importances, color='skyblue')
            ax.set_yticks(range(len(features)))
            ax.set_yticklabels(features)
            ax.set_xlabel('SHAP重要度', fontsize=12)
            ax.set_title('特徴量重要度ランキング (Top 10)', fontsize=14, fontweight='bold')
            
            # 値をバーの右に表示
            for i, (bar, importance) in enumerate(zip(bars, importances)):
                ax.text(bar.get_width() + max(importances) * 0.01, bar.get_y() + bar.get_height()/2,
                       f'{importance:.3f}', va='center', fontsize=10)
            
            plt.tight_layout()
            
            file_path = self.output_dir / "feature_importance.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"特徴量重要度チャート作成エラー: {e}")
            return None
    
    def create_individual_explanation_chart(self, individual_explanations: List[Dict[str, Any]]) -> Optional[str]:
        """個別説明チャート作成"""
        
        try:
            if not individual_explanations:
                return None
            
            fig, axes = plt.subplots(len(individual_explanations), 1, 
                                   figsize=(14, 4 * len(individual_explanations)))
            
            if len(individual_explanations) == 1:
                axes = [axes]
            
            for i, (explanation, ax) in enumerate(zip(individual_explanations, axes)):
                horse_name = explanation['horse_name']
                positive_factors = explanation['top_positive_factors']
                negative_factors = explanation['top_negative_factors']
                
                # 正の要因と負の要因を結合
                all_factors = []
                colors = []
                
                for factor in positive_factors:
                    all_factors.append({
                        'feature': self._translate_feature_name(factor['feature']),
                        'value': factor['shap_value']
                    })
                    colors.append('green')
                
                for factor in negative_factors:
                    all_factors.append({
                        'feature': self._translate_feature_name(factor['feature']),
                        'value': factor['shap_value']
                    })
                    colors.append('red')
                
                if all_factors:
                    features = [f['feature'] for f in all_factors]
                    values = [f['value'] for f in all_factors]
                    
                    bars = ax.barh(range(len(features)), values, color=colors, alpha=0.7)
                    ax.set_yticks(range(len(features)))
                    ax.set_yticklabels(features)
                    ax.set_xlabel('SHAP値', fontsize=10)
                    ax.set_title(f'{horse_name} の予想根拠', fontsize=12, fontweight='bold')
                    ax.axvline(x=0, color='black', linestyle='-', linewidth=0.8)
                    
                    # 値をバーに表示
                    for bar, value in zip(bars, values):
                        if value >= 0:
                            ax.text(bar.get_width() + max(values) * 0.01, 
                                   bar.get_y() + bar.get_height()/2,
                                   f'{value:.3f}', va='center', fontsize=9)
                        else:
                            ax.text(bar.get_width() - abs(min(values)) * 0.01,
                                   bar.get_y() + bar.get_height()/2,
                                   f'{value:.3f}', va='center', ha='right', fontsize=9)
            
            plt.tight_layout()
            
            file_path = self.output_dir / "individual_explanations.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"個別説明チャート作成エラー: {e}")
            return None
    
    def create_correlation_heatmap(self, X: pd.DataFrame) -> Optional[str]:
        """相関ヒートマップ作成"""
        
        try:
            correlation_matrix = X.corr()
            
            # 重要な特徴量のみ表示（相関が高いもの優先）
            high_var_features = X.var().nlargest(15).index
            subset_corr = correlation_matrix.loc[high_var_features, high_var_features]
            
            fig, ax = plt.subplots(figsize=(12, 10))
            
            # ヒートマップ作成
            sns.heatmap(subset_corr, annot=True, cmap='coolwarm', center=0,
                       square=True, ax=ax, fmt='.2f', cbar_kws={'label': '相関係数'})
            
            ax.set_title('特徴量相関マトリックス (主要特徴量)', fontsize=14, fontweight='bold')
            
            plt.tight_layout()
            
            file_path = self.output_dir / "feature_correlation.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"相関ヒートマップ作成エラー: {e}")
            return None
    
    def generate_explanation_report(self, explanation_result: Dict[str, Any]) -> str:
        """説明レポート生成"""
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("AI予想根拠説明レポート")
        report_lines.append("=" * 80)
        
        # 特徴量重要度
        importance_data = explanation_result.get('feature_importance', {})
        top_features = importance_data.get('top_features', [])
        
        if top_features:
            report_lines.append(f"\n📊 最重要特徴量 (Top 5):")
            report_lines.append(f"{'順位':>4} {'特徴量':>20} {'重要度':>10}")
            report_lines.append("-" * 40)
            
            for i, feature in enumerate(top_features[:5]):
                feature_name = self._translate_feature_name(feature['feature'])
                importance = feature['importance']
                report_lines.append(f"{i+1:>4} {feature_name:>20} {importance:>9.3f}")
        
        # 個別説明
        individual_explanations = explanation_result.get('individual_explanations', [])
        if individual_explanations:
            report_lines.append(f"\n🐎 個別予想根拠 (上位3頭):")
            
            for explanation in individual_explanations[:3]:
                horse_name = explanation['horse_name']
                reason = explanation['overall_prediction_reason']
                
                report_lines.append(f"\n• {horse_name}:")
                report_lines.append(f"  {reason}")
                
                # 主要な正の要因
                positive_factors = explanation['top_positive_factors']
                if positive_factors:
                    report_lines.append(f"  主な有利要因:")
                    for factor in positive_factors[:2]:
                        feature_name = self._translate_feature_name(factor['feature'])
                        shap_value = factor['shap_value']
                        report_lines.append(f"    - {feature_name}: +{shap_value:.3f}")
                
                # 主要な負の要因
                negative_factors = explanation['top_negative_factors']
                if negative_factors:
                    report_lines.append(f"  主な不利要因:")
                    for factor in negative_factors[:2]:
                        feature_name = self._translate_feature_name(factor['feature'])
                        shap_value = factor['shap_value']
                        report_lines.append(f"    - {feature_name}: {shap_value:.3f}")
        
        # モデル洞察
        model_insights = explanation_result.get('model_insights', {})
        if model_insights:
            report_lines.append(f"\n🧠 モデル分析:")
            
            most_influential = model_insights.get('most_influential_feature', '')
            if most_influential:
                translated_feature = self._translate_feature_name(most_influential)
                report_lines.append(f"最も影響力のある要因: {translated_feature}")
            
            complexity = model_insights.get('model_complexity', 0)
            report_lines.append(f"モデル複雑度: {complexity} 個の有効特徴量")
            
            stability = model_insights.get('prediction_stability', 0)
            if stability < 0.1:
                stability_level = "安定"
            elif stability < 0.2:
                stability_level = "普通"
            else:
                stability_level = "不安定"
            report_lines.append(f"予想安定性: {stability_level} (偏差: {stability:.3f})")
        
        # 判断要因
        decision_factors = explanation_result.get('decision_factors', {})
        if decision_factors:
            report_lines.append(f"\n⚖️ 判断パターン分析:")
            
            positive_features = decision_factors.get('consistently_positive_features', [])
            if positive_features:
                translated_positive = [self._translate_feature_name(f) for f in positive_features[:3]]
                report_lines.append(f"常に有利に働く要因: {', '.join(translated_positive)}")
            
            negative_features = decision_factors.get('consistently_negative_features', [])
            if negative_features:
                translated_negative = [self._translate_feature_name(f) for f in negative_features[:3]]
                report_lines.append(f"常に不利に働く要因: {', '.join(translated_negative)}")
            
            variable_features = decision_factors.get('variable_impact_features', [])
            if variable_features:
                translated_variable = [self._translate_feature_name(f) for f in variable_features[:3]]
                report_lines.append(f"状況依存要因: {', '.join(translated_variable)}")
        
        report_lines.append(f"\n" + "=" * 80)
        report_lines.append("予想根拠説明完了")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)