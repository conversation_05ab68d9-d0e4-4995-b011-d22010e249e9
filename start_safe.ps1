# Safe PowerShell Startup Script (ASCII-only)
Write-Host "==================================================" -ForegroundColor Green
Write-Host "  Keiba AI Prediction System Startup" -ForegroundColor Green  
Write-Host "==================================================" -ForegroundColor Green
Write-Host ""

# Check if main file exists
if (-not (Test-Path "keiba_ai_main.py")) {
    Write-Host "ERROR: keiba_ai_main.py not found" -ForegroundColor Red
    Write-Host "Please run from the project directory" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Try to find and activate virtual environment
$VenvActivated = $false

# Method 1: Direct python execution from venv
if (Test-Path "venv\Scripts\python.exe") {
    Write-Host "Using venv python directly..." -ForegroundColor Green
    $PythonPath = "venv\Scripts\python.exe"
    $VenvActivated = $true
}
# Method 2: Activate venv script
elseif (Test-Path "venv\Scripts\Activate.ps1") {
    Write-Host "Activating virtual environment..." -ForegroundColor Green
    try {
        . "venv\Scripts\Activate.ps1"
        $PythonPath = "python"
        $VenvActivated = $true
        Write-Host "Virtual environment activated" -ForegroundColor Green
    } catch {
        Write-Host "Activation failed, using system python" -ForegroundColor Yellow
        $PythonPath = "python"
    }
}
# Method 3: Custom activation script
elseif (Test-Path "activate_env.ps1") {
    Write-Host "Running activate_env.ps1..." -ForegroundColor Yellow
    try {
        . ".\activate_env.ps1"
        $PythonPath = "python"
        $VenvActivated = $true
    } catch {
        Write-Host "Custom activation failed, using system python" -ForegroundColor Yellow
        $PythonPath = "python"
    }
}
# Fallback: System python
else {
    Write-Host "No virtual environment found, using system python" -ForegroundColor Yellow
    $PythonPath = "python"
}

# Test python execution
Write-Host ""
Write-Host "Testing Python environment..." -ForegroundColor Cyan
try {
    $Version = & $PythonPath --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Python version: $Version" -ForegroundColor Green
    } else {
        throw "Python execution failed"
    }
} catch {
    Write-Host "ERROR: Cannot execute Python" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Launch main system
Write-Host ""
Write-Host "Launching main system..." -ForegroundColor Green
Write-Host ""

try {
    & $PythonPath keiba_ai_main.py
    Write-Host ""
    Write-Host "System execution completed" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "ERROR during execution: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "==================================================" -ForegroundColor Blue
Write-Host "  Keiba AI System Terminated" -ForegroundColor Blue
Write-Host "==================================================" -ForegroundColor Blue
Read-Host "Press Enter to close"