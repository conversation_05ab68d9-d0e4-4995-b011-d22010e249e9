#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
レースデータのファイルI/Oと処理フローを管理するモジュール
"""

import datetime
import glob
import io
import logging
import os
import re
import pickle
from concurrent.futures import ThreadPoolExecutor, as_completed
import html5lib
import pandas as pd
from bs4 import BeautifulSoup
from tqdm.auto import tqdm

from core.utils.constants import LocalPaths, RaceProcessorConstants, ResultsCols
from core.processors.race_html_parser import RaceHtmlParser
from core.processors.corner_analyzer import CornerAnalyzer
from typing import Any, Dict, List, Optional, Set, Tuple, Union # html5lib の代わりにここに移動

class RaceFileHandler:
    """
    レースデータのファイルI/O（読み込み、書き込み）および
    処理フロー（パース、バッチ処理）を管理するクラス
    """
    def __init__(self, html_parser: RaceHtmlParser, corner_analyzer: CornerAnalyzer, config: Optional[Dict[str, Any]] = None):
        """
        初期化

        Parameters
        ----------
        html_parser : RaceHtmlParser
            HTMLパースを行うインスタンス
        corner_analyzer : CornerAnalyzer
            コーナー特徴量分析を行うインスタンス
        config : Dict[str, Any], optional
            処理の設定情報
        """
        self._config = config or {}
        self.logger = logging.getLogger(__name__)
        self._html_parser = html_parser
        self._corner_analyzer = corner_analyzer

    def _get_html_file_paths(self, year: Optional[str] = None, race_id: Optional[str] = None, max_files: Optional[int] = None) -> List[str]:
        """
        指定された年またはレースIDに基づいてHTMLファイルのパスリストを取得する。

        Args:
            year (Optional[str], optional): 処理する年。
            race_id (Optional[str], optional): 処理する特定のレースID。
            max_files (Optional[int], optional): 取得するファイルの最大数。

        Returns:
            List[str]: HTMLファイルのパスのリスト。
        """
        html_path_list: List[str] = []
        search_description = ""

        if race_id:
            year_from_id = race_id[:4]
            # LocalPaths.HTML_RACE_DIR の構造を考慮したパス候補
            path_candidates = [
                os.path.join(LocalPaths.HTML_RACE_DIR, year_from_id, f"{race_id}.bin"),
                os.path.join(LocalPaths.HTML_RACE_DIR, "race_by_year", year_from_id, f"{race_id}.bin"),
                os.path.join(LocalPaths.HTML_RACE_DIR, f"{race_id}.bin") # ルート直下も考慮
            ]
            html_path_list = [p for p in path_candidates if os.path.exists(p)]
            search_description = f"レースID {race_id}"
        elif year:
            pattern_candidates = [
                os.path.join(LocalPaths.HTML_RACE_DIR, str(year), "*.bin"),
                os.path.join(LocalPaths.HTML_RACE_DIR, "race_by_year", str(year), "*.bin"),
                os.path.join(LocalPaths.HTML_RACE_DIR, f"*{year}*.bin") # ルート直下の年パターンも考慮
            ]
            for pattern in pattern_candidates:
                html_path_list.extend(glob.glob(pattern))
            html_path_list = sorted(list(set(html_path_list))) # 重複削除とソート
            search_description = f"{year}年"
        else:
            # 年もレースIDも指定されない場合は全ファイルを対象 (サブディレクトリも検索)
            html_path_list = glob.glob(os.path.join(LocalPaths.HTML_RACE_DIR, "**", "*.bin"), recursive=True)
            search_description = "全ファイル"

        if not html_path_list:
            self.logger.warning(f"処理対象のHTMLファイルが見つかりません ({search_description})。")
            return []

        if max_files is not None and len(html_path_list) > max_files:
            self.logger.info(f"処理ファイル数を{max_files}に制限しました ({search_description}、元ファイル数: {len(html_path_list)})。")
            html_path_list = html_path_list[:max_files]

        self.logger.info(f"取得したHTMLファイル数: {len(html_path_list)} ({search_description})")
        return html_path_list

    def extract_horse_ids_from_race_data(
        self,
        year: Optional[str] = None,
        race_id: Optional[str] = None,
        save_csv: bool = False,
        use_cache: bool = True,
        parallel: bool = True,
        max_workers: int = 4,
    ) -> List[str]:
        """
        レース結果から馬IDを抽出する（高速化版）

        Args:
            year (Optional[str], optional): 処理する年
            race_id (Optional[str], optional): 処理する特定のレースID
            save_csv (bool, optional): 抽出した馬IDをCSVとして保存するかどうか
            use_cache (bool, optional): キャッシュを使用するかどうか
            parallel (bool, optional): 並列処理を使用するかどうか
            max_workers (int, optional): 並列処理の最大ワーカー数

        Returns:
            list: 抽出した馬IDのリスト
        """
        self.logger.info(f"レースデータから馬IDを抽出します...")

        # キャッシュファイルのパスを決定
        cache_dir = os.path.join(LocalPaths.DATA_DIR, "cache")
        os.makedirs(cache_dir, exist_ok=True)

        cache_key_parts = ["horse_ids"]
        if year:
            cache_key_parts.append(str(year))
        if race_id:
            cache_key_parts.append(str(race_id))
        cache_key = "_".join(cache_key_parts)
        cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

        # キャッシュが存在し、使用する場合はキャッシュから読み込む
        if use_cache and os.path.exists(cache_file):
            self.logger.info(f"キャッシュからデータを読み込みます: {cache_file}")
            try:
                with open(cache_file, "rb") as f:
                    horse_ids: List[str] = pickle.load(f)
                    self.logger.info(f"キャッシュから読み込んだ馬ID数: {len(horse_ids)}")
                    return horse_ids
            except Exception as e:
                self.logger.warning(f"キャッシュの読み込みに失敗しました: {e}。通常の処理を続行します。")
                # キャッシュの読み込みに失敗した場合は、通常の処理を続行

        # 処理対象のファイルパスを決定
        bin_files = self._get_html_file_paths(year, race_id)

        # ファイルが見つからない場合
        if not bin_files: # _get_html_file_paths 内でログ出力済み
            return []

        # 単一ファイルから馬IDを抽出する内部関数
        def _extract_ids_from_single_file(file_path: str) -> List[str]:
            try:
                # 先頭バイトでHTMLか判定
                with open(file_path, 'rb') as f:
                    head = f.read(1)
                if head != b'<':
                    self.logger.debug(f"スキップ: {file_path} はHTMLファイルではない可能性（先頭バイト: {head}）") # ログレベルをDEBUGに変更
                    return []
                # HTMLパーサーのメソッドを呼び出す
                horse_ids = self._html_parser.extract_horse_ids_from_html(file_path)
                return horse_ids
            except Exception as e:
                self.logger.error(f"ファイル {file_path} の処理中にエラーが発生しました: {e}", exc_info=True)
                return []

        all_horse_ids: Set[str] = set()

        # 並列処理のワーカー数を決定
        num_workers = max_workers if parallel and len(bin_files) > 1 else 1

        if parallel and len(bin_files) > 1:
            # 並列処理の実行
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                futures = {executor.submit(_extract_ids_from_single_file, file_path): file_path for file_path in bin_files}

                for future in tqdm(as_completed(futures), total=len(bin_files), desc="馬ID抽出 (並列)"):
                    file_path_completed = futures[future]
                    try:
                        horse_ids_from_file = future.result()
                        all_horse_ids.update(horse_ids_from_file)
                    except Exception as e:
                        self.logger.error(
                            f"ファイル {file_path_completed} の処理結果の取得中にエラーが発生しました: {e}", exc_info=True
                        )
        else:
            # 通常の処理（並列処理を使用しない場合、またはファイルが1つの場合）
            desc_text = "馬ID抽出 (非並列)" if len(bin_files) > 1 else "馬ID抽出"
            for file_path in tqdm(bin_files, desc=desc_text):
                try:
                    horse_ids_from_file = _extract_ids_from_single_file(file_path)
                    all_horse_ids.update(horse_ids_from_file)
                except Exception as e:
                    self.logger.error(f"ファイル {file_path} の処理中にエラーが発生しました: {e}", exc_info=True)

        # セットをソート済みリストに変換
        horse_ids = sorted(list(all_horse_ids))

        if not horse_ids:
            self.logger.info("処理対象のファイルから馬IDが見つかりませんでした。")
            return []

        self.logger.info(f"抽出された馬ID数: {len(horse_ids)}")

        # キャッシュに保存
        try:
            with open(cache_file, "wb") as f:
                pickle.dump(horse_ids, f)
            self.logger.info(f"馬IDをキャッシュに保存しました: {cache_file}")
        except Exception as e:
            self.logger.error(f"キャッシュの保存に失敗しました: {e}", exc_info=True)

        # CSVとして保存
        if save_csv and horse_ids:
            # 保存先ディレクトリを作成
            csv_dir = os.path.join(LocalPaths.DATA_DIR, "csv")
            os.makedirs(csv_dir, exist_ok=True)

            # ファイル名を設定
            suffix_parts = []
            if year:
                suffix_parts.append(str(year))
            if race_id:
                suffix_parts.append(str(race_id))
            suffix = f"_{'_'.join(suffix_parts)}" if suffix_parts else ""

            horse_ids_csv = os.path.join(csv_dir, f"extracted_horse_ids{suffix}.csv")

            # DataFrameに変換して保存
            pd.DataFrame({"horse_id": horse_ids}).to_csv(horse_ids_csv, index=False)
            self.logger.info(f"馬IDをCSVとして保存しました: {horse_ids_csv}")

        return horse_ids

    def process_race_bin_files(self, year: Optional[str] = None, race_id: Optional[str] = None,
                               parallel: bool = True, max_workers: Optional[int] = None,
                               max_files: Optional[int] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        指定された年またはレースIDのレース情報binファイルを処理し、
        レース情報とレース結果のDataFrameを生成する。

        Parameters
        ----------
        year : str, optional
            処理する年。Noneの場合はrace_idが必須。
        race_id : str, optional
            処理する特定のレースID。Noneの場合はyearが必須。
        parallel : bool, default True
            並列処理を使用するかどうか。
        max_workers : int, optional
            並列処理の最大ワーカー数。Noneの場合はCPUコア数。
        max_files : int, optional
            処理するファイルの最大数（年度指定時）。Noneの場合は全ファイルを処理。

        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            (レース情報のDataFrame, レース結果のDataFrame)
        """
        self.logger.info(f"レースbinファイルの処理を開始: year={year}, race_id={race_id}")

        # HTMLファイルパスのリストを取得
        html_path_list = self._get_html_file_paths(year, race_id, max_files)

        if not html_path_list:
            # _get_html_file_paths 内でログ出力済み
            return pd.DataFrame(), pd.DataFrame()

        all_race_info = []
        all_race_results = []

        if not max_workers:
            max_workers = os.cpu_count() or 1

        if parallel and len(html_path_list) > 1:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(self._html_parser.parse_race_html, path): path for path in html_path_list}
                for future in tqdm(as_completed(futures), total=len(html_path_list), desc="レースHTMLパース(並列)", leave=False):
                    path_completed = futures[future]
                    try:
                        info_df, results_df = future.result()
                        if not info_df.empty:
                            all_race_info.append(info_df)
                        if not results_df.empty:
                            all_race_results.append(results_df)
                    except Exception as e:
                        self.logger.error(f"ファイル {path_completed} のパース中にエラー: {e}", exc_info=True)
        else:
            for path in tqdm(html_path_list, desc="レースHTMLパース(逐次)", leave=False):
                try:
                    info_df, results_df = self._html_parser.parse_race_html(path)
                    if not info_df.empty:
                        all_race_info.append(info_df)
                    if not results_df.empty:
                        all_race_results.append(results_df)
                except Exception as e:
                    self.logger.error(f"ファイル {path} のパース中にエラー: {e}", exc_info=True)

        # 結果を結合して返す
        race_info_df = pd.concat(all_race_info, ignore_index=True) if all_race_info else pd.DataFrame()
        race_results_df = pd.concat(all_race_results, ignore_index=True) if all_race_results else pd.DataFrame()

        self.logger.info(f"レースbinファイルの処理完了。レース情報: {len(race_info_df)}件, レース結果: {len(race_results_df)}件")

        return race_info_df, race_results_df

    def _save_batch_results_to_pickle(self, race_info_df: pd.DataFrame,
                                     race_results_df: pd.DataFrame,
                                     corner_features_df: Optional[pd.DataFrame],
                                     year: Optional[str] = None,
                                     race_id: Optional[str] = None,
                                     pickle_dir: Optional[str] = None) -> None:
        """
        バッチ処理結果をpickleファイルに保存する

        Parameters
        ----------
        race_info_df : pd.DataFrame
            レース情報DataFrame
        race_results_df : pd.DataFrame
            レース結果DataFrame
        corner_features_df : Optional[pd.DataFrame]
            コーナー特徴量DataFrame
        year : str, optional
            年
        race_id : str, optional
            レースID
        pickle_dir : str, optional
            保存先ディレクトリ
        """
        if pickle_dir is None:
            pickle_dir = os.path.join(LocalPaths.DATA_DIR, "processed")

        os.makedirs(pickle_dir, exist_ok=True)

        # ファイル名のサフィックスを決定
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        suffix = ""
        if race_id:
            suffix = f"_{race_id}"
        elif year:
            suffix = f"_{year}"

        # レース情報を保存
        if not race_info_df.empty:
            info_filename = f"race_info{suffix}_{timestamp}.pickle"
            info_path = os.path.join(pickle_dir, info_filename)
            race_info_df.to_pickle(info_path)
            self.logger.info(f"レース情報をpickleファイルに保存: {info_path}")

        # レース結果を保存
        if not race_results_df.empty:
            results_filename = f"race_results{suffix}_{timestamp}.pickle"
            results_path = os.path.join(pickle_dir, results_filename)
            race_results_df.to_pickle(results_path)
            self.logger.info(f"レース結果をpickleファイルに保存: {results_path}")

        # コーナー特徴量を保存
        if corner_features_df is not None and not corner_features_df.empty:
            corner_filename = f"corner_features{suffix}_{timestamp}.pickle"
            corner_path = os.path.join(pickle_dir, corner_filename)
            corner_features_df.to_pickle(corner_path)
            self.logger.info(f"コーナー特徴量をpickleファイルに保存: {corner_path}")

    def save_race_data_to_csv(self, race_info_df: pd.DataFrame, race_results_df: pd.DataFrame,
                              year: Optional[str] = None, race_id: Optional[str] = None,
                              prefix: str = "race_data") -> None:
        """
        処理されたレース情報と結果をCSVファイルに保存する。
        ファイル名には年またはレースID、およびタイムスタンプが含まれる。

        Parameters
        ----------
        race_info_df : pd.DataFrame
            レース情報DataFrame
        race_results_df : pd.DataFrame
            レース結果DataFrame
        year : str, optional
            ファイル名に含める年。
        race_id : str, optional
            ファイル名に含めるレースID。yearより優先される。
        prefix : str, default "race_data"
            ファイル名のプレフィックス。
        """
        if race_info_df.empty and race_results_df.empty:
            self.logger.warning("保存するデータがありません。")
            return

        # 保存先ディレクトリを作成
        csv_dir = os.path.join(LocalPaths.DATA_DIR, "csv")
        os.makedirs(csv_dir, exist_ok=True)

        # ファイル名を生成
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        suffix = ""
        if race_id:
            suffix = f"_{race_id}"
        elif year:
            suffix = f"_{year}"

        # レース情報を保存
        if not race_info_df.empty:
            info_filename = f"{prefix}_info{suffix}_{timestamp}.csv"
            info_path = os.path.join(csv_dir, info_filename)
            race_info_df.to_csv(info_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"レース情報をCSVファイルに保存しました: {info_path}")

        # レース結果を保存
        if not race_results_df.empty:
            results_filename = f"{prefix}_results{suffix}_{timestamp}.csv"
            results_path = os.path.join(csv_dir, results_filename)
            race_results_df.to_csv(results_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"レース結果をCSVファイルに保存しました: {results_path}")

    def process_race_pickle_files(self,
                                 year: Optional[str] = None,
                                 race_id: Optional[str] = None,
                                 pickle_base_dir: str = os.path.join(LocalPaths.DATA_DIR, "processed"),
                                 race_info_filename_pattern: str = "race_info_{year}.pickle",
                                 race_results_filename_pattern: str = "race_results_{year}.pickle",
                                 corner_features_filename_pattern: str = "corner_features_{year}.pickle" # コーナー特徴量ファイルパターンを追加
                                 ) -> Tuple[pd.DataFrame, pd.DataFrame, Optional[pd.DataFrame]]: # 戻り値にコーナー特徴量DataFrameを追加
        """
        指定された年またはレースIDのレース情報pickleファイルを処理し、
        レース情報、レース結果、およびオプションでコーナー特徴量のDataFrameを返す。

        Parameters
        ----------
        year : str, optional
            処理する年。race_idが指定されていない場合は必須。
        race_id : str, optional
            処理する特定のレースID。指定された場合、yearのデータからフィルタリングする。
        pickle_base_dir : str, optional
            pickleファイルが保存されているベースディレクトリ。
            デフォルトは LocalPaths.DATA_DIR / "processed"
        race_info_filename_pattern : str, optional
            レース情報pickleファイル名のパターン。'{year}'プレースホルダを含む。
            デフォルトは "race_info_{year}.pickle"
        race_results_filename_pattern : str, optional
            レース結果pickleファイル名のパターン。'{year}'プレースホルダを含む。
            デフォルトは "race_results_{year}.pickle"
        corner_features_filename_pattern : str, optional
            コーナー特徴量pickleファイル名のパターン。'{year}'プレースホルダを含む。
            デフォルトは "corner_features_{year}.pickle"

        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame, Optional[pd.DataFrame]]
            (レース情報のDataFrame, レース結果のDataFrame, コーナー特徴量のDataFrame または None)
        """
        self.logger.info(f"レースpickleファイルの処理を開始: year={year}, race_id={race_id}, base_dir={pickle_base_dir}")

        if not year and not race_id:
            self.logger.error("yearまたはrace_idのどちらかを指定する必要があります。")
            return pd.DataFrame(), pd.DataFrame(), None

        target_year = year
        if race_id and not year:
            target_year = race_id[:4]
            self.logger.info(f"race_idから年を抽出: {target_year}")
        elif race_id and year and year != race_id[:4]:
            self.logger.warning(f"指定されたyear ({year}) と race_id ({race_id}) の年が異なります。race_idの年 ({race_id[:4]}) を使用します。")
            target_year = race_id[:4]

        if not target_year:
            self.logger.error("処理対象の年を特定できませんでした。")
            return pd.DataFrame(), pd.DataFrame(), None

        info_file_path_pattern = os.path.join(pickle_base_dir, race_info_filename_pattern.format(year=target_year))
        results_file_path_pattern = os.path.join(pickle_base_dir, race_results_filename_pattern.format(year=target_year))
        corner_file_path_pattern = os.path.join(pickle_base_dir, corner_features_filename_pattern.format(year=target_year))

        info_files = glob.glob(info_file_path_pattern)
        results_files = glob.glob(results_file_path_pattern)
        corner_files = glob.glob(corner_file_path_pattern)

        race_info_df = pd.DataFrame()
        race_results_df = pd.DataFrame()
        corner_features_df = None

        if not info_files:
            self.logger.warning(f"レース情報pickleファイルが見つかりません: {info_file_path_pattern}")
        else:
            info_file_path = info_files[0] # 最初に見つかったファイルを使用
            try:
                self.logger.info(f"レース情報pickleファイルを読み込み中: {info_file_path}")
                race_info_df = pd.read_pickle(info_file_path)
                self.logger.info(f"レース情報読み込み完了: {len(race_info_df)}件")
            except Exception as e:
                self.logger.error(f"レース情報pickleファイルの読み込みに失敗: {info_file_path}, エラー: {e}", exc_info=True)

        if not results_files:
            self.logger.warning(f"レース結果pickleファイルが見つかりません: {results_file_path_pattern}")
        else:
            results_file_path = results_files[0] # 最初に見つかったファイルを使用
            try:
                self.logger.info(f"レース結果pickleファイルを読み込み中: {results_file_path}")
                race_results_df = pd.read_pickle(results_file_path)
                self.logger.info(f"レース結果読み込み完了: {len(race_results_df)}件")
            except Exception as e:
                self.logger.error(f"レース結果pickleファイルの読み込みに失敗: {results_file_path}, エラー: {e}", exc_info=True)

        if corner_files:
             corner_file_path = corner_files[0] # 最初に見つかったファイルを使用
             try:
                 self.logger.info(f"コーナー特徴量pickleファイルを読み込み中: {corner_file_path}")
                 corner_features_df = pd.read_pickle(corner_file_path)
                 self.logger.info(f"コーナー特徴量読み込み完了: {len(corner_features_df)}件")
             except Exception as e:
                 self.logger.error(f"コーナー特徴量pickleファイルの読み込みに失敗: {corner_file_path}, エラー: {e}", exc_info=True)

        # race_id が指定されている場合はフィルタリング
        if race_id:
            if not race_info_df.empty and 'race_id' in race_info_df.columns:
                race_info_df = race_info_df[race_info_df['race_id'] == race_id].copy()
            if not race_results_df.empty and 'race_id' in race_results_df.columns:
                race_results_df = race_results_df[race_results_df['race_id'] == race_id].copy()
            if corner_features_df is not None and not corner_features_df.empty and 'race_id' in corner_features_df.columns:
                 corner_features_df = corner_features_df[corner_features_df['race_id'] == race_id].copy()

        return race_info_df, race_results_df, corner_features_df

    def _process_file_for_yearly_pickles(self, html_path: str, include_corner_features: bool, corner_imputation_strategy: str) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """
        単一のHTMLファイルを処理し、レース情報、レース結果、およびオプションでコーナー特徴量を返す。
        process_race_bin_to_yearly_pickles のためのヘルパー関数。
        """
        try:
            # 1. レース情報と結果のパース
            info_df, results_df = self._html_parser.parse_race_html(html_path)

            corner_features_df = None
            if include_corner_features:
                # 2. コーナー特徴量の抽出
                # Note: 現状の CornerAnalyzer.extract_corner_features_from_html_file は内部で再度HTMLパースを行う。
                #       これを避けるには CornerAnalyzer 側の改修が必要。
                temp_corner_df = self._corner_analyzer.extract_corner_features_from_html_file(html_path)
                if temp_corner_df is not None and not temp_corner_df.empty:
                    if corner_imputation_strategy != "none":
                        self.logger.debug(f"コーナー特徴量の欠損値補完を実行: {corner_imputation_strategy} for {html_path}")
                        temp_corner_df = self._corner_analyzer.apply_corner_imputation(temp_corner_df, corner_imputation_strategy)
                    corner_features_df = temp_corner_df
            
            return info_df, results_df, corner_features_df
        except Exception as e:
            self.logger.error(f"ファイル {html_path} の年間pickle処理中にエラー: {e}", exc_info=True)
            return None, None, None

    def process_race_bin_to_yearly_pickles(self,
                                           years: List[str],
                                           bin_base_dir: Optional[str] = None,
                                           output_dir: Optional[str] = None,
                                           parallel: bool = True,
                                           max_workers: Optional[int] = None,
                                           max_files_per_year: Optional[int] = None,
                                           include_corner_features: bool = False,
                                           corner_imputation_strategy: str = "none"
                                           ) -> Tuple[pd.DataFrame, pd.DataFrame, Optional[pd.DataFrame]]:
        """
        指定された複数年のレース情報binファイルを処理し、年ごとにレース情報、
        レース結果、およびオプションでコーナー特徴量のDataFrameをpickleファイルとして保存する。

        Parameters
        ----------
        years : List[str]
            処理する年のリスト。
        bin_base_dir : str, optional
            .binファイルが格納されているベースディレクトリ。
            デフォルトは LocalPaths.HTML_RACE_DIR。
        output_dir : str, optional
            pickleファイルの保存先ディレクトリ。
            デフォルトは LocalPaths.DATA_DIR / "processed"。
        parallel : bool, default True
            並列処理を使用するかどうか。
        max_workers : int, optional
            並列処理の最大ワーカー数。Noneの場合はCPUコア数。
        max_files_per_year : int, optional
            各年で処理する最大のファイル数。Noneの場合は全ファイルを処理。
        include_corner_features : bool, default True
            コーナー特徴量を生成し保存するかどうか。
        corner_imputation_strategy : str, default "none"
            コーナー特徴量の欠損値補完戦略。
        """
        self.logger.info(f"指定年のbinファイルから年単位のpickleファイルへのバッチ処理を開始: {years}, コーナー特徴量処理: {include_corner_features}")

        # bin_base_dir の決定: 引数指定 > config['data_dir'] > デフォルトパス
        if bin_base_dir is None:
            bin_base_dir = self._config.get('data_dir', LocalPaths.HTML_RACE_DIR)
        # output_dir の決定: 引数指定 > config['output_dir'] > デフォルトパス
        if output_dir is None:
            output_dir = self._config.get('output_dir', os.path.join(LocalPaths.DATA_DIR, "processed"))

        os.makedirs(output_dir, exist_ok=True)

        if not max_workers:
            max_workers = os.cpu_count() or 1

        # 全年の結果を格納するリストを初期化
        all_years_race_info_dfs: List[pd.DataFrame] = []
        all_years_race_results_dfs: List[pd.DataFrame] = []
        all_years_corner_features_dfs: List[pd.DataFrame] = []
        for year in years:
            self.logger.info(f"{year}年のbinファイル処理を開始")

            # 年ごとのbinファイルパスを取得
            # _get_html_file_paths は LocalPaths.HTML_RACE_DIR を基準にするため、
            # bin_base_dir が指定されている場合は、globを直接使用する方が柔軟性がある。
            # ここでは、指定された bin_base_dir を優先する。
            current_year_path_pattern = ""
            if bin_base_dir:
                # パターン1: bin_base_dir/year/*.bin
                path_candidate1 = os.path.join(bin_base_dir, str(year), "*.bin")
                # パターン2: bin_base_dir/race_by_year/year/*.bin (一般的な構造を考慮)
                path_candidate2 = os.path.join(bin_base_dir, "race_by_year", str(year), "*.bin")
                
                bin_files = glob.glob(path_candidate1)
                if not bin_files: # パターン1で見つからなければパターン2を試す
                    bin_files = glob.glob(path_candidate2)
                current_year_path_pattern = f"{path_candidate1} または {path_candidate2}"
            else: # bin_base_dir がNoneの場合は _get_html_file_paths を利用
                bin_files = self._get_html_file_paths(year=year) # max_files は後で適用
                current_year_path_pattern = f"_get_html_file_paths(year={year}) の結果"

            if not bin_files:
                self.logger.warning(f"{year}年のbinファイルが見つかりません。検索パターン/メソッド: {current_year_path_pattern}")
                continue

            if max_files_per_year is not None and len(bin_files) > max_files_per_year:
                self.logger.info(f"{year}年の処理ファイル数を {max_files_per_year} に制限します (元ファイル数: {len(bin_files)})。")
                bin_files = bin_files[:max_files_per_year]

            year_race_info_list: List[pd.DataFrame] = []
            year_race_results_list: List[pd.DataFrame] = []
            year_corner_features_list: List[pd.DataFrame] = []

            if parallel and len(bin_files) > 1:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = {
                        executor.submit(self._process_file_for_yearly_pickles, path, include_corner_features, corner_imputation_strategy): path
                        for path in bin_files
                    }
                    for future in tqdm(as_completed(futures), total=len(bin_files), desc=f"{year}年ファイル処理(並列)", leave=False):
                        path_completed = futures[future]
                        try:
                            info_df, results_df, corner_df = future.result()
                            if info_df is not None and not info_df.empty: year_race_info_list.append(info_df)
                            if results_df is not None and not results_df.empty: year_race_results_list.append(results_df)
                            if corner_df is not None and not corner_df.empty: year_corner_features_list.append(corner_df)
                        except Exception as e:
                            self.logger.error(f"ファイル {path_completed} の処理結果取得中にエラー: {e}", exc_info=True)
            else:
                for path in tqdm(bin_files, desc=f"{year}年ファイル処理(逐次)", leave=False):
                    info_df, results_df, corner_df = self._process_file_for_yearly_pickles(path, include_corner_features, corner_imputation_strategy)
                    if info_df is not None and not info_df.empty: year_race_info_list.append(info_df)
                    if results_df is not None and not results_df.empty: year_race_results_list.append(results_df)
                    if corner_df is not None and not corner_df.empty: year_corner_features_list.append(corner_df)

            # 年ごとのDataFrameを結合し、全年のリストに追加
            if year_race_info_list:
                year_race_info_df = pd.concat(year_race_info_list, ignore_index=True)
                year_race_info_df.to_pickle(os.path.join(output_dir, f'race_info_{year}.pickle'))
                self.logger.info(f'race_info_{year}.pickle を保存 ({len(year_race_info_df)}レース分)')
                all_years_race_info_dfs.append(year_race_info_df)

            if year_race_results_list:
                year_race_results_df = pd.concat(year_race_results_list, ignore_index=True)
                year_race_results_df.to_pickle(os.path.join(output_dir, f'race_results_{year}.pickle'))
                self.logger.info(f'race_results_{year}.pickle を保存 ({len(year_race_results_df)}行)')
                all_years_race_results_dfs.append(year_race_results_df)

            if year_corner_features_list: # include_corner_features が True の場合のみリストに要素が入る
                year_corner_features_df = pd.concat(year_corner_features_list, ignore_index=True)
                year_corner_features_df.to_pickle(os.path.join(output_dir, f'corner_features_{year}.pickle'))
                self.logger.info(f'corner_features_{year}.pickle を保存 ({len(year_corner_features_df)}行)')
                all_years_corner_features_dfs.append(year_corner_features_df)
            elif include_corner_features:
                 self.logger.info(f"{year}年のコーナー特徴量データは生成されませんでした（リストが空）。")

        self.logger.info('全ての年の処理が完了しました。')

        # 全年のDataFrameを結合して返す
        final_race_info_df = pd.concat(all_years_race_info_dfs, ignore_index=True) if all_years_race_info_dfs else pd.DataFrame()
        final_race_results_df = pd.concat(all_years_race_results_dfs, ignore_index=True) if all_years_race_results_dfs else pd.DataFrame()
        final_corner_features_df = pd.concat(all_years_corner_features_dfs, ignore_index=True) if all_years_corner_features_dfs else None

        return final_race_info_df, final_race_results_df, final_corner_features_df