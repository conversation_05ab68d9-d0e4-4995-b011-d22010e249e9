#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
モデル解釈システム統合テスト
完全に動作することを確認
"""

import sys
sys.path.append('.')

import pandas as pd
import numpy as np
import pickle
import logging
from pathlib import Path
from core.analysis.model_explainer import ModelExplainer
from enhanced_live_predictor import EnhancedLiveRacePredictor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def full_integration_test():
    """完全統合テスト: 実際の予想データでモデル解釈"""
    
    print("=" * 80)
    print("モデル解釈システム 完全統合テスト")
    print("=" * 80)
    
    try:
        # 1. 実際のレース予想を実行
        print("🏇 実際のレース予想を実行中...")
        race_id = "202505021211"
        
        predictor = EnhancedLiveRacePredictor(use_selenium=False)
        predictor.load_latest_model()
        results_df, race_info = predictor.predict_race(race_id)
        
        if results_df.empty:
            print("❌ レース予想に失敗")
            return False
        
        print(f"✅ レース予想成功: {len(results_df)}頭")
        
        # 2. ModelExplainerの初期化
        print("\n🔍 ModelExplainer初期化...")
        explainer = ModelExplainer(
            model=predictor.model, 
            features=predictor.features,
            output_dir="model_interpretation_results"
        )
        
        if explainer.explainer is None:
            print("❌ ModelExplainer初期化失敗")
            return False
        
        print("✅ ModelExplainer初期化成功")
        
        # 3. 実際の特徴量データで解釈実行
        print("\n🧠 予想根拠の解釈実行...")
        
        # 馬名取得
        horse_names = []
        for _, row in results_df.iterrows():
            horse_name = row.get('馬名', f"馬{row['馬番']}")
            horse_names.append(horse_name)
        
        # 特徴量データ取得（predictorの最後の入力データ）
        if hasattr(predictor, 'last_X') and predictor.last_X is not None:
            X_data = predictor.last_X
            print(f"  📊 特徴量データ: {X_data.shape}")
        else:
            print("❌ 特徴量データが取得できません")
            return False
        
        # 解釈実行
        explanation_result = explainer.explain_predictions(X_data, horse_names)
        
        if not explanation_result:
            print("❌ 解釈実行失敗")
            return False
        
        print("✅ 解釈実行成功")
        
        # 4. 詳細な結果確認
        print("\n📊 解釈結果の詳細確認:")
        
        # 特徴量重要度
        if 'feature_importance' in explanation_result:
            importance = explanation_result['feature_importance']
            top_features = importance.get('top_features', [])
            print(f"  • 特徴量重要度分析: {len(top_features)}個")
            
            if top_features:
                print(f"    最重要特徴量:")
                for i, feature in enumerate(top_features[:3]):
                    print(f"      {i+1}. {feature['feature']}: {feature['importance']:.4f}")
        
        # 個別説明
        if 'individual_explanations' in explanation_result:
            individual = explanation_result['individual_explanations']
            print(f"  • 個別予想根拠: {len(individual)}頭分")
            
            if individual:
                print(f"    上位3頭の予想根拠:")
                for i, exp in enumerate(individual[:3]):
                    horse_name = exp['horse_name']
                    reason = exp['overall_prediction_reason']
                    print(f"      {i+1}. {horse_name}: {reason}")
        
        # モデル洞察
        if 'model_insights' in explanation_result:
            insights = explanation_result['model_insights']
            complexity = insights.get('model_complexity', 0)
            stability = insights.get('prediction_stability', 0)
            most_influential = insights.get('most_influential_feature', '')
            
            print(f"  • モデル分析:")
            print(f"    複雑度: {complexity}個の有効特徴量")
            print(f"    安定性: {stability:.4f}")
            print(f"    最重要要因: {most_influential}")
        
        # 5. 可視化生成
        print(f"\n🎨 可視化生成...")
        visualization_files = explainer.create_explanation_visualizations(explanation_result, X_data)
        
        if visualization_files:
            print(f"✅ 可視化成功: {len(visualization_files)}個のファイル")
            for viz_type, file_path in visualization_files.items():
                print(f"  • {viz_type}: {file_path}")
        else:
            print("⚠️ 可視化ファイル生成なし")
        
        # 6. 統合レポート生成
        print(f"\n📄 統合レポート生成...")
        report = explainer.generate_explanation_report(explanation_result)
        
        if report:
            report_file = Path("model_interpretation_results/integrated_explanation_report.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"✅ レポート保存: {report_file}")
            
            # レポート要約表示
            lines = report.split('\n')
            summary_lines = [line for line in lines if '最重要特徴量' in line or '予想根拠' in line or '複雑度' in line]
            if summary_lines:
                print(f"  📝 レポート要約:")
                for line in summary_lines[:5]:
                    print(f"    {line.strip()}")
        
        # 7. 最終検証
        print(f"\n✅ 完全統合テスト成功!")
        print(f"  • 予想: {len(results_df)}頭の競走馬")
        print(f"  • 解釈: {len(explanation_result.get('individual_explanations', []))}頭分の根拠")
        print(f"  • 可視化: {len(visualization_files)}個のグラフ")
        print(f"  • レポート: 詳細解釈レポート生成済み")
        
        return True
        
    except Exception as e:
        print(f"❌ 統合テスト中にエラー: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_system_status_report():
    """システム状態レポート生成"""
    
    print(f"\n" + "=" * 80)
    print("⚙️ システム評価 - モデル解釈機能")
    print("=" * 80)
    
    # 各コンポーネントの状態チェック
    status_items = []
    
    # 1. ライブラリチェック
    try:
        import shap, matplotlib, seaborn, plotly, japanize_matplotlib
        status_items.append(("📚 必要ライブラリ", "✅ 全て利用可能"))
    except ImportError as e:
        status_items.append(("📚 必要ライブラリ", f"❌ 不足: {e}"))
    
    # 2. モデルファイルチェック
    model_files = list(Path("models").glob("*leakage_model*.pkl"))
    if model_files:
        status_items.append(("🤖 モデルファイル", "✅ 利用可能"))
    else:
        status_items.append(("🤖 モデルファイル", "❌ 見つからない"))
    
    # 3. 特徴量ファイルチェック
    feature_files = list(Path("models").glob("*leakage_features*.pkl"))
    if feature_files:
        status_items.append(("🔧 特徴量定義", "✅ 利用可能"))
    else:
        status_items.append(("🔧 特徴量定義", "❌ 見つからない"))
    
    # 4. 出力ディレクトリチェック
    try:
        Path("model_interpretation_results").mkdir(exist_ok=True)
        status_items.append(("📁 出力ディレクトリ", "✅ 書き込み可能"))
    except Exception:
        status_items.append(("📁 出力ディレクトリ", "❌ 権限エラー"))
    
    # 5. 統合テスト結果
    integration_success = full_integration_test()
    if integration_success:
        status_items.append(("🔄 統合動作テスト", "✅ 正常動作"))
    else:
        status_items.append(("🔄 統合動作テスト", "❌ 動作異常"))
    
    # レポート出力
    print(f"\n📋 システム状態:")
    print("-" * 60)
    
    all_ok = True
    for component, status in status_items:
        print(f"{component:>20}: {status}")
        if "❌" in status:
            all_ok = False
    
    print(f"\n🎯 総合評価:")
    if all_ok:
        print("✅ モデル解釈システム: 完全動作")
        print("   制限事項なし - 全機能が利用可能です")
    else:
        print("⚠️ モデル解釈システム: 一部制限")
        print("   上記の❌項目の解決が必要です")
    
    print("=" * 80)
    
    return all_ok

def main():
    """メイン実行"""
    
    print("モデル解釈システムの最終検証を開始します...")
    
    # 完全統合テストと状態レポート
    system_ok = generate_system_status_report()
    
    if system_ok:
        print(f"\n🎉 おめでとうございます！")
        print("モデル解釈システムは完全に動作しています。")
        print("✅ AI予想システム: 正常動作")
        print("✅ 分析エンジン: 正常動作") 
        print("✅ 可視化システム: 正常動作")
        print("✅ 解説システム: 正常動作")
        print("✅ モデル解釈: 完全動作")  # ← この部分が修正されました
        print("✅ 結果検証: 完了")
    else:
        print(f"\n⚠️ システムに一部制限があります。")
        print("上記の指示に従って修正してください。")

if __name__ == "__main__":
    main()