#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
クイック複数年モデルテスト（修正版）

データパスの問題を修正し、シンプルな複数年比較テストを実行
"""

import os
import sys
import json
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 機械学習関連
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import joblib
import pickle

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickMultiYearTester:
    """クイック複数年モデルテスト"""
    
    def __init__(self):
        """初期化"""
        self.base_path = "/mnt/h/AI/keiba_ai_system/output"
        self.available_years = self._check_available_years()
        self.results = {}
        
        logger.info(f"利用可能年度: {self.available_years}")
    
    def _check_available_years(self) -> List[str]:
        """利用可能な年度をチェック"""
        available = []
        for year in range(2008, 2025):  # 2008年から開始
            year_str = str(year)
            race_info_path = f"{self.base_path}/race_info_{year_str}.pickle"
            race_results_path = f"{self.base_path}/race_results_{year_str}.pickle"
            
            if os.path.exists(race_info_path) and os.path.exists(race_results_path):
                available.append(year_str)
        
        return available
    
    def load_year_data_simple(self, year: str, sample_rate: float = 0.3) -> pd.DataFrame:
        """
        シンプルな年度データ読み込み
        
        Parameters
        ----------
        year : str
            年度
        sample_rate : float
            サンプリング率
            
        Returns
        -------
        pd.DataFrame
            読み込まれたデータ
        """
        try:
            logger.info(f"年度 {year} のデータ読み込み中...")
            
            # レース結果データの読み込み
            results_path = f"{self.base_path}/race_results_{year}.pickle"
            with open(results_path, 'rb') as f:
                race_results = pickle.load(f)
            
            if race_results.empty:
                logger.warning(f"年度 {year} のレース結果が空です")
                return pd.DataFrame()
            
            # サンプリング
            if sample_rate < 1.0:
                n_samples = int(len(race_results) * sample_rate)
                race_results = race_results.sample(n=n_samples, random_state=42)
            
            logger.info(f"年度 {year}: {len(race_results):,}件")
            return race_results
            
        except Exception as e:
            logger.error(f"年度 {year} の読み込みエラー: {e}")
            return pd.DataFrame()
    
    def prepare_features_simple(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
        """
        シンプルな特徴量準備
        
        Parameters
        ----------
        data : pd.DataFrame
            元データ
            
        Returns
        -------
        Tuple[pd.DataFrame, pd.Series]
            特徴量とターゲット
        """
        # ターゲット変数（1着かどうか）
        if '着順' not in data.columns:
            raise ValueError("着順カラムが見つかりません")
        
        target = (pd.to_numeric(data['着順'], errors='coerce') == 1).astype(int)
        
        # 基本特徴量のみを使用
        feature_columns = []
        
        # 数値特徴量
        numeric_features = ['馬番', '枠番', '斤量', '人気']
        for col in numeric_features:
            if col in data.columns:
                feature_columns.append(col)
        
        # 性齢から年齢を抽出
        if '性齢' in data.columns:
            age_series = data['性齢'].str.extract(r'(\d+)')[0]
            data['年齢_extracted'] = pd.to_numeric(age_series, errors='coerce')
            feature_columns.append('年齢_extracted')
        
        # 調教師をエンコーディング
        if '調教師' in data.columns:
            le_trainer = LabelEncoder()
            data['調教師_encoded'] = le_trainer.fit_transform(data['調教師'].fillna('UNKNOWN'))
            feature_columns.append('調教師_encoded')
        
        # 騎手をエンコーディング
        if '騎手' in data.columns:
            le_jockey = LabelEncoder()
            data['騎手_encoded'] = le_jockey.fit_transform(data['騎手'].fillna('UNKNOWN'))
            feature_columns.append('騎手_encoded')
        
        # 特徴量データの準備
        features = data[feature_columns].fillna(0)
        
        # 有効データのフィルタリング
        valid_mask = target.notna() & features.notna().all(axis=1) & (target != -1)
        features = features[valid_mask]
        target = target[valid_mask]
        
        logger.info(f"特徴量: {len(features)}行 × {len(features.columns)}列")
        logger.info(f"ターゲット分布: 0={sum(target==0)}, 1={sum(target==1)} (勝率: {sum(target==1)/len(target):.3f})")
        
        return features, target
    
    def train_evaluate_simple(self, train_years: List[str], test_year: str, sample_rate: float = 0.2) -> Dict[str, Any]:
        """
        シンプルなモデル学習・評価
        
        Parameters
        ----------
        train_years : List[str]
            学習用年度
        test_year : str
            テスト用年度
        sample_rate : float
            サンプリング率
            
        Returns
        -------
        Dict[str, Any]
            評価結果
        """
        try:
            logger.info(f"学習: {train_years} → テスト: {test_year}")
            
            # 学習データの準備
            train_data_list = []
            for year in train_years:
                if year in self.available_years:
                    year_data = self.load_year_data_simple(year, sample_rate)
                    if not year_data.empty:
                        train_data_list.append(year_data)
            
            if not train_data_list:
                raise ValueError("学習データが空です")
            
            train_data = pd.concat(train_data_list, ignore_index=True)
            
            # テストデータの準備
            if test_year not in self.available_years:
                raise ValueError(f"テスト年度 {test_year} が利用できません")
            
            test_data = self.load_year_data_simple(test_year, sample_rate)
            if test_data.empty:
                raise ValueError("テストデータが空です")
            
            # 特徴量準備
            X_train, y_train = self.prepare_features_simple(train_data)
            X_test, y_test = self.prepare_features_simple(test_data)
            
            # データが不足している場合はスキップ
            if len(X_train) < 100 or len(X_test) < 50:
                raise ValueError(f"データ不足: 学習={len(X_train)}, テスト={len(X_test)}")
            
            # 特徴量の標準化
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # LightGBMモデルの学習
            lgb_params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42
            }
            
            # データセット作成
            train_dataset = lgb.Dataset(X_train_scaled, label=y_train)
            
            # モデル学習
            model = lgb.train(
                lgb_params,
                train_dataset,
                num_boost_round=100,
                valid_sets=[train_dataset],
                callbacks=[lgb.early_stopping(stopping_rounds=10), lgb.log_evaluation(0)]
            )
            
            # 予測
            y_pred_proba = model.predict(X_test_scaled, num_iteration=model.best_iteration)
            y_pred = (y_pred_proba > 0.5).astype(int)
            
            # 評価指標の計算
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            f1 = f1_score(y_test, y_pred, zero_division=0)
            
            try:
                auc = roc_auc_score(y_test, y_pred_proba)
            except ValueError:
                auc = 0.5
            
            # 結果をまとめ
            result = {
                'train_years': train_years,
                'test_year': test_year,
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'train_win_rate': float(y_train.mean()),
                'test_win_rate': float(y_test.mean()),
                'accuracy': float(accuracy),
                'precision': float(precision),
                'recall': float(recall),
                'f1_score': float(f1),
                'auc_score': float(auc),
                'feature_names': list(X_train.columns)
            }
            
            logger.info(f"評価完了 - AUC: {auc:.3f}, F1: {f1:.3f}, Accuracy: {accuracy:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"モデル学習・評価エラー: {e}")
            return {
                'train_years': train_years,
                'test_year': test_year,
                'error': str(e),
                'accuracy': 0.0,
                'auc_score': 0.0,
                'f1_score': 0.0
            }
    
    def run_comprehensive_test(self, sample_rate: float = 0.15) -> Dict[str, Any]:
        """
        包括的なテストを実行
        
        Parameters
        ----------
        sample_rate : float
            サンプリング率
            
        Returns
        -------
        Dict[str, Any]
            全テスト結果
        """
        logger.info("=== クイック複数年モデルテスト開始 ===")
        
        test_summary = {
            'test_time': datetime.now().isoformat(),
            'available_years': self.available_years,
            'sample_rate': sample_rate,
            'results': [],
            'best_models': {},
            'recommendations': []
        }
        
        # テストパターンの定義（2008年からの拡張版）
        if len(self.available_years) >= 8:
            test_patterns = [
                # 単年学習
                ([self.available_years[-2]], self.available_years[-1], '直前1年'),
                ([self.available_years[-3]], self.available_years[-1], '2年前単独'),
                ([self.available_years[-6]], self.available_years[-1], '5年前単独'),
                
                # 短期間学習（2-3年）
                (self.available_years[-3:-1], self.available_years[-1], '直前2年'),
                (self.available_years[-4:-1], self.available_years[-1], '直前3年'),
                
                # 中期間学習（5-7年）
                (self.available_years[-6:-1], self.available_years[-1], '直前5年'),
                (self.available_years[-8:-1], self.available_years[-1], '直前7年'),
                
                # 長期間学習（10年以上）
                (self.available_years[-11:-1], self.available_years[-1], '直前10年'),
                (self.available_years[:-1], self.available_years[-1], '全期間（2008-2023）'),
                
                # 特定期間組み合わせ
                (self.available_years[8:13], self.available_years[-1], '中期（2016-2020）'),
                (self.available_years[13:16], self.available_years[-1], '近年（2021-2023）'),
            ]
        elif len(self.available_years) >= 3:
            test_patterns = [
                ([self.available_years[-2]], self.available_years[-1], '直前1年'),
                ([self.available_years[-3]], self.available_years[-1], '2年前単独'),
                (self.available_years[-3:-1], self.available_years[-1], '直前2年'),
                (self.available_years[-4:-1], self.available_years[-1], '直前3年'),
            ]
        else:
            logger.warning("テストに十分な年度データがありません")
            return test_summary
        
        # 最新年度をテスト用に設定
        test_year = self.available_years[-1]
        
        best_auc = 0
        
        for train_years, test_yr, description in test_patterns:
            # 利用可能な年度のみを使用
            available_train_years = [y for y in train_years if y in self.available_years]
            
            if not available_train_years:
                logger.warning(f"スキップ: {description} (データなし)")
                continue
            
            logger.info(f"\n--- {description} ---")
            
            # モデル学習・評価
            result = self.train_evaluate_simple(
                train_years=available_train_years,
                test_year=test_yr,
                sample_rate=sample_rate
            )
            
            result['description'] = description
            test_summary['results'].append(result)
            
            # ベストモデルの更新
            if result.get('auc_score', 0) > best_auc:
                best_auc = result['auc_score']
                test_summary['best_models']['best_auc'] = result
        
        # 推奨事項の生成
        self._generate_recommendations(test_summary)
        
        # 結果保存
        self.results = test_summary
        
        logger.info("=== クイックテスト完了 ===")
        return test_summary
    
    def _generate_recommendations(self, summary: Dict[str, Any]):
        """推奨事項を生成"""
        results = summary.get('results', [])
        recommendations = []
        
        if not results:
            recommendations.append("評価可能なデータがありませんでした。")
            summary['recommendations'] = recommendations
            return
        
        # エラーのない結果のみを使用
        valid_results = [r for r in results if 'error' not in r]
        
        if not valid_results:
            recommendations.append("全てのテストでエラーが発生しました。")
            summary['recommendations'] = recommendations
            return
        
        # AUCスコア順にソート
        results_by_auc = sorted(valid_results, key=lambda x: x.get('auc_score', 0), reverse=True)
        best_result = results_by_auc[0]
        
        recommendations.append(
            f"最高性能: {best_result['description']} "
            f"(AUC: {best_result['auc_score']:.3f}, F1: {best_result['f1_score']:.3f})"
        )
        
        # 学習期間の長さによる分析
        period_performance = {}
        for result in valid_results:
            period_length = len(result['train_years'])
            if period_length not in period_performance:
                period_performance[period_length] = []
            period_performance[period_length].append(result['auc_score'])
        
        for length, scores in sorted(period_performance.items()):
            avg_score = np.mean(scores)
            max_score = np.max(scores)
            recommendations.append(
                f"{length}年間学習の平均AUC: {avg_score:.3f} (最高: {max_score:.3f})"
            )
        
        # 最適な学習期間の推奨
        if period_performance:
            best_length = max(period_performance.keys(), 
                             key=lambda x: np.mean(period_performance[x]))
            best_avg_auc = np.mean(period_performance[best_length])
            recommendations.append(
                f"推奨学習期間: {best_length}年間 (平均AUC: {best_avg_auc:.3f})"
            )
        
        # 長期データの効果分析
        if len(valid_results) >= 5:
            short_term = [r for r in valid_results if len(r['train_years']) <= 3]
            long_term = [r for r in valid_results if len(r['train_years']) >= 7]
            
            if short_term and long_term:
                short_avg = np.mean([r['auc_score'] for r in short_term])
                long_avg = np.mean([r['auc_score'] for r in long_term])
                improvement = ((long_avg - short_avg) / short_avg) * 100
                
                recommendations.append(
                    f"長期データ効果: 短期({short_avg:.3f}) vs 長期({long_avg:.3f}) "
                    f"改善率: {improvement:+.1f}%"
                )
        
        summary['recommendations'] = recommendations
    
    def save_results(self, output_path: str = "quick_multi_year_test_results.json"):
        """結果をファイルに保存"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            logger.info(f"結果を保存しました: {output_path}")
        except Exception as e:
            logger.error(f"結果保存エラー: {e}")
    
    def generate_report(self, output_path: str = "quick_multi_year_test_report.txt"):
        """テストレポートを生成"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("=== クイック複数年モデルテストレポート ===\n\n")
                f.write(f"テスト日時: {self.results.get('test_time', 'N/A')}\n")
                f.write(f"利用可能年度: {', '.join(self.results.get('available_years', []))}\n")
                f.write(f"サンプリング率: {self.results.get('sample_rate', 'N/A')}\n\n")
                
                # ベストモデル
                best_models = self.results.get('best_models', {})
                if 'best_auc' in best_models:
                    f.write("=== ベストモデル ===\n")
                    best = best_models['best_auc']
                    f.write(f"学習期間: {best['description']}\n")
                    f.write(f"学習年度: {', '.join(best['train_years'])}\n")
                    f.write(f"テスト年度: {best['test_year']}\n")
                    f.write(f"AUC: {best['auc_score']:.3f}\n")
                    f.write(f"F1スコア: {best['f1_score']:.3f}\n")
                    f.write(f"精度: {best['accuracy']:.3f}\n")
                    f.write(f"学習データ: {best['train_samples']:,}件\n")
                    f.write(f"テストデータ: {best['test_samples']:,}件\n\n")
                
                # 全結果
                f.write("=== 全テスト結果 ===\n")
                results = sorted(self.results.get('results', []), 
                               key=lambda x: x.get('auc_score', 0), reverse=True)
                
                for i, result in enumerate(results, 1):
                    f.write(f"\n{i}. {result['description']}\n")
                    f.write(f"   学習年度: {', '.join(result['train_years'])}\n")
                    f.write(f"   テスト年度: {result['test_year']}\n")
                    f.write(f"   AUC: {result.get('auc_score', 0):.3f}\n")
                    f.write(f"   F1: {result.get('f1_score', 0):.3f}\n")
                    f.write(f"   精度: {result.get('accuracy', 0):.3f}\n")
                    if 'train_samples' in result:
                        f.write(f"   学習データ: {result['train_samples']:,}件\n")
                        f.write(f"   テストデータ: {result['test_samples']:,}件\n")
                    if 'error' in result:
                        f.write(f"   エラー: {result['error']}\n")
                
                # 推奨事項
                f.write("\n=== 推奨事項 ===\n")
                recommendations = self.results.get('recommendations', [])
                for i, rec in enumerate(recommendations, 1):
                    f.write(f"{i}. {rec}\n")
            
            logger.info(f"レポートを保存しました: {output_path}")
            
        except Exception as e:
            logger.error(f"レポート生成エラー: {e}")


def main():
    """メイン関数"""
    # テストシステムの初期化
    tester = QuickMultiYearTester()
    
    # 包括的テストの実行
    results = tester.run_comprehensive_test(sample_rate=0.1)  # 高速化のため10%サンプル
    
    # 結果の表示
    print("\n=== テスト結果サマリー ===")
    best_models = results.get('best_models', {})
    
    if 'best_auc' in best_models:
        best = best_models['best_auc']
        print(f"最高AUCモデル: {best['description']}")
        print(f"  AUC: {best['auc_score']:.3f}")
        print(f"  F1: {best['f1_score']:.3f}")
        print(f"  学習年度: {', '.join(best['train_years'])}")
        print(f"  テスト年度: {best['test_year']}")
    
    print(f"\n推奨事項:")
    for i, rec in enumerate(results.get('recommendations', []), 1):
        print(f"  {i}. {rec}")
    
    # 結果保存
    tester.save_results()
    tester.generate_report()
    
    return results


if __name__ == "__main__":
    main()