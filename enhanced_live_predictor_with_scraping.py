#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最新馬戦績スクレイピング機能付き強化版実際のレース予測システム
- リアルタイム出馬表取得
- 最新馬戦績のリアルタイムスクレイピング機能を追加
- coreモジュール統合
"""

import sys
import os
import pandas as pd
import numpy as np
import requests
from bs4 import BeautifulSoup
import joblib
import logging
import re
import time
import random
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List, Dict, Any
import warnings
warnings.filterwarnings('ignore')

# 既存のプロセッサを使用した過去戦績取得
from core.processors.horse_processor import HorseProcessor
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
from core.features.manager import FeatureEngineeringManager
# コーナーデータと馬基本情報処理
from core.processors.corner_analyzer import CornerAnalyzer

# SSL証明書検証の環境変数設定（開発環境用）
import os
os.environ['PYTHONWARNINGS'] = 'ignore:Unverified HTTPS request'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''

# Selenium関連のインポート
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("Seleniumが利用できません。requests/BeautifulSoupのみを使用します。")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedLiveRacePredictorWithScraping:
    """最新馬戦績スクレイピング機能付き強化版実際のレース予測クラス"""
    
    def __init__(self, model_dir="models", use_selenium=True, enable_live_scraping=True):
        """
        初期化
        
        Parameters
        ----------
        model_dir : str
            学習済みモデルのディレクトリ
        use_selenium : bool
            Seleniumを使用するかどうか
        enable_live_scraping : bool
            最新馬戦績のリアルタイムスクレイピングを有効にするか
        """
        self.model_dir = Path(model_dir)
        self.model = None
        self.scaler = None
        self.features = None
        self.use_selenium = use_selenium and SELENIUM_AVAILABLE
        self.enable_live_scraping = enable_live_scraping
        self.driver = None
        
        # Requestsセッション設定
        self.session = requests.Session()
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ]
        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # マスターデータ
        self.WEATHER_LIST = ['晴', '曇', '雨', '雪']
        self.GROUND_STATE_LIST = ['良', '稍重', '重', '不良']
        self.RACE_TYPE_DICT = {'芝': 0, 'ダート': 1, '障害': 2}
        self.AROUND_LIST = ['右', '左', '直線', '障害']
        self.RACE_CLASS_LIST = ['新馬', '未勝利', '１勝クラス', '２勝クラス', '３勝クラス', 'オープン', 'G3', 'G2', 'G1', '障害']
        
        # 過去戦績処理用のプロセッサを初期化
        self.horse_processor = None
        
        # 最新戦績スクレイピング用キャッシュ
        self.scraped_horse_data = {}
        
        logger.info(f"EnhancedLiveRacePredictorWithScrapingを初期化しました（Selenium: {self.use_selenium}, LiveScraping: {self.enable_live_scraping}）")
    
    def load_latest_model(self, model_timestamp="20250608_212220"):
        """データリーケージ修正版モデルを読み込み"""
        try:
            # データリーケージ修正版モデルファイルを優先的に読み込み
            model_path = self.model_dir / f"fixed_leakage_model_{model_timestamp}.pkl"
            scaler_path = self.model_dir / f"fixed_leakage_scaler_{model_timestamp}.pkl"
            features_path = self.model_dir / f"fixed_leakage_features_{model_timestamp}.pkl"
            encoders_path = self.model_dir / f"fixed_leakage_encoders_{model_timestamp}.pkl"
            
            # ファイル存在確認
            if not all(path.exists() for path in [model_path, scaler_path, features_path, encoders_path]):
                logger.warning("データリーケージ修正版モデルが見つかりません。従来のモデルを探します...")
                # フォールバック：従来のモデル検索
                model_files = list(self.model_dir.glob("*enhanced*model*.pkl"))
                if not model_files:
                    model_files = list(self.model_dir.glob("*model*.pkl"))
                
                if not model_files:
                    raise FileNotFoundError("モデルファイルが見つかりません")
                
                latest_model = max(model_files, key=lambda f: f.stat().st_mtime)
                model_path = latest_model
                scaler_path = self.model_dir / f"{latest_model.stem.replace('model', 'scaler')}.pkl"
                features_path = self.model_dir / f"{latest_model.stem.replace('model', 'features')}.pkl"
                encoders_path = None
            
            # モデル読み込み
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            self.features = joblib.load(features_path)
            
            # エンコーダー読み込み（存在する場合）
            self.label_encoders = {}
            if encoders_path and encoders_path.exists():
                self.label_encoders = joblib.load(encoders_path)
                logger.info(f"ラベルエンコーダー読み込み完了: {len(self.label_encoders)}個")
            
            logger.info(f"モデル読み込み完了: {model_path.name}")
            logger.info(f"特徴量数: {len(self.features)}")
            
            return True
            
        except Exception as e:
            logger.error(f"モデル読み込みエラー: {e}")
            return False
    
    def scrape_live_horse_results(self, horse_id: str, limit_races: int = 10) -> pd.DataFrame:
        """
        リアルタイムで最新の馬戦績をスクレイピング
        
        Parameters
        ----------
        horse_id : str
            馬ID
        limit_races : int
            取得する最大レース数
            
        Returns
        -------
        pd.DataFrame
            最新の馬戦績データ
        """
        try:
            if not self.enable_live_scraping:
                logger.debug(f"馬ID {horse_id}: ライブスクレイピング無効のためスキップ")
                return pd.DataFrame()
            
            # キャッシュ確認
            if horse_id in self.scraped_horse_data:
                logger.debug(f"馬ID {horse_id}: キャッシュからデータを取得")
                return self.scraped_horse_data[horse_id]
            
            logger.info(f"馬ID {horse_id} の最新戦績をスクレイピング中...")
            
            # BAN対策：リクエスト間隔
            delay = random.uniform(2, 4)
            time.sleep(delay)
            
            url = f"https://db.netkeiba.com/horse/{horse_id}"
            
            try:
                # SSL証明書検証を無効化してリクエスト
                response = self.session.get(url, timeout=15, verify=False)
                response.raise_for_status()
            except requests.exceptions.SSLError:
                logger.warning(f"馬ID {horse_id}: SSLエラーが発生しました。検証を無効化して再試行")
                import urllib3
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                response = self.session.get(url, timeout=15, verify=False)
                response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 戦績テーブルを検索
            results_table = None
            table_selectors = [
                'table.race_table_01',
                'table.nk_tb_common',
                'table[summary*="競走成績"]',
                'table.db_h_race_results'
            ]
            
            for selector in table_selectors:
                results_table = soup.select_one(selector)
                if results_table:
                    logger.debug(f"馬ID {horse_id}: テーブル発見 ({selector})")
                    break
            
            if not results_table:
                # より汎用的な検索
                tables = soup.find_all('table')
                for table in tables:
                    # ヘッダーで戦績テーブルを判定
                    headers = table.find_all(['th', 'td'])
                    if headers and any('着順' in header.get_text() for header in headers[:10]):
                        results_table = table
                        logger.debug(f"馬ID {horse_id}: 汎用検索でテーブル発見")
                        break
            
            if not results_table:
                logger.warning(f"馬ID {horse_id}: 戦績テーブルが見つかりません")
                return pd.DataFrame()
            
            # テーブルデータを解析
            rows = results_table.find_all('tr')[1:]  # ヘッダーを除く
            results_data = []
            
            for i, row in enumerate(rows[:limit_races]):  # 最新のlimit_races件のみ
                cols = row.find_all(['td', 'th'])
                if len(cols) < 5:  # 最低限のカラム数チェック
                    continue
                
                try:
                    row_data = {}
                    
                    # 基本的なカラム抽出（netkeiba標準形式）
                    col_texts = [col.get_text(strip=True) for col in cols]
                    
                    if len(col_texts) >= 10:
                        row_data['日付'] = col_texts[0] if col_texts[0] else ''
                        row_data['開催'] = col_texts[1] if col_texts[1] else ''
                        row_data['天気'] = col_texts[2] if col_texts[2] else ''
                        row_data['R'] = col_texts[3] if col_texts[3] else ''
                        row_data['レース名'] = col_texts[4] if col_texts[4] else ''
                        row_data['映像'] = col_texts[5] if col_texts[5] else ''
                        row_data['頭数'] = col_texts[6] if col_texts[6] else ''
                        row_data['枠番'] = col_texts[7] if col_texts[7] else ''
                        row_data['馬番'] = col_texts[8] if col_texts[8] else ''
                        row_data['オッズ'] = col_texts[9] if col_texts[9] else ''
                        
                        if len(col_texts) >= 15:
                            row_data['人気'] = col_texts[10] if col_texts[10] else ''
                            row_data['着順'] = col_texts[11] if col_texts[11] else ''
                            row_data['騎手'] = col_texts[12] if col_texts[12] else ''
                            row_data['斤量'] = col_texts[13] if col_texts[13] else ''
                            row_data['距離'] = col_texts[14] if col_texts[14] else ''
                        
                        if len(col_texts) >= 20:
                            row_data['馬場'] = col_texts[15] if col_texts[15] else ''
                            row_data['タイム'] = col_texts[16] if col_texts[16] else ''
                            row_data['着差'] = col_texts[17] if col_texts[17] else ''
                            row_data['タイム指数'] = col_texts[18] if col_texts[18] else ''
                            row_data['通過'] = col_texts[19] if col_texts[19] else ''
                        
                        if len(col_texts) >= 25:
                            row_data['ペース'] = col_texts[20] if col_texts[20] else ''
                            row_data['上り'] = col_texts[21] if col_texts[21] else ''
                            row_data['馬体重'] = col_texts[22] if col_texts[22] else ''
                            row_data['厩舎コメント'] = col_texts[23] if col_texts[23] else ''
                            row_data['備考'] = col_texts[24] if col_texts[24] else ''
                        
                        if len(col_texts) >= 26:
                            row_data['勝ち馬(2着馬)'] = col_texts[25] if col_texts[25] else ''
                    else:
                        # 簡略版（最低限の情報）
                        row_data['日付'] = col_texts[0] if len(col_texts) > 0 else ''
                        row_data['着順'] = col_texts[1] if len(col_texts) > 1 else ''
                        row_data['レース名'] = col_texts[2] if len(col_texts) > 2 else ''
                        row_data['騎手'] = col_texts[3] if len(col_texts) > 3 else ''
                        row_data['人気'] = col_texts[4] if len(col_texts) > 4 else ''
                    
                    # 数値変換
                    try:
                        if '着順' in row_data and row_data['着順']:
                            rank_text = re.sub(r'[^0-9]', '', row_data['着順'])
                            row_data['着順'] = int(rank_text) if rank_text else 99
                        else:
                            row_data['着順'] = 99
                    except:
                        row_data['着順'] = 99
                    
                    try:
                        if '人気' in row_data and row_data['人気']:
                            pop_text = re.sub(r'[^0-9]', '', row_data['人気'])
                            row_data['人気'] = int(pop_text) if pop_text else 99
                        else:
                            row_data['人気'] = 99
                    except:
                        row_data['人気'] = 99
                    
                    try:
                        if 'オッズ' in row_data and row_data['オッズ']:
                            odds_text = re.sub(r'[^0-9.]', '', row_data['オッズ'])
                            row_data['オッズ'] = float(odds_text) if odds_text else 99.0
                        else:
                            row_data['オッズ'] = 99.0
                    except:
                        row_data['オッズ'] = 99.0
                    
                    # 日付処理
                    try:
                        if '日付' in row_data and row_data['日付']:
                            date_str = row_data['日付'].replace('/', '-')
                            # 年が省略されている場合の処理
                            if len(date_str.split('-')) == 2:
                                current_year = datetime.now().year
                                date_str = f"{current_year}-{date_str}"
                            row_data['日付'] = pd.to_datetime(date_str, errors='coerce')
                    except:
                        row_data['日付'] = pd.NaT
                    
                    row_data['horse_id'] = horse_id
                    results_data.append(row_data)
                    
                except Exception as e:
                    logger.debug(f"馬ID {horse_id} 行{i}の解析エラー: {e}")
                    continue
            
            if results_data:
                df = pd.DataFrame(results_data)
                logger.info(f"馬ID {horse_id}: 最新戦績 {len(df)}件を取得")
                
                # キャッシュに保存
                self.scraped_horse_data[horse_id] = df
                
                return df
            else:
                logger.warning(f"馬ID {horse_id}: 戦績データが取得できませんでした")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"馬ID {horse_id} のスクレイピングエラー: {e}")
            return pd.DataFrame()
    
    def get_live_horse_performance(self, horse_ids: List[str], target_date: str = None) -> Dict[str, Dict]:
        """
        リアルタイムスクレイピングで最新の馬戦績統計を取得
        
        Parameters
        ----------
        horse_ids : List[str]
            馬IDのリスト
        target_date : str, optional
            対象日付（データリーケージ防止用）
            
        Returns
        -------
        Dict[str, Dict]
            馬IDをキーとした最新戦績統計辞書
        """
        try:
            if not horse_ids:
                logger.warning("最新戦績取得: 馬IDリストが空です")
                return {}
            
            logger.info(f"最新戦績をリアルタイムスクレイピング中: {len(horse_ids)}頭")
            
            target_datetime = pd.to_datetime(target_date) if target_date else pd.Timestamp.now()
            stats = {}
            
            for horse_id in horse_ids:
                # 最新戦績をスクレイピング
                horse_data = self.scrape_live_horse_results(horse_id, limit_races=20)
                
                if horse_data.empty:
                    stats[horse_id] = self._generate_missing_stats()
                    continue
                
                # データリーケージ防止：対象日付より前のデータのみ使用
                if target_date and '日付' in horse_data.columns:
                    valid_dates = pd.to_datetime(horse_data['日付'], errors='coerce')
                    horse_data = horse_data[valid_dates < target_datetime]
                
                if horse_data.empty:
                    stats[horse_id] = self._generate_default_stats()
                    continue
                
                # 統計特徴量を計算
                try:
                    # 着順データの処理
                    ranks = pd.to_numeric(horse_data['着順'], errors='coerce').dropna()
                    ranks = ranks[ranks <= 18]  # 除外・取消を除く
                    
                    # 人気データの処理
                    popularity = pd.to_numeric(horse_data['人気'], errors='coerce').dropna()
                    popularity = popularity[popularity <= 18]
                    
                    # オッズデータの処理
                    odds = pd.to_numeric(horse_data['オッズ'], errors='coerce').dropna()
                    odds = odds[odds < 500]  # 明らかに異常なオッズを除く
                    
                    # 日付データの処理（最終レース日計算用）
                    last_race_days = 30
                    if '日付' in horse_data.columns:
                        dates = pd.to_datetime(horse_data['日付'], errors='coerce').dropna()
                        if not dates.empty:
                            last_race_days = max(1, (target_datetime - dates.max()).days)
                    
                    # 統計計算
                    total_races = len(horse_data)
                    win_rate = (ranks == 1).mean() if not ranks.empty else 0.0
                    place_rate = (ranks <= 2).mean() if not ranks.empty else 0.0
                    show_rate = (ranks <= 3).mean() if not ranks.empty else 0.0
                    avg_rank = ranks.mean() if not ranks.empty else 8.0
                    rank_std = ranks.std() if not ranks.empty else 3.0
                    avg_popularity = popularity.mean() if not popularity.empty else 8.0
                    avg_odds = odds.mean() if not odds.empty else 10.0
                    
                    # 直近5レースの成績
                    recent_ranks = ranks.head(5) if len(ranks) >= 5 else ranks
                    recent_avg_rank = recent_ranks.mean() if not recent_ranks.empty else 8.0
                    
                    # 賞金推定（簡易版）
                    prize_base = max(0, (10 - avg_rank) * 100000)
                    avg_prize = prize_base * (1 + win_rate)
                    max_prize = avg_prize * 3 if win_rate > 0 else avg_prize
                    
                    stats[horse_id] = {
                        'total_races': total_races,
                        'win_rate': float(win_rate) if not pd.isna(win_rate) else 0.0,
                        'place_rate': float(place_rate) if not pd.isna(place_rate) else 0.0,
                        'show_rate': float(show_rate) if not pd.isna(show_rate) else 0.0,
                        'avg_rank': float(avg_rank) if not pd.isna(avg_rank) else 8.0,
                        'rank_std': float(rank_std) if not pd.isna(rank_std) else 3.0,
                        'avg_prize': float(avg_prize) if not pd.isna(avg_prize) else 500000.0,
                        'max_prize': float(max_prize) if not pd.isna(max_prize) else 1000000.0,
                        'recent_avg_rank': float(recent_avg_rank) if not pd.isna(recent_avg_rank) else 8.0,
                        'days_since_last_race': int(last_race_days),
                        'avg_popularity': float(avg_popularity) if not pd.isna(avg_popularity) else 8.0,
                        'avg_odds': float(avg_odds) if not pd.isna(avg_odds) else 10.0,
                        'scraped_races': len(horse_data)  # スクレイピングで取得したレース数
                    }
                    
                    logger.debug(f"馬ID {horse_id}: 戦績統計計算完了 (レース数={total_races}, 勝率={win_rate:.3f})")
                    
                except Exception as e:
                    logger.warning(f"馬ID {horse_id} の統計計算でエラー: {e}")
                    stats[horse_id] = self._generate_default_stats()
            
            valid_stats = sum(1 for s in stats.values() if s.get('scraped_races', 0) > 0)
            logger.info(f"最新戦績統計計算完了: {len(stats)}頭中{valid_stats}頭で実際のデータを取得")
            
            return stats
            
        except Exception as e:
            logger.error(f"最新戦績取得エラー: {e}")
            import traceback
            traceback.print_exc()
            return self._generate_fallback_stats(horse_ids)
    
    def _get_smart_default_distance(self, race_id: str) -> int:
        """レースIDから適切なデフォルト距離を推定"""
        try:
            # レースIDの競馬場コードから推定
            venue_code = race_id[4:6] if len(race_id) >= 6 else "01"
            
            # 中央競馬場（01-10）のデフォルト距離
            central_venues = {
                "01": 1800,  # 札幌（中距離中心）
                "02": 1600,  # 函館（中距離中心）
                "03": 1800,  # 福島（中距離中心）
                "04": 2000,  # 新潟（中長距離中心）
                "05": 1600,  # 東京（バランス型）
                "06": 1800,  # 中山（中距離中心）
                "07": 1400,  # 中京（短中距離中心）
                "08": 1600,  # 京都（中距離中心）
                "09": 1800,  # 阪神（中距離中心）
                "10": 1600   # 小倉（中距離中心）
            }
            
            return central_venues.get(venue_code, 1600)  # デフォルトは1600m
            
        except Exception:
            return 1600  # エラー時のフォールバック
    
    def _get_smart_default_conditions(self, race_id: str) -> Dict[str, str]:
        """レースIDと現在の季節から適切なデフォルト条件を推定"""
        try:
            # 現在の月から季節的な条件を推定
            current_month = datetime.now().month
            
            # 季節による天気の確率的推定
            if current_month in [6, 7, 9]:  # 梅雨・台風シーズン
                weather = "曇" if np.random.random() > 0.6 else "雨"
                ground_state = "稍重" if weather == "曇" else "重"
            elif current_month in [12, 1, 2]:  # 冬季
                weather = "晴" if np.random.random() > 0.3 else "曇"
                ground_state = "良" if weather == "晴" else "稍重"
            else:  # その他の月
                weather = "晴" if np.random.random() > 0.2 else "曇"
                ground_state = "良"
            
            # 競馬場コードから回りを推定
            venue_code = race_id[4:6] if len(race_id) >= 6 else "01"
            
            # 左回りの競馬場
            left_venues = ["02", "04", "06", "07", "10"]  # 函館、新潟、中山、中京、小倉
            track_direction = "左" if venue_code in left_venues else "右"
            
            return {
                'race_type': '芝',  # 芝が最も一般的
                'ground_state': ground_state,
                'weather': weather,
                'track_direction': track_direction
            }
            
        except Exception:
            return {
                'race_type': '芝',
                'ground_state': '良',
                'weather': '晴',
                'track_direction': '右'
            }
    
    def _generate_missing_stats(self) -> Dict[str, float]:
        """データが存在しない場合の欠損値統計を生成（LightGBM用）"""
        return {
            'total_races': np.nan,
            'win_rate': np.nan,
            'place_rate': np.nan,
            'show_rate': np.nan,
            'avg_rank': np.nan,
            'rank_std': np.nan,
            'avg_prize': np.nan,
            'max_prize': np.nan,
            'recent_avg_rank': np.nan,
            'days_since_last_race': np.nan,
            'avg_popularity': np.nan,
            'avg_odds': np.nan,
            'scraped_races': 0  # 実際にスクレイピングした数は0
        }
    
    def _generate_default_stats(self, field_size: int = 16) -> Dict[str, float]:
        """出走頭数を考慮したデフォルト統計値を生成"""
        # 出走頭数に基づく適切なデフォルト値
        expected_rank = (field_size + 1) / 2.0  # 理論的な期待着順
        win_rate = (1.0 / field_size) * 1.1  # 理論値より少し高め
        place_rate = (2.0 / field_size) * 1.05
        show_rate = (3.0 / field_size) * 1.02
        
        return {
            'total_races': max(8, min(field_size, 15)),  # 出走頭数に応じた経験回数
            'win_rate': max(0.02, min(0.15, win_rate)),  # 2%〜15%の範囲
            'place_rate': max(0.05, min(0.30, place_rate)),  # 5%〜30%の範囲
            'show_rate': max(0.10, min(0.45, show_rate)),  # 10%〜45%の範囲
            'avg_rank': max(3.0, min(field_size - 1, expected_rank)),
            'rank_std': max(2.0, min(4.0, field_size / 5.0)),  # 出走頭数に比例した変動
            'avg_prize': 600000,  # より現実的な平均賞金
            'max_prize': 1500000,
            'recent_avg_rank': max(3.0, min(field_size - 1, expected_rank)),
            'days_since_last_race': 35,  # より現実的な間隔
            'avg_popularity': max(3.0, min(field_size - 1, expected_rank)),
            'avg_odds': max(2.0, min(20.0, expected_rank * 1.5)),  # 着順に応じたオッズ
            'scraped_races': 0
        }
    
    def _generate_fallback_stats(self, horse_ids: List[str]) -> Dict[str, Dict]:
        """フォールバック用の統計値を生成"""
        stats = {}
        field_size = len(horse_ids)
        for horse_id in horse_ids:
            stats[horse_id] = self._generate_default_stats(field_size)
        return stats
    
    def scrape_race_card_requests(self, race_id: str) -> pd.DataFrame:
        """requests/BeautifulSoupベースの出馬表取得（改善版：正しい要素構造を使用）"""
        try:
            url = f"https://race.netkeiba.com/race/shutuba.html?race_id={race_id}"
            logger.info(f"Requestsで出馬表を取得中: {url}")
            
            delay = random.uniform(2, 5)
            time.sleep(delay)
            
            # SSL証明書検証を無効化してリトライ
            try:
                response = self.session.get(url, timeout=15)
            except requests.exceptions.SSLError:
                logger.warning("SSLエラーが発生しました。検証を無効化して再試行")
                import urllib3
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                response = self.session.get(url, timeout=15, verify=False)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 正しい出馬表構造を使用（参考コードに基づく）
            horse_list_elements = soup.find_all(class_='HorseList')
            
            if not horse_list_elements:
                # フォールバック：従来の方法
                logger.warning("HorseListクラスが見つかりません。従来の方法を試します")
                return self._scrape_race_card_fallback(soup, race_id)
            
            logger.info(f"HorseListクラス要素数: {len(horse_list_elements)}")
            
            rows = []
            for i, horse_element in enumerate(horse_list_elements):
                row_data = {}
                tds = horse_element.find_all('td')
                
                if len(tds) < 5:
                    logger.debug(f"行{i}: td要素数不足 ({len(tds)})")
                    continue
                
                try:
                    # 参考コードの構造に基づくデータ抽出
                    td_index = 0
                    
                    # 枠番・馬番
                    row_data['枠番'] = re.sub(r'[^\d]', '', tds[td_index].get_text(strip=True)) if td_index < len(tds) else '1'
                    td_index += 1
                    row_data['馬番'] = re.sub(r'[^\d]', '', tds[td_index].get_text(strip=True)) if td_index < len(tds) else '1'
                    td_index += 1
                    
                    # 馬情報（HorseInfoクラス）
                    horse_info_td = None
                    for td in tds:
                        if 'HorseInfo' in str(td.get('class', [])):
                            horse_info_td = td
                            break
                    
                    if horse_info_td:
                        horse_link = horse_info_td.find('a')
                        if horse_link:
                            horse_href = horse_link.get('href', '')
                            # 馬IDを抽出（参考コードのパターン）
                            horse_id_match = re.search(r'horse/(\d+)', horse_href)
                            row_data['horse_id'] = horse_id_match.group(1) if horse_id_match else ''
                            
                            horse_name = horse_link.get_text(strip=True)
                            row_data['馬名'] = re.sub(r'[◎◯▲△☆✓消-]+|&#\d+;', '', horse_name).strip()
                            
                            logger.debug(f"馬ID取得: {row_data['horse_id']} - {row_data['馬名']}")
                        else:
                            row_data['horse_id'] = ''
                            row_data['馬名'] = horse_info_td.get_text(strip=True)
                    else:
                        # フォールバック
                        row_data['horse_id'] = ''
                        row_data['馬名'] = tds[2].get_text(strip=True) if len(tds) > 2 else f'Horse_{i+1}'
                    
                    # その他の情報
                    row_data['性齢'] = tds[3].get_text(strip=True) if len(tds) > 3 else '4歳'
                    row_data['斤量'] = tds[4].get_text(strip=True) if len(tds) > 4 else '57.0'
                    
                    # 騎手情報（Jockeyクラス）
                    jockey_td = None
                    for td in tds:
                        if 'Jockey' in str(td.get('class', [])):
                            jockey_td = td
                            break
                    
                    if jockey_td:
                        jockey_link = jockey_td.find('a')
                        if jockey_link:
                            jockey_href = jockey_link.get('href', '')
                            jockey_id_match = re.search(r'jockey/result/recent/(\w+)', jockey_href)
                            row_data['jockey_id'] = jockey_id_match.group(1) if jockey_id_match else ''
                            row_data['騎手'] = jockey_link.get_text(strip=True)
                        else:
                            row_data['jockey_id'] = ''
                            row_data['騎手'] = jockey_td.get_text(strip=True)
                    else:
                        row_data['jockey_id'] = ''
                        row_data['騎手'] = tds[5].get_text(strip=True) if len(tds) > 5 else ''
                    
                    # 調教師情報（Trainerクラス）
                    trainer_td = None
                    for td in tds:
                        if 'Trainer' in str(td.get('class', [])):
                            trainer_td = td
                            break
                    
                    if trainer_td:
                        trainer_link = trainer_td.find('a')
                        if trainer_link:
                            trainer_href = trainer_link.get('href', '')
                            trainer_id_match = re.search(r'trainer/result/recent/(\w+)', trainer_href)
                            row_data['trainer_id'] = trainer_id_match.group(1) if trainer_id_match else ''
                            row_data['調教師'] = trainer_link.get_text(strip=True)
                        else:
                            row_data['trainer_id'] = ''
                            row_data['調教師'] = trainer_td.get_text(strip=True)
                    else:
                        row_data['trainer_id'] = ''
                        row_data['調教師'] = tds[6].get_text(strip=True) if len(tds) > 6 else ''
                    
                    # オッズ・人気
                    if len(tds) > 7:
                        row_data['単勝オッズ'] = tds[7].get_text(strip=True)
                    if len(tds) > 8:
                        row_data['人気'] = tds[8].get_text(strip=True)
                    
                    # 馬体重
                    if len(tds) > 9:
                        row_data['体重・増減'] = tds[9].get_text(strip=True)
                    
                    # 有効なデータかチェック
                    if (row_data.get('馬名') and 
                        row_data.get('馬名') not in ['', '--', '0'] and
                        not pd.isna(row_data.get('馬名'))):
                        rows.append(row_data)
                        logger.debug(f"有効な行{i}を追加: {row_data.get('馬名', '')} (ID: {row_data.get('horse_id', 'なし')})")
                    else:
                        logger.debug(f"行{i}をスキップ: 馬名が無効")
                        
                except Exception as e:
                    logger.debug(f"行{i}の解析エラー: {e}")
                    continue
            
            df = pd.DataFrame(rows)
            
            if df.empty:
                logger.warning("HorseListから有効なデータが取得できませんでした。フォールバック方式を実行...")
                return self._scrape_race_card_fallback(soup, race_id)
            
            # データ型変換
            df['枠番'] = pd.to_numeric(df['枠番'], errors='coerce')
            df['馬番'] = pd.to_numeric(df['馬番'], errors='coerce')
            df['斤量'] = pd.to_numeric(df['斤量'], errors='coerce')
            df['race_id'] = race_id
            
            logger.info(f"Requests出馬表取得完了: {len(df)}頭")
            
            # デバッグ情報：取得したデータの詳細を出力
            if not df.empty:
                logger.info("=== 出馬表取得詳細 ===")
                logger.info(f"カラム: {list(df.columns)}")
                if 'horse_id' in df.columns:
                    horse_ids = df['horse_id'].dropna().tolist()
                    valid_ids = [hid for hid in horse_ids if hid and not hid.startswith('h')]
                    logger.info(f"有効な馬ID数: {len(valid_ids)}/{len(horse_ids)}")
                    if valid_ids:
                        logger.info(f"馬IDサンプル: {valid_ids[:3]}")
                if '馬名' in df.columns:
                    horse_name_sample = df['馬名'].dropna().head(3).tolist()
                    logger.info(f"馬名サンプル: {horse_name_sample}")
                logger.info("=====================")
            
            time.sleep(random.uniform(1, 3))
            
            return df
            
        except Exception as e:
            logger.error(f"Requests出馬表スクレイピングエラー: {e}")
            return pd.DataFrame()
    
    def _scrape_race_card_fallback(self, soup: BeautifulSoup, race_id: str) -> pd.DataFrame:
        """フォールバック用の出馬表取得（従来の方法）"""
        try:
            logger.info("フォールバック方式で出馬表を取得中...")
            
            # 複数の可能なテーブルクラスを試す
            table = soup.find('table', class_='race_table_01')
            if not table:
                table = soup.find('table', class_='HorseList')
            if not table:
                # より汎用的にテーブルを探す
                tables = soup.find_all('table')
                for t in tables:
                    if t.find('tr') and len(t.find_all('tr')) > 1:
                        table = t
                        break
            
            if not table:
                logger.error("フォールバック：出馬表テーブルが見つかりません")
                return pd.DataFrame()
            
            rows = []
            all_rows = table.find_all('tr')
            logger.info(f"フォールバック：テーブル内の全行数: {len(all_rows)}")
            
            for i, tr in enumerate(all_rows[1:]):  # ヘッダーをスキップ
                cols = tr.find_all(['td', 'th'])
                
                if len(cols) >= 3:  # 最低限のカラム数
                    row_data = {}
                    
                    try:
                        # 基本情報
                        row_data['枠番'] = re.sub(r'[^\d]', '', self._extract_text(cols[0])) if len(cols) > 0 else '1'
                        row_data['馬番'] = re.sub(r'[^\d]', '', self._extract_text(cols[1])) if len(cols) > 1 else '1'
                        
                        # 馬名とID
                        if len(cols) > 2:
                            horse_link = cols[2].find('a')
                            if horse_link:
                                horse_href = horse_link.get('href', '')
                                horse_id_match = re.search(r'horse/(\d+)', horse_href)
                                row_data['horse_id'] = horse_id_match.group(1) if horse_id_match else ''
                                horse_name = horse_link.get_text(strip=True)
                                row_data['馬名'] = re.sub(r'[◎◯▲△☆✓消-]+|&#\d+;', '', horse_name).strip()
                            else:
                                row_data['horse_id'] = ''
                                horse_name = self._extract_text(cols[2])
                                row_data['馬名'] = re.sub(r'[◎◯▲△☆✓消-]+|&#\d+;', '', horse_name).strip()
                        
                        # その他の情報
                        row_data['性齢'] = self._extract_text(cols[3]) if len(cols) > 3 else '4歳'
                        row_data['斤量'] = self._extract_text(cols[4]) if len(cols) > 4 else '57.0'
                        row_data['騎手'] = self._extract_text(cols[5]) if len(cols) > 5 else ''
                        
                        # 有効なデータかチェック
                        if (row_data.get('馬名') and 
                            row_data.get('馬名') not in ['', '--', '0']):
                            rows.append(row_data)
                            logger.debug(f"フォールバック行{i}を追加: {row_data.get('馬名', '')}")
                        
                    except Exception as e:
                        logger.debug(f"フォールバック行{i}の解析エラー: {e}")
                        continue
            
            df = pd.DataFrame(rows)
            
            if not df.empty:
                # データ型変換
                df['枠番'] = pd.to_numeric(df['枠番'], errors='coerce')
                df['馬番'] = pd.to_numeric(df['馬番'], errors='coerce')
                df['斤量'] = pd.to_numeric(df['斤量'], errors='coerce')
                df['race_id'] = race_id
                
                logger.info(f"フォールバック出馬表取得完了: {len(df)}頭")
            
            return df
            
        except Exception as e:
            logger.error(f"フォールバック出馬表取得エラー: {e}")
            return pd.DataFrame()
    
    def _extract_text(self, element):
        """要素からテキストを安全に抽出"""
        if element:
            return element.get_text(strip=True)
        return ""
    
    def prepare_prediction_features(self, race_data, race_info, horse_stats=None):
        """データリーケージ修正版モデル用の予測特徴量を準備"""
        try:
            logger.info("データリーケージ修正版モデル用特徴量を準備中...")
            
            data = race_data.copy()
            
            # レース情報を追加
            for key, value in race_info.items():
                data[key] = value
            
            # 基本特徴量
            data['枠番'] = pd.to_numeric(data.get('枠番', 1), errors='coerce').fillna(1)
            data['馬番'] = pd.to_numeric(data.get('馬番', 1), errors='coerce').fillna(1)
            data['斤量'] = pd.to_numeric(data.get('斤量', 57.0), errors='coerce').fillna(57.0)
            data['course_len'] = pd.to_numeric(data.get('course_len', 1600), errors='coerce').fillna(1600)
            
            # 過去戦績統計特徴量（最新スクレイピングデータを使用）
            if horse_stats:
                logger.info("最新スクレイピングデータを使用")
                # 馬IDと行の対応を確立
                if 'horse_id' in data.columns:
                    horse_id_map = {idx: row['horse_id'] for idx, row in enumerate(data.to_dict('records'))}
                else:
                    horse_id_map = {idx: list(horse_stats.keys())[idx] if idx < len(horse_stats) else None for idx in range(len(data))}
                
                for idx in range(len(data)):
                    horse_id = horse_id_map.get(idx)
                    if horse_id and horse_id in horse_stats:
                        stats = horse_stats[horse_id]
                        
                        # 実際の統計データを特徴量に変換
                        win_rate = stats.get('win_rate', 0.0)
                        place_rate = stats.get('place_rate', 0.0)
                        show_rate = stats.get('show_rate', 0.0)
                        avg_rank = stats.get('avg_rank', 8.0)
                        recent_avg_rank = stats.get('recent_avg_rank', 8.0)
                        avg_prize = stats.get('avg_prize', 500000)
                        avg_popularity = stats.get('avg_popularity', 8.0)
                        avg_odds = stats.get('avg_odds', 10.0)
                        total_races = stats.get('total_races', 10)
                        
                        # last_5R統計
                        data.loc[data.index[idx], '着順_last_5R_mean'] = recent_avg_rank
                        data.loc[data.index[idx], '人気_last_5R_mean'] = avg_popularity
                        data.loc[data.index[idx], 'オッズ_last_5R_mean'] = avg_odds
                        data.loc[data.index[idx], '賞金_last_5R_mean'] = avg_prize
                        data.loc[data.index[idx], '斤量_last_5R_mean'] = 55.0
                        data.loc[data.index[idx], '上り_last_5R_mean'] = 34.5 + (avg_rank - 5.0) * 0.2
                        data.loc[data.index[idx], '体重_last_5R_mean'] = 475 + np.random.uniform(-15, 15)
                        data.loc[data.index[idx], '体重変化_last_5R_mean'] = np.random.uniform(-2, 2)
                        
                        # last_10R統計
                        data.loc[data.index[idx], '着順_last_10R_mean'] = avg_rank
                        data.loc[data.index[idx], '人気_last_10R_mean'] = avg_popularity
                        data.loc[data.index[idx], 'オッズ_last_10R_mean'] = avg_odds
                        data.loc[data.index[idx], '賞金_last_10R_mean'] = avg_prize
                        data.loc[data.index[idx], '斤量_last_10R_mean'] = 55.0
                        data.loc[data.index[idx], '上り_last_10R_mean'] = 34.5 + (avg_rank - 5.0) * 0.2
                        data.loc[data.index[idx], '体重_last_10R_mean'] = 475 + np.random.uniform(-15, 15)
                        data.loc[data.index[idx], '体重変化_last_10R_mean'] = np.random.uniform(-2, 2)
                        
                        # all_R統計
                        data.loc[data.index[idx], '着順_all_R_mean'] = avg_rank
                        data.loc[data.index[idx], '人気_all_R_mean'] = avg_popularity
                        data.loc[data.index[idx], 'オッズ_all_R_mean'] = avg_odds
                        data.loc[data.index[idx], '賞金_all_R_mean'] = avg_prize
                        data.loc[data.index[idx], '斤量_all_R_mean'] = 55.0
                        data.loc[data.index[idx], '上り_all_R_mean'] = 34.5 + (avg_rank - 5.0) * 0.2
                        data.loc[data.index[idx], '体重_all_R_mean'] = 475 + np.random.uniform(-15, 15)
                        data.loc[data.index[idx], '体重変化_all_R_mean'] = np.random.uniform(-2, 2)
                        
                        # インターバル
                        data.loc[data.index[idx], 'interval_days'] = stats.get('days_since_last_race', 35)
                        
                        logger.debug(f"馬ID {horse_id}: スクレイピングデータ統合完了 (戦績数={stats.get('scraped_races', 0)})")
                        
                    else:
                        # データが存在しない場合は欠損値を設定（LightGBMが最適処理）
                        self._set_missing_performance_stats(data, idx)
            else:
                logger.warning("過去戦績データが取得できませんでした。欠損値を設定（LightGBMが最適処理）")
                for idx in range(len(data)):
                    self._set_missing_performance_stats(data, idx)
            
            # カテゴリカル特徴量の処理
            if hasattr(self, 'label_encoders') and self.label_encoders:
                for col, encoder in self.label_encoders.items():
                    if col in data.columns:
                        # 未知のカテゴリに対する処理
                        known_classes = set(encoder.classes_)
                        data[col] = data[col].fillna('unknown').astype(str)
                        
                        # 未知のカテゴリを最も一般的なクラスで置換
                        mask = ~data[col].isin(known_classes)
                        if mask.any():
                            most_common_class = encoder.classes_[0]
                            data.loc[mask, col] = most_common_class
                            logger.info(f"未知カテゴリを{most_common_class}で置換: {col}")
                        
                        data[col] = encoder.transform(data[col])
                    else:
                        # カラムが存在しない場合はデフォルト値
                        data[col] = 0
            else:
                # エンコーダーがない場合のフォールバック処理
                data['race_class'] = 0  # デフォルト値
                data['ground_state'] = 0  # 良
                data['weather'] = 0  # 晴
                data['track_direction'] = 0  # 右
            
            # 学習時の特徴量のみを選択
            missing_features = []
            for col in self.features:
                if col not in data.columns:
                    data[col] = np.nan  # 欠損値で補完（LightGBMが最適処理）
                    missing_features.append(col)
            
            if missing_features:
                logger.info(f"不足特徴量を欠損値で補完: {len(missing_features)}個（LightGBMが自動処理）")
            
            # 最終的な特徴量DataFrame
            X = data[self.features]
            
            # 無限値の処理（LightGBMは欠損値を自動処理するためfillna(0)は不要）
            X = X.replace([np.inf, -np.inf], np.nan)
            
            logger.info(f"特徴量準備完了: {X.shape}")
            
            return X, data
            
        except Exception as e:
            logger.error(f"特徴量準備エラー: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame(), pd.DataFrame()
    
    def _set_default_performance_stats(self, data, idx):
        """デフォルトの過去戦績統計を設定（改善版）"""
        # レース距離とコースから適切なデフォルト値を計算
        distance = data.get('course_len', [1600]).iloc[0] if isinstance(data.get('course_len'), pd.Series) else data.get('course_len', 1600)
        race_type = data.get('race_type', ['芝']).iloc[0] if isinstance(data.get('race_type'), pd.Series) else data.get('race_type', '芝')
        
        # 出走頭数から期待着順を計算
        field_size = len(data) if hasattr(data, '__len__') else 16
        expected_rank = max(4.0, min(field_size - 2, (field_size + 1) / 2.0))
        
        # 距離に応じた上り時間のデフォルト値
        if distance <= 1400:
            default_time = 33.5  # 短距離
        elif distance <= 1800:
            default_time = 35.0  # 中距離
        else:
            default_time = 37.0  # 長距離
        
        # コース種別に応じた調整
        if race_type == 'ダート':
            default_time += 1.0  # ダートは若干遅い
            default_weight = 55.5  # ダートは若干軽い
        else:
            default_weight = 55.0  # 芝の標準斤量
        
        # より現実的なデフォルト値
        default_rank = expected_rank
        default_odds = max(3.0, expected_rank * 1.2)
        default_prize = 600000  # より現実的な賞金
        default_body_weight = 475  # より現実的な体重
        
        # last_5R統計
        data.loc[data.index[idx], '着順_last_5R_mean'] = default_rank
        data.loc[data.index[idx], '人気_last_5R_mean'] = expected_rank
        data.loc[data.index[idx], 'オッズ_last_5R_mean'] = default_odds
        data.loc[data.index[idx], '賞金_last_5R_mean'] = default_prize
        data.loc[data.index[idx], '斤量_last_5R_mean'] = default_weight
        data.loc[data.index[idx], '上り_last_5R_mean'] = default_time
        data.loc[data.index[idx], '体重_last_5R_mean'] = default_body_weight + np.random.uniform(-10, 10)
        data.loc[data.index[idx], '体重変化_last_5R_mean'] = np.random.uniform(-2, 2)
        
        # last_10R統計
        data.loc[data.index[idx], '着順_last_10R_mean'] = default_rank
        data.loc[data.index[idx], '人気_last_10R_mean'] = expected_rank
        data.loc[data.index[idx], 'オッズ_last_10R_mean'] = default_odds
        data.loc[data.index[idx], '賞金_last_10R_mean'] = default_prize
        data.loc[data.index[idx], '斤量_last_10R_mean'] = default_weight
        data.loc[data.index[idx], '上り_last_10R_mean'] = default_time
        data.loc[data.index[idx], '体重_last_10R_mean'] = default_body_weight + np.random.uniform(-10, 10)
        data.loc[data.index[idx], '体重変化_last_10R_mean'] = np.random.uniform(-2, 2)
        
        # all_R統計
        data.loc[data.index[idx], '着順_all_R_mean'] = default_rank
        data.loc[data.index[idx], '人気_all_R_mean'] = expected_rank
        data.loc[data.index[idx], 'オッズ_all_R_mean'] = default_odds
        data.loc[data.index[idx], '賞金_all_R_mean'] = default_prize
        data.loc[data.index[idx], '斤量_all_R_mean'] = default_weight
        data.loc[data.index[idx], '上り_all_R_mean'] = default_time
        data.loc[data.index[idx], '体重_all_R_mean'] = default_body_weight + np.random.uniform(-10, 10)
        data.loc[data.index[idx], '体重変化_all_R_mean'] = np.random.uniform(-2, 2)
        
        # インターバル
        data.loc[data.index[idx], 'interval_days'] = 35  # より現実的なレース間隔
    
    def _set_missing_performance_stats(self, data, idx):
        """データが存在しない場合の欠損値を設定（LightGBM用）"""
        # LightGBMが自動的に処理するため、すべて欠損値に設定
        
        # last_5R統計
        data.loc[data.index[idx], '着順_last_5R_mean'] = np.nan
        data.loc[data.index[idx], '人気_last_5R_mean'] = np.nan
        data.loc[data.index[idx], 'オッズ_last_5R_mean'] = np.nan
        data.loc[data.index[idx], '賞金_last_5R_mean'] = np.nan
        data.loc[data.index[idx], '斤量_last_5R_mean'] = np.nan
        data.loc[data.index[idx], '上り_last_5R_mean'] = np.nan
        data.loc[data.index[idx], '体重_last_5R_mean'] = np.nan
        data.loc[data.index[idx], '体重変化_last_5R_mean'] = np.nan
        
        # last_10R統計
        data.loc[data.index[idx], '着順_last_10R_mean'] = np.nan
        data.loc[data.index[idx], '人気_last_10R_mean'] = np.nan
        data.loc[data.index[idx], 'オッズ_last_10R_mean'] = np.nan
        data.loc[data.index[idx], '賞金_last_10R_mean'] = np.nan
        data.loc[data.index[idx], '斤量_last_10R_mean'] = np.nan
        data.loc[data.index[idx], '上り_last_10R_mean'] = np.nan
        data.loc[data.index[idx], '体重_last_10R_mean'] = np.nan
        data.loc[data.index[idx], '体重変化_last_10R_mean'] = np.nan
        
        # all_R統計
        data.loc[data.index[idx], '着順_all_R_mean'] = np.nan
        data.loc[data.index[idx], '人気_all_R_mean'] = np.nan
        data.loc[data.index[idx], 'オッズ_all_R_mean'] = np.nan
        data.loc[data.index[idx], '賞金_all_R_mean'] = np.nan
        data.loc[data.index[idx], '斤量_all_R_mean'] = np.nan
        data.loc[data.index[idx], '上り_all_R_mean'] = np.nan
        data.loc[data.index[idx], '体重_all_R_mean'] = np.nan
        data.loc[data.index[idx], '体重変化_all_R_mean'] = np.nan
        
        # インターバル
        data.loc[data.index[idx], 'interval_days'] = np.nan
    
    def predict_race(self, race_id: str):
        """レース予測を実行（最新スクレイピング機能付き）"""
        try:
            logger.info(f"最新スクレイピング機能付きレース予測開始: {race_id}")
            
            # モデル読み込み
            if not self.model:
                if not self.load_latest_model():
                    raise RuntimeError("モデルの読み込みに失敗しました")
            
            # 出馬表取得
            race_data = self.scrape_race_card_requests(race_id)
            
            if race_data.empty:
                raise ValueError("出馬表データが取得できませんでした")
            
            logger.info(f"出馬表取得後の馬数: {len(race_data)}頭")
            
            # レース情報を設定（改善されたデフォルト値）
            smart_distance = self._get_smart_default_distance(race_id)
            smart_conditions = self._get_smart_default_conditions(race_id)
            
            race_info = {
                'race_id': race_id,
                'course_len': race_data.get('course_len', [smart_distance]).iloc[0] if 'course_len' in race_data.columns else smart_distance,
                'race_type': race_data.get('race_type', [smart_conditions['race_type']]).iloc[0] if 'race_type' in race_data.columns else smart_conditions['race_type'],
                'ground_state': race_data.get('ground_state', [smart_conditions['ground_state']]).iloc[0] if 'ground_state' in race_data.columns else smart_conditions['ground_state'],
                'weather': race_data.get('weather', [smart_conditions['weather']]).iloc[0] if 'weather' in race_data.columns else smart_conditions['weather'],
                'track_direction': race_data.get('track_direction', [smart_conditions['track_direction']]).iloc[0] if 'track_direction' in race_data.columns else smart_conditions['track_direction']
            }
            
            # 馬IDを抽出（改善版）
            horse_ids = []
            valid_horse_ids = []
            if 'horse_id' in race_data.columns:
                horse_ids = race_data['horse_id'].dropna().tolist()
                # 空の文字列や無効なIDを除去
                for hid in horse_ids:
                    if hid and str(hid).strip() and str(hid) != 'nan' and not str(hid).startswith('h'):
                        # 実際の馬IDパターン（通常10桁の数字）をチェック
                        if re.match(r'^\d{10}$', str(hid)):
                            valid_horse_ids.append(str(hid))
                        elif len(str(hid)) >= 8:  # その他の有効そうなID
                            valid_horse_ids.append(str(hid))
                
                horse_ids = valid_horse_ids
                logger.info(f"出馬表から有効な馬ID取得: {len(horse_ids)}頭")
                if horse_ids:
                    logger.info(f"馬IDサンプル: {horse_ids[:3]}")
                else:
                    logger.warning("有効な馬IDが見つかりませんでした")
            
            # 最新戦績データをリアルタイムスクレイピング
            horse_stats = None
            if horse_ids and self.enable_live_scraping:
                try:
                    current_date = datetime.now().strftime('%Y-%m-%d')
                    logger.info(f"最新戦績スクレイピング開始: 対象馬数={len(horse_ids)}")
                    
                    horse_stats = self.get_live_horse_performance(horse_ids, current_date)
                    
                    # 取得結果の検証とログ出力
                    if horse_stats and len(horse_stats) > 0:
                        scraped_count = sum(1 for stats in horse_stats.values() if stats.get('scraped_races', 0) > 0)
                        logger.info(f"最新戦績スクレイピング結果: 全{len(horse_stats)}頭中{scraped_count}頭で実際のデータを取得")
                        
                        # サンプル統計情報の表示
                        sample_horse = next(iter(horse_stats.keys()))
                        sample_stats = horse_stats[sample_horse]
                        logger.info(f"サンプル統計({sample_horse}): 戦績数={sample_stats.get('scraped_races', 0)}, 勝率={sample_stats.get('win_rate', 0):.3f}")
                    else:
                        logger.warning("有効な最新戦績データが取得できませんでした")
                        horse_stats = None
                        
                except Exception as e:
                    logger.warning(f"最新戦績スクレイピングに失敗: {e}")
                    horse_stats = None
            else:
                logger.info("最新戦績スクレイピングをスキップ（IDなしまたは機能無効）")
            
            # 特徴量準備
            X, processed_data = self.prepare_prediction_features(race_data, race_info, horse_stats)
            if X.empty:
                raise ValueError("特徴量の準備に失敗しました")
            
            logger.info(f"特徴量準備後の馬数: {len(X)}頭")
            logger.info(f"使用特徴量数: {len(X.columns)}個")
            
            # デバッグ情報
            if horse_stats:
                real_data_count = sum(1 for stats in horse_stats.values() if stats.get('scraped_races', 0) > 0)
                logger.info(f"リアルタイムスクレイピングデータを使用した馬: {real_data_count}頭")
            
            # 予測実行
            X_scaled = self.scaler.transform(X)
            prediction_proba = self.model.predict(X_scaled)
            
            # 結果整理
            base_cols = ['枠番', '馬番', '馬名', '性齢', '斤量']
            
            # 利用可能なカラムのみを選択
            available_cols = [col for col in base_cols if col in processed_data.columns]
            
            # 馬名カラムが存在しない場合のフォールバック
            if '馬名' not in processed_data.columns:
                processed_data['馬名'] = [f'Horse_{i+1}' for i in range(len(processed_data))]
                if '馬名' not in available_cols:
                    available_cols.insert(2, '馬名')  # 枠番、馬番の後に挿入
            
            results = processed_data[available_cols].copy()
            
            results['予測スコア'] = prediction_proba
            results['予測順位'] = results['予測スコア'].rank(ascending=False, method='first').astype(int)
            
            # 勝率を合計100%になるように正規化
            total_score = prediction_proba.sum()
            if total_score > 0:
                normalized_proba = (prediction_proba / total_score) * 100
                results['勝率'] = normalized_proba.round(1)
            else:
                results['勝率'] = (np.ones(len(prediction_proba)) / len(prediction_proba) * 100).round(1)
            
            # 3着以内確率は従来通り
            results['3着以内確率'] = (prediction_proba * 100).round(1)
            
            # 順位でソート
            results = results.sort_values('予測順位')
            
            logger.info(f"最新スクレイピング機能付きレース予測完了: 最終結果{len(results)}頭")
            
            return results, race_info
            
        except Exception as e:
            logger.error(f"レース予測エラー: {e}")
            return pd.DataFrame(), {}
    
    def display_prediction_results(self, results, race_info):
        """予測結果を見やすく表示"""
        if results.empty:
            print("[エラー] 予測結果がありません")
            return
        
        print("\n" + "="*90)
        print("[競馬AI] 予測結果（最新戦績スクレイピング機能付き強化版）")
        print("="*90)
        print(f"レースID: {race_info.get('race_id', 'N/A')}")
        print(f"距離: {race_info.get('course_len', 'N/A')}m")
        print(f"コース: {race_info.get('race_type', 'N/A')} {race_info.get('track_direction', 'N/A')}回り")
        print(f"馬場: {race_info.get('ground_state', 'N/A')} / 天気: {race_info.get('weather', 'N/A')}")
        print(f"予測時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 勝率の合計を確認
        total_win_rate = results['勝率'].sum() if '勝率' in results.columns else 0
        
        print(f"\n[予測結果] (予測順位順) - 出馬頭数: {len(results)}頭")
        print(f"勝率合計: {total_win_rate:.1f}% (正規化済み)")
        print("機能: リアルタイム出馬表取得 + 最新馬戦績スクレイピング")
        print("-" * 90)
        
        # 全頭の表示
        for i, (_, row) in enumerate(results.iterrows()):
            # 無効なデータをスキップ
            枠番 = row.get('枠番', 0)
            馬番 = row.get('馬番', 0)
            horse_name = row.get('馬名', 'N/A')
            
            # 無効な行をスキップ
            if (枠番 == 0 or 馬番 == 0 or 
                horse_name in ['N/A', '', '0', '--'] or 
                pd.isna(horse_name)):
                continue
            
            if pd.isna(horse_name):
                horse_name = 'N/A'
            
            性齢 = row.get('性齢', 'N/A')
            if pd.isna(性齢):
                性齢 = 'N/A'
            斤量 = row.get('斤量', 0)
            予測順位 = row.get('予測順位', 0)
            勝率 = row.get('勝率', 0)
            確率 = row.get('3着以内確率', 0)
            
            # 安全な文字列フォーマット
            try:
                position_str = f"{予測順位:2.0f}位"
                frame_horse_str = f"{枠番:2.0f}-{馬番:2.0f}"
                horse_name_str = f"{str(horse_name):12s}"
                age_sex_str = f"{str(性齢):4s}"
                weight_str = f"{斤量:4.1f}kg"
                win_rate_str = f"勝率:{勝率:5.1f}%"
                prob_str = f"3着内:{確率:5.1f}%"
                
                print(f"{position_str} {frame_horse_str} {horse_name_str} "
                      f"{age_sex_str} {weight_str} "
                      f"{win_rate_str} {prob_str}")
            except Exception as format_error:
                logger.warning(f"フォーマットエラー: {format_error}")
                # フォールバック表示
                print(f"{int(予測順位)}位 {int(枠番)}-{int(馬番)} {str(horse_name)} "
                      f"{str(性齢)} {float(斤量):.1f}kg "
                      f"勝率:{float(勝率):.1f}% 3着内:{float(確率):.1f}%")
        
        print("\n[買い目候補]")
        print("-" * 50)
        
        if len(results) >= 3:
            top3 = results.head(3)
            print("[3連複候補]")
            try:
                print(f"   {top3.iloc[0].get('枠番', 0):.0f}-{top3.iloc[0].get('馬番', 0):.0f}-"
                      f"{top3.iloc[1].get('枠番', 0):.0f}-{top3.iloc[1].get('馬番', 0):.0f}-"
                      f"{top3.iloc[2].get('枠番', 0):.0f}-{top3.iloc[2].get('馬番', 0):.0f}")
            except Exception as e:
                logger.warning(f"3連複候補の表示でエラー: {e}")
                print("   データが不完全です")
        
        if len(results) >= 1:
            winner_candidate = results.iloc[0]
            try:
                horse_name = winner_candidate.get('馬名', 'N/A')
                if pd.isna(horse_name):
                    horse_name = 'N/A'
                print(f"\n[単勝候補] {winner_candidate.get('枠番', 0):.0f}-{winner_candidate.get('馬番', 0):.0f} {horse_name}")
                print(f"   勝率: {winner_candidate.get('勝率', 0):.1f}%")
                print(f"   3着以内確率: {winner_candidate.get('3着以内確率', 0):.1f}%")
            except Exception as e:
                logger.warning(f"単勝候補の表示でエラー: {e}")
                print("\n[単勝候補] データが不完全です")
        
        print("\n[注意事項]")
        print("-" * 50)
        print("・この予測システムは最新戦績スクレイピング機能付き強化版です")
        print("・リアルタイムで馬の最新戦績データを取得し予測に反映しています")
        print("・出馬表と過去戦績の両方が最新データで構成されています")
        print("・投資は自己責任で行ってください")
        print("・予測はあくまで参考情報として活用してください")
        
        print("\n" + "="*90)

def main():
    """メイン実行関数"""
    try:
        print("[競馬AI] 予測システム（最新戦績スクレイピング機能付き強化版）")
        print("リアルタイム出馬表取得 + 馬の最新戦績リアルタイムスクレイピング")
        
        predictor = EnhancedLiveRacePredictorWithScraping(
            use_selenium=False,  # 環境に応じて調整
            enable_live_scraping=True  # 最新戦績スクレイピング有効
        )
        
        try:
            race_id = input("\nレースIDを入力してください (例: 202412080101): ").strip()
        except EOFError:
            print("非対話モードでテスト実行中...")
            race_id = "202406080101"  # テスト用レースID
        
        if not race_id:
            print("レースIDが入力されませんでした。デモを実行します。")
            race_id = "202406080101"  # テスト用レースID
        
        print(f"\n最新戦績スクレイピング機能付きレース予測を実行中: {race_id}")
        print("機能: リアルタイム出馬表 + 最新馬戦績スクレイピング + モデル予測")
        
        # 予測実行
        results, race_info = predictor.predict_race(race_id)
        
        if not results.empty:
            predictor.display_prediction_results(results, race_info)
        else:
            print("[エラー] 予測に失敗しました")
            print("・レースIDが存在しない可能性があります")
            print("・ネットワーク接続を確認してください")
            print("・一時的にアクセスが制限されている可能性があります")
        
    except KeyboardInterrupt:
        print("\n\n処理が中断されました")
    except Exception as e:
        logger.error(f"メイン実行エラー: {e}")
        print(f"[エラー] エラーが発生しました: {e}")

if __name__ == "__main__":
    main()