#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DateProcessorのテストコード
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import sys
import os

# プロジェクトパスの追加
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from core.processors.date_processor import DateProcessor, process_date_data

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class TestDateProcessor(unittest.TestCase):
    """DateProcessorのテストクラス"""
    
    def setUp(self):
        """テスト前の準備"""
        self.processor = DateProcessor()
        
        # テスト用のサンプルデータ
        self.sample_data = pd.DataFrame({
            'date': ['2020年7月25日', '2020年8月15日', '2020年9月5日', None, '2020年12月31日'],
            'birthday': ['2017年3月10日', '2018年5月20日', '2016年12月1日', '2019年1月15日', None],
            'race_date': ['2020/7/25', '2020/8/15', '2020/9/5', '2020/10/10', '2020/12/31'],
            'other_date': ['2020-07-25', '2020-08-15', '2020-09-05', '2020-10-10', '2020-12-31'],
            'race_id': ['202007250101', '202008150101', '202009050101', '202010100101', '202012310101'],
            'value': [1, 2, 3, 4, 5]
        })
    
    def test_parse_japanese_date(self):
        """日本語日付文字列の変換テスト"""
        # 正常なケース
        self.assertEqual(
            self.processor.parse_japanese_date('2020年7月25日'),
            pd.Timestamp('2020-07-25')
        )
        
        # Noneや空文字のケース
        self.assertTrue(pd.isna(self.processor.parse_japanese_date(None)))
        self.assertTrue(pd.isna(self.processor.parse_japanese_date('')))
        self.assertTrue(pd.isna(self.processor.parse_japanese_date('nan')))
        
        # 既にdatetime型のケース
        dt = datetime(2020, 7, 25)
        self.assertEqual(
            self.processor.parse_japanese_date(dt),
            pd.Timestamp('2020-07-25')
        )
        
        # 他の日付フォーマット
        self.assertEqual(
            self.processor.parse_japanese_date('2020/7/25'),
            pd.Timestamp('2020-07-25')
        )
        
        self.assertEqual(
            self.processor.parse_japanese_date('2020-07-25'),
            pd.Timestamp('2020-07-25')
        )
    
    def test_process_date_column(self):
        """日付カラムの処理テスト"""
        # 日本語日付カラムの処理
        japanese_dates = pd.Series(['2020年7月25日', '2020年8月15日', None, '2020年12月31日'])
        processed = self.processor.process_date_column(japanese_dates, 'test_date')
        
        # 結果の検証
        self.assertEqual(processed.dtype, 'datetime64[ns]')
        self.assertEqual(processed[0], pd.Timestamp('2020-07-25'))
        self.assertEqual(processed[1], pd.Timestamp('2020-08-15'))
        self.assertTrue(pd.isna(processed[2]))
        self.assertEqual(processed[3], pd.Timestamp('2020-12-31'))
    
    def test_detect_date_columns(self):
        """日付カラムの自動検出テスト"""
        detected = self.processor._detect_date_columns(self.sample_data)
        
        # 期待される日付カラムが検出されるかチェック
        expected_columns = ['date', 'birthday', 'race_date', 'other_date']
        for col in expected_columns:
            self.assertIn(col, detected)
        
        # 非日付カラムが検出されないかチェック
        self.assertNotIn('race_id', detected)
        self.assertNotIn('value', detected)
    
    def test_process_dataframe(self):
        """データフレーム全体の処理テスト"""
        processed_df = self.processor.process_dataframe(self.sample_data)
        
        # 日付カラムがdatetime型に変換されているかチェック
        date_columns = ['date', 'birthday', 'race_date', 'other_date']
        for col in date_columns:
            self.assertEqual(processed_df[col].dtype, 'datetime64[ns]')
        
        # 非日付カラムが変更されていないかチェック
        self.assertEqual(processed_df['race_id'].dtype, self.sample_data['race_id'].dtype)
        self.assertEqual(processed_df['value'].dtype, self.sample_data['value'].dtype)
        
        # 日付の値が正しく変換されているかチェック
        self.assertEqual(processed_df['date'][0], pd.Timestamp('2020-07-25'))
        self.assertEqual(processed_df['race_date'][0], pd.Timestamp('2020-07-25'))
        self.assertEqual(processed_df['other_date'][0], pd.Timestamp('2020-07-25'))
    
    def test_validate_date_range(self):
        """日付範囲の検証テスト"""
        # 正常なデータで検証
        processed_df = self.processor.process_dataframe(self.sample_data)
        validation = self.processor.validate_date_range(processed_df, 'date')
        
        self.assertTrue(validation['valid'])
        self.assertEqual(validation['min_date'], pd.Timestamp('2020-07-25'))
        self.assertEqual(validation['max_date'], pd.Timestamp('2020-12-31'))
        self.assertEqual(validation['valid_dates'], 4)  # Noneを除く
        self.assertEqual(validation['nat_count'], 1)
        
        # 存在しないカラムでの検証
        validation_invalid = self.processor.validate_date_range(processed_df, 'nonexistent')
        self.assertFalse(validation_invalid['valid'])
    
    def test_create_date_features(self):
        """日付派生特徴量の生成テスト"""
        processed_df = self.processor.process_dataframe(self.sample_data)
        featured_df = self.processor.create_date_features(processed_df, 'date')
        
        # 派生特徴量が生成されているかチェック
        expected_features = ['date_year', 'date_month', 'date_day', 'date_weekday', 'date_quarter', 'date_season']
        for feature in expected_features:
            self.assertIn(feature, featured_df.columns)
        
        # 値が正しく計算されているかチェック
        self.assertEqual(featured_df['date_year'][0], 2020)
        self.assertEqual(featured_df['date_month'][0], 7)
        self.assertEqual(featured_df['date_day'][0], 25)
    
    def test_error_handling(self):
        """エラーハンドリングのテスト"""
        # 厳密モードでのテスト
        strict_processor = DateProcessor(config={'strict_mode': True, 'error_handling': 'coerce'})
        
        # 不正な日付が多い場合
        bad_data = pd.DataFrame({
            'bad_date': ['invalid', 'also_invalid', 'still_invalid', '2020年1月1日', None]
        })
        
        # 厳密モードでは例外が発生する可能性がある
        try:
            result = strict_processor.process_dataframe(bad_data)
            # 例外が発生しない場合は、結果を確認
            self.assertTrue('bad_date' in result.columns)
        except ValueError:
            # 厳密モードで期待される例外
            pass
    
    def test_convenience_function(self):
        """便利関数のテスト"""
        result = process_date_data(self.sample_data)
        
        # 結果が正しく処理されているかチェック
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), len(self.sample_data))
        
        # 日付カラムが変換されているかチェック
        self.assertEqual(result['date'].dtype, 'datetime64[ns]')


class TestIntegrationWithComprehensiveIntegrator(unittest.TestCase):
    """ComprehensiveDataIntegratorとの統合テスト"""
    
    def setUp(self):
        """テスト前の準備"""
        # enhanced_comprehensive_data_2020.pickleが存在する場合のテスト
        self.test_pickle_path = 'enhanced_comprehensive_data_2020.pickle'
    
    def test_enhanced_data_loading(self):
        """修正済みデータの読み込みテスト"""
        if os.path.exists(self.test_pickle_path):
            data = pd.read_pickle(self.test_pickle_path)
            
            # データが正しく読み込まれているかチェック
            self.assertIsInstance(data, pd.DataFrame)
            self.assertGreater(len(data), 0)
            
            # 日付カラムがdatetime型になっているかチェック
            if 'date' in data.columns:
                self.assertEqual(data['date'].dtype, 'datetime64[ns]')
                
                # 日付範囲の妥当性チェック
                min_date = data['date'].min()
                max_date = data['date'].max()
                self.assertTrue(pd.Timestamp('2020-01-01') <= min_date <= pd.Timestamp('2020-12-31'))
                self.assertTrue(pd.Timestamp('2020-01-01') <= max_date <= pd.Timestamp('2020-12-31'))
                
                print(f"✅ データ期間: {min_date} ～ {max_date}")
        else:
            self.skipTest(f"テストファイル {self.test_pickle_path} が見つかりません")


def run_comprehensive_test():
    """包括的なテストの実行"""
    print("🧪 DateProcessorの包括的テストを開始")
    
    # 基本的なテストスイート
    suite = unittest.TestSuite()
    
    # 各テストクラスのテストを追加
    suite.addTest(unittest.makeSuite(TestDateProcessor))
    suite.addTest(unittest.makeSuite(TestIntegrationWithComprehensiveIntegrator))
    
    # テスト実行
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 結果のサマリー
    print(f"\n📊 テスト結果サマリー:")
    print(f"  実行したテスト数: {result.testsRun}")
    print(f"  失敗: {len(result.failures)}")
    print(f"  エラー: {len(result.errors)}")
    print(f"  スキップ: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.failures:
        print(f"\n❌ 失敗したテスト:")
        for test, error in result.failures:
            print(f"  - {test}: {error}")
    
    if result.errors:
        print(f"\n💥 エラーが発生したテスト:")
        for test, error in result.errors:
            print(f"  - {test}: {error}")
    
    if result.wasSuccessful():
        print(f"\n🎉 全てのテストが成功しました！")
        return True
    else:
        print(f"\n⚠️ 一部のテストが失敗しました")
        return False


if __name__ == "__main__":
    # 単体テストとして実行される場合
    if len(sys.argv) > 1 and sys.argv[1] == 'comprehensive':
        # 包括的テストを実行
        success = run_comprehensive_test()
        sys.exit(0 if success else 1)
    else:
        # 標準的なunittestを実行
        unittest.main()