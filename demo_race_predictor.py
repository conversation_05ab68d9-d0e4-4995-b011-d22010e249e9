#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
デモ用レース予測システム
学習済みモデルを使用してサンプルレースの予測を実行
"""

import pandas as pd
import numpy as np
import joblib
import logging
from datetime import datetime
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DemoRacePredictor:
    """デモ用レース予測クラス"""
    
    def __init__(self, model_dir="models"):
        """初期化"""
        self.model_dir = Path(model_dir)
        self.model = None
        self.scaler = None
        self.features = None
        logger.info("DemoRacePredictorを初期化しました")
    
    def load_latest_model(self):
        """最新の学習済みモデルを読み込み"""
        try:
            # 最新のモデルファイルを検索
            model_files = list(self.model_dir.glob("*enhanced*model*.pkl"))
            if not model_files:
                model_files = list(self.model_dir.glob("*model*.pkl"))
            
            if not model_files:
                raise FileNotFoundError("モデルファイルが見つかりません")
            
            # 最新のファイルを選択
            latest_model = max(model_files, key=lambda f: f.stat().st_mtime)
            
            # モデル、スケーラー、特徴量を読み込み
            model_path = latest_model
            scaler_path = self.model_dir / f"{latest_model.stem.replace('model', 'scaler')}.pkl"
            features_path = self.model_dir / f"{latest_model.stem.replace('model', 'features')}.pkl"
            
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            self.features = joblib.load(features_path)
            
            logger.info(f"モデル読み込み完了: {latest_model.name}")
            logger.info(f"特徴量数: {len(self.features)}")
            
            return True
            
        except Exception as e:
            logger.error(f"モデル読み込みエラー: {e}")
            return False
    
    def create_sample_race_data(self):
        """サンプルレースデータを作成"""
        # 実際の競馬場を参考にしたデモレース
        sample_horses = [
            {"枠番": 1, "馬番": 1, "馬名": "デモホース1号", "性齢": "牡4", "斤量": 57.0, "騎手": "デモ騎手A"},
            {"枠番": 1, "馬番": 2, "馬名": "デモホース2号", "性齢": "牝3", "斤量": 54.0, "騎手": "デモ騎手B"},
            {"枠番": 2, "馬番": 3, "馬名": "デモホース3号", "性齢": "牡5", "斤量": 58.0, "騎手": "デモ騎手C"},
            {"枠番": 2, "馬番": 4, "馬名": "デモホース4号", "性齢": "牝4", "斤量": 55.0, "騎手": "デモ騎手D"},
            {"枠番": 3, "馬番": 5, "馬名": "デモホース5号", "性齢": "牡3", "斤量": 56.0, "騎手": "デモ騎手E"},
            {"枠番": 3, "馬番": 6, "馬名": "デモホース6号", "性齢": "セ6", "斤量": 57.0, "騎手": "デモ騎手F"},
            {"枠番": 4, "馬番": 7, "馬名": "デモホース7号", "性齢": "牡4", "斤量": 56.0, "騎手": "デモ騎手G"},
            {"枠番": 4, "馬番": 8, "馬名": "デモホース8号", "性齢": "牝5", "斤量": 55.0, "騎手": "デモ騎手H"},
            {"枠番": 5, "馬番": 9, "馬名": "デモホース9号", "性齢": "牡3", "斤量": 56.0, "騎手": "デモ騎手I"},
            {"枠番": 5, "馬番": 10, "馬名": "デモホース10号", "性齢": "牝4", "斤量": 54.0, "騎手": "デモ騎手J"},
            {"枠番": 6, "馬番": 11, "馬名": "デモホース11号", "性齢": "牡6", "斤量": 57.0, "騎手": "デモ騎手K"},
            {"枠番": 6, "馬番": 12, "馬名": "デモホース12号", "性齢": "牝3", "斤量": 52.0, "騎手": "デモ騎手L"},
        ]
        
        race_info = {
            "レース名": "デモステークス",
            "course_len": 1600,
            "race_type": "芝",
            "ground_state": "良",
            "weather": "晴",
            "track_direction": "右"
        }
        
        return pd.DataFrame(sample_horses), race_info
    
    def prepare_prediction_features(self, race_data, race_info):
        """予測用特徴量を準備"""
        try:
            logger.info("デモ用特徴量を準備中...")
            
            data = race_data.copy()
            
            # レース情報を追加
            for key, value in race_info.items():
                data[key] = value
            
            # 基本特徴量
            data['course_len'] = race_info['course_len']
            data['枠番'] = pd.to_numeric(data['枠番'], errors='coerce')
            data['馬番'] = pd.to_numeric(data['馬番'], errors='coerce')
            data['斤量'] = pd.to_numeric(data['斤量'], errors='coerce')
            
            # 性別・年齢
            if '性齢' in data.columns:
                data['年齢'] = data['性齢'].str.extract(r'(\d+)').astype(float)
                data['性別_牡'] = data['性齢'].str.contains('牡', na=False).astype(int)
                data['性別_牝'] = data['性齢'].str.contains('牝', na=False).astype(int)
                data['性別_セ'] = data['性齢'].str.contains('セ', na=False).astype(int)
            
            # カテゴリカル特徴量のエンコーディング
            categorical_mapping = {
                'race_type': {'芝': 0, 'ダート': 1},
                'ground_state': {'良': 0, '稍重': 1, '重': 2, '不良': 3},
                'weather': {'晴': 0, '曇': 1, '雨': 2, '雪': 3},
                'track_direction': {'右': 0, '左': 1, '直線': 2}
            }
            
            for col, mapping in categorical_mapping.items():
                if col in data.columns:
                    data[f'{col}_encoded'] = data[col].map(mapping).fillna(0)
            
            # 距離カテゴリ
            data['距離_短距離'] = (data['course_len'] <= 1400).astype(int)
            data['距離_マイル'] = ((data['course_len'] > 1400) & (data['course_len'] <= 1800)).astype(int)
            data['距離_中距離'] = ((data['course_len'] > 1800) & (data['course_len'] <= 2200)).astype(int)
            data['距離_長距離'] = (data['course_len'] > 2200).astype(int)
            
            # 過去戦績特徴量（リアルなデモデータ）
            np.random.seed(42)
            n_horses = len(data)
            
            # より現実的な過去戦績データを生成
            data['total_races'] = np.random.randint(5, 40, n_horses)
            data['avg_rank'] = np.random.uniform(4, 10, n_horses)
            data['win_rate'] = np.random.beta(2, 8, n_horses)  # 勝率は低めに偏るベータ分布
            data['top3_rate'] = np.random.beta(3, 5, n_horses)  # 3着以内率
            data['rank_std'] = np.random.uniform(2, 4, n_horses)
            data['days_since_last_race'] = np.random.randint(14, 90, n_horses)
            
            # 年齢から生後日数を計算
            data['days_old'] = data['年齢'] * 365.25 + np.random.randint(-100, 100, n_horses)
            data['age_years'] = data['days_old'] / 365.25
            data['age_months'] = data['days_old'] / 30.44
            data['is_young'] = (data['age_years'] <= 3).astype(int)
            data['is_prime'] = ((data['age_years'] > 3) & (data['age_years'] <= 6)).astype(int)
            data['is_veteran'] = (data['age_years'] > 6).astype(int)
            
            # 派生特徴量
            data['経験豊富'] = (data['total_races'] >= 15).astype(int)
            data['好調'] = (data['win_rate'] >= 0.1).astype(int)
            data['連続出走'] = (data['days_since_last_race'] <= 30).astype(int)
            data['ベテラン_戦績'] = (data['total_races'] >= 25).astype(int)
            data['安定'] = (data['rank_std'] <= 3.0).astype(int)
            data['経験密度'] = data['total_races'] / data['age_years']
            data['若手有望'] = ((data['age_years'] <= 4) & (data['win_rate'] >= 0.1)).astype(int)
            data['円熟期'] = ((data['age_years'] >= 4) & (data['age_years'] <= 7) & (data['total_races'] >= 15)).astype(int)
            
            # 欠損値処理
            data = data.fillna(0)
            
            # 学習時の特徴量のみを選択
            available_features = []
            for col in self.features:
                if col in data.columns:
                    available_features.append(col)
                else:
                    # 不足している特徴量をゼロで埋める
                    data[col] = 0
                    available_features.append(col)
            
            X = data[self.features]
            
            logger.info(f"デモ特徴量準備完了: {X.shape}")
            return X, data
            
        except Exception as e:
            logger.error(f"特徴量準備エラー: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def predict_demo_race(self):
        """デモレース予測を実行"""
        try:
            logger.info("デモレース予測開始")
            
            # モデル読み込み
            if not self.load_latest_model():
                raise RuntimeError("モデルの読み込みに失敗しました")
            
            # サンプルデータ作成
            race_data, race_info = self.create_sample_race_data()
            
            # 特徴量準備
            X, processed_data = self.prepare_prediction_features(race_data, race_info)
            if X.empty:
                raise ValueError("特徴量の準備に失敗しました")
            
            # 予測実行
            X_scaled = self.scaler.transform(X)
            prediction_proba = self.model.predict(X_scaled)
            
            # 結果整理
            results = processed_data[['枠番', '馬番', '馬名', '性齢', '斤量', '騎手']].copy()
            results['予測スコア'] = prediction_proba
            results['予測順位'] = results['予測スコア'].rank(ascending=False, method='first').astype(int)
            results['3着以内確率'] = (prediction_proba * 100).round(1)
            
            # より詳細な情報を追加
            results['総レース数'] = processed_data['total_races'].astype(int)
            results['勝率'] = (processed_data['win_rate'] * 100).round(1)
            results['3着以内率'] = (processed_data['top3_rate'] * 100).round(1)
            results['前走からの日数'] = processed_data['days_since_last_race'].astype(int)
            
            # 順位でソート
            results = results.sort_values('予測順位')
            
            logger.info("デモレース予測完了")
            return results, race_info
            
        except Exception as e:
            logger.error(f"デモレース予測エラー: {e}")
            return pd.DataFrame(), {}
    
    def display_prediction_results(self, results, race_info):
        """予測結果を見やすく表示"""
        if results.empty:
            print("❌ 予測結果がありません")
            return
        
        print("\n" + "="*90)
        print("🐎 競馬AI予測システム - デモレース結果")
        print("="*90)
        print(f"レース: {race_info.get('レース名', 'デモレース')}")
        print(f"距離: {race_info.get('course_len', 1600)}m")
        print(f"コース: {race_info.get('race_type', '芝')} {race_info.get('track_direction', '右')}回り")
        print(f"馬場: {race_info.get('ground_state', '良')} / 天気: {race_info.get('weather', '晴')}")
        print(f"予測時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"使用モデル: 年齢詳細化版 (過去戦績＋生後日数ベース)")
        print("\n📊 予測結果詳細")
        print("-" * 90)
        print("順位 枠-馬 馬名          性齢 斤量  騎手      3着以内確率 勝率  3着率 総レース 前走日数")
        print("-" * 90)
        
        for _, row in results.iterrows():
            print(f"{row['予測順位']:2d}位 {row['枠番']:2.0f}-{row['馬番']:2.0f} {row['馬名']:12s} "
                  f"{row['性齢']:4s} {row['斤量']:4.1f}kg {row['騎手']:8s} "
                  f"{row['3着以内確率']:6.1f}%   {row['勝率']:4.1f}% {row['3着以内率']:4.1f}% "
                  f"{row['総レース数']:6d}戦 {row['前走からの日数']:6d}日")
        
        print("\n🎯 買い目候補")
        print("-" * 50)
        
        # 上位3頭
        top3 = results.head(3)
        print("🥇 3連複候補:")
        print(f"   {top3.iloc[0]['枠番']:.0f}-{top3.iloc[0]['馬番']:.0f}-{top3.iloc[1]['枠番']:.0f}-{top3.iloc[1]['馬番']:.0f}-{top3.iloc[2]['枠番']:.0f}-{top3.iloc[2]['馬番']:.0f}")
        print(f"   ({top3.iloc[0]['馬名']}-{top3.iloc[1]['馬名']}-{top3.iloc[2]['馬名']})")
        
        # 単勝候補
        winner_candidate = results.iloc[0]
        print(f"\n🏆 単勝候補: {winner_candidate['枠番']:.0f}-{winner_candidate['馬番']:.0f} {winner_candidate['馬名']}")
        print(f"   3着以内確率: {winner_candidate['3着以内確率']:.1f}%")
        
        # 穴馬候補
        dark_horses = results[(results['予測順位'] >= 6) & (results['3着以内確率'] >= 20)]
        if not dark_horses.empty:
            print(f"\n🌟 穴馬候補:")
            for _, row in dark_horses.iterrows():
                print(f"   {row['枠番']:.0f}-{row['馬番']:.0f} {row['馬名']} (確率:{row['3着以内確率']:.1f}%)")
        
        print("\n📈 予測根拠 (上位3頭)")
        print("-" * 50)
        for i, (_, row) in enumerate(results.head(3).iterrows(), 1):
            reasons = []
            if row['勝率'] >= 10:
                reasons.append(f"勝率{row['勝率']:.1f}%")
            if row['3着以内率'] >= 30:
                reasons.append(f"3着内率{row['3着以内率']:.1f}%")
            if row['総レース数'] >= 20:
                reasons.append("豊富な経験")
            if row['前走からの日数'] <= 30:
                reasons.append("間隔良好")
            
            reason_text = "、".join(reasons) if reasons else "安定した能力"
            print(f"{i}位 {row['馬名']}: {reason_text}")
        
        print("\n⚠️  注意事項")
        print("-" * 50)
        print("・これはデモ用の予測結果です")
        print("・実際の馬の過去戦績ではなく、サンプルデータを使用しています")
        print("・投資は自己責任で行ってください")
        print("・予測はあくまで参考情報として活用してください")
        
        print("\n" + "="*90)

def main():
    """メイン実行関数"""
    try:
        print("🐎 競馬AI予測システム - デモ版")
        print("学習済みモデルを使用してサンプルレースの予測を実行します\n")
        
        predictor = DemoRacePredictor()
        
        # デモ予測実行
        results, race_info = predictor.predict_demo_race()
        
        if not results.empty:
            predictor.display_prediction_results(results, race_info)
        else:
            print("❌ 予測に失敗しました")
            print("学習済みモデルが存在するか確認してください")
        
    except Exception as e:
        logger.error(f"メイン実行エラー: {e}")
        print(f"❌ エラーが発生しました: {e}")

if __name__ == "__main__":
    main()