#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
競馬レース分析・解説システム

このモジュールは競馬レースの詳細分析と解説を提供します。
- データ可視化
- 統計分析
- 予想根拠の説明
- レース解説の生成
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
import japanize_matplotlib
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import pickle

warnings.filterwarnings('ignore')

class RaceAnalyzer:
    """競馬レース分析・解説クラス"""
    
    def __init__(self, output_dir: str = "analysis_output"):
        """
        初期化
        
        Parameters
        ----------
        output_dir : str
            分析結果出力ディレクトリ
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # 日本語フォント設定
        plt.rcParams['font.family'] = 'DejaVu Sans'
        
    def analyze_race_prediction(self, 
                              predicted_results: Dict[str, Any], 
                              actual_results: pd.DataFrame,
                              race_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        レース予想の詳細分析
        
        Parameters
        ----------
        predicted_results : Dict[str, Any]
            AI予想結果
        actual_results : pd.DataFrame
            実際のレース結果
        race_info : Dict[str, Any]
            レース基本情報
            
        Returns
        -------
        Dict[str, Any]
            分析結果
        """
        analysis_result = {
            'race_overview': self._analyze_race_overview(race_info, actual_results),
            'prediction_accuracy': self._analyze_prediction_accuracy(predicted_results, actual_results),
            'odds_analysis': self._analyze_odds_distribution(actual_results),
            'performance_factors': self._analyze_performance_factors(predicted_results, actual_results),
            'recommendations': self._generate_recommendations(predicted_results, actual_results)
        }
        
        return analysis_result
    
    def _analyze_race_overview(self, race_info: Dict[str, Any], actual_results: pd.DataFrame) -> Dict[str, Any]:
        """レース概要分析"""
        overview = {
            'race_id': race_info.get('race_id', 'Unknown'),
            'total_horses': len(actual_results),
            'race_characteristics': {
                'distance': race_info.get('course_len', 'Unknown'),
                'track_type': race_info.get('race_type', 'Unknown'),
                'ground_condition': race_info.get('ground_state', 'Unknown'),
                'weather': race_info.get('weather', 'Unknown')
            }
        }
        
        # 人気分布分析
        if 'popularity' in actual_results.columns:
            popularity_stats = {
                'favorite_won': actual_results.iloc[0]['popularity'] == 1 if not actual_results.empty else False,
                'top3_popular_in_top3': 0,
                'upset_level': 'normal'
            }
            
            # 上位3着の人気分析
            top3_results = actual_results.head(3)
            if not top3_results.empty and 'popularity' in top3_results.columns:
                popular_horses_in_top3 = len(top3_results[top3_results['popularity'] <= 3])
                popularity_stats['top3_popular_in_top3'] = popular_horses_in_top3
                
                # 波乱度判定
                winner_popularity = actual_results.iloc[0]['popularity'] if not actual_results.empty else 1
                if winner_popularity >= 10:
                    popularity_stats['upset_level'] = 'major_upset'
                elif winner_popularity >= 5:
                    popularity_stats['upset_level'] = 'moderate_upset'
                else:
                    popularity_stats['upset_level'] = 'normal'
            
            overview['popularity_analysis'] = popularity_stats
        
        return overview
    
    def _analyze_prediction_accuracy(self, predicted_results: Dict[str, Any], 
                                   actual_results: pd.DataFrame) -> Dict[str, Any]:
        """予想精度の詳細分析"""
        predicted_top3 = predicted_results['馬番'][:3]
        actual_top3 = actual_results['umaban'].head(3).tolist() if 'umaban' in actual_results.columns else []
        
        accuracy = {
            'top3_matches': len(set(predicted_top3) & set(actual_top3)),
            'exact_winner': predicted_results['馬番'][0] == actual_top3[0] if actual_top3 else False,
            'detailed_comparison': []
        }
        
        # 各予想馬の詳細分析
        for i, horse_num in enumerate(predicted_results['馬番'][:5]):
            actual_rank = None
            if not actual_results.empty and 'umaban' in actual_results.columns:
                horse_actual = actual_results[actual_results['umaban'] == horse_num]
                if not horse_actual.empty:
                    actual_rank = horse_actual['rank'].iloc[0]
            
            horse_analysis = {
                'predicted_rank': i + 1,
                'horse_number': horse_num,
                'horse_name': predicted_results['馬名'][i],
                'predicted_win_rate': predicted_results['予想勝率'][i],
                'actual_rank': actual_rank,
                'rank_difference': abs((i + 1) - actual_rank) if actual_rank else None
            }
            
            accuracy['detailed_comparison'].append(horse_analysis)
        
        return accuracy
    
    def _analyze_odds_distribution(self, actual_results: pd.DataFrame) -> Dict[str, Any]:
        """オッズ分布分析"""
        odds_analysis = {}
        
        if 'tansho_odds' in actual_results.columns:
            odds_data = actual_results['tansho_odds'].dropna()
            
            if not odds_data.empty:
                odds_analysis = {
                    'winner_odds': actual_results.iloc[0]['tansho_odds'] if not actual_results.empty else None,
                    'odds_statistics': {
                        'mean': odds_data.mean(),
                        'median': odds_data.median(),
                        'min': odds_data.min(),
                        'max': odds_data.max(),
                        'std': odds_data.std()
                    },
                    'market_efficiency': self._calculate_market_efficiency(odds_data)
                }
        
        return odds_analysis
    
    def _calculate_market_efficiency(self, odds_data: pd.Series) -> Dict[str, float]:
        """市場効率性分析"""
        if len(odds_data) == 0:
            return {}
        
        # 理論確率の計算
        implied_probabilities = 1 / odds_data
        total_probability = implied_probabilities.sum()
        
        return {
            'total_probability': total_probability,
            'overround': (total_probability - 1) * 100,  # 控除率
            'market_bias': total_probability - 1
        }
    
    def _analyze_performance_factors(self, predicted_results: Dict[str, Any], 
                                   actual_results: pd.DataFrame) -> Dict[str, Any]:
        """パフォーマンス要因分析"""
        factors = {
            'prediction_model_performance': {
                'confidence_level': np.mean(predicted_results['予想勝率']),
                'prediction_variance': np.var(predicted_results['予想勝率']),
                'top_horse_confidence': predicted_results['予想勝率'][0]
            }
        }
        
        # 実際のレース結果から要因分析
        if not actual_results.empty:
            factors['race_competitiveness'] = {
                'field_size': len(actual_results),
                'competitive_balance': self._calculate_competitive_balance(actual_results)
            }
        
        return factors
    
    def _calculate_competitive_balance(self, results: pd.DataFrame) -> float:
        """競走バランス計算"""
        if 'tansho_odds' not in results.columns or len(results) == 0:
            return 0.0
        
        odds = results['tansho_odds'].dropna()
        if len(odds) == 0:
            return 0.0
        
        # オッズの変動係数で競争バランスを測定
        return odds.std() / odds.mean() if odds.mean() > 0 else 0.0
    
    def _generate_recommendations(self, predicted_results: Dict[str, Any], 
                                actual_results: pd.DataFrame) -> Dict[str, Any]:
        """推奨事項生成"""
        recommendations = {
            'betting_strategy': {
                'single_win': {},
                'exacta': {},
                'trifecta': {}
            },
            'model_improvements': [],
            'future_considerations': []
        }
        
        # 単勝推奨分析
        if predicted_results['予想勝率'][0] > 10:  # 10%以上の勝率
            recommendations['betting_strategy']['single_win'] = {
                'recommended': True,
                'horse_number': predicted_results['馬番'][0],
                'confidence': predicted_results['予想勝率'][0],
                'reason': f"AI予想勝率{predicted_results['予想勝率'][0]:.1f}%の高い信頼度"
            }
        
        # モデル改善提案
        predicted_top3 = predicted_results['馬番'][:3]
        actual_top3 = actual_results['umaban'].head(3).tolist() if 'umaban' in actual_results.columns else []
        match_rate = len(set(predicted_top3) & set(actual_top3)) / 3
        
        if match_rate < 0.3:
            recommendations['model_improvements'].append(
                "予想精度が低いため、特徴量エンジニアリングの見直しが必要"
            )
        
        return recommendations
    
    def create_analysis_visualizations(self, analysis_result: Dict[str, Any],
                                     predicted_results: Dict[str, Any],
                                     actual_results: pd.DataFrame) -> Dict[str, str]:
        """分析結果の可視化作成"""
        
        visualization_files = {}
        
        # 1. 予想vs実際の比較チャート
        fig1_path = self.create_prediction_comparison_chart(predicted_results, actual_results)
        if fig1_path:
            visualization_files['prediction_comparison'] = fig1_path
        
        # 2. オッズ分布チャート
        if 'tansho_odds' in actual_results.columns:
            fig2_path = self.create_odds_distribution_chart(actual_results)
            if fig2_path:
                visualization_files['odds_distribution'] = fig2_path
        
        # 3. パフォーマンス分析チャート
        fig3_path = self.create_performance_analysis_chart(analysis_result)
        if fig3_path:
            visualization_files['performance_analysis'] = fig3_path
        
        return visualization_files
    
    def create_prediction_comparison_chart(self, predicted_results: Dict[str, Any],
                                         actual_results: pd.DataFrame) -> Optional[str]:
        """予想vs実際の比較チャート作成"""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 予想上位10頭の表示
            top10_pred = predicted_results['馬番'][:10]
            top10_rates = predicted_results['予想勝率'][:10]
            top10_names = [name[:8] for name in predicted_results['馬名'][:10]]  # 名前を8文字に短縮
            
            # 予想結果
            bars1 = ax1.bar(range(len(top10_pred)), top10_rates, color='skyblue', alpha=0.7)
            ax1.set_title('AI予想 上位10頭 (勝率%)', fontsize=14, fontweight='bold')
            ax1.set_xlabel('予想順位', fontsize=12)
            ax1.set_ylabel('予想勝率 (%)', fontsize=12)
            ax1.set_xticks(range(len(top10_pred)))
            ax1.set_xticklabels([f"{i+1}位\n馬{num}" for i, num in enumerate(top10_pred)], rotation=45)
            
            # 値をバーの上に表示
            for i, (bar, rate) in enumerate(zip(bars1, top10_rates)):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                        f'{rate:.1f}%', ha='center', va='bottom', fontsize=10)
            
            # 実際の結果（上位10頭）
            if not actual_results.empty and 'umaban' in actual_results.columns:
                top10_actual = actual_results.head(10)
                actual_horses = top10_actual['umaban'].tolist()
                actual_odds = top10_actual['tansho_odds'].tolist() if 'tansho_odds' in top10_actual.columns else [0] * len(actual_horses)
                
                bars2 = ax2.bar(range(len(actual_horses)), actual_odds, color='lightcoral', alpha=0.7)
                ax2.set_title('実際の結果 上位10頭 (オッズ)', fontsize=14, fontweight='bold')
                ax2.set_xlabel('着順', fontsize=12)
                ax2.set_ylabel('単勝オッズ', fontsize=12)
                ax2.set_xticks(range(len(actual_horses)))
                ax2.set_xticklabels([f"{i+1}着\n馬{num}" for i, num in enumerate(actual_horses)], rotation=45)
                
                # 値をバーの上に表示
                for i, (bar, odds) in enumerate(zip(bars2, actual_odds)):
                    if odds > 0:
                        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(actual_odds)*0.01,
                                f'{odds:.1f}', ha='center', va='bottom', fontsize=10)
            
            plt.tight_layout()
            
            file_path = self.output_dir / "prediction_comparison.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"予想比較チャート作成エラー: {e}")
            return None
    
    def create_odds_distribution_chart(self, actual_results: pd.DataFrame) -> Optional[str]:
        """オッズ分布チャート作成"""
        try:
            if 'tansho_odds' not in actual_results.columns:
                return None
            
            odds_data = actual_results['tansho_odds'].dropna()
            if len(odds_data) == 0:
                return None
            
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            
            # 1. オッズヒストグラム
            ax1.hist(odds_data, bins=20, color='lightblue', alpha=0.7, edgecolor='black')
            ax1.set_title('単勝オッズ分布', fontsize=14, fontweight='bold')
            ax1.set_xlabel('オッズ', fontsize=12)
            ax1.set_ylabel('頭数', fontsize=12)
            ax1.axvline(odds_data.mean(), color='red', linestyle='--', label=f'平均: {odds_data.mean():.1f}')
            ax1.legend()
            
            # 2. 人気vs着順の散布図
            if 'popularity' in actual_results.columns and 'rank' in actual_results.columns:
                popularity = actual_results['popularity'].dropna()
                rank = actual_results['rank'].dropna()
                
                ax2.scatter(popularity, rank, c='orange', alpha=0.6, s=60)
                ax2.set_title('人気 vs 着順', fontsize=14, fontweight='bold')
                ax2.set_xlabel('人気', fontsize=12)
                ax2.set_ylabel('着順', fontsize=12)
                ax2.grid(True, alpha=0.3)
                
                # 理想的な相関線を追加
                ax2.plot([1, max(popularity)], [1, max(popularity)], 'r--', alpha=0.5, label='理想的相関')
                ax2.legend()
            
            # 3. オッズ vs 着順
            rank_data = actual_results['rank'].dropna() if 'rank' in actual_results.columns else range(1, len(odds_data)+1)
            ax3.scatter(odds_data, rank_data, c='green', alpha=0.6, s=60)
            ax3.set_title('オッズ vs 着順', fontsize=14, fontweight='bold')
            ax3.set_xlabel('単勝オッズ', fontsize=12)
            ax3.set_ylabel('着順', fontsize=12)
            ax3.grid(True, alpha=0.3)
            
            # 4. 馬番 vs オッズ
            horse_numbers = actual_results['umaban'].dropna() if 'umaban' in actual_results.columns else range(1, len(odds_data)+1)
            ax4.bar(horse_numbers, odds_data, color='purple', alpha=0.6)
            ax4.set_title('馬番別オッズ', fontsize=14, fontweight='bold')
            ax4.set_xlabel('馬番', fontsize=12)
            ax4.set_ylabel('単勝オッズ', fontsize=12)
            
            plt.tight_layout()
            
            file_path = self.output_dir / "odds_distribution.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"オッズ分布チャート作成エラー: {e}")
            return None
    
    def create_performance_analysis_chart(self, analysis_result: Dict[str, Any]) -> Optional[str]:
        """パフォーマンス分析チャート作成"""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            
            # 1. 予想精度サマリー
            accuracy_data = analysis_result.get('prediction_accuracy', {})
            top3_matches = accuracy_data.get('top3_matches', 0)
            exact_winner = accuracy_data.get('exact_winner', False)
            
            categories = ['Top3的中', '1位的中', '外れ']
            values = [top3_matches, 1 if exact_winner else 0, 3 - top3_matches]
            colors = ['green', 'gold', 'red']
            
            ax1.pie(values, labels=categories, colors=colors, autopct='%1.1f%%', startangle=90)
            ax1.set_title('予想精度分析', fontsize=14, fontweight='bold')
            
            # 2. 予想勝率分布
            if 'detailed_comparison' in accuracy_data:
                comparison_data = accuracy_data['detailed_comparison']
                predicted_rates = [horse['predicted_win_rate'] for horse in comparison_data]
                horse_numbers = [horse['horse_number'] for horse in comparison_data]
                
                bars = ax2.bar(range(len(predicted_rates)), predicted_rates, color='lightcoral')
                ax2.set_title('予想勝率分布（上位5頭）', fontsize=14, fontweight='bold')
                ax2.set_xlabel('予想順位', fontsize=12)
                ax2.set_ylabel('予想勝率 (%)', fontsize=12)
                ax2.set_xticks(range(len(predicted_rates)))
                ax2.set_xticklabels([f"馬{num}" for num in horse_numbers])
                
                # 値をバーの上に表示
                for bar, rate in zip(bars, predicted_rates):
                    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                            f'{rate:.1f}%', ha='center', va='bottom', fontsize=10)
            
            # 3. 市場効率性分析
            odds_analysis = analysis_result.get('odds_analysis', {})
            if 'market_efficiency' in odds_analysis:
                efficiency_data = odds_analysis['market_efficiency']
                overround = efficiency_data.get('overround', 0)
                
                ax3.bar(['控除率'], [overround], color='orange')
                ax3.set_title('市場効率性分析', fontsize=14, fontweight='bold')
                ax3.set_ylabel('控除率 (%)', fontsize=12)
                ax3.axhline(y=20, color='red', linestyle='--', label='一般的控除率')
                ax3.legend()
                
                # 値を表示
                ax3.text(0, overround + 0.5, f'{overround:.1f}%', ha='center', va='bottom', fontsize=12)
            
            # 4. レース特性
            race_overview = analysis_result.get('race_overview', {})
            characteristics = race_overview.get('race_characteristics', {})
            
            char_text = f"""
レース特性:
距離: {characteristics.get('distance', 'Unknown')}m
コース: {characteristics.get('track_type', 'Unknown')}
馬場: {characteristics.get('ground_condition', 'Unknown')}
天気: {characteristics.get('weather', 'Unknown')}

出走頭数: {race_overview.get('total_horses', 'Unknown')}頭
"""
            
            ax4.text(0.1, 0.5, char_text, fontsize=12, verticalalignment='center',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue"))
            ax4.set_xlim(0, 1)
            ax4.set_ylim(0, 1)
            ax4.axis('off')
            ax4.set_title('レース基本情報', fontsize=14, fontweight='bold')
            
            plt.tight_layout()
            
            file_path = self.output_dir / "performance_analysis.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"パフォーマンス分析チャート作成エラー: {e}")
            return None
    
    def generate_detailed_report(self, analysis_result: Dict[str, Any],
                               predicted_results: Dict[str, Any],
                               actual_results: pd.DataFrame) -> str:
        """詳細分析レポート生成"""
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("競馬レース詳細分析レポート")
        report_lines.append("=" * 80)
        
        # レース概要
        overview = analysis_result.get('race_overview', {})
        report_lines.append(f"\n📋 レース概要:")
        report_lines.append(f"レースID: {overview.get('race_id', 'Unknown')}")
        report_lines.append(f"出走頭数: {overview.get('total_horses', 'Unknown')}頭")
        
        characteristics = overview.get('race_characteristics', {})
        report_lines.append(f"距離: {characteristics.get('distance', 'Unknown')}m")
        report_lines.append(f"コース: {characteristics.get('track_type', 'Unknown')}")
        report_lines.append(f"馬場状態: {characteristics.get('ground_condition', 'Unknown')}")
        report_lines.append(f"天気: {characteristics.get('weather', 'Unknown')}")
        
        # 予想精度分析
        accuracy = analysis_result.get('prediction_accuracy', {})
        report_lines.append(f"\n🎯 予想精度分析:")
        report_lines.append(f"Top3的中数: {accuracy.get('top3_matches', 0)}/3")
        report_lines.append(f"1位的中: {'○' if accuracy.get('exact_winner', False) else '×'}")
        
        # 詳細比較
        if 'detailed_comparison' in accuracy:
            report_lines.append(f"\n📊 詳細予想比較:")
            report_lines.append(f"{'予想順位':>6} {'馬番':>4} {'馬名':>16} {'予想勝率':>8} {'実着順':>6} {'順位差':>6}")
            report_lines.append("-" * 70)
            
            for horse in accuracy['detailed_comparison']:
                predicted_rank = horse['predicted_rank']
                horse_number = horse['horse_number']
                horse_name = horse['horse_name'][:16]
                win_rate = horse['predicted_win_rate']
                actual_rank = horse['actual_rank']
                rank_diff = horse['rank_difference']
                
                actual_rank_str = str(actual_rank) if actual_rank else "---"
                rank_diff_str = str(rank_diff) if rank_diff else "---"
                
                report_lines.append(f"{predicted_rank:>6} {horse_number:>4} {horse_name:>16} "
                                  f"{win_rate:>7.1f}% {actual_rank_str:>6} {rank_diff_str:>6}")
        
        # オッズ分析
        odds_analysis = analysis_result.get('odds_analysis', {})
        if odds_analysis:
            report_lines.append(f"\n💰 オッズ分析:")
            winner_odds = odds_analysis.get('winner_odds')
            if winner_odds:
                report_lines.append(f"勝利馬オッズ: {winner_odds:.1f}倍")
            
            odds_stats = odds_analysis.get('odds_statistics', {})
            if odds_stats:
                report_lines.append(f"オッズ統計:")
                report_lines.append(f"  平均: {odds_stats.get('mean', 0):.1f}倍")
                report_lines.append(f"  中央値: {odds_stats.get('median', 0):.1f}倍")
                report_lines.append(f"  最高: {odds_stats.get('max', 0):.1f}倍")
                report_lines.append(f"  最低: {odds_stats.get('min', 0):.1f}倍")
        
        # 推奨事項
        recommendations = analysis_result.get('recommendations', {})
        if recommendations:
            report_lines.append(f"\n🎯 推奨事項:")
            
            single_win = recommendations.get('betting_strategy', {}).get('single_win', {})
            if single_win.get('recommended', False):
                report_lines.append(f"単勝推奨: 馬番{single_win.get('horse_number')} "
                                  f"(信頼度{single_win.get('confidence', 0):.1f}%)")
            
            improvements = recommendations.get('model_improvements', [])
            if improvements:
                report_lines.append(f"\nモデル改善提案:")
                for improvement in improvements:
                    report_lines.append(f"  • {improvement}")
        
        # 総合評価
        report_lines.append(f"\n📈 総合評価:")
        
        top3_matches = accuracy.get('top3_matches', 0)
        if top3_matches >= 2:
            report_lines.append("予想精度: 優秀")
        elif top3_matches >= 1:
            report_lines.append("予想精度: 普通")
        else:
            report_lines.append("予想精度: 要改善")
        
        # 波乱度評価
        popularity_analysis = overview.get('popularity_analysis', {})
        upset_level = popularity_analysis.get('upset_level', 'normal')
        if upset_level == 'major_upset':
            report_lines.append("レース性質: 大波乱")
        elif upset_level == 'moderate_upset':
            report_lines.append("レース性質: 中波乱")
        else:
            report_lines.append("レース性質: 堅実")
        
        report_lines.append("\n" + "=" * 80)
        report_lines.append("分析完了")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)