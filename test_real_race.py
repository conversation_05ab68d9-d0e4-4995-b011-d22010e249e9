#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
実際のレースデータでのテスト実行
"""

import sys
import os
import pandas as pd
import numpy as np
import pickle
import joblib
import logging
from pathlib import Path
from datetime import datetime

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_with_real_race_data():
    """実際のレースデータを使用したテスト"""
    try:
        print("=== 実際のレースデータでのモデルテスト ===")
        
        # 2024年のレース結果データを読み込み
        race_results_path = "output/race_results_2024.pickle"
        race_info_path = "output/race_info_2024.pickle"
        
        if not os.path.exists(race_results_path):
            print(f"エラー: {race_results_path} が見つかりません")
            return False
        
        # データ読み込み
        print("2024年のレースデータを読み込み中...")
        race_results = pd.read_pickle(race_results_path)
        
        if os.path.exists(race_info_path):
            race_info = pd.read_pickle(race_info_path)
            print(f"レース情報: {len(race_info)}件")
        else:
            race_info = pd.DataFrame()
            print("レース情報ファイルが見つかりません")
        
        print(f"レース結果: {len(race_results)}件")
        print(f"ユニークレース数: {race_results['race_id'].nunique()}件")
        
        # サンプルレースを選択（データが豊富なレース）
        race_counts = race_results['race_id'].value_counts()
        sample_race_ids = race_counts[race_counts >= 10].head(3).index.tolist()
        
        print(f"\nテスト対象レース:")
        for race_id in sample_race_ids:
            race_horses = race_results[race_results['race_id'] == race_id]
            print(f"  {race_id}: {len(race_horses)}頭")
        
        # 最新のモデルを読み込み
        print("\n最新のモデルを読み込み中...")
        model_dir = Path("models")
        
        # データリーケージ修正版モデルを優先
        model_timestamp = "20250608_212220"
        model_path = model_dir / f"fixed_leakage_model_{model_timestamp}.pkl"
        scaler_path = model_dir / f"fixed_leakage_scaler_{model_timestamp}.pkl"
        features_path = model_dir / f"fixed_leakage_features_{model_timestamp}.pkl"
        encoders_path = model_dir / f"fixed_leakage_encoders_{model_timestamp}.pkl"
        
        if not all(path.exists() for path in [model_path, scaler_path, features_path]):
            # フォールバック: 他のモデルを探す
            model_files = list(model_dir.glob("*model*.pkl"))
            if model_files:
                latest_model = max(model_files, key=lambda f: f.stat().st_mtime)
                model_path = latest_model
                scaler_path = model_dir / f"{latest_model.stem.replace('model', 'scaler')}.pkl"
                features_path = model_dir / f"{latest_model.stem.replace('model', 'features')}.pkl"
                print(f"フォールバックモデル使用: {model_path.name}")
            else:
                print("エラー: モデルファイルが見つかりません")
                return False
        
        # モデル読み込み
        model = joblib.load(model_path)
        scaler = joblib.load(scaler_path)
        features = joblib.load(features_path)
        
        # エンコーダー読み込み（存在する場合）
        label_encoders = {}
        if encoders_path.exists():
            label_encoders = joblib.load(encoders_path)
            print(f"ラベルエンコーダー読み込み: {len(label_encoders)}個")
        
        print(f"モデル読み込み完了: {model_path.name}")
        print(f"使用特徴量数: {len(features)}")
        
        # テスト結果を保存
        test_results = []
        
        # 各レースでテスト
        for test_race_id in sample_race_ids[:2]:  # 最初の2レースをテスト
            print(f"\n--- レース {test_race_id} のテスト ---")
            
            # レースデータを取得
            race_horses = race_results[race_results['race_id'] == test_race_id].copy()
            print(f"出馬頭数: {len(race_horses)}頭")
            
            # 実際の結果
            actual_results = race_horses[['馬名', '着順', '人気']].copy()
            actual_results = actual_results.sort_values('着順')
            
            print("実際の結果 (着順順):")
            for _, horse in actual_results.head(5).iterrows():
                print(f"  {horse['着順']}着: {horse['馬名']} (人気: {horse['人気']})")
            
            # 予測用特徴量を準備
            try:
                X = prepare_prediction_features_simple(race_horses, features, label_encoders)
                
                if X.empty:
                    print("特徴量準備に失敗")
                    continue
                
                # 予測実行
                X_scaled = scaler.transform(X)
                predictions = model.predict(X_scaled)
                
                # 結果整理
                results_df = race_horses[['馬名', '着順', '人気']].copy()
                results_df['予測スコア'] = predictions
                results_df['予測順位'] = results_df['予測スコア'].rank(ascending=False, method='first')
                results_df = results_df.sort_values('予測順位')
                
                print("\n予測結果 (予測順位順):")
                for _, horse in results_df.head(5).iterrows():
                    print(f"  予測{horse['予測順位']:.0f}位: {horse['馬名']} "
                          f"(実際{horse['着順']}着, 人気{horse['人気']}, スコア{horse['予測スコア']:.3f})")
                
                # 精度評価
                # 1着的中
                predicted_winner = results_df.iloc[0]['馬名']
                actual_winner = actual_results.iloc[0]['馬名']
                winner_hit = predicted_winner == actual_winner
                
                # 3着以内的中率
                predicted_top3 = set(results_df.head(3)['馬名'].tolist())
                actual_top3 = set(actual_results.head(3)['馬名'].tolist())
                top3_hits = len(predicted_top3 & actual_top3)
                top3_accuracy = top3_hits / 3
                
                # スピアマン相関（順位相関）
                try:
                    merged = results_df.merge(actual_results[['馬名', '着順']], on='馬名', suffixes=('_pred', '_actual'))
                    rank_correlation = merged['予測順位'].corr(merged['着順_actual'], method='spearman')
                    if pd.isna(rank_correlation):
                        rank_correlation = 0.0
                except Exception as corr_error:
                    print(f"相関計算エラー: {corr_error}")
                    rank_correlation = 0.0
                
                result_summary = {
                    'race_id': test_race_id,
                    'horses': len(race_horses),
                    'winner_hit': winner_hit,
                    'top3_accuracy': top3_accuracy,
                    'rank_correlation': rank_correlation,
                    'predicted_winner': predicted_winner,
                    'actual_winner': actual_winner
                }
                
                test_results.append(result_summary)
                
                print(f"\n=== 評価結果 ===")
                print(f"1着的中: {'○' if winner_hit else '×'} ({predicted_winner} vs {actual_winner})")
                print(f"3着以内的中率: {top3_accuracy:.1%} ({top3_hits}/3)")
                print(f"順位相関: {rank_correlation:.3f}")
                
            except Exception as e:
                print(f"予測エラー: {e}")
                continue
        
        # 総合評価
        if test_results:
            print(f"\n=== 総合評価結果 ===")
            total_races = len(test_results)
            winner_hits = sum(1 for r in test_results if r['winner_hit'])
            avg_top3_accuracy = np.mean([r['top3_accuracy'] for r in test_results])
            avg_correlation = np.mean([r['rank_correlation'] for r in test_results])
            
            print(f"テストレース数: {total_races}")
            print(f"1着的中率: {winner_hits/total_races:.1%} ({winner_hits}/{total_races})")
            print(f"平均3着以内的中率: {avg_top3_accuracy:.1%}")
            print(f"平均順位相関: {avg_correlation:.3f}")
            
            # 結果の保存
            results_df = pd.DataFrame(test_results)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_path = f"real_race_test_results_{timestamp}.csv"
            results_df.to_csv(results_path, index=False)
            print(f"\n詳細結果を保存: {results_path}")
            
            return True
        else:
            print("テスト結果がありません")
            return False
            
    except Exception as e:
        logger.error(f"実際のレーステストエラー: {e}")
        import traceback
        traceback.print_exc()
        return False

def prepare_prediction_features_simple(race_data, feature_names, label_encoders=None):
    """シンプルな特徴量準備"""
    try:
        data = race_data.copy()
        
        # 基本的な数値特徴量
        data['枠番'] = pd.to_numeric(data.get('枠番', 1), errors='coerce').fillna(1)
        data['馬番'] = pd.to_numeric(data.get('馬番', 1), errors='coerce').fillna(1)
        data['斤量'] = pd.to_numeric(data.get('斤量', 57.0), errors='coerce').fillna(57.0)
        data['人気'] = pd.to_numeric(data.get('人気', 8), errors='coerce').fillna(8)
        
        # コース長（デフォルト値）
        data['course_len'] = 1600  # デフォルト値
        
        # 過去戦績統計（デフォルト値で設定）
        # last_5R統計
        data['着順_last_5R_mean'] = 8.0
        data['人気_last_5R_mean'] = 8.0
        data['オッズ_last_5R_mean'] = 10.0
        data['賞金_last_5R_mean'] = 500000
        data['斤量_last_5R_mean'] = 57.0
        data['上り_last_5R_mean'] = 35.0
        data['体重_last_5R_mean'] = 480
        data['体重変化_last_5R_mean'] = 0.0
        
        # last_10R統計
        data['着順_last_10R_mean'] = 8.0
        data['人気_last_10R_mean'] = 8.0
        data['オッズ_last_10R_mean'] = 10.0
        data['賞金_last_10R_mean'] = 500000
        data['斤量_last_10R_mean'] = 57.0
        data['上り_last_10R_mean'] = 35.0
        data['体重_last_10R_mean'] = 480
        data['体重変化_last_10R_mean'] = 0.0
        
        # all_R統計
        data['着順_all_R_mean'] = 8.0
        data['人気_all_R_mean'] = 8.0
        data['オッズ_all_R_mean'] = 10.0
        data['賞金_all_R_mean'] = 500000
        data['斤量_all_R_mean'] = 57.0
        data['上り_all_R_mean'] = 35.0
        data['体重_all_R_mean'] = 480
        data['体重変化_all_R_mean'] = 0.0
        
        # インターバル
        data['interval_days'] = 30
        
        # カテゴリカル変数（エンコーダーがある場合）
        if label_encoders:
            for col, encoder in label_encoders.items():
                if col in data.columns:
                    # 未知のカテゴリを処理
                    known_classes = set(encoder.classes_)
                    data[col] = data[col].fillna('unknown').astype(str)
                    mask = ~data[col].isin(known_classes)
                    if mask.any():
                        data.loc[mask, col] = encoder.classes_[0]  # 最初のクラスで置換
                    data[col] = encoder.transform(data[col])
                else:
                    data[col] = 0  # デフォルト値
        
        # 必要な特徴量のみを選択
        missing_features = []
        for col in feature_names:
            if col not in data.columns:
                data[col] = 0  # デフォルト値
                missing_features.append(col)
        
        if missing_features:
            print(f"デフォルト値で補完した特徴量: {len(missing_features)}個")
        
        X = data[feature_names]
        
        # 無限値やNaNの処理
        X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
        
        return X
        
    except Exception as e:
        print(f"特徴量準備エラー: {e}")
        return pd.DataFrame()

if __name__ == "__main__":
    test_with_real_race_data()