================================================================================
AI予想根拠説明レポート
================================================================================

📊 最重要特徴量 (Top 5):
  順位                  特徴量        重要度
----------------------------------------
   1           <USER>     <GROUP>.289
   2      着順_last_5R_mean     0.275
   3                   馬番     0.263
   4      賞金_last_5R_mean     0.196
   5    オッズ_last_10R_mean     0.131

🐎 個別予想根拠 (上位3頭):

• テスト馬1:
  有利要因: オッズ_last_10R_mean; 不利要因: race_class, 賞金_all_R_mean
  主な有利要因:
    - オッズ_last_10R_mean: +0.309
    - interval_days: +0.085
  主な不利要因:
    - race_class: -0.504
    - 賞金_all_R_mean: -0.195

• テスト馬2:
  有利要因: 着順_last_5R_mean; 不利要因: 賞金_all_R_mean, 馬番
  主な有利要因:
    - 着順_last_5R_mean: +0.657
    - 枠番: +0.083
  主な不利要因:
    - 賞金_all_R_mean: -0.356
    - 馬番: -0.254

• テスト馬3:
  有利要因: 斤量; 不利要因: 賞金_last_5R_mean, 上り_last_5R_mean
  主な有利要因:
    - 斤量: +0.149
    - 人気_last_10R_mean: +0.109
  主な不利要因:
    - 賞金_last_5R_mean: -0.282
    - 上り_last_5R_mean: -0.239

🧠 モデル分析:
最も影響力のある要因: race_class
モデル複雑度: 30 個の有効特徴量
予想安定性: 安定 (偏差: 0.075)

⚖️ 判断パターン分析:
常に有利に働く要因: 枠番, 斤量, 着順_last_10R_mean
常に不利に働く要因: 馬番, 斤量_last_5R_mean, 斤量_last_10R_mean
状況依存要因: 距離, 着順_last_5R_mean, 人気_last_5R_mean

================================================================================
予想根拠説明完了
================================================================================