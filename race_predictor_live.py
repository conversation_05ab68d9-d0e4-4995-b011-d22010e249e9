#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
実際のレース予想システム

学習済みのデータリーケージ修正版モデルを使用して、実際のレースの予想を行います。
"""

import pandas as pd
import numpy as np
import joblib
import logging
import argparse
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# プロジェクトモジュールのインポート
import sys
sys.path.append('.')
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LiveRacePredictor:
    """実際のレース予想クラス"""
    
    def __init__(self, model_timestamp: str = "20250608_212220"):
        """
        初期化
        
        Parameters
        ----------
        model_timestamp : str
            使用するモデルのタイムスタンプ
        """
        self.model_timestamp = model_timestamp
        self.models_dir = Path("models")
        
        # モデルファイルパス
        self.model_path = self.models_dir / f"fixed_leakage_model_{model_timestamp}.pkl"
        self.scaler_path = self.models_dir / f"fixed_leakage_scaler_{model_timestamp}.pkl"
        self.features_path = self.models_dir / f"fixed_leakage_features_{model_timestamp}.pkl"
        self.encoders_path = self.models_dir / f"fixed_leakage_encoders_{model_timestamp}.pkl"
        
        # モデル関連オブジェクト
        self.model = None
        self.scaler = None
        self.feature_columns = None
        self.label_encoders = None
        
        # データ統合器
        self.integrator = ComprehensiveDataIntegrator()
    
    def load_model(self) -> bool:
        """
        学習済みモデルを読み込み
        
        Returns
        -------
        bool
            読み込み成功フラグ
        """
        try:
            logger.info(f"モデル読み込み開始: {self.model_timestamp}")
            
            # ファイル存在確認
            for file_path in [self.model_path, self.scaler_path, self.features_path, self.encoders_path]:
                if not file_path.exists():
                    logger.error(f"ファイルが見つかりません: {file_path}")
                    return False
            
            # モデル読み込み
            self.model = joblib.load(self.model_path)
            self.scaler = joblib.load(self.scaler_path)
            self.feature_columns = joblib.load(self.features_path)
            self.label_encoders = joblib.load(self.encoders_path)
            
            logger.info(f"モデル読み込み完了: {len(self.feature_columns)}個の特徴量")
            return True
            
        except Exception as e:
            logger.error(f"モデル読み込みエラー: {e}")
            return False
    
    def get_race_data(self, race_id: str, year: str = None) -> Optional[pd.DataFrame]:
        """
        指定レースのデータを取得
        
        Parameters
        ----------
        race_id : str
            レースID (例: '202406010101')
        year : str, optional
            年度 (レースIDから自動推定される)
            
        Returns
        -------
        Optional[pd.DataFrame]
            レースデータ
        """
        try:
            logger.info(f"レースデータ取得開始: {race_id}")
            
            # 年度の推定
            if year is None:
                year = "20" + race_id[:2]  # レースIDの最初の2桁から年を推定
            
            logger.info(f"推定年度: {year}")
            
            # ComprehensiveDataIntegratorでデータ取得
            race_data = self.integrator.generate_comprehensive_table(
                year=year,
                race_id=race_id,
                include_race_info=True,
                include_horse_info=True,
                include_past_performance=True,
                use_pickle_source=True
            )
            
            if race_data.empty:
                logger.warning(f"レースデータが見つかりません: {race_id}")
                return None
            
            logger.info(f"レースデータ取得完了: {len(race_data)}頭の馬")
            return race_data
            
        except Exception as e:
            logger.error(f"レースデータ取得エラー: {e}")
            return None
    
    def prepare_features_for_prediction(self, race_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        予想用特徴量を準備
        
        Parameters
        ----------
        race_data : pd.DataFrame
            レースデータ
            
        Returns
        -------
        Optional[pd.DataFrame]
            予想用特徴量DataFrame
        """
        try:
            logger.info("予想用特徴量準備開始")
            
            # 必要な特徴量を抽出
            available_features = [col for col in self.feature_columns if col in race_data.columns]
            missing_features = [col for col in self.feature_columns if col not in race_data.columns]
            
            if missing_features:
                logger.warning(f"不足している特徴量 ({len(missing_features)}個): {missing_features[:5]}...")
            
            # 利用可能な特徴量でDataFrame作成
            feature_data = race_data[available_features].copy()
            
            # 不足している特徴量は0で補完
            for col in missing_features:
                feature_data[col] = 0
                logger.debug(f"特徴量を0で補完: {col}")
            
            # 特徴量の順序を学習時と合わせる
            feature_data = feature_data[self.feature_columns]
            
            # カテゴリカル特徴量のエンコーディング
            for col, encoder in self.label_encoders.items():
                if col in feature_data.columns:
                    # 欠損値処理
                    feature_data[col] = feature_data[col].fillna('unknown').astype(str)
                    
                    # 未知のカテゴリへの対処
                    known_labels = set(encoder.classes_)
                    mask = ~feature_data[col].isin(known_labels)
                    
                    if mask.any():
                        # 最も頻度の高いラベルで代替
                        most_common = encoder.classes_[0]
                        feature_data.loc[mask, col] = most_common
                        logger.debug(f"未知カテゴリを{most_common}で代替: {col}")
                    
                    feature_data[col] = encoder.transform(feature_data[col])
            
            # 数値特徴量の欠損値処理
            numeric_cols = feature_data.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if feature_data[col].isnull().any():
                    median_val = feature_data[col].median()
                    feature_data[col] = feature_data[col].fillna(median_val)
            
            # 無限値やNaNの最終チェック
            feature_data = feature_data.replace([np.inf, -np.inf], np.nan).fillna(0)
            
            logger.info(f"特徴量準備完了: {feature_data.shape}")
            return feature_data
            
        except Exception as e:
            logger.error(f"特徴量準備エラー: {e}")
            return None
    
    def predict_race(self, race_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        レース予想を実行
        
        Parameters
        ----------
        race_data : pd.DataFrame
            レースデータ
            
        Returns
        -------
        Optional[pd.DataFrame]
            予想結果
        """
        try:
            logger.info("レース予想実行開始")
            
            # 特徴量準備
            features = self.prepare_features_for_prediction(race_data)
            if features is None:
                return None
            
            # 特徴量スケーリング
            features_scaled = self.scaler.transform(features)
            
            # 予想実行
            win_probabilities = self.model.predict(features_scaled, num_iteration=self.model.best_iteration)
            
            # 結果をDataFrameにまとめ
            result_data = race_data[['馬番', '馬名', '騎手', '斤量']].copy()
            result_data['勝率'] = win_probabilities
            result_data['勝率(%)'] = (win_probabilities * 100).round(1)
            
            # 勝率順でソート
            result_data = result_data.sort_values('勝率', ascending=False).reset_index(drop=True)
            result_data['予想順位'] = range(1, len(result_data) + 1)
            
            # 基本統計
            logger.info(f"予想完了: {len(result_data)}頭")
            logger.info(f"最高勝率: {result_data['勝率(%)'].max():.1f}%")
            logger.info(f"平均勝率: {result_data['勝率(%)'].mean():.1f}%")
            
            return result_data
            
        except Exception as e:
            logger.error(f"予想実行エラー: {e}")
            return None
    
    def display_prediction_results(self, results: pd.DataFrame, race_id: str):
        """
        予想結果を表示
        
        Parameters
        ----------
        results : pd.DataFrame
            予想結果
        race_id : str
            レースID
        """
        print("\n" + "=" * 80)
        print(f"🏇 レース予想結果 - レースID: {race_id}")
        print("=" * 80)
        
        print(f"{'順位':>4} {'馬番':>4} {'馬名':>12} {'騎手':>8} {'斤量':>6} {'勝率':>8}")
        print("-" * 50)
        
        for _, row in results.head(10).iterrows():  # 上位10頭を表示
            print(f"{row['予想順位']:>4} {row['馬番']:>4} {str(row['馬名'])[:12]:>12} "
                  f"{str(row['騎手'])[:8]:>8} {row['斤量']:>6} {row['勝率(%)']:>7.1f}%")
        
        print("\n📊 予想サマリー:")
        print(f"   出走頭数: {len(results)}頭")
        print(f"   最高勝率: {results['勝率(%)'].max():.1f}% (馬番{results.iloc[0]['馬番']})")
        print(f"   平均勝率: {results['勝率(%)'].mean():.1f}%")
        
        # 上位3頭の推奨
        print(f"\n🎯 推奨:")
        top3 = results.head(3)
        print(f"   1着候補: 馬番{top3.iloc[0]['馬番']} {top3.iloc[0]['馬名']} ({top3.iloc[0]['勝率(%)']:.1f}%)")
        print(f"   2着候補: 馬番{top3.iloc[1]['馬番']} {top3.iloc[1]['馬名']} ({top3.iloc[1]['勝率(%)']:.1f}%)")
        print(f"   3着候補: 馬番{top3.iloc[2]['馬番']} {top3.iloc[2]['馬名']} ({top3.iloc[2]['勝率(%)']:.1f}%)")
        
        print("=" * 80)
    
    def save_prediction_results(self, results: pd.DataFrame, race_id: str) -> str:
        """
        予想結果をファイルに保存
        
        Parameters
        ----------
        results : pd.DataFrame
            予想結果
        race_id : str
            レースID
            
        Returns
        -------
        str
            保存ファイルパス
        """
        try:
            # 出力ディレクトリ作成
            output_dir = Path("predictions")
            output_dir.mkdir(exist_ok=True)
            
            # ファイル名
            timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
            filename = f"prediction_{race_id}_{timestamp}.csv"
            filepath = output_dir / filename
            
            # CSV保存
            results.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            logger.info(f"予想結果保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"結果保存エラー: {e}")
            return ""
    
    def predict_single_race(self, race_id: str, year: str = None, save_results: bool = True) -> Optional[pd.DataFrame]:
        """
        単一レースの予想を実行
        
        Parameters
        ----------
        race_id : str
            レースID
        year : str, optional
            年度
        save_results : bool
            結果保存フラグ
            
        Returns
        -------
        Optional[pd.DataFrame]
            予想結果
        """
        try:
            logger.info(f"レース予想開始: {race_id}")
            
            # 1. モデル読み込み
            if not self.load_model():
                logger.error("モデル読み込みに失敗しました")
                return None
            
            # 2. レースデータ取得
            race_data = self.get_race_data(race_id, year)
            if race_data is None:
                logger.error("レースデータの取得に失敗しました")
                return None
            
            # 3. 予想実行
            results = self.predict_race(race_data)
            if results is None:
                logger.error("予想実行に失敗しました")
                return None
            
            # 4. 結果表示
            self.display_prediction_results(results, race_id)
            
            # 5. 結果保存
            if save_results:
                filepath = self.save_prediction_results(results, race_id)
                if filepath:
                    print(f"\n💾 予想結果を保存しました: {filepath}")
            
            return results
            
        except Exception as e:
            logger.error(f"予想処理エラー: {e}")
            import traceback
            traceback.print_exc()
            return None


def main():
    """メイン実行関数"""
    parser = argparse.ArgumentParser(description='競馬レース予想システム')
    parser.add_argument('race_id', help='レースID (例: 202406010101)')
    parser.add_argument('--year', help='年度 (省略時は自動推定)')
    parser.add_argument('--model', default='20250608_212220', help='モデルタイムスタンプ')
    parser.add_argument('--no-save', action='store_true', help='結果保存をスキップ')
    
    args = parser.parse_args()
    
    # 予想システム実行
    predictor = LiveRacePredictor(model_timestamp=args.model)
    
    try:
        results = predictor.predict_single_race(
            race_id=args.race_id,
            year=args.year,
            save_results=not args.no_save
        )
        
        if results is not None:
            print(f"\n✅ レース{args.race_id}の予想が完了しました！")
        else:
            print(f"\n❌ レース{args.race_id}の予想に失敗しました。")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 予想中にエラーが発生しました: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()