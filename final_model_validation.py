#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最終的なモデル検証スクリプト

データリーケージ修正版モデルの包括的な検証を実行します。
"""

import pandas as pd
import numpy as np
import joblib
import logging
from pathlib import Path
from typing import Dict, Any, List
import warnings
warnings.filterwarnings('ignore')

# プロジェクトモジュールのインポート
import sys
sys.path.append('.')

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FinalModelValidator:
    """最終モデル検証クラス"""
    
    def __init__(self, model_timestamp: str = "20250608_212220"):
        """
        初期化
        
        Parameters
        ----------
        model_timestamp : str
            モデルのタイムスタンプ
        """
        self.model_timestamp = model_timestamp
        self.models_dir = Path("models")
        
        # モデルファイルパス
        self.model_path = self.models_dir / f"fixed_leakage_model_{model_timestamp}.pkl"
        self.scaler_path = self.models_dir / f"fixed_leakage_scaler_{model_timestamp}.pkl"
        self.features_path = self.models_dir / f"fixed_leakage_features_{model_timestamp}.pkl"
        self.encoders_path = self.models_dir / f"fixed_leakage_encoders_{model_timestamp}.pkl"
    
    def check_model_files(self) -> Dict[str, bool]:
        """
        モデルファイルの存在確認
        
        Returns
        -------
        Dict[str, bool]
            ファイル存在状況
        """
        logger.info("モデルファイルの存在確認")
        
        file_status = {
            'model': self.model_path.exists(),
            'scaler': self.scaler_path.exists(), 
            'features': self.features_path.exists(),
            'encoders': self.encoders_path.exists()
        }
        
        for file_type, exists in file_status.items():
            status = "✓" if exists else "✗"
            logger.info(f"  {status} {file_type}: {getattr(self, f'{file_type}_path')}")
        
        all_exist = all(file_status.values())
        logger.info(f"全ファイル存在: {'Yes' if all_exist else 'No'}")
        
        return file_status
    
    def validate_model_structure(self) -> Dict[str, Any]:
        """
        モデル構造の検証
        
        Returns
        -------
        Dict[str, Any]
            モデル構造情報
        """
        logger.info("モデル構造の検証開始")
        
        try:
            # モデル読み込み
            model = joblib.load(self.model_path)
            scaler = joblib.load(self.scaler_path)
            features = joblib.load(self.features_path)
            encoders = joblib.load(self.encoders_path)
            
            # モデル情報の取得
            structure_info = {
                'model_type': type(model).__name__,
                'num_features': len(features),
                'feature_names': features,
                'num_encoders': len(encoders),
                'encoder_features': list(encoders.keys()),
                'scaler_type': type(scaler).__name__,
                'scaler_features': len(scaler.feature_names_in_) if hasattr(scaler, 'feature_names_in_') else 'Unknown'
            }
            
            logger.info(f"モデルタイプ: {structure_info['model_type']}")
            logger.info(f"特徴量数: {structure_info['num_features']}")
            logger.info(f"エンコーダー数: {structure_info['num_encoders']}")
            logger.info(f"スケーラータイプ: {structure_info['scaler_type']}")
            
            # 特徴量の詳細ログ
            logger.info("使用特徴量:")
            for i, feature in enumerate(features):
                logger.info(f"  {i+1:2d}. {feature}")
            
            # エンコーダーの詳細ログ
            logger.info("エンコードされる特徴量:")
            for feature, encoder in encoders.items():
                classes = encoder.classes_ if hasattr(encoder, 'classes_') else []
                logger.info(f"  {feature}: {len(classes)}個のクラス")
            
            return structure_info
            
        except Exception as e:
            logger.error(f"モデル構造検証エラー: {e}")
            return {}
    
    def validate_data_leakage_prevention(self) -> Dict[str, Any]:
        """
        データリーケージ防止機能の検証
        
        Returns
        -------
        Dict[str, Any]
            データリーケージ防止検証結果
        """
        logger.info("データリーケージ防止機能の検証")
        
        # 危険な特徴量のリスト
        risky_features = [
            '人気', '単勝', 'オッズ', '着差', 'タイム', 'ﾀｲﾑ指数', 
            '通過', '上り', '調教ﾀｲﾑ', '厩舎ｺﾒﾝﾄ', '備考', '着順'
        ]
        
        # 使用特徴量の読み込み
        features = joblib.load(self.features_path)
        
        # リーケージリスクの確認
        found_risky = []
        for risky in risky_features:
            if risky in features:
                found_risky.append(risky)
        
        leakage_validation = {
            'risky_features_found': found_risky,
            'is_safe': len(found_risky) == 0,
            'total_features': len(features),
            'safe_features': len(features) - len(found_risky)
        }
        
        if leakage_validation['is_safe']:
            logger.info("✓ データリーケージリスクのある特徴量は検出されませんでした")
        else:
            logger.warning(f"✗ リスクのある特徴量が検出されました: {found_risky}")
        
        logger.info(f"安全な特徴量: {leakage_validation['safe_features']}/{leakage_validation['total_features']}")
        
        return leakage_validation
    
    def create_validation_summary(self) -> Dict[str, Any]:
        """
        検証結果の総合まとめ
        
        Returns
        -------
        Dict[str, Any]
            総合検証結果
        """
        logger.info("=" * 60)
        logger.info("最終的なモデル検証実行")
        logger.info("=" * 60)
        
        try:
            # 1. ファイル存在確認
            file_status = self.check_model_files()
            
            # 2. モデル構造検証
            structure_info = self.validate_model_structure()
            
            # 3. データリーケージ防止検証
            leakage_validation = self.validate_data_leakage_prevention()
            
            # 4. 総合評価
            overall_valid = (
                all(file_status.values()) and
                bool(structure_info) and
                leakage_validation['is_safe']
            )
            
            validation_summary = {
                'timestamp': self.model_timestamp,
                'file_status': file_status,
                'structure_info': structure_info,
                'leakage_validation': leakage_validation,
                'overall_valid': overall_valid,
                'validation_date': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            logger.info("=" * 60)
            logger.info("最終検証結果")
            logger.info("=" * 60)
            logger.info(f"モデルタイムスタンプ: {self.model_timestamp}")
            logger.info(f"ファイル完整性: {'✓ OK' if all(file_status.values()) else '✗ NG'}")
            logger.info(f"モデル構造: {'✓ OK' if structure_info else '✗ NG'}")
            logger.info(f"データリーケージ対策: {'✓ OK' if leakage_validation['is_safe'] else '✗ NG'}")
            logger.info(f"総合評価: {'✓ 合格' if overall_valid else '✗ 不合格'}")
            
            if overall_valid:
                logger.info("\nモデルは本番環境での使用に適しています。")
                logger.info("以下のファイルが利用可能です:")
                logger.info(f"  - モデル: {self.model_path}")
                logger.info(f"  - スケーラー: {self.scaler_path}")
                logger.info(f"  - 特徴量リスト: {self.features_path}")
                logger.info(f"  - エンコーダー: {self.encoders_path}")
            else:
                logger.warning("\nモデルに問題があります。修正が必要です。")
            
            return validation_summary
            
        except Exception as e:
            logger.error(f"検証中にエラーが発生: {e}")
            import traceback
            traceback.print_exc()
            return {'overall_valid': False, 'error': str(e)}


def main():
    """メイン実行関数"""
    
    validator = FinalModelValidator()
    
    try:
        results = validator.create_validation_summary()
        
        if results['overall_valid']:
            print("\n最終検証が正常に完了しました！")
            print("モデルは本番環境での使用準備が整いました。")
        else:
            print("\n検証で問題が発見されました。")
            print("詳細はログを確認してください。")
            sys.exit(1)
        
    except Exception as e:
        print(f"\n検証中にエラーが発生しました: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()