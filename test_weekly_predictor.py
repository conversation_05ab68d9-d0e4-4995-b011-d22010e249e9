#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
週間予測機能のテストスクリプト
"""

import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_weekly_predictor():
    """週間予測機能テスト"""
    try:
        print("🧪 週間予測機能テスト開始")
        print("=" * 50)
        
        # 1. モジュールインポートテスト
        print("\n1️⃣ モジュールインポートテスト")
        try:
            from weekly_race_predictor import WeeklyRacePredictor
            print("✅ weekly_race_predictor インポート成功")
        except ImportError as e:
            print(f"❌ インポートエラー: {e}")
            return False
        
        # 2. 予測システム初期化テスト
        print("\n2️⃣ 予測システム初期化テスト")
        predictor = WeeklyRacePredictor()
        
        # モデル確認
        models_dir = Path("models")
        if not models_dir.exists():
            print("❌ modelsディレクトリが見つかりません")
            return False
        
        model_files = list(models_dir.glob("*model*.pkl"))
        if not model_files:
            print("❌ モデルファイルが見つかりません")
            return False
        
        print(f"✅ モデルファイル確認: {len(model_files)}個")
        
        # 3. 日付計算テスト
        print("\n3️⃣ 日付計算テスト")
        test_dates = [
            "2024-12-14",  # 土曜日
            "2024-12-15",  # 日曜日
            "2024-12-16",  # 月曜日
            None           # 今日
        ]
        
        for test_date in test_dates:
            week_dates = predictor.get_week_dates(test_date)
            if week_dates:
                date_str = test_date if test_date else "今日"
                print(f"✅ {date_str}: {week_dates[0].strftime('%Y-%m-%d')}(土) - {week_dates[1].strftime('%Y-%m-%d')}(日)")
            else:
                print(f"❌ 日付計算失敗: {test_date}")
        
        # 4. レースID生成テスト（scraper.py統合版）
        print("\n4️⃣ レースID生成テスト（scraper.py統合版）")
        from datetime import datetime as dt
        test_date = dt(2024, 7, 2)  # 過去の開催日
        
        print(f"📅 テスト対象日: {test_date.strftime('%Y年%m月%d日')}")
        
        try:
            race_ids = predictor.generate_race_ids_for_date(test_date)
            
            if race_ids:
                print(f"✅ 取得されたレースID数: {len(race_ids)}")
                print(f"   例: {race_ids[:5]}...")
                if len(race_ids) > 5:
                    print(f"   例: {race_ids[-5:]}")
                
                # レースIDの形式確認
                valid_format = all(len(rid) == 12 and rid.isdigit() for rid in race_ids[:5])
                print(f"   レースID形式: {'✅ 正常' if valid_format else '❌ 異常'}")
            else:
                print("⚠️ この日は競馬の開催がない可能性があります")
                
        except Exception as e:
            print(f"❌ レースID取得テストエラー: {e}")
            # フォールバック機能のテスト
            print("📋 フォールバック機能をテスト中...")
            fallback_ids = predictor._generate_fallback_race_ids(test_date)
            print(f"   フォールバック: {len(fallback_ids)}件のレースID")
        
        # 5. 小規模予測テスト（1レースのみ）
        print("\n5️⃣ 小規模予測テスト")
        try:
            # 改善版予測システムの利用可能性確認
            from improved_live_predictor import ImprovedLiveRacePredictor
            
            test_predictor = ImprovedLiveRacePredictor()
            model_loaded = test_predictor.load_latest_model()
            
            if model_loaded:
                print("✅ 改善版予測システム利用可能")
                
                # テスト用レースIDで予測実行
                test_race_id = "202407020203"
                print(f"🎯 テスト予測実行: {test_race_id}")
                
                results, race_info = test_predictor.predict_race_improved(test_race_id)
                
                if not results.empty:
                    print(f"✅ テスト予測成功: {len(results)}頭")
                    top_horse = results.iloc[0]
                    print(f"   本命: {top_horse['馬名']} (人気{top_horse['人気']}, オッズ{top_horse['単勝オッズ']:.1f})")
                else:
                    print("❌ テスト予測失敗（結果なし）")
            else:
                print("❌ 改善版予測システム初期化失敗")
                
        except Exception as e:
            print(f"❌ 小規模予測テストエラー: {e}")
        
        # 6. 結果保存機能テスト
        print("\n6️⃣ 結果保存機能テスト")
        test_results = [
            {
                'race_id': '202412140101',
                'date': '2024-12-14',
                'venue': '01',
                'race_num': 1,
                'top1_horse': 'テストホース',
                'top1_odds': 3.5,
                'top1_popularity': 2,
                'top1_win_rate': 25.5,
                'top3_horses': ['テストホース', 'サンプル馬', 'ダミー馬'],
                'prediction_scores': [0.8, 0.6, 0.4],
                'distance': 1600,
                'horses_count': 16
            }
        ]
        
        try:
            predictor.save_results(test_results)
            print("✅ 結果保存機能テスト成功")
        except Exception as e:
            print(f"❌ 結果保存テストエラー: {e}")
        
        # 7. 統合システムメニューテスト
        print("\n7️⃣ 統合システムメニューテスト")
        try:
            from keiba_ai_main import KeibaAISystem
            system = KeibaAISystem()
            
            # prediction_menuの存在確認
            if hasattr(system, 'prediction_menu'):
                print("✅ prediction_menu存在確認")
            else:
                print("❌ prediction_menu不在")
            
            # 週間予測メソッド存在確認
            if hasattr(system, '_run_weekly_race_prediction'):
                print("✅ _run_weekly_race_prediction存在確認")
            else:
                print("❌ _run_weekly_race_prediction不在")
                
        except Exception as e:
            print(f"❌ 統合システムテストエラー: {e}")
        
        print("\n🎉 週間予測機能テスト完了")
        print("=" * 50)
        
        # 使用方法表示
        # 8. スケジュール確認機能テスト
        print("\n8️⃣ スケジュール確認機能テスト")
        try:
            from race_schedule_checker import RaceScheduleChecker
            
            schedule_checker = RaceScheduleChecker()
            print("✅ RaceScheduleCheckerインポート成功")
            
            # 簡単なテスト実行
            test_date = dt(2024, 7, 2)  # 過去の開催日
            
            print(f"📅 {test_date.strftime('%Y年%m月%d日')} の開催確認テスト...")
            race_dates = schedule_checker.get_race_dates_in_period(test_date, test_date)
            
            if race_dates:
                print(f"✅ 開催日確認成功: {len(race_dates)}日")
            else:
                print("⚠️ 開催日が見つからないか、アクセスエラー")
                
        except Exception as e:
            print(f"❌ スケジュール確認テストエラー: {e}")
        
        print("\n💡 使用方法:")
        print("1. 統合メニューから:")
        print("   python keiba_ai_main.py → 3(予測) → 8(週間予測)")
        print("\n2. スタンドアロンツールから:")
        print("   python weekly_race_predictor.py")
        print("   python weekly_race_predictor.py --date 2024-12-14")
        print("   python weekly_race_predictor.py --max-races 10")
        print("\n3. スケジュール確認:")
        print("   python race_schedule_checker.py")
        print("   python race_schedule_checker.py --date 2024-12-14 --save")
        
        return True
        
    except Exception as e:
        logger.error(f"テストエラー: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_date_calculations():
    """日付計算の詳細テスト"""
    print("\n📅 日付計算詳細テスト")
    print("-" * 30)
    
    test_cases = [
        ("2024-12-09", "月曜日"),  # 月曜 → 同じ週の土日
        ("2024-12-10", "火曜日"),  # 火曜 → 同じ週の土日
        ("2024-12-11", "水曜日"),  # 水曜 → 同じ週の土日
        ("2024-12-12", "木曜日"),  # 木曜 → 同じ週の土日
        ("2024-12-13", "金曜日"),  # 金曜 → 同じ週の土日
        ("2024-12-14", "土曜日"),  # 土曜 → 同じ週の土日
        ("2024-12-15", "日曜日"),  # 日曜 → 前日土曜と当日
    ]
    
    from weekly_race_predictor import WeeklyRacePredictor
    predictor = WeeklyRacePredictor()
    
    for date_str, weekday in test_cases:
        week_dates = predictor.get_week_dates(date_str)
        if week_dates:
            saturday, sunday = week_dates
            print(f"{date_str} ({weekday}) → {saturday.strftime('%m/%d')}(土) - {sunday.strftime('%m/%d')}(日)")
        else:
            print(f"❌ {date_str} 計算失敗")

if __name__ == "__main__":
    success = test_weekly_predictor()
    
    # 詳細テスト実行
    test_date_calculations()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 週間予測機能テストが正常に完了しました")
        sys.exit(0)
    else:
        print("❌ 週間予測機能テストに問題があります")
        sys.exit(1)