#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改善されたデフォルト値システムのテストスクリプト
"""

import sys
import os
import pandas as pd
import logging

# パスを追加
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# ロガー設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_improved_default_values():
    """改善されたデフォルト値システムのテスト"""
    try:
        print("=== 改善されたデフォルト値システムテスト ===")
        
        from enhanced_live_predictor_with_scraping import EnhancedLiveRacePredictorWithScraping
        
        # スクレイピング機能を無効化してテスト（安全のため）
        predictor = EnhancedLiveRacePredictorWithScraping(
            use_selenium=False,
            enable_live_scraping=False
        )
        
        # モデル読み込み
        model_loaded = predictor.load_latest_model()
        if not model_loaded:
            print("❌ モデル読み込み失敗")
            return False
        
        print("✅ モデル読み込み成功")
        
        # 距離推定機能のテスト
        print("\n=== 距離推定機能テスト ===")
        test_cases = [
            ('202406080101', '中山'),  # 中山競馬場
            ('202407040201', '中京'),  # 中京競馬場  
            ('202408100501', '東京'),  # 東京競馬場
            ('202409020601', '中山'),  # 中山競馬場
            ('202410040701', '中京'),  # 中京競馬場
        ]
        
        for race_id, venue_name in test_cases:
            smart_distance = predictor._get_smart_default_distance(race_id)
            smart_conditions = predictor._get_smart_default_conditions(race_id)
            
            print(f"{venue_name}({race_id}): {smart_distance}m, "
                  f"{smart_conditions['race_type']}, {smart_conditions['ground_state']}, "
                  f"{smart_conditions['weather']}, {smart_conditions['track_direction']}回り")
        
        # 出走頭数別デフォルト統計テスト
        print("\n=== 出走頭数別デフォルト統計テスト ===")
        for field_size in [8, 12, 16, 18]:
            stats = predictor._generate_default_stats(field_size)
            print(f"{field_size:2d}頭立て: 期待着順{stats['avg_rank']:4.1f}位, "
                  f"勝率{stats['win_rate']*100:4.1f}%, "
                  f"複勝率{stats['show_rate']*100:4.1f}%, "
                  f"平均オッズ{stats['avg_odds']:4.1f}倍")
        
        # 特徴量準備テスト（モックデータ）
        print("\n=== 特徴量準備テスト ===")
        
        # モック出馬表データ
        mock_race_data = pd.DataFrame({
            'horse_id': ['2022104922', '2022104714', '2021105522'],
            '枠番': [1, 2, 3],
            '馬番': [1, 2, 3],
            '馬名': ['テストホース1', 'テストホース2', 'テストホース3'],
            '性齢': ['牡4', '牝3', 'セ5'],
            '斤量': [55.0, 54.0, 57.0]
        })
        
        # モックレース情報（改善されたデフォルト値を使用）
        race_id = '202406080101'
        race_info = {
            'race_id': race_id,
            'course_len': predictor._get_smart_default_distance(race_id),
            **predictor._get_smart_default_conditions(race_id)
        }
        
        print(f"レース情報: {race_info}")
        
        # 特徴量準備
        X, processed_data = predictor.prepare_prediction_features(
            mock_race_data, race_info, horse_stats=None
        )
        
        if not X.empty:
            print(f"✅ 特徴量準備成功: {X.shape}")
            print(f"サンプル特徴量:")
            sample_features = {
                'course_len': X['course_len'].iloc[0] if 'course_len' in X.columns else 'N/A',
                '着順_last_5R_mean': X['着順_last_5R_mean'].iloc[0] if '着順_last_5R_mean' in X.columns else 'N/A',
                '体重_last_5R_mean': X['体重_last_5R_mean'].iloc[0] if '体重_last_5R_mean' in X.columns else 'N/A',
                '斤量_last_5R_mean': X['斤量_last_5R_mean'].iloc[0] if '斤量_last_5R_mean' in X.columns else 'N/A',
                'interval_days': X['interval_days'].iloc[0] if 'interval_days' in X.columns else 'N/A'
            }
            for key, value in sample_features.items():
                print(f"  {key}: {value}")
            
            # 改善前後の比較
            print(f"\n改善された値:")
            print(f"  距離: {race_info['course_len']}m (従来: 1600m固定)")
            print(f"  期待着順: {sample_features.get('着順_last_5R_mean', 'N/A')} (従来: 8.0位固定)")
            print(f"  体重: {sample_features.get('体重_last_5R_mean', 'N/A')}kg (従来: 480kg固定)")
            print(f"  斤量: {sample_features.get('斤量_last_5R_mean', 'N/A')}kg (従来: 57.0kg固定)")
            print(f"  間隔: {sample_features.get('interval_days', 'N/A')}日 (従来: 30日固定)")
            
            return True
        else:
            print("❌ 特徴量準備失敗")
            return False
        
    except Exception as e:
        print(f"❌ テストエラー: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """メインテスト実行"""
    print("改善されたデフォルト値システムテスト")
    print("=" * 50)
    
    # 改善されたデフォルト値システムのテスト
    improved_test = test_improved_default_values()
    
    print("\n" + "=" * 50)
    print("テスト結果まとめ:")
    print(f"  改善されたデフォルト値システム: {'✅ 成功' if improved_test else '❌ 失敗'}")
    
    overall_success = improved_test
    print(f"\n総合結果: {'✅ 改善確認' if overall_success else '❌ 改善に問題'}")
    
    if overall_success:
        print("\n🎉 デフォルト値システムの改善が確認できました！")
        print("   - 競馬場別の適切な距離推定")
        print("   - 出走頭数に応じた統計値")
        print("   - 季節に応じた馬場・天気条件")
        print("   - より現実的な数値特徴量")
    else:
        print("\n⚠️  デフォルト値システムに問題があります")
        print("   - 設定を確認してください")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)