#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改善版実際のレース予測システム
- リアルタイム過去戦績取得強化
- 人気・オッズ重み調整
- 特徴量エンジニアリング見直し
"""

import sys
import os
import pandas as pd
import numpy as np
import requests
from bs4 import BeautifulSoup
import joblib
import logging
import re
import time
import random
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

# コアモジュールのインポート
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from core.scrapers.scraper import scrape_html_horse
from core.utils.constants import LocalPaths

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedLiveRacePredictor:
    """改善版実際のレース予測クラス"""
    
    def __init__(self, model_dir="models"):
        self.model_dir = Path(model_dir)
        self.model = None
        self.scaler = None
        self.features = None
        self.label_encoders = {}
        
        # セッション設定
        self.session = requests.Session()
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
            'Connection': 'keep-alive'
        })
        
        # 過去戦績データキャッシュ
        self.past_performance_cache = {}
        
        logger.info("ImprovedLiveRacePredictorを初期化しました")
    
    def load_latest_model(self, model_timestamp="20250608_212220"):
        """最新モデルを読み込み"""
        try:
            model_path = self.model_dir / f"fixed_leakage_model_{model_timestamp}.pkl"
            scaler_path = self.model_dir / f"fixed_leakage_scaler_{model_timestamp}.pkl"
            features_path = self.model_dir / f"fixed_leakage_features_{model_timestamp}.pkl"
            encoders_path = self.model_dir / f"fixed_leakage_encoders_{model_timestamp}.pkl"
            
            if not all(path.exists() for path in [model_path, scaler_path, features_path]):
                raise FileNotFoundError("必要なモデルファイルが見つかりません")
            
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            self.features = joblib.load(features_path)
            
            if encoders_path.exists():
                self.label_encoders = joblib.load(encoders_path)
                logger.info(f"ラベルエンコーダー読み込み: {len(self.label_encoders)}個")
            
            logger.info(f"モデル読み込み完了: {model_path.name}")
            logger.info(f"特徴量数: {len(self.features)}")
            return True
            
        except Exception as e:
            logger.error(f"モデル読み込みエラー: {e}")
            return False
    
    def scrape_race_card_improved(self, race_id: str) -> pd.DataFrame:
        """改善版出馬表取得（人気・オッズ重視）"""
        try:
            url = f"https://race.netkeiba.com/race/shutuba.html?race_id={race_id}"
            logger.info(f"出馬表取得: {url}")
            
            time.sleep(random.uniform(2, 4))
            
            try:
                response = self.session.get(url, timeout=15, verify=False)
                response.raise_for_status()
            except Exception as e:
                logger.error(f"出馬表取得エラー: {e}")
                return pd.DataFrame()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 出馬表テーブルを探す
            table = soup.find('table', class_='HorseList')
            if not table:
                # フォールバック
                tables = soup.find_all('table')
                for t in tables:
                    if len(t.find_all('tr')) > 5:  # 十分な行数
                        table = t
                        break
            
            if not table:
                logger.error("出馬表テーブルが見つかりません")
                return pd.DataFrame()
            
            rows = []
            all_rows = table.find_all('tr')
            
            for i, tr in enumerate(all_rows[1:]):  # ヘッダーをスキップ
                cols = tr.find_all(['td', 'th'])
                
                if len(cols) >= 6:
                    row_data = {}
                    
                    # 基本情報
                    row_data['枠番'] = self._extract_number(cols[0])
                    row_data['馬番'] = self._extract_number(cols[1])
                    
                    # 馬名とhorse_id
                    horse_link = cols[2].find('a') if len(cols) > 2 else None
                    if horse_link:
                        horse_href = horse_link.get('href', '')
                        # netkeibaの馬IDパターン: /horse/2020103456 のような10桁の数字
                        horse_id_match = re.search(r'/horse/(\d{10})', horse_href)
                        if not horse_id_match:
                            # 別のパターンも試す: horse_id=パラメータ
                            horse_id_match = re.search(r'horse_id=(\d+)', horse_href)
                        if not horse_id_match:
                            # さらに別のパターン: /horse/ped/ または /horse/result/
                            horse_id_match = re.search(r'/horse/(?:ped/|result/)?(\d+)', horse_href)
                        
                        row_data['horse_id'] = horse_id_match.group(1) if horse_id_match else ''
                        row_data['馬名'] = self._clean_horse_name(horse_link.get_text(strip=True))
                        
                        # デバッグ情報
                        if row_data['horse_id']:
                            logger.debug(f"馬ID取得成功: {row_data['馬名']} -> {row_data['horse_id']}")
                        else:
                            logger.warning(f"馬ID取得失敗: {row_data['馬名']} (href: {horse_href})")
                    else:
                        row_data['horse_id'] = ''
                        row_data['馬名'] = self._clean_horse_name(self._extract_text(cols[2]))
                    
                    # 基本データ
                    row_data['性齢'] = self._extract_text(cols[3]) if len(cols) > 3 else ''
                    row_data['斤量'] = self._extract_number(cols[4]) if len(cols) > 4 else 57.0
                    row_data['騎手'] = self._extract_text(cols[5]) if len(cols) > 5 else ''
                    
                    # デフォルト値設定
                    row_data['人気'] = 8  # デフォルト人気
                    row_data['単勝オッズ'] = 10.0  # デフォルトオッズ
                    
                    # 重要：人気とオッズの取得
                    if len(cols) >= 10:
                        # 人気の取得（様々なカラム位置を試す）
                        for col_idx in [7, 8, 9, 10]:
                            if col_idx < len(cols):
                                text = self._extract_text(cols[col_idx])
                                try:
                                    if '人気' in text or (text.isdigit() and len(text) <= 2):
                                        popularity = self._extract_number_from_text(text)
                                        if 1 <= popularity <= 18:  # 妥当な人気範囲
                                            row_data['人気'] = popularity
                                            break
                                except (ValueError, TypeError):
                                    continue
                        
                        # オッズの取得
                        for col_idx in [6, 7, 8, 9]:
                            if col_idx < len(cols):
                                text = self._extract_text(cols[col_idx])
                                try:
                                    if '.' in text and any(c.isdigit() for c in text):
                                        odds = self._extract_odds(text)
                                        if 1.0 <= odds <= 999.9:  # 妥当なオッズ範囲
                                            row_data['単勝オッズ'] = odds
                                            break
                                except (ValueError, TypeError):
                                    continue
                    
                    # データ品質チェック
                    if (row_data.get('馬名') and 
                        row_data['馬名'] not in ['', '--', 'N/A'] and
                        row_data.get('枠番', 0) > 0):
                        rows.append(row_data)
            
            df = pd.DataFrame(rows)
            
            if df.empty:
                return df
            
            # データ型変換
            df['枠番'] = pd.to_numeric(df['枠番'], errors='coerce').fillna(1)
            df['馬番'] = pd.to_numeric(df['馬番'], errors='coerce').fillna(1)
            df['斤量'] = pd.to_numeric(df['斤量'], errors='coerce').fillna(57.0)
            
            # 人気とオッズの安全な処理
            if '人気' not in df.columns:
                df['人気'] = 8
            df['人気'] = pd.to_numeric(df['人気'], errors='coerce').fillna(8)
            
            if '単勝オッズ' not in df.columns:
                df['単勝オッズ'] = 10.0
            df['単勝オッズ'] = pd.to_numeric(df['単勝オッズ'], errors='coerce').fillna(10.0)
            
            # レース情報の取得
            df = self._add_race_info(df, soup, race_id)
            
            logger.info(f"出馬表取得完了: {len(df)}頭")
            return df
            
        except Exception as e:
            logger.error(f"出馬表取得エラー: {e}")
            return pd.DataFrame()
    
    def get_horse_past_performance_enhanced(self, horse_ids: List[str], target_date: str = None) -> Dict[str, Dict]:
        """強化版過去戦績取得（リアルタイムスクレイピング対応）"""
        try:
            if not horse_ids:
                return {}
            
            logger.info(f"過去戦績を取得中: {len(horse_ids)}頭")
            
            stats = {}
            target_datetime = pd.to_datetime(target_date) if target_date else pd.Timestamp.now()
            
            # まず既存の過去戦績データを確認
            past_results_data = self._load_existing_horse_data(horse_ids, target_datetime)
            
            # 既存データがない馬のIDを収集（正規の10桁IDのみ）
            missing_horse_ids = []
            for hid in horse_ids:
                if hid not in past_results_data and re.match(r'^\d{10}$', str(hid)):
                    missing_horse_ids.append(hid)
            
            # 不足分はリアルタイムでスクレイピング
            if missing_horse_ids:
                logger.info(f"リアルタイムで過去戦績を取得: {len(missing_horse_ids)}頭")
                realtime_data = self._scrape_realtime_horse_data(missing_horse_ids, target_datetime)
                past_results_data.update(realtime_data)
            
            # 統計計算
            for horse_id in horse_ids:
                if horse_id in past_results_data:
                    horse_data = past_results_data[horse_id]
                    stats[horse_id] = self._calculate_enhanced_stats(horse_data, target_datetime)
                else:
                    stats[horse_id] = self._generate_default_stats()
            
            logger.info(f"過去戦績取得完了: {len(stats)}頭")
            return stats
            
        except Exception as e:
            logger.error(f"過去戦績取得エラー: {e}")
            return self._generate_fallback_stats(horse_ids)
    
    def _load_existing_horse_data(self, horse_ids: List[str], target_date: pd.Timestamp) -> Dict[str, pd.DataFrame]:
        """既存の馬データからターゲット日付以前のデータを取得"""
        try:
            year = target_date.year
            horse_data = {}
            
            # 過去数年のデータを確認
            for check_year in range(year - 3, year + 1):
                try:
                    results_path = f"output/race_results_{check_year}.pickle"
                    if os.path.exists(results_path):
                        race_results = pd.read_pickle(results_path)
                        
                        # 日付列を追加（推定）
                        if 'date' not in race_results.columns and 'race_id' in race_results.columns:
                            race_results['date'] = race_results['race_id'].apply(self._extract_date_from_race_id)
                        
                        # ターゲット日付以前のデータのみ
                        if 'date' in race_results.columns:
                            race_results['date'] = pd.to_datetime(race_results['date'], errors='coerce')
                            race_results = race_results[race_results['date'] < target_date]
                        
                        # 各馬のデータを抽出
                        for horse_id in horse_ids:
                            if horse_id in race_results['horse_id'].values:
                                horse_races = race_results[race_results['horse_id'] == horse_id]
                                if len(horse_races) > 0:
                                    if horse_id not in horse_data:
                                        horse_data[horse_id] = horse_races
                                    else:
                                        horse_data[horse_id] = pd.concat([horse_data[horse_id], horse_races], ignore_index=True)
                except Exception as e:
                    logger.warning(f"{check_year}年のデータ読み込みエラー: {e}")
                    continue
            
            return horse_data
            
        except Exception as e:
            logger.error(f"既存馬データ読み込みエラー: {e}")
            return {}
    
    def _calculate_enhanced_stats(self, horse_data: pd.DataFrame, target_date: pd.Timestamp) -> Dict[str, float]:
        """強化版統計計算（スクレイピングデータ対応）"""
        try:
            if horse_data.empty:
                return self._generate_default_stats()
            
            # 最新順にソート
            if 'date' in horse_data.columns:
                horse_data = horse_data.sort_values('date', ascending=False)
            
            stats = {}
            
            # 基本統計
            stats['total_races'] = len(horse_data)
            stats['win_rate'] = (horse_data['着順'] == 1).mean() if '着順' in horse_data.columns else 0.05
            stats['place_rate'] = (horse_data['着順'] <= 2).mean() if '着順' in horse_data.columns else 0.15
            stats['show_rate'] = (horse_data['着順'] <= 3).mean() if '着順' in horse_data.columns else 0.25
            
            # 順位統計
            if '着順' in horse_data.columns:
                ranks = pd.to_numeric(horse_data['着順'], errors='coerce').dropna()
                stats['avg_rank'] = ranks.mean() if len(ranks) > 0 else 8.0
                stats['rank_std'] = ranks.std() if len(ranks) > 0 else 3.0
                stats['recent_avg_rank'] = ranks.head(5).mean() if len(ranks) >= 5 else stats['avg_rank']
            else:
                stats['avg_rank'] = 8.0
                stats['rank_std'] = 3.0
                stats['recent_avg_rank'] = 8.0
            
            # 人気統計（重要な改善点）
            if '人気' in horse_data.columns:
                popularity = pd.to_numeric(horse_data['人気'], errors='coerce').dropna()
                # 人気が99（データなし）の場合は除外
                popularity = popularity[popularity < 50]
                stats['avg_popularity'] = popularity.mean() if len(popularity) > 0 else 8.0
                stats['recent_popularity'] = popularity.head(5).mean() if len(popularity) >= 5 else stats['avg_popularity']
                
                # 人気と着順の関係性
                if '着順' in horse_data.columns and len(popularity) > 0:
                    # 人気と着順の両方が有効なデータのみ使用
                    valid_data = horse_data[(horse_data['人気'] < 50) & (horse_data['着順'] < 50)]
                    if len(valid_data) > 0:
                        # 人気より良い着順の割合
                        better_than_popularity = (valid_data['着順'] < valid_data['人気']).mean()
                        stats['performance_vs_popularity'] = better_than_popularity
                    else:
                        stats['performance_vs_popularity'] = 0.5
                else:
                    stats['performance_vs_popularity'] = 0.5
            else:
                stats['avg_popularity'] = 8.0
                stats['recent_popularity'] = 8.0
                stats['performance_vs_popularity'] = 0.5
            
            # 賞金統計（スクレイピングデータ対応）
            prize_col = '賞金' if '賞金' in horse_data.columns else '賞金(万円)' if '賞金(万円)' in horse_data.columns else None
            if prize_col:
                prize_money = pd.to_numeric(horse_data[prize_col], errors='coerce').fillna(0)
                # すでに万円単位の場合は10000倍する
                if prize_col == '賞金' or '万円' in prize_col:
                    prize_money = prize_money * 10000
                stats['avg_prize'] = prize_money.mean()
                stats['max_prize'] = prize_money.max()
                stats['recent_avg_prize'] = prize_money.head(5).mean()
            else:
                stats['avg_prize'] = 500000
                stats['max_prize'] = 1000000
                stats['recent_avg_prize'] = 500000
            
            # 間隔統計
            if 'date' in horse_data.columns and len(horse_data) > 0:
                last_race_date = horse_data['date'].max()
                if pd.notna(last_race_date):
                    stats['days_since_last_race'] = (target_date - last_race_date).days
                else:
                    stats['days_since_last_race'] = 30
            else:
                stats['days_since_last_race'] = 30
            
            # 一貫性指標
            stats['consistency'] = 1.0 / (stats['rank_std'] + 1.0)
            
            # 調子の指標（直近5戦の成績）
            if len(horse_data) >= 5:
                recent_races = horse_data.head(5)
                if '着順' in recent_races.columns:
                    recent_ranks = pd.to_numeric(recent_races['着順'], errors='coerce').dropna()
                    # 失格等（99）を除外
                    recent_ranks = recent_ranks[recent_ranks < 50]
                    if len(recent_ranks) > 0:
                        stats['recent_form'] = max(0, 5 - recent_ranks.mean())  # 数値が大きいほど好調
                    else:
                        stats['recent_form'] = 1.0
                else:
                    stats['recent_form'] = 1.0
            else:
                stats['recent_form'] = 1.0
            
            # オッズ統計（スクレイピングデータ対応）
            if 'オッズ' in horse_data.columns:
                odds = pd.to_numeric(horse_data['オッズ'], errors='coerce').dropna()
                odds = odds[odds > 0]  # 有効なオッズのみ
                if len(odds) > 0:
                    stats['avg_odds'] = odds.mean()
                    stats['recent_odds'] = odds.head(5).mean() if len(odds) >= 5 else stats['avg_odds']
                else:
                    stats['avg_odds'] = 10.0
                    stats['recent_odds'] = 10.0
            else:
                stats['avg_odds'] = 10.0
                stats['recent_odds'] = 10.0
            
            # NaN値の処理
            for key, value in stats.items():
                if pd.isna(value):
                    if key in ['win_rate', 'place_rate', 'show_rate', 'performance_vs_popularity']:
                        stats[key] = 0.0
                    elif key in ['avg_rank', 'recent_avg_rank', 'avg_popularity', 'recent_popularity']:
                        stats[key] = 8.0
                    else:
                        stats[key] = 0.0
            
            return stats
            
        except Exception as e:
            logger.warning(f"統計計算エラー: {e}")
            return self._generate_default_stats()
    
    def prepare_improved_features(self, race_data: pd.DataFrame, race_info: Dict, horse_stats: Dict = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """改善版特徴量準備（人気・オッズ重視）"""
        try:
            logger.info("改善版特徴量を準備中...")
            
            data = race_data.copy()
            
            # レース情報追加
            for key, value in race_info.items():
                if key not in data.columns:
                    data[key] = value
            
            # 基本特徴量
            data['枠番'] = pd.to_numeric(data.get('枠番', 1), errors='coerce').fillna(1)
            data['馬番'] = pd.to_numeric(data.get('馬番', 1), errors='coerce').fillna(1)
            data['斤量'] = pd.to_numeric(data.get('斤量', 57.0), errors='coerce').fillna(57.0)
            data['course_len'] = pd.to_numeric(data.get('course_len', 1600), errors='coerce').fillna(1600)
            
            # 重要：人気とオッズ特徴量（大幅に改善）
            data['人気'] = pd.to_numeric(data.get('人気', 8), errors='coerce').fillna(8)
            data['単勝オッズ'] = pd.to_numeric(data.get('単勝オッズ', 10.0), errors='coerce').fillna(10.0)
            
            # 人気・オッズ由来の特徴量
            data['人気逆数'] = 1.0 / data['人気']  # 人気が高いほど大きい値
            data['オッズ逆数'] = 1.0 / data['単勝オッズ']  # オッズが低いほど大きい値
            data['人気順位正規化'] = (len(data) + 1 - data['人気']) / len(data)  # 0-1正規化
            data['log_オッズ'] = np.log(data['単勝オッズ'])  # 対数変換
            
            # 過去戦績特徴量（改善版）
            if horse_stats:
                logger.info("実際の過去戦績データを使用")
                horse_id_list = list(horse_stats.keys())
                
                for idx in range(len(data)):
                    horse_id = horse_id_list[idx] if idx < len(horse_id_list) else None
                    
                    if horse_id and horse_id in horse_stats:
                        stats = horse_stats[horse_id]
                        
                        # 基本統計（実データベース）
                        data.loc[data.index[idx], '過去勝率'] = stats.get('win_rate', 0.05)
                        data.loc[data.index[idx], '過去連対率'] = stats.get('place_rate', 0.15)
                        data.loc[data.index[idx], '過去複勝率'] = stats.get('show_rate', 0.25)
                        data.loc[data.index[idx], '平均着順'] = stats.get('avg_rank', 8.0)
                        data.loc[data.index[idx], '着順安定性'] = stats.get('consistency', 0.5)
                        
                        # 人気関連統計（新規追加）
                        data.loc[data.index[idx], '平均人気'] = stats.get('avg_popularity', 8.0)
                        data.loc[data.index[idx], '人気対成績'] = stats.get('performance_vs_popularity', 0.5)
                        
                        # 調子関連
                        data.loc[data.index[idx], '最近調子'] = stats.get('recent_form', 1.0)
                        data.loc[data.index[idx], '休養明け'] = min(100, stats.get('days_since_last_race', 30)) / 100
                        
                        # 賞金関連
                        data.loc[data.index[idx], '平均賞金'] = stats.get('avg_prize', 500000) / 1000000  # 百万円単位
                        data.loc[data.index[idx], '最高賞金'] = stats.get('max_prize', 1000000) / 1000000
                        
                        # 過去戦績統計（従来のモデル互換性のため）
                        data.loc[data.index[idx], '着順_last_5R_mean'] = stats.get('recent_avg_rank', 8.0)
                        data.loc[data.index[idx], '人気_last_5R_mean'] = stats.get('recent_popularity', 8.0)
                        data.loc[data.index[idx], 'オッズ_last_5R_mean'] = stats.get('recent_odds', stats.get('recent_popularity', 8.0) * 2)
                        data.loc[data.index[idx], '賞金_last_5R_mean'] = stats.get('recent_avg_prize', 500000)
                        data.loc[data.index[idx], '斤量_last_5R_mean'] = 57.0  # デフォルト値
                        data.loc[data.index[idx], '上り_last_5R_mean'] = 35.0   # デフォルト値
                        data.loc[data.index[idx], '体重_last_5R_mean'] = 480    # デフォルト値
                        data.loc[data.index[idx], '体重変化_last_5R_mean'] = 0.0  # デフォルト値
                        
                        # last_10R統計
                        data.loc[data.index[idx], '着順_last_10R_mean'] = stats.get('avg_rank', 8.0)
                        data.loc[data.index[idx], '人気_last_10R_mean'] = stats.get('avg_popularity', 8.0)
                        data.loc[data.index[idx], 'オッズ_last_10R_mean'] = stats.get('avg_odds', stats.get('avg_popularity', 8.0) * 2)
                        data.loc[data.index[idx], '賞金_last_10R_mean'] = stats.get('avg_prize', 500000)
                        data.loc[data.index[idx], '斤量_last_10R_mean'] = 57.0
                        data.loc[data.index[idx], '上り_last_10R_mean'] = 35.0
                        data.loc[data.index[idx], '体重_last_10R_mean'] = 480
                        data.loc[data.index[idx], '体重変化_last_10R_mean'] = 0.0
                        
                        # all_R統計
                        data.loc[data.index[idx], '着順_all_R_mean'] = stats.get('avg_rank', 8.0)
                        data.loc[data.index[idx], '人気_all_R_mean'] = stats.get('avg_popularity', 8.0)
                        data.loc[data.index[idx], 'オッズ_all_R_mean'] = stats.get('avg_odds', stats.get('avg_popularity', 8.0) * 2)
                        data.loc[data.index[idx], '賞金_all_R_mean'] = stats.get('avg_prize', 500000)
                        data.loc[data.index[idx], '斤量_all_R_mean'] = 57.0
                        data.loc[data.index[idx], '上り_all_R_mean'] = 35.0
                        data.loc[data.index[idx], '体重_all_R_mean'] = 480
                        data.loc[data.index[idx], '体重変化_all_R_mean'] = 0.0
                        
                        # インターバル
                        data.loc[data.index[idx], 'interval_days'] = stats.get('days_since_last_race', 30)
                    else:
                        # デフォルト値
                        self._set_default_enhanced_stats(data, idx)
            else:
                logger.warning("過去戦績データなし。人気・オッズベースで予測")
                for idx in range(len(data)):
                    self._set_default_enhanced_stats(data, idx)
            
            # カテゴリカル特徴量処理
            if self.label_encoders:
                for col, encoder in self.label_encoders.items():
                    if col in data.columns:
                        known_classes = set(encoder.classes_)
                        data[col] = data[col].fillna('unknown').astype(str)
                        mask = ~data[col].isin(known_classes)
                        if mask.any():
                            data.loc[mask, col] = encoder.classes_[0]
                        data[col] = encoder.transform(data[col])
                    else:
                        data[col] = 0
            
            # 学習時特徴量に合わせる
            missing_features = []
            for col in self.features:
                if col not in data.columns:
                    # 人気・オッズから推定
                    if 'rank' in col.lower() or '着順' in col:
                        data[col] = data['人気'].fillna(8.0)  # 人気を着順の代替として使用
                    elif 'odds' in col.lower() or 'オッズ' in col:
                        data[col] = data['単勝オッズ'].fillna(10.0)
                    elif 'popularity' in col.lower() or '人気' in col:
                        data[col] = data['人気'].fillna(8.0)
                    else:
                        data[col] = 0
                    missing_features.append(col)
            
            if missing_features:
                logger.info(f"推定値で補完した特徴量: {len(missing_features)}個")
            
            # 最終特徴量
            X = data[self.features]
            X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
            
            logger.info(f"改善版特徴量準備完了: {X.shape}")
            return X, data
            
        except Exception as e:
            logger.error(f"特徴量準備エラー: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def predict_race_improved(self, race_id: str) -> Tuple[pd.DataFrame, Dict]:
        """改善版レース予測"""
        try:
            logger.info(f"改善版レース予測開始: {race_id}")
            
            # モデル読み込み
            if not self.model:
                if not self.load_latest_model():
                    raise RuntimeError("モデル読み込み失敗")
            
            # 改善版出馬表取得
            race_data = self.scrape_race_card_improved(race_id)
            if race_data.empty:
                raise ValueError("出馬表データ取得失敗")
            
            logger.info(f"出馬表取得: {len(race_data)}頭")
            
            # レース情報
            race_info = {
                'race_id': race_id,
                'course_len': race_data['course_len'].iloc[0] if 'course_len' in race_data.columns and len(race_data) > 0 else 1600,
                'race_type': race_data['race_type'].iloc[0] if 'race_type' in race_data.columns and len(race_data) > 0 else '芝'
            }
            
            # 馬ID取得
            if 'horse_id' in race_data.columns:
                horse_ids = race_data['horse_id'].fillna('').tolist()
                # 空のhorse_idを馬名ベースのIDに置換
                for i, (hid, name) in enumerate(zip(horse_ids, race_data.get('馬名', []))):
                    if not hid:
                        # 馬名から仮のIDを生成
                        if name and name not in ['', 'N/A', '--']:
                            horse_ids[i] = f"temp_{name}_{i}"
                        else:
                            horse_ids[i] = f"temp_horse_{i}"
                
                logger.info(f"馬IDリスト: {horse_ids[:5]}...")  # 最初の5件をログ出力
                
                # 有効な馬ID（10桁の数字）と一時IDを分離
                valid_horse_ids = [hid for hid in horse_ids if re.match(r'^\d{10}$', str(hid))]
                temp_horse_ids = [hid for hid in horse_ids if not re.match(r'^\d{10}$', str(hid))]
                
                logger.info(f"有効な馬ID: {len(valid_horse_ids)}頭, 一時ID: {len(temp_horse_ids)}頭")
            else:
                # horse_idカラムがない場合
                horse_ids = [f"temp_horse_{i}" for i in range(len(race_data))]
                valid_horse_ids = []
                temp_horse_ids = horse_ids
                logger.warning("horse_idカラムが存在しません")
            
            # 強化版過去戦績取得
            current_date = self._extract_date_from_race_id(race_id)
            horse_stats = self.get_horse_past_performance_enhanced(horse_ids, current_date)
            
            # 改善版特徴量準備
            X, processed_data = self.prepare_improved_features(race_data, race_info, horse_stats)
            if X.empty:
                raise ValueError("特徴量準備失敗")
            
            # 予測実行
            X_scaled = self.scaler.transform(X)
            prediction_proba = self.model.predict(X_scaled)
            
            # 人気重み調整（重要な改善）
            popularity_weight = self._calculate_popularity_weight(processed_data)
            adjusted_proba = prediction_proba * popularity_weight
            
            # 結果整理
            results = processed_data[['枠番', '馬番', '馬名', '性齢', '斤量', '人気', '単勝オッズ']].copy()
            results['生予測スコア'] = prediction_proba
            results['予測スコア'] = adjusted_proba
            results['予測順位'] = results['予測スコア'].rank(ascending=False, method='first').astype(int)
            
            # 勝率計算（正規化）
            total_score = adjusted_proba.sum()
            if total_score > 0:
                results['勝率'] = (adjusted_proba / total_score * 100).round(1)
            else:
                results['勝率'] = (100 / len(results)).round(1)
            
            results['3着以内確率'] = (adjusted_proba * 100).round(1)
            
            # 順位でソート
            results = results.sort_values('予測順位')
            
            logger.info(f"改善版レース予測完了: {len(results)}頭")
            return results, race_info
            
        except Exception as e:
            logger.error(f"改善版予測エラー: {e}")
            return pd.DataFrame(), {}
    
    def _calculate_popularity_weight(self, data: pd.DataFrame) -> np.ndarray:
        """人気に基づく重み調整"""
        try:
            if '人気' not in data.columns:
                return np.ones(len(data))
            
            popularity = data['人気'].values
            # 人気が高いほど重みが大きくなるように調整
            # 1番人気=1.5倍、2番人気=1.3倍、3番人気=1.1倍、4番人気以下=1.0倍未満
            weights = np.where(popularity == 1, 1.5,
                      np.where(popularity == 2, 1.3,
                      np.where(popularity == 3, 1.1,
                      np.where(popularity <= 5, 1.0,
                      np.maximum(0.3, 1.0 - (popularity - 5) * 0.1)))))
            
            return weights
            
        except Exception as e:
            logger.error(f"人気重み計算エラー: {e}")
            return np.ones(len(data))
    
    def display_improved_results(self, results: pd.DataFrame, race_info: Dict):
        """最新版結果表示"""
        if results.empty:
            print("[エラー] 予測結果がありません")
            return
        
        print("\n" + "="*100)
        print("[競馬AI] 最新版予測結果（リアルタイム過去戦績対応）")
        print("="*100)
        print(f"レースID: {race_info.get('race_id', 'N/A')}")
        print(f"距離: {race_info.get('course_len', 'N/A')}m")
        print(f"予測時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        total_win_rate = results['勝率'].sum() if '勝率' in results.columns else 0
        print(f"\n[予測結果] 出馬頭数: {len(results)}頭 (勝率合計: {total_win_rate:.1f}%)")
        print("-" * 100)
        print(f"{'順位':>2} {'枠':>2}-{'馬番':>2} {'馬名':12} {'人気':>2} {'オッズ':>6} {'勝率':>6} {'3着内':>6} {'生スコア':>8}")
        print("-" * 100)
        
        for _, row in results.head(10).iterrows():
            try:
                print(f"{row['予測順位']:2.0f} "
                      f"{row['枠番']:2.0f}-{row['馬番']:2.0f} "
                      f"{str(row['馬名'])[:12]:12} "
                      f"{row['人気']:2.0f} "
                      f"{row['単勝オッズ']:6.1f} "
                      f"{row['勝率']:5.1f}% "
                      f"{row['3着以内確率']:5.1f}% "
                      f"{row['生予測スコア']:8.3f}")
            except Exception as e:
                logger.warning(f"結果表示エラー: {e}")
                continue
        
        print("\n[買い目提案]")
        print("-" * 50)
        top3 = results.head(3)
        print(f"◎本命: {top3.iloc[0]['枠番']:.0f}-{top3.iloc[0]['馬番']:.0f} {top3.iloc[0]['馬名']} (人気{top3.iloc[0]['人気']:.0f})")
        print(f"○対抗: {top3.iloc[1]['枠番']:.0f}-{top3.iloc[1]['馬番']:.0f} {top3.iloc[1]['馬名']} (人気{top3.iloc[1]['人気']:.0f})")
        print(f"▲単穴: {top3.iloc[2]['枠番']:.0f}-{top3.iloc[2]['馬番']:.0f} {top3.iloc[2]['馬名']} (人気{top3.iloc[2]['人気']:.0f})")
        
        print(f"\n3連複: {top3.iloc[0]['馬番']:.0f}-{top3.iloc[1]['馬番']:.0f}-{top3.iloc[2]['馬番']:.0f}")
        
        print("\n[システム特徴]")
        print("- リアルタイムで最新過去戦績をスクレイピング")
        print("- 人気・オッズを重視した予測")
        print("- 実際の過去戦績データによる高精度分析")
        print("- 人気重み調整による精度向上")
        print("="*100)
    
    # ユーティリティメソッド（以下のメソッドはスペース節約のため省略可能な部分もありますが、そのまま含めます）
    def _extract_text(self, element):
        """要素からテキストを安全に抽出"""
        return element.get_text(strip=True) if element else ""
    
    def _extract_number(self, element):
        """要素から数値を抽出"""
        text = self._extract_text(element)
        numbers = re.findall(r'\d+', text)
        return int(numbers[0]) if numbers else 0
    
    def _extract_number_from_text(self, text):
        """テキストから数値を抽出"""
        try:
            numbers = re.findall(r'\d+', str(text))
            return int(numbers[0]) if numbers else 0
        except (ValueError, IndexError):
            return 0
    
    def _extract_odds(self, text):
        """オッズを抽出"""
        try:
            # 小数点を含む数値を抽出
            odds_match = re.search(r'(\d+\.?\d*)', str(text))
            return float(odds_match.group(1)) if odds_match else 10.0
        except:
            return 10.0
    
    def _clean_horse_name(self, name):
        """馬名をクリーニング"""
        if not name:
            return ""
        # 不要な記号を除去
        cleaned = re.sub(r'[◎◯▲△☆✓消-]+|&#\d+;', '', name).strip()
        return cleaned if cleaned else name
    
    def _add_race_info(self, df, soup, race_id):
        """レース情報を追加"""
        try:
            df['race_id'] = race_id
            df['course_len'] = 1600  # デフォルト値
            df['race_type'] = '芝'
            df['ground_state'] = '良'
            df['weather'] = '晴'
            df['track_direction'] = '右'
            return df
        except:
            return df
    
    def _extract_date_from_race_id(self, race_id):
        """レースIDから日付を抽出"""
        try:
            year = int(race_id[:4])
            month = int(race_id[4:6])
            day = int(race_id[6:8])
            return f"{year}-{month:02d}-{day:02d}"
        except:
            return datetime.now().strftime('%Y-%m-%d')
    
    def _scrape_realtime_horse_data(self, horse_ids: List[str], target_date: pd.Timestamp) -> Dict[str, pd.DataFrame]:
        """リアルタイムで馬の過去戦績をスクレイピング"""
        try:
            # スクレイパーを使ってHTMLを取得
            logger.info(f"馬HTMLをスクレイピング中...")
            successful_ids = scrape_html_horse(horse_ids, skip=False, sleep_range=(1, 2))
            
            horse_data = {}
            for horse_id in successful_ids:
                try:
                    # HTMLファイルのパスを構築
                    year = str(horse_id)[:4]
                    html_path = os.path.join(LocalPaths.HTML_HORSE_DIR, year, f"{horse_id}.bin")
                    
                    if os.path.exists(html_path):
                        # HTMLを解析
                        with open(html_path, 'rb') as f:
                            html_content = f.read()
                        
                        # 過去戦績データを抽出
                        race_data = self._parse_horse_race_history(html_content, horse_id, target_date)
                        if not race_data.empty:
                            horse_data[horse_id] = race_data
                            logger.info(f"馬 {horse_id} の過去戦績: {len(race_data)}レース")
                    
                except Exception as e:
                    logger.warning(f"馬 {horse_id} のHTML解析エラー: {e}")
                    continue
            
            return horse_data
            
        except Exception as e:
            logger.error(f"リアルタイムスクレイピングエラー: {e}")
            return {}
    
    def _parse_horse_race_history(self, html_content: bytes, horse_id: str, target_date: pd.Timestamp) -> pd.DataFrame:
        """馬のHTMLから過去戦績を解析"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 競走成績テーブルを探す
            result_table = soup.find('table', class_='db_h_race_results')
            if not result_table:
                # 別のクラス名を試す
                tables = soup.find_all('table')
                for table in tables:
                    if '着順' in str(table) and '距離' in str(table):
                        result_table = table
                        break
            
            if not result_table:
                logger.warning(f"馬 {horse_id} の競走成績テーブルが見つかりません")
                return pd.DataFrame()
            
            # テーブルデータを抽出
            rows = []
            all_rows = result_table.find_all('tr')
            
            for tr in all_rows[1:]:  # ヘッダーをスキップ
                cols = tr.find_all(['td', 'th'])
                if len(cols) >= 10:  # 必要な列数があるか確認
                    try:
                        row_data = {
                            'horse_id': horse_id,
                            '日付': self._extract_text(cols[0]),
                            '開催': self._extract_text(cols[1]),
                            '天気': self._extract_text(cols[2]),
                            'R': self._extract_text(cols[3]),
                            'レース名': self._extract_text(cols[4]),
                            '頭数': self._extract_number_from_text(self._extract_text(cols[6])),
                            '枠番': self._extract_number_from_text(self._extract_text(cols[7])),
                            '馬番': self._extract_number_from_text(self._extract_text(cols[8])),
                            'オッズ': self._extract_odds(self._extract_text(cols[9])),
                            '人気': self._extract_number_from_text(self._extract_text(cols[10])),
                            '着順': self._extract_rank(self._extract_text(cols[11])),
                            '騎手': self._extract_text(cols[12]),
                            '斤量': self._extract_number_from_text(self._extract_text(cols[13])),
                            '距離': self._extract_text(cols[14]),
                            '馬場': self._extract_text(cols[15]),
                            'タイム': self._extract_text(cols[17]),
                            '着差': self._extract_text(cols[18]),
                            '通過': self._extract_text(cols[20]),
                            'ペース': self._extract_text(cols[21]),
                            '上り': self._extract_text(cols[22]),
                            '馬体重': self._extract_text(cols[23]),
                            '賞金': self._extract_prize_money(cols[27]) if len(cols) > 27 else 0
                        }
                        
                        # 日付を解析
                        date_str = row_data['日付']
                        if date_str:
                            try:
                                # 日付形式を推定（例: "2024/06/09"）
                                race_date = pd.to_datetime(date_str, format='%Y/%m/%d', errors='coerce')
                                if pd.isna(race_date):
                                    # 別の形式を試す（例: "24/06/09"）
                                    race_date = pd.to_datetime(date_str, format='%y/%m/%d', errors='coerce')
                                
                                # ターゲット日付より前のデータのみ
                                if pd.notna(race_date) and race_date < target_date:
                                    row_data['date'] = race_date
                                    rows.append(row_data)
                            except:
                                pass
                                
                    except Exception as e:
                        logger.debug(f"行の解析エラー: {e}")
                        continue
            
            if rows:
                df = pd.DataFrame(rows)
                # 日付でソート（新しい順）
                df = df.sort_values('date', ascending=False)
                return df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"HTML解析エラー: {e}")
            return pd.DataFrame()
    
    def _extract_rank(self, text: str) -> int:
        """着順を抽出（失格や中止なども考慮）"""
        try:
            # 数字のみの場合
            if text.isdigit():
                return int(text)
            # 「1着」のような形式
            rank_match = re.search(r'(\d+)', text)
            if rank_match:
                return int(rank_match.group(1))
            # 失格、中止などの場合
            if any(x in text for x in ['失格', '中止', '除外', '取消']):
                return 99
            return 99
        except:
            return 99
    
    def _extract_prize_money(self, element) -> float:
        """賞金を抽出（万円単位）"""
        try:
            text = self._extract_text(element)
            # カンマを除去して数値を抽出
            text = text.replace(',', '').replace('万', '')
            money_match = re.search(r'([\d.]+)', text)
            if money_match:
                return float(money_match.group(1))
            return 0.0
        except:
            return 0.0
    
    def _generate_default_stats(self):
        """デフォルト統計値"""
        return {
            'total_races': 10,
            'win_rate': 0.05,
            'place_rate': 0.15,
            'show_rate': 0.25,
            'avg_rank': 8.0,
            'rank_std': 3.0,
            'recent_avg_rank': 8.0,
            'avg_popularity': 8.0,
            'recent_popularity': 8.0,
            'performance_vs_popularity': 0.5,
            'avg_prize': 500000,
            'max_prize': 1000000,
            'recent_avg_prize': 500000,
            'days_since_last_race': 30,
            'consistency': 0.5,
            'recent_form': 1.0
        }
    
    def _generate_fallback_stats(self, horse_ids):
        """フォールバック統計値"""
        return {horse_id: self._generate_default_stats() for horse_id in horse_ids}
    
    def _set_default_enhanced_stats(self, data, idx):
        """デフォルト強化統計を設定"""
        # 人気ベースの推定値
        popularity = data.loc[data.index[idx], '人気'] if '人気' in data.columns else 8.0
        estimated_rank = popularity  # 人気 ≈ 期待着順
        estimated_win_rate = max(0.01, 0.3 / popularity)  # 人気に反比例
        
        # 改善版統計
        data.loc[data.index[idx], '過去勝率'] = estimated_win_rate
        data.loc[data.index[idx], '過去連対率'] = estimated_win_rate * 2.5
        data.loc[data.index[idx], '過去複勝率'] = estimated_win_rate * 4
        data.loc[data.index[idx], '平均着順'] = estimated_rank
        data.loc[data.index[idx], '着順安定性'] = 0.5
        data.loc[data.index[idx], '平均人気'] = popularity
        data.loc[data.index[idx], '人気対成績'] = 0.5
        data.loc[data.index[idx], '最近調子'] = 1.0
        data.loc[data.index[idx], '休養明け'] = 0.3
        data.loc[data.index[idx], '平均賞金'] = 0.5
        data.loc[data.index[idx], '最高賞金'] = 1.0
        
        # 従来互換性
        data.loc[data.index[idx], '着順_last_5R_mean'] = estimated_rank
        data.loc[data.index[idx], '人気_last_5R_mean'] = popularity
        data.loc[data.index[idx], 'オッズ_last_5R_mean'] = popularity * 2
        data.loc[data.index[idx], '賞金_last_5R_mean'] = 500000
        data.loc[data.index[idx], '斤量_last_5R_mean'] = 57.0
        data.loc[data.index[idx], '上り_last_5R_mean'] = 35.0
        data.loc[data.index[idx], '体重_last_5R_mean'] = 480
        data.loc[data.index[idx], '体重変化_last_5R_mean'] = 0.0
        
        # 同様の値でlast_10R、all_R統計も設定
        for period in ['last_10R', 'all_R']:
            data.loc[data.index[idx], f'着順_{period}_mean'] = estimated_rank
            data.loc[data.index[idx], f'人気_{period}_mean'] = popularity
            data.loc[data.index[idx], f'オッズ_{period}_mean'] = popularity * 2
            data.loc[data.index[idx], f'賞金_{period}_mean'] = 500000
            data.loc[data.index[idx], f'斤量_{period}_mean'] = 57.0
            data.loc[data.index[idx], f'上り_{period}_mean'] = 35.0
            data.loc[data.index[idx], f'体重_{period}_mean'] = 480
            data.loc[data.index[idx], f'体重変化_{period}_mean'] = 0.0
        
        data.loc[data.index[idx], 'interval_days'] = 30

def main():
    """メイン実行"""
    try:
        print("[競馬AI] 最新版予測システム")
        print("- リアルタイムスクレイピング対応")
        print("- 最新過去戦績を自動取得")
        print("- 人気・オッズ重み調整")
        print("- 特徴量エンジニアリング最適化")
        
        predictor = ImprovedLiveRacePredictor()
        
        # テスト用レースID
        test_race_ids = [
            "202401010101",  # 2024年1月1日
            "202407020203",  # 2024年7月2日
            "202410020402"   # 2024年10月2日
        ]
        
        try:
            race_id = input(f"\nレースIDを入力 (例: {test_race_ids[0]}): ").strip()
        except EOFError:
            race_id = test_race_ids[0]
            print(f"テストモード: {race_id}")
        
        if not race_id or len(race_id) != 12:
            race_id = test_race_ids[0]
            print(f"デフォルトID使用: {race_id}")
        
        print(f"\n最新版レース予測実行: {race_id}")
        print("※馬の過去戦績をリアルタイムで取得します。初回実行時は時間がかかる場合があります。")
        
        # 予測実行
        results, race_info = predictor.predict_race_improved(race_id)
        
        if not results.empty:
            predictor.display_improved_results(results, race_info)
        else:
            print("[エラー] 予測に失敗しました")
        
    except Exception as e:
        logger.error(f"メイン実行エラー: {e}")
        print(f"[エラー] {e}")

if __name__ == "__main__":
    main()
