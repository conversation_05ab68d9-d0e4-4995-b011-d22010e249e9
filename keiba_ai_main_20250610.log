2025-06-10 15:44:21,052 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-10 15:44:21,055 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-10 15:44:29,701 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-10 15:44:29,704 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-10 15:44:29,705 - KeibaAI - ERROR - 💥 システムエラー: EOF when reading a line
2025-06-10 15:47:52,081 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-10 15:47:52,082 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-10 15:48:13,068 - KeibaAI - INFO - 🚀 拡張モデル訓練開始...
2025-06-10 15:48:42,185 - KeibaAI - ERROR - ❌ 訓練エラー: 2025-06-10 15:48:14,502 - INFO - レースデータ読み込み完了: 48282件
2025-06-10 15:48:14,502 - INFO - 馬戦績データ読み込み完了: 336142件
2025-06-10 15:48:14,507 - INFO - 馬の過去戦績統計を計算中...
2025-06-10 15:48:14,826 - INFO - 過去戦績計算進捗: 0/11702 (0.0%)
2025-06-10 15:48:16,996 - INFO - 過去戦績計算進捗: 1000/11702 (8.5%)
2025-06-10 15:48:19,092 - INFO - 過去戦績計算進捗: 2000/11702 (17.1%)
2025-06-10 15:48:21,250 - INFO - 過去戦績計算進捗: 3000/11702 (25.6%)
2025-06-10 15:48:23,489 - INFO - 過去戦績計算進捗: 4000/11702 (34.2%)
2025-06-10 15:48:25,667 - INFO - 過去戦績計算進捗: 5000/11702 (42.7%)
2025-06-10 15:48:27,984 - INFO - 過去戦績計算進捗: 6000/11702 (51.3%)
2025-06-10 15:48:30,353 - INFO - 過去戦績計算進捗: 7000/11702 (59.8%)
2025-06-10 15:48:32,541 - INFO - 過去戦績計算進捗: 8000/11702 (68.4%)
2025-06-10 15:48:34,808 - INFO - 過去戦績計算進捗: 9000/11702 (76.9%)
2025-06-10 15:48:37,431 - INFO - 過去戦績計算進捗: 10000/11702 (85.5%)
2025-06-10 15:48:39,802 - INFO - 過去戦績計算進捗: 11000/11702 (94.0%)
2025-06-10 15:48:41,429 - INFO - === 過去戦績統計サマリー ===
2025-06-10 15:48:41,429 - INFO - 過去レース数 - 平均: 0.0, 最大: 0.0
2025-06-10 15:48:41,429 - INFO - 勝率 - 平均: 0.000
2025-06-10 15:48:41,430 - INFO - 3着以内率 - 平均: 0.000
2025-06-10 15:48:41,430 - INFO - 平均着順 - 全体平均: 0.00
2025-06-10 15:48:41,430 - INFO - 特徴量準備開始（過去戦績込み）
2025-06-10 15:48:41,568 - INFO - 特徴量数: 28
2025-06-10 15:48:41,568 - INFO - 基本特徴量: ['course_len', '枠番', '馬番', '斤量', 'days_since_last_race', 'race_type_encoded', 'ground_state_encoded', 'weather_encoded', 'track_direction_encoded', '年齢', '性別_牡', '性別_牝', '距離_短距離', '距離_マイル', '距離_中距離', '距離_長距離', '経験豊富', '好調', '連続出走']
2025-06-10 15:48:41,568 - INFO - 過去戦績特徴量: ['past_races_count', 'past_win_rate', 'past_top3_rate', 'past_avg_rank', 'past_std_rank', 'past_distance_similar_races', 'past_distance_win_rate', 'past_surface_races', 'past_surface_win_rate']
2025-06-10 15:48:41,568 - INFO - データ形状: X=(47876, 28), y=(47876,)
2025-06-10 15:48:41,568 - INFO - 正例率: 0.217
2025-06-10 15:48:41,568 - INFO - モデル学習開始
2025-06-10 15:48:41,923 - INFO - === 学習結果（過去戦績込み） ===
2025-06-10 15:48:41,923 - INFO - 精度: 0.7832
2025-06-10 15:48:41,923 - INFO - AUC: 0.5808
2025-06-10 15:48:41,923 - INFO - 適中率@10%: 0.3260
2025-06-10 15:48:41,923 - INFO - 適中率@20%: 0.3044
2025-06-10 15:48:41,923 - INFO - 学習データ: 38300件
2025-06-10 15:48:41,923 - INFO - テストデータ: 9576件
2025-06-10 15:48:41,941 - INFO - 強化モデル保存完了:
2025-06-10 15:48:41,941 - INFO -   モデル: models\enhanced_lgb_model_20250610_154841.pkl
2025-06-10 15:48:41,941 - INFO -   スケーラー: models\enhanced_scaler_20250610_154841.pkl
2025-06-10 15:48:41,941 - INFO -   特徴量: models\enhanced_features_20250610_154841.pkl
2025-06-10 15:48:41,942 - ERROR - 学習中にエラー: 'cp932' codec can't encode character '\U0001f40e' in position 0: illegal multibyte sequence
Traceback (most recent call last):
  File "H:\AI\keiba_ai_system\enhanced_train.py", line 452, in <module>
    main()
  File "H:\AI\keiba_ai_system\enhanced_train.py", line 422, in main
    print("\U0001f40e 競馬AI学習完了（過去戦績強化版）!")
UnicodeEncodeError: 'cp932' codec can't encode character '\U0001f40e' in position 0: illegal multibyte sequence

2025-06-10 15:49:09,230 - KeibaAI - INFO - 🕷️ データスクレイピング開始...
2025-06-10 15:53:25,353 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-10 15:53:25,357 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-10 15:53:25,364 - KeibaAI - INFO - 📅 複数年統合訓練開始: 3年分
2025-06-10 15:53:26,592 - KeibaAI - ERROR - ❌ 複数年訓練エラー: Traceback (most recent call last):
  File "/mnt/h/AI/keiba_ai_system/temp_multi_year_train.py", line 228, in main
    X, y, features = prepare_features(race_data, horse_data, MODE)
  File "/mnt/h/AI/keiba_ai_system/temp_multi_year_train.py", line 107, in prepare_features
    y = (data['着順'] <= 3).astype(int)  # 3着以内を正例
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/ops/common.py", line 72, in new_method
    return method(self, other)
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/arraylike.py", line 54, in __le__
    return self._cmp_method(other, operator.le)
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/series.py", line 6243, in _cmp_method
    res_values = ops.comparison_op(lvalues, rvalues, op)
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/ops/array_ops.py", line 287, in comparison_op
    res_values = comp_method_OBJECT_ARRAY(op, lvalues, rvalues)
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/ops/array_ops.py", line 75, in comp_method_OBJECT_ARRAY
    result = libops.scalar_compare(x.ravel(), y, op)
  File "pandas/_libs/ops.pyx", line 107, in pandas._libs.ops.scalar_compare
TypeError: '<=' not supported between instances of 'str' and 'int'

2025-06-10 15:53:26,593 - KeibaAI - ERROR - 💥 システムエラー: EOF when reading a line
2025-06-10 15:53:52,174 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-10 15:53:52,177 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-10 15:53:52,183 - KeibaAI - INFO - 📅 複数年統合訓練開始: 5年分
2025-06-10 15:54:14,616 - KeibaAI - INFO - ✅ 複数年統合訓練成功
2025-06-10 15:54:14,617 - KeibaAI - ERROR - 💥 システムエラー: EOF when reading a line
2025-06-10 16:00:17,333 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-10 16:00:17,334 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-10 16:00:35,882 - KeibaAI - INFO - 🏆 レース202506010101のUltimate Live Prediction開始...
2025-06-10 16:04:05,540 - KeibaAI - INFO - ✅ Ultimate Live Prediction完了
