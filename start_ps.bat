@echo off
echo.
echo ======================================================
echo Keiba AI System (via PowerShell)
echo ======================================================
echo.

REM Try safe PowerShell script first
if exist "start_safe.ps1" (
    echo Executing safe PowerShell script...
    echo.
    powershell.exe -ExecutionPolicy Bypass -File "start_safe.ps1"
) else if exist "start_keiba_ai.ps1" (
    echo Executing main PowerShell script...
    echo.
    powershell.exe -ExecutionPolicy Bypass -File "start_keiba_ai.ps1"
) else (
    echo ERROR: No PowerShell script found
    echo Please ensure start_safe.ps1 or start_keiba_ai.ps1 exists
    pause
    exit /b 1
)

echo.
echo ======================================================
echo PowerShell script execution completed
echo ======================================================
pause