#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全な実際のレース予測システム（BAN対策済み）
scraper.pyと学習済みモデルを使用してリアルタイム予測を実行
"""

import pandas as pd
import numpy as np
import requests
from bs4 import BeautifulSoup
import joblib
import logging
import re
import time
import random
from datetime import datetime, timedelta
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SafeLiveRacePredictor:
    """安全な実際のレース予測クラス（BAN対策済み）"""
    
    def __init__(self, model_dir="models"):
        """
        初期化
        
        Parameters
        ----------
        model_dir : str
            学習済みモデルのディレクトリ
        """
        self.model_dir = Path(model_dir)
        self.model = None
        self.scaler = None
        self.features = None
        self.session = requests.Session()
        
        # より安全なUser-Agentを設定
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ]
        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        logger.info("SafeLiveRacePredictorを初期化しました（BAN対策済み）")
    
    def load_latest_model(self):
        """最新の学習済みモデルを読み込み"""
        try:
            # 最新のモデルファイルを検索
            model_files = list(self.model_dir.glob("*enhanced*model*.pkl"))
            if not model_files:
                model_files = list(self.model_dir.glob("*model*.pkl"))
            
            if not model_files:
                raise FileNotFoundError("モデルファイルが見つかりません")
            
            # 最新のファイルを選択
            latest_model = max(model_files, key=lambda f: f.stat().st_mtime)
            timestamp = latest_model.stem.split('_')[-1]
            
            # モデル、スケーラー、特徴量を読み込み
            model_path = latest_model
            scaler_path = self.model_dir / f"{latest_model.stem.replace('model', 'scaler')}.pkl"
            features_path = self.model_dir / f"{latest_model.stem.replace('model', 'features')}.pkl"
            
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            self.features = joblib.load(features_path)
            
            logger.info(f"モデル読み込み完了: {latest_model.name}")
            logger.info(f"特徴量数: {len(self.features)}")
            
            return True
            
        except Exception as e:
            logger.error(f"モデル読み込みエラー: {e}")
            return False
    
    def safe_request(self, url, min_delay=2, max_delay=5):
        """
        安全なHTTPリクエスト（BAN対策）
        
        Parameters
        ----------
        url : str
            リクエストURL
        min_delay : int
            最小待機時間（秒）
        max_delay : int
            最大待機時間（秒）
            
        Returns
        -------
        requests.Response
            レスポンス
        """
        # ランダムな待機時間
        delay = random.uniform(min_delay, max_delay)
        logger.info(f"BAN対策: {delay:.1f}秒待機中...")
        time.sleep(delay)
        
        try:
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"リクエストエラー: {e}")
            # エラー時はさらに長く待機
            time.sleep(random.uniform(10, 20))
            raise
    
    def scrape_race_card(self, race_id):
        """
        レース出馬表をスクレイピング（BAN対策済み）
        
        Parameters
        ----------
        race_id : str
            レースID (例: 202412080101)
            
        Returns
        -------
        pd.DataFrame
            出馬表データ
        """
        try:
            url = f"https://race.netkeiba.com/race/shutuba.html?race_id={race_id}"
            logger.info(f"安全にレース出馬表を取得中: {url}")
            
            response = self.safe_request(url, min_delay=3, max_delay=7)
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 出馬表テーブルを検索
            table = soup.find('table', class_='race_table_01')
            if not table:
                logger.error("出馬表テーブルが見つかりません")
                return pd.DataFrame()
            
            # テーブルデータを抽出
            rows = []
            for tr in table.find_all('tr')[1:]:  # ヘッダー行をスキップ
                cols = tr.find_all(['td', 'th'])
                if len(cols) >= 8:  # 最低限の列数をチェック
                    row_data = {}
                    
                    # 基本データを抽出
                    row_data['枠番'] = self._extract_text(cols[0])
                    row_data['馬番'] = self._extract_text(cols[1])
                    
                    # 馬名とhorse_idを抽出
                    horse_link = cols[2].find('a')
                    if horse_link:
                        horse_href = horse_link.get('href', '')
                        horse_id_match = re.search(r'horse_id=(\w+)', horse_href)
                        row_data['horse_id'] = horse_id_match.group(1) if horse_id_match else ''
                        row_data['馬名'] = horse_link.get_text(strip=True)
                    else:
                        row_data['horse_id'] = ''
                        row_data['馬名'] = self._extract_text(cols[2])
                    
                    # 性齢
                    row_data['性齢'] = self._extract_text(cols[3])
                    
                    # 斤量
                    row_data['斤量'] = self._extract_text(cols[4])
                    
                    # 騎手
                    row_data['騎手'] = self._extract_text(cols[5])
                    
                    # 調教師
                    row_data['調教師'] = self._extract_text(cols[6])
                    
                    # 馬主
                    if len(cols) > 7:
                        row_data['馬主'] = self._extract_text(cols[7])
                    
                    rows.append(row_data)
            
            df = pd.DataFrame(rows)
            
            if df.empty:
                logger.warning("出馬表データが空です")
                return df
            
            # データ型変換
            df['枠番'] = pd.to_numeric(df['枠番'], errors='coerce')
            df['馬番'] = pd.to_numeric(df['馬番'], errors='coerce')
            df['斤量'] = pd.to_numeric(df['斤量'], errors='coerce')
            df['race_id'] = race_id
            
            logger.info(f"出馬表取得完了: {len(df)}頭")
            
            # 取得成功後は少し休憩
            time.sleep(random.uniform(2, 4))
            
            return df
            
        except Exception as e:
            logger.error(f"出馬表スクレイピングエラー: {e}")
            return pd.DataFrame()
    
    def scrape_race_info(self, race_id):
        """
        レース情報をスクレイピング（BAN対策済み）
        
        Parameters
        ----------
        race_id : str
            レースID
            
        Returns
        -------
        dict
            レース情報
        """
        try:
            url = f"https://race.netkeiba.com/race/shutuba.html?race_id={race_id}"
            response = self.safe_request(url, min_delay=2, max_delay=5)
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            race_info = {'race_id': race_id}
            
            # レース名
            race_title = soup.find('h1', class_='raceTitle')
            if race_title:
                race_info['レース名'] = race_title.get_text(strip=True)
            
            # レース条件
            race_data = soup.find('div', class_='racedata')
            if race_data:
                race_text = race_data.get_text()
                
                # 距離
                distance_match = re.search(r'(\d+)m', race_text)
                if distance_match:
                    race_info['course_len'] = int(distance_match.group(1))
                
                # コース種別
                if '芝' in race_text:
                    race_info['race_type'] = '芝'
                elif 'ダート' in race_text or 'ダ' in race_text:
                    race_info['race_type'] = 'ダート'
                else:
                    race_info['race_type'] = '不明'
                
                # 馬場状態
                for condition in ['良', '稍重', '重', '不良']:
                    if condition in race_text:
                        race_info['ground_state'] = condition
                        break
                
                # 天気
                for weather in ['晴', '曇', '雨', '雪']:
                    if weather in race_text:
                        race_info['weather'] = weather
                        break
                
                # 回り
                if '右' in race_text:
                    race_info['track_direction'] = '右'
                elif '左' in race_text:
                    race_info['track_direction'] = '左'
                else:
                    race_info['track_direction'] = '直線'
            
            # デフォルト値設定
            race_info.setdefault('course_len', 1600)
            race_info.setdefault('race_type', '芝')
            race_info.setdefault('ground_state', '良')
            race_info.setdefault('weather', '晴')
            race_info.setdefault('track_direction', '右')
            
            logger.info(f"レース情報取得完了: {race_info.get('レース名', 'レース名不明')}")
            
            # 情報取得後は休憩
            time.sleep(random.uniform(1, 3))
            
            return race_info
            
        except Exception as e:
            logger.error(f"レース情報スクレイピングエラー: {e}")
            return {'race_id': race_id}
    
    def scrape_horse_past_performance(self, horse_id, max_races=10):
        """
        馬の過去戦績をスクレイピング（BAN対策済み）
        
        Parameters
        ----------
        horse_id : str
            馬ID
        max_races : int
            取得する最大レース数
            
        Returns
        -------
        dict
            過去戦績統計
        """
        try:
            url = f"https://db.netkeiba.com/horse/{horse_id}"
            logger.info(f"馬の過去戦績を取得中: {horse_id}")
            
            response = self.safe_request(url, min_delay=4, max_delay=8)
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 戦績テーブルを検索
            table = soup.find('table', class_='db_h_race_results')
            if not table:
                logger.warning(f"馬{horse_id}の戦績テーブルが見つかりません")
                return self._get_default_horse_stats()
            
            # 戦績データを抽出
            races = []
            for tr in table.find_all('tr')[1:max_races+1]:  # 最新のmax_races戦まで
                cols = tr.find_all(['td', 'th'])
                if len(cols) >= 8:
                    race_data = {}
                    try:
                        race_data['着順'] = pd.to_numeric(self._extract_text(cols[11]), errors='coerce')
                        race_data['頭数'] = pd.to_numeric(self._extract_text(cols[7]), errors='coerce')
                        races.append(race_data)
                    except:
                        continue
            
            # 統計計算
            if not races:
                return self._get_default_horse_stats()
            
            ranks = [r['着順'] for r in races if pd.notna(r['着順'])]
            
            stats = {
                'total_races': len(ranks),
                'avg_rank': np.mean(ranks) if ranks else 10,
                'win_rate': sum(1 for r in ranks if r == 1) / len(ranks) if ranks else 0,
                'top3_rate': sum(1 for r in ranks if r <= 3) / len(ranks) if ranks else 0,
                'rank_std': np.std(ranks) if len(ranks) > 1 else 3.0
            }
            
            logger.info(f"馬{horse_id}の戦績取得完了: {stats['total_races']}戦")
            
            # 戦績取得後は長めに休憩
            time.sleep(random.uniform(5, 10))
            
            return stats
            
        except Exception as e:
            logger.error(f"馬{horse_id}の戦績取得エラー: {e}")
            return self._get_default_horse_stats()
    
    def _get_default_horse_stats(self):
        """デフォルトの馬統計"""
        return {
            'total_races': 10,
            'avg_rank': 7.0,
            'win_rate': 0.05,
            'top3_rate': 0.25,
            'rank_std': 3.0
        }
    
    def _extract_text(self, element):
        """要素からテキストを安全に抽出"""
        if element:
            return element.get_text(strip=True)
        return ""
    
    def prepare_prediction_features(self, race_data, race_info, horse_stats=None):
        """
        予測用特徴量を準備
        
        Parameters
        ----------
        race_data : pd.DataFrame
            出馬表データ
        race_info : dict
            レース情報
        horse_stats : dict, optional
            馬の過去戦績統計
            
        Returns
        -------
        pd.DataFrame
            予測用特徴量データ
        """
        try:
            logger.info("予測用特徴量を準備中...")
            
            data = race_data.copy()
            
            # レース情報を追加
            for key, value in race_info.items():
                data[key] = value
            
            # 基本特徴量
            data['course_len'] = pd.to_numeric(data.get('course_len', 1600), errors='coerce')
            data['枠番'] = pd.to_numeric(data['枠番'], errors='coerce')
            data['馬番'] = pd.to_numeric(data['馬番'], errors='coerce')
            data['斤量'] = pd.to_numeric(data['斤量'], errors='coerce')
            
            # 性別・年齢
            if '性齢' in data.columns:
                data['年齢'] = data['性齢'].str.extract(r'(\d+)').astype(float)
                data['性別_牡'] = data['性齢'].str.contains('牡', na=False).astype(int)
                data['性別_牝'] = data['性齢'].str.contains('牝', na=False).astype(int)
                data['性別_セ'] = data['性齢'].str.contains('セ', na=False).astype(int)
            
            # カテゴリカル特徴量のエンコーディング
            categorical_mapping = {
                'race_type': {'芝': 0, 'ダート': 1, '不明': 2},
                'ground_state': {'良': 0, '稍重': 1, '重': 2, '不良': 3},
                'weather': {'晴': 0, '曇': 1, '雨': 2, '雪': 3},
                'track_direction': {'右': 0, '左': 1, '直線': 2}
            }
            
            for col, mapping in categorical_mapping.items():
                if col in data.columns:
                    data[f'{col}_encoded'] = data[col].map(mapping).fillna(0)
            
            # 距離カテゴリ
            if 'course_len' in data.columns:
                data['距離_短距離'] = (data['course_len'] <= 1400).astype(int)
                data['距離_マイル'] = ((data['course_len'] > 1400) & (data['course_len'] <= 1800)).astype(int)
                data['距離_中距離'] = ((data['course_len'] > 1800) & (data['course_len'] <= 2200)).astype(int)
                data['距離_長距離'] = (data['course_len'] > 2200).astype(int)
            
            # 過去戦績特徴量（実際のデータまたはダミーデータ）
            if horse_stats:
                for idx, stats in enumerate(horse_stats):
                    for key, value in stats.items():
                        data.loc[idx, key] = value
            else:
                # ダミーデータ（実際の馬戦績が取得できない場合）
                np.random.seed(42)
                n_horses = len(data)
                
                data['total_races'] = np.random.randint(5, 40, n_horses)
                data['avg_rank'] = np.random.uniform(4, 10, n_horses)
                data['win_rate'] = np.random.beta(2, 8, n_horses)
                data['top3_rate'] = np.random.beta(3, 5, n_horses)
                data['rank_std'] = np.random.uniform(2, 4, n_horses)
            
            data['days_since_last_race'] = np.random.randint(14, 90, len(data))
            
            # 年齢関連特徴量
            data['days_old'] = data['年齢'] * 365.25 + np.random.randint(-50, 50, len(data))
            data['age_years'] = data['days_old'] / 365.25
            data['age_months'] = data['days_old'] / 30.44
            data['is_young'] = (data['age_years'] <= 3).astype(int)
            data['is_prime'] = ((data['age_years'] > 3) & (data['age_years'] <= 6)).astype(int)
            data['is_veteran'] = (data['age_years'] > 6).astype(int)
            
            # 派生特徴量
            data['経験豊富'] = (data['total_races'] >= 15).astype(int)
            data['好調'] = (data['win_rate'] >= 0.1).astype(int)
            data['連続出走'] = (data['days_since_last_race'] <= 30).astype(int)
            data['ベテラン_戦績'] = (data['total_races'] >= 25).astype(int)
            data['安定'] = (data['rank_std'] <= 3.0).astype(int)
            data['経験密度'] = data['total_races'] / data['age_years']
            data['若手有望'] = ((data['age_years'] <= 4) & (data['win_rate'] >= 0.1)).astype(int)
            data['円熟期'] = ((data['age_years'] >= 4) & (data['age_years'] <= 7) & (data['total_races'] >= 15)).astype(int)
            
            # 欠損値処理
            data = data.fillna(0)
            
            # 学習時の特徴量のみを選択
            available_features = []
            for col in self.features:
                if col in data.columns:
                    available_features.append(col)
                else:
                    data[col] = 0
                    available_features.append(col)
            
            X = data[self.features]
            
            logger.info(f"特徴量準備完了: {X.shape}")
            return X, data
            
        except Exception as e:
            logger.error(f"特徴量準備エラー: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def predict_race(self, race_id, get_horse_stats=False):
        """
        レース予測を実行（BAN対策済み）
        
        Parameters
        ----------
        race_id : str
            レースID
        get_horse_stats : bool
            実際の馬の過去戦績を取得するか（時間がかかります）
            
        Returns
        -------
        pd.DataFrame
            予測結果
        """
        try:
            logger.info(f"安全なレース予測開始: {race_id}")
            
            # モデル読み込み
            if not self.model:
                if not self.load_latest_model():
                    raise RuntimeError("モデルの読み込みに失敗しました")
            
            # レースデータ取得
            race_data = self.scrape_race_card(race_id)
            if race_data.empty:
                raise ValueError("出馬表データが取得できませんでした")
            
            race_info = self.scrape_race_info(race_id)
            
            # 馬の過去戦績取得（オプション）
            horse_stats = None
            if get_horse_stats and 'horse_id' in race_data.columns:
                logger.info("馬の過去戦績を取得中（時間がかかります）...")
                horse_stats = []
                for horse_id in race_data['horse_id']:
                    if horse_id:
                        stats = self.scrape_horse_past_performance(horse_id)
                        horse_stats.append(stats)
                    else:
                        horse_stats.append(self._get_default_horse_stats())
            
            # 特徴量準備
            X, processed_data = self.prepare_prediction_features(race_data, race_info, horse_stats)
            if X.empty:
                raise ValueError("特徴量の準備に失敗しました")
            
            # 予測実行
            X_scaled = self.scaler.transform(X)
            prediction_proba = self.model.predict(X_scaled)
            
            # 結果整理
            results = processed_data[['枠番', '馬番', '馬名', '性齢', '斤量', '騎手']].copy()
            results['予測スコア'] = prediction_proba
            results['予測順位'] = results['予測スコア'].rank(ascending=False, method='first').astype(int)
            results['3着以内確率'] = (prediction_proba * 100).round(1)
            
            # 過去戦績情報を追加
            if horse_stats:
                results['総レース数'] = [s['total_races'] for s in horse_stats]
                results['勝率'] = [(s['win_rate'] * 100) for s in horse_stats]
                results['3着以内率'] = [(s['top3_rate'] * 100) for s in horse_stats]
            
            # 順位でソート
            results = results.sort_values('予測順位')
            
            logger.info("安全なレース予測完了")
            return results, race_info
            
        except Exception as e:
            logger.error(f"レース予測エラー: {e}")
            return pd.DataFrame(), {}
    
    def display_prediction_results(self, results, race_info):
        """予測結果を見やすく表示"""
        if results.empty:
            print("❌ 予測結果がありません")
            return
        
        print("\n" + "="*90)
        print("🐎 競馬AI予測結果（BAN対策版）")
        print("="*90)
        print(f"レース: {race_info.get('レース名', 'レース情報なし')}")
        print(f"距離: {race_info.get('course_len', 'N/A')}m")
        print(f"コース: {race_info.get('race_type', 'N/A')} {race_info.get('track_direction', 'N/A')}回り")
        print(f"馬場: {race_info.get('ground_state', 'N/A')} / 天気: {race_info.get('weather', 'N/A')}")
        print(f"予測時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"BAN対策: ランダム待機時間を設定済み")
        print("\n📊 予測結果 (3着以内確率順)")
        print("-" * 90)
        
        for _, row in results.head(10).iterrows():
            extra_info = ""
            if '総レース数' in row:
                extra_info = f" {row['総レース数']:.0f}戦 勝率{row['勝率']:.1f}%"
            
            print(f"{row['予測順位']:2d}位 {row['枠番']:2.0f}-{row['馬番']:2.0f} {row['馬名']:12s} "
                  f"{row['性齢']:4s} {row['斤量']:4.1f}kg {row['騎手']:8s} "
                  f"確率:{row['3着以内確率']:5.1f}%{extra_info}")
        
        print("\n🎯 買い目候補")
        print("-" * 50)
        top3 = results.head(3)
        print("🥇 3連複候補:")
        print(f"   {top3.iloc[0]['枠番']:.0f}-{top3.iloc[0]['馬番']:.0f}-{top3.iloc[1]['枠番']:.0f}-{top3.iloc[1]['馬番']:.0f}-{top3.iloc[2]['枠番']:.0f}-{top3.iloc[2]['馬番']:.0f}")
        
        winner_candidate = results.iloc[0]
        print(f"\n🏆 単勝候補: {winner_candidate['枠番']:.0f}-{winner_candidate['馬番']:.0f} {winner_candidate['馬名']}")
        print(f"   3着以内確率: {winner_candidate['3着以内確率']:.1f}%")
        
        print("\n⚠️  注意事項")
        print("-" * 50)
        print("・この予測システムはBAN対策済みです")
        print("・リクエスト間に適切な待機時間を設定しています")
        print("・投資は自己責任で行ってください")
        print("・予測はあくまで参考情報として活用してください")
        
        print("\n" + "="*90)

def main():
    """メイン実行関数"""
    try:
        predictor = SafeLiveRacePredictor()
        
        print("🐎 競馬AI予測システム（BAN対策版）")
        print("注意: 実際のレースデータを安全に取得します")
        print("BAN対策として適切な待機時間を設定しています")
        
        # ユーザーからレースIDを入力
        race_id = input("\nレースIDを入力してください (例: 202412080101): ").strip()
        
        if not race_id:
            print("レースIDが入力されませんでした。デモを実行します。")
            race_id = "202412080101"
        
        get_horse_stats = input("実際の馬の過去戦績を取得しますか？ (y/N): ").strip().lower() == 'y'
        
        if get_horse_stats:
            print("⚠️  馬の過去戦績取得には10-15分程度かかります")
        
        print(f"\nレース予測を実行中: {race_id}")
        
        # 予測実行
        results, race_info = predictor.predict_race(race_id, get_horse_stats=get_horse_stats)
        
        if not results.empty:
            predictor.display_prediction_results(results, race_info)
        else:
            print("❌ 予測に失敗しました")
            print("・レースIDが存在しない可能性があります")
            print("・ネットワーク接続を確認してください")
            print("・一時的にアクセスが制限されている可能性があります")
        
    except KeyboardInterrupt:
        print("\n\n処理が中断されました")
    except Exception as e:
        logger.error(f"メイン実行エラー: {e}")
        print(f"❌ エラーが発生しました: {e}")

if __name__ == "__main__":
    main()