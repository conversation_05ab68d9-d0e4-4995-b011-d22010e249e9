#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
競馬AI予測システム - 統合メインエントリーポイント

このファイルを実行することで、プロジェクトの主要機能にアクセスできます：
1. データスクレイピング
2. モデル訓練
3. リアルタイム予測
4. データ統合・処理
5. 特徴量エンジニアリング
6. システム設定・管理

使用方法:
    python keiba_ai_main.py
"""

import sys
import os
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import warnings

# Windows環境でのUnicode文字対応
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

warnings.filterwarnings('ignore')

# ログ設定
class KeibaAILogger:
    """統合ログ管理システム"""
    
    def __init__(self, log_level=logging.INFO):
        self.log_level = log_level
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """ロガーの初期化"""
        logger = logging.getLogger('KeibaAI')
        logger.setLevel(self.log_level)
        
        if not logger.handlers:
            # コンソールハンドラ
            console_handler = logging.StreamHandler()
            console_handler.setLevel(self.log_level)
            
            # ファイルハンドラ
            log_file = f"keiba_ai_main_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            
            # フォーマッター
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(formatter)
            file_handler.setFormatter(formatter)
            
            logger.addHandler(console_handler)
            logger.addHandler(file_handler)
        
        return logger
    
    def info(self, message):
        self.logger.info(message)
    
    def error(self, message):
        self.logger.error(message)
    
    def warning(self, message):
        self.logger.warning(message)
    
    def debug(self, message):
        self.logger.debug(message)


class KeibaAISystem:
    """競馬AI予測システム統合クラス"""
    
    def __init__(self):
        self.logger = KeibaAILogger()
        self.logger.info("🏇 競馬AI予測システム初期化中...")
        
        # 基本パス設定
        self.project_root = Path(__file__).parent
        self.data_dir = self.project_root / "data"
        self.output_dir = self.project_root / "output"
        self.models_dir = self.project_root / "models"
        
        # ディレクトリ作成
        self.data_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        self.models_dir.mkdir(exist_ok=True)
        
        self.logger.info("✅ システム初期化完了")
    
    def show_main_menu(self):
        """メインメニュー表示"""
        print("\n" + "="*60)
        print("🏇 競馬AI予測システム - メインメニュー")
        print("="*60)
        print("1. 📊 データ収集・スクレイピング")
        print("2. 🤖 モデル訓練")
        print("3. 🎯 リアルタイム予測")
        print("4. 📈 データ統合・処理")
        print("5. 🔧 特徴量エンジニアリング")
        print("6. 📋 システム情報・設定")
        print("7. 🧪 分析・実験ツール")
        print("0. 🚪 終了")
        print("="*60)
    
    def data_scraping_menu(self):
        """データスクレイピングメニュー"""
        print("\n📊 データ収集・スクレイピング")
        print("-" * 40)
        print("1. netkeiba.com データ収集")
        print("2. 特定年度データ収集")
        print("3. 特定レースデータ収集")
        print("4. 馬データ収集")
        print("5. 血統データ収集")
        print("0. メインメニューに戻る")
        
        choice = input("\n選択してください (0-5): ").strip()
        
        if choice == "1":
            self._run_data_scraping()
        elif choice == "2":
            self._run_yearly_data_scraping()
        elif choice == "3":
            self._run_specific_race_scraping()
        elif choice == "4":
            self._run_horse_data_scraping()
        elif choice == "5":
            self._run_pedigree_scraping()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def model_training_menu(self):
        """モデル訓練メニュー"""
        print("\n🤖 モデル訓練")
        print("-" * 40)
        print("1. 基本モデル訓練 (simple_train.py)")
        print("2. 拡張モデル訓練 (enhanced_train.py)")
        print("3. 高速訓練 (quick_enhanced_train.py)")
        print("4. 年齢特化訓練 (age_enhanced_train.py)")
        print("5. 📅 複数年統合訓練")
        print("6. 📊 時系列検証訓練")
        print("7. カスタム訓練設定")
        print("0. メインメニューに戻る")
        
        choice = input("\n選択してください (0-7): ").strip()
        
        if choice == "1":
            self._run_simple_training()
        elif choice == "2":
            self._run_enhanced_training()
        elif choice == "3":
            self._run_quick_training()
        elif choice == "4":
            self._run_age_training()
        elif choice == "5":
            self._run_multi_year_training()
        elif choice == "6":
            self._run_time_series_training()
        elif choice == "7":
            self._run_custom_training()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def prediction_menu(self):
        """予測メニュー"""
        print("\n🎯 リアルタイム予測")
        print("-" * 40)
        print("1. 🏆 Ultimate Live Predictor (最強統合版) ⭐NEW⭐")
        print("2. 🚀 改善版ライブレース予測")
        print("3. 📊 従来版ライブレース予測")
        print("4. 🎯 特定レース予測")
        print("5. 📈 複数レース一括予測")
        print("6. 🛡️ 安全モード予測")
        print("7. 🧠 TensorFlow Ranking予測")
        print("8. 🧪 改善版モデルテスト")
        print("9. 🗓️ 週間レース一括予測")
        print("0. メインメニューに戻る")
        
        choice = input("\n選択してください (0-9): ").strip()
        
        if choice == "1":
            self._run_ultimate_live_prediction()
        elif choice == "2":
            self._run_improved_live_prediction()
        elif choice == "3":
            self._run_live_prediction()
        elif choice == "4":
            self._run_specific_race_prediction()
        elif choice == "5":
            self._run_batch_prediction()
        elif choice == "6":
            self._run_safe_prediction()
        elif choice == "7":
            self._run_tf_ranking_prediction()
        elif choice == "8":
            self._run_improved_model_test()
        elif choice == "9":
            self._run_weekly_race_prediction()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def data_integration_menu(self):
        """データ統合メニュー"""
        print("\n📈 データ統合・処理")
        print("-" * 40)
        print("1. 包括的データ統合")
        print("2. レースデータ処理")
        print("3. 馬データ処理")
        print("4. コーナー分析")
        print("5. データ品質チェック")
        print("0. メインメニューに戻る")
        
        choice = input("\n選択してください (0-5): ").strip()
        
        if choice == "1":
            self._run_comprehensive_integration()
        elif choice == "2":
            self._run_race_data_processing()
        elif choice == "3":
            self._run_horse_data_processing()
        elif choice == "4":
            self._run_corner_analysis()
        elif choice == "5":
            self._run_data_quality_check()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def feature_engineering_menu(self):
        """特徴量エンジニアリングメニュー"""
        print("\n🔧 特徴量エンジニアリング")
        print("-" * 40)
        print("1. 特徴量計算実行")
        print("2. 特徴量設定表示")
        print("3. カスタム特徴量追加")
        print("4. 特徴量重要度分析")
        print("5. 特徴量設定編集")
        print("0. メインメニューに戻る")
        
        choice = input("\n選択してください (0-5): ").strip()
        
        if choice == "1":
            self._run_feature_calculation()
        elif choice == "2":
            self._show_feature_config()
        elif choice == "3":
            self._add_custom_feature()
        elif choice == "4":
            self._analyze_feature_importance()
        elif choice == "5":
            self._edit_feature_config()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def system_info_menu(self):
        """システム情報メニュー"""
        print("\n📋 システム情報・設定")
        print("-" * 40)
        print("1. プロジェクト情報表示")
        print("2. 学習済みモデル一覧")
        print("3. データファイル一覧")
        print("4. 設定ファイル確認")
        print("5. ログファイル確認")
        print("6. 依存関係チェック")
        print("0. メインメニューに戻る")
        
        choice = input("\n選択してください (0-6): ").strip()
        
        if choice == "1":
            self._show_project_info()
        elif choice == "2":
            self._show_models_list()
        elif choice == "3":
            self._show_data_files()
        elif choice == "4":
            self._show_config_files()
        elif choice == "5":
            self._show_log_files()
        elif choice == "6":
            self._check_dependencies()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def analysis_tools_menu(self):
        """分析・実験ツールメニュー"""
        print("\n🧪 分析・実験ツール")
        print("-" * 40)
        print("1. 🔬 Optuna最適化")
        print("2. 🔍 モデル解釈分析")
        print("3. 📈 性能詳細分析")
        print("4. 🧠 TensorFlow Ranking分析")
        print("5. 📊 データ可視化")
        print("6. 🚀 改善版モデルテスト")
        print("7. 📅 複数年モデル評価")
        print("8. 🎯 実際レースでのテスト")
        print("0. メインメニューに戻る")
        
        choice = input("\n選択してください (0-8): ").strip()
        
        if choice == "1":
            self._run_optuna_optimization()
        elif choice == "2":
            self._run_model_interpretation()
        elif choice == "3":
            self._run_performance_analysis()
        elif choice == "4":
            self._run_tf_ranking_analysis()
        elif choice == "5":
            self._run_data_visualization()
        elif choice == "6":
            self._run_improved_model_test()
        elif choice == "7":
            self._run_multi_year_evaluation()
        elif choice == "8":
            self._run_real_race_test()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    # 各機能の実装メソッド
    def _run_data_scraping(self):
        """データスクレイピング実行"""
        try:
            self.logger.info("🕷️ データスクレイピング開始...")
            import subprocess
            result = subprocess.run([sys.executable, "netkeiba_scraping_tool.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ データスクレイピング完了")
                print(result.stdout)
            else:
                self.logger.error(f"❌ データスクレイピングエラー: {result.stderr}")
        except Exception as e:
            self.logger.error(f"❌ データスクレイピング実行エラー: {e}")
    
    def _run_yearly_data_scraping(self):
        """特定年度データ収集"""
        year = input("収集する年度を入力してください (例: 2024): ").strip()
        if not year.isdigit() or len(year) != 4:
            print("❌ 無効な年度です")
            return
        
        try:
            self.logger.info(f"📅 {year}年度データ収集開始...")
            
            print(f"📊 {year}年度のデータ収集を開始します...")
            print("収集対象:")
            print("  - レース基本情報")
            print("  - レース結果")
            print("  - 馬の基本情報")
            print("  - 過去戦績")
            print("  - コーナー通過順位")
            
            proceed = input("\n実行しますか？ (y/N): ").strip().lower()
            if proceed not in ['y', 'yes']:
                print("❌ データ収集をキャンセルしました")
                return
            
            # スクレイピングツールを年度指定で実行
            import subprocess
            result = subprocess.run([
                sys.executable, "netkeiba_scraping_tool.py", 
                "--year", year,
                "--include-race-info",
                "--include-results", 
                "--include-horse-info"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"✅ {year}年度データ収集完了")
                print(f"✅ {year}年度のデータ収集が完了しました")
                print(result.stdout)
                
                # データ統合の提案
                integrate = input(f"\n{year}年度データを統合処理しますか？ (y/N): ").strip().lower()
                if integrate in ['y', 'yes']:
                    self._integrate_yearly_data(year)
            else:
                self.logger.error(f"❌ {year}年度データ収集エラー: {result.stderr}")
                print(f"❌ データ収集中にエラーが発生しました")
                print(result.stderr)
                
        except Exception as e:
            self.logger.error(f"❌ 年度データ収集エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _integrate_yearly_data(self, year: str):
        """年度データの統合処理"""
        try:
            self.logger.info(f"📈 {year}年度データ統合開始...")
            
            from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
            
            integrator = ComprehensiveDataIntegrator()
            
            print(f"📈 {year}年度データを統合中...")
            data = integrator.generate_comprehensive_table(
                year=year,
                include_race_info=True,
                include_horse_info=True,
                include_past_performance=True,
                save_to_pickle=True
            )
            
            self.logger.info(f"✅ {year}年度データ統合完了: {len(data)}件")
            print(f"✅ 統合完了: {len(data):,}件のデータ")
            print(f"📄 保存先: output/integrated_data_{year}.pickle")
            
        except Exception as e:
            self.logger.error(f"❌ {year}年度データ統合エラー: {e}")
            print(f"❌ データ統合エラー: {e}")
    
    def _run_specific_race_scraping(self):
        """特定レースデータ収集"""
        race_id = input("レースIDを入力してください (例: 202406010101): ").strip()
        if len(race_id) != 12 or not race_id.isdigit():
            print("❌ 無効なレースID形式です")
            return
        
        try:
            self.logger.info(f"🏇 レース{race_id}のデータ収集開始...")
            
            print(f"📊 レース{race_id}のデータ収集を開始します...")
            print("収集対象:")
            print("  - レース基本情報")
            print("  - 出馬表")
            print("  - レース結果")
            print("  - 各馬の詳細データ")
            
            proceed = input("\n実行しますか？ (y/N): ").strip().lower()
            if proceed not in ['y', 'yes']:
                print("❌ データ収集をキャンセルしました")
                return
            
            from core.scrapers.scraper import NetkeibaScraper
            from core.processors.race_processor import RaceProcessor
            
            # スクレイパー初期化
            scraper = NetkeibaScraper()
            race_processor = RaceProcessor()
            
            print("🕷️ レース情報を収集中...")
            
            # レース基本情報の取得
            race_info_url = f"https://race.netkeiba.com/race/shutuba.html?race_id={race_id}"
            race_info_data = scraper.scrape_race_info(race_info_url)
            
            if race_info_data:
                print("✅ レース基本情報取得完了")
                
                # レース結果の取得
                race_result_url = f"https://race.netkeiba.com/race/result.html?race_id={race_id}"
                race_result_data = scraper.scrape_race_results(race_result_url)
                
                if race_result_data:
                    print("✅ レース結果取得完了")
                    
                    # データ保存
                    output_file = self.output_dir / f"race_{race_id}_data.pickle"
                    race_data = {
                        'race_id': race_id,
                        'race_info': race_info_data,
                        'race_results': race_result_data,
                        'collected_at': datetime.now().isoformat()
                    }
                    
                    import pickle
                    with open(output_file, 'wb') as f:
                        pickle.dump(race_data, f)
                    
                    print(f"📄 データ保存完了: {output_file}")
                    
                    # 馬データの詳細収集を提案
                    collect_horses = input("\n出走馬の詳細データも収集しますか？ (y/N): ").strip().lower()
                    if collect_horses in ['y', 'yes']:
                        self._collect_race_horse_details(race_id, race_result_data)
                    
                    self.logger.info(f"✅ レース{race_id}データ収集完了")
                    
                else:
                    print("❌ レース結果の取得に失敗しました")
            else:
                print("❌ レース基本情報の取得に失敗しました")
                
        except Exception as e:
            self.logger.error(f"❌ レースデータ収集エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _collect_race_horse_details(self, race_id: str, race_results: list):
        """レース出走馬の詳細データ収集"""
        try:
            from core.processors.horse_processor import HorseProcessor
            
            horse_processor = HorseProcessor()
            
            # 出走馬のIDを抽出
            horse_ids = []
            for result in race_results:
                if 'horse_id' in result:
                    horse_ids.append(result['horse_id'])
            
            if not horse_ids:
                print("❌ 馬IDが見つかりませんでした")
                return
            
            print(f"🐎 出走馬 {len(horse_ids)}頭の詳細データを収集中...")
            
            # 馬の基本情報収集
            horse_info_data = horse_processor.process_horse_info_for_ids(
                horse_ids=horse_ids,
                parallel=True,
                max_workers=3
            )
            
            # 過去戦績収集
            horse_results_data = horse_processor.process_horse_results_for_ids(
                horse_ids=horse_ids,
                parallel=True,
                max_workers=3
            )
            
            # データ保存
            output_file = self.output_dir / f"race_{race_id}_horse_details.pickle"
            horse_data = {
                'race_id': race_id,
                'horse_info': horse_info_data,
                'horse_results': horse_results_data,
                'collected_at': datetime.now().isoformat()
            }
            
            import pickle
            with open(output_file, 'wb') as f:
                pickle.dump(horse_data, f)
            
            print(f"✅ 馬詳細データ収集完了")
            print(f"📄 保存先: {output_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 馬詳細データ収集エラー: {e}")
            print(f"❌ 馬詳細データ収集エラー: {e}")
    
    def _run_horse_data_scraping(self):
        """馬データ収集"""
        print("\n🐎 馬データ収集")
        print("-" * 30)
        print("1. 馬ID指定収集")
        print("2. 馬名検索収集")
        print("3. 調教師別馬一覧収集")
        print("4. 馬主別馬一覧収集")
        print("0. 戻る")
        
        choice = input("\n選択してください (0-4): ").strip()
        
        if choice == "1":
            self._collect_horse_by_id()
        elif choice == "2":
            self._collect_horse_by_name()
        elif choice == "3":
            self._collect_horses_by_trainer()
        elif choice == "4":
            self._collect_horses_by_owner()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def _collect_horse_by_id(self):
        """馬ID指定でデータ収集"""
        horse_id = input("馬IDを入力してください (例: 2020104005): ").strip()
        if not horse_id:
            print("❌ 馬IDが入力されていません")
            return
        
        try:
            from core.processors.horse_processor import HorseProcessor
            
            horse_processor = HorseProcessor()
            
            print(f"🐎 馬ID {horse_id} のデータを収集中...")
            
            # 馬基本情報収集
            horse_info = horse_processor.process_horse_info_for_ids([horse_id])
            
            # 過去戦績収集
            horse_results = horse_processor.process_horse_results_for_ids([horse_id])
            
            if not horse_info.empty:
                horse_name = horse_info.iloc[0].get('馬名', horse_id)
                print(f"✅ 馬情報収集完了: {horse_name}")
                print(f"   基本情報: {len(horse_info)}件")
                print(f"   過去戦績: {len(horse_results)}件")
                
                # データ保存
                output_file = self.output_dir / f"horse_{horse_id}_data.pickle"
                horse_data = {
                    'horse_id': horse_id,
                    'horse_info': horse_info,
                    'horse_results': horse_results,
                    'collected_at': datetime.now().isoformat()
                }
                
                import pickle
                with open(output_file, 'wb') as f:
                    pickle.dump(horse_data, f)
                
                print(f"📄 データ保存完了: {output_file}")
            else:
                print("❌ 馬情報が取得できませんでした")
                
        except Exception as e:
            self.logger.error(f"❌ 馬データ収集エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _collect_horse_by_name(self):
        """馬名検索でデータ収集"""
        horse_name = input("馬名を入力してください (例: アーモンドアイ): ").strip()
        if not horse_name:
            print("❌ 馬名が入力されていません")
            return
        
        try:
            print(f"🔍 馬名 '{horse_name}' を検索中...")
            print("⚠️ この機能は検索APIの実装が必要です")
            print("現在は馬ID直接指定をご利用ください")
            
        except Exception as e:
            self.logger.error(f"❌ 馬名検索エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _collect_horses_by_trainer(self):
        """調教師別馬一覧収集"""
        trainer_id = input("調教師IDを入力してください (例: 01087): ").strip()
        if not trainer_id:
            print("❌ 調教師IDが入力されていません")
            return
        
        try:
            print(f"🏇 調教師ID {trainer_id} の管理馬を収集中...")
            print("⚠️ この機能は調教師ページのスクレイピングが必要です")
            print("現在は個別の馬ID指定をご利用ください")
            
        except Exception as e:
            self.logger.error(f"❌ 調教師別収集エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _collect_horses_by_owner(self):
        """馬主別馬一覧収集"""
        owner_id = input("馬主IDを入力してください: ").strip()
        if not owner_id:
            print("❌ 馬主IDが入力されていません")
            return
        
        try:
            print(f"👑 馬主ID {owner_id} の所有馬を収集中...")
            print("⚠️ この機能は馬主ページのスクレイピングが必要です")
            print("現在は個別の馬ID指定をご利用ください")
            
        except Exception as e:
            self.logger.error(f"❌ 馬主別収集エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_pedigree_scraping(self):
        """血統データ収集"""
        print("\n🧬 血統データ収集")
        print("-" * 30)
        print("1. 馬ID指定血統収集")
        print("2. 血統データベース更新")
        print("3. 血統分析レポート")
        print("0. 戻る")
        
        choice = input("\n選択してください (0-3): ").strip()
        
        if choice == "1":
            self._collect_pedigree_by_horse_id()
        elif choice == "2":
            self._update_pedigree_database()
        elif choice == "3":
            self._generate_pedigree_analysis()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def _collect_pedigree_by_horse_id(self):
        """馬ID指定血統収集"""
        horse_id = input("馬IDを入力してください (例: 2020104005): ").strip()
        if not horse_id:
            print("❌ 馬IDが入力されていません")
            return
        
        try:
            print(f"🧬 馬ID {horse_id} の血統データを収集中...")
            
            from core.scrapers.scraper import NetkeibaScraper
            scraper = NetkeibaScraper()
            
            # 血統ページURL
            pedigree_url = f"https://db.netkeiba.com/horse/ped/{horse_id}"
            
            print(f"🔍 血統ページを取得中: {pedigree_url}")
            
            # スクレイピング実行（基本実装）
            import requests
            from bs4 import BeautifulSoup
            import time
            import random
            
            time.sleep(random.uniform(2, 4))
            
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            try:
                response = session.get(pedigree_url, timeout=15)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 血統情報を抽出
                pedigree_data = {
                    'horse_id': horse_id,
                    'father': '',
                    'mother': '',
                    'father_father': '',
                    'father_mother': '',
                    'mother_father': '',
                    'mother_mother': '',
                    'collected_at': datetime.now().isoformat()
                }
                
                # 血統テーブルを探す
                pedigree_table = soup.find('table', class_='blood_table')
                if pedigree_table:
                    cells = pedigree_table.find_all('td')
                    if len(cells) >= 6:
                        pedigree_data['father'] = cells[0].get_text(strip=True) if cells[0] else ''
                        pedigree_data['mother'] = cells[1].get_text(strip=True) if cells[1] else ''
                        pedigree_data['father_father'] = cells[2].get_text(strip=True) if cells[2] else ''
                        pedigree_data['father_mother'] = cells[3].get_text(strip=True) if cells[3] else ''
                        pedigree_data['mother_father'] = cells[4].get_text(strip=True) if cells[4] else ''
                        pedigree_data['mother_mother'] = cells[5].get_text(strip=True) if cells[5] else ''
                
                # データ保存
                output_file = self.output_dir / f"pedigree_{horse_id}_data.pickle"
                import pickle
                with open(output_file, 'wb') as f:
                    pickle.dump(pedigree_data, f)
                
                print("✅ 血統データ収集完了")
                print(f"父: {pedigree_data['father']}")
                print(f"母: {pedigree_data['mother']}")
                print(f"父父: {pedigree_data['father_father']}")
                print(f"父母: {pedigree_data['father_mother']}")
                print(f"母父: {pedigree_data['mother_father']}")
                print(f"母母: {pedigree_data['mother_mother']}")
                print(f"📄 データ保存: {output_file}")
                
            except Exception as e:
                print(f"❌ 血統データ取得エラー: {e}")
                
        except Exception as e:
            self.logger.error(f"❌ 血統収集エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _update_pedigree_database(self):
        """血統データベース更新"""
        print("🔄 血統データベース更新機能")
        print("複数馬の血統データを一括更新します")
        
        try:
            # 既存の馬IDリストを取得
            horse_ids = []
            
            # race_resultsから馬IDを抽出
            for year in range(2020, 2025):
                try:
                    results_path = f"output/race_results_{year}.pickle"
                    if os.path.exists(results_path):
                        race_results = pd.read_pickle(results_path)
                        if 'horse_id' in race_results.columns:
                            year_horse_ids = race_results['horse_id'].unique().tolist()
                            horse_ids.extend(year_horse_ids)
                except Exception as e:
                    continue
            
            unique_horse_ids = list(set(horse_ids))[:100]  # 最初の100頭に制限
            
            if not unique_horse_ids:
                print("❌ 対象馬IDが見つかりません")
                return
            
            print(f"📊 対象馬数: {len(unique_horse_ids)}頭")
            
            proceed = input("血統データ収集を実行しますか？ (y/N): ").strip().lower()
            if proceed not in ['y', 'yes']:
                print("❌ 血統データ更新をキャンセルしました")
                return
            
            success_count = 0
            for i, horse_id in enumerate(unique_horse_ids[:20], 1):  # 最初の20頭
                try:
                    print(f"[{i}/20] 馬ID {horse_id} の血統データを収集中...")
                    # 簡易版血統収集（詳細は_collect_pedigree_by_horse_idと同様）
                    time.sleep(random.uniform(1, 3))
                    success_count += 1
                except Exception as e:
                    print(f"❌ 馬ID {horse_id} でエラー: {e}")
                    continue
            
            print(f"✅ 血統データベース更新完了: {success_count}/20頭")
            
        except Exception as e:
            self.logger.error(f"❌ 血統データベース更新エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _generate_pedigree_analysis(self):
        """血統分析レポート生成"""
        print("📊 血統分析レポート生成")
        
        try:
            # 血統データファイルを検索
            pedigree_files = list(self.output_dir.glob("pedigree_*_data.pickle"))
            
            if not pedigree_files:
                print("❌ 血統データが見つかりません")
                print("まず血統データ収集を実行してください")
                return
            
            print(f"📄 血統データファイル: {len(pedigree_files)}個")
            
            # 血統分析を実行
            analysis_results = {
                'total_horses': len(pedigree_files),
                'father_stats': {},
                'mother_stats': {},
                'analyzed_at': datetime.now().isoformat()
            }
            
            for pedigree_file in pedigree_files:
                try:
                    with open(pedigree_file, 'rb') as f:
                        pedigree_data = pickle.load(f)
                    
                    father = pedigree_data.get('father', '')
                    if father:
                        analysis_results['father_stats'][father] = analysis_results['father_stats'].get(father, 0) + 1
                    
                    mother = pedigree_data.get('mother', '')
                    if mother:
                        analysis_results['mother_stats'][mother] = analysis_results['mother_stats'].get(mother, 0) + 1
                        
                except Exception as e:
                    continue
            
            # レポート生成
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"pedigree_analysis_report_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("血統分析レポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"分析対象馬数: {analysis_results['total_horses']}頭\n")
                f.write(f"分析実行日時: {analysis_results['analyzed_at']}\n\n")
                
                f.write("父馬TOP10:\n")
                f.write("-" * 30 + "\n")
                sorted_fathers = sorted(analysis_results['father_stats'].items(), key=lambda x: x[1], reverse=True)[:10]
                for i, (father, count) in enumerate(sorted_fathers, 1):
                    f.write(f"{i:2d}. {father}: {count}頭\n")
                
                f.write("\n母馬TOP10:\n")
                f.write("-" * 30 + "\n")
                sorted_mothers = sorted(analysis_results['mother_stats'].items(), key=lambda x: x[1], reverse=True)[:10]
                for i, (mother, count) in enumerate(sorted_mothers, 1):
                    f.write(f"{i:2d}. {mother}: {count}頭\n")
            
            print(f"✅ 血統分析レポート生成完了")
            print(f"📄 レポート保存: {report_path}")
            
            # サマリー表示
            print(f"\n📊 分析サマリー:")
            print(f"  分析対象: {analysis_results['total_horses']}頭")
            print(f"  父馬種類: {len(analysis_results['father_stats'])}種")
            print(f"  母馬種類: {len(analysis_results['mother_stats'])}種")
            
        except Exception as e:
            self.logger.error(f"❌ 血統分析エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_simple_training(self):
        """基本モデル訓練"""
        try:
            self.logger.info("🤖 基本モデル訓練開始...")
            import subprocess
            result = subprocess.run([sys.executable, "simple_train.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ 基本モデル訓練完了")
                print(result.stdout)
            else:
                self.logger.error(f"❌ 訓練エラー: {result.stderr}")
        except Exception as e:
            self.logger.error(f"❌ 基本モデル訓練エラー: {e}")
    
    def _run_enhanced_training(self):
        """拡張モデル訓練"""
        try:
            self.logger.info("🚀 拡張モデル訓練開始...")
            import subprocess
            result = subprocess.run([sys.executable, "enhanced_train.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ 拡張モデル訓練完了")
                print(result.stdout)
            else:
                self.logger.error(f"❌ 訓練エラー: {result.stderr}")
        except Exception as e:
            self.logger.error(f"❌ 拡張モデル訓練エラー: {e}")
    
    def _run_quick_training(self):
        """高速訓練"""
        try:
            self.logger.info("⚡ 高速モデル訓練開始...")
            import subprocess
            result = subprocess.run([sys.executable, "quick_enhanced_train.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ 高速モデル訓練完了")
                print(result.stdout)
            else:
                self.logger.error(f"❌ 訓練エラー: {result.stderr}")
        except Exception as e:
            self.logger.error(f"❌ 高速モデル訓練エラー: {e}")
    
    def _run_age_training(self):
        """年齢特化訓練"""
        try:
            self.logger.info("📊 年齢特化モデル訓練開始...")
            import subprocess
            result = subprocess.run([sys.executable, "age_enhanced_train.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ 年齢特化モデル訓練完了")
                print(result.stdout)
            else:
                self.logger.error(f"❌ 訓練エラー: {result.stderr}")
        except Exception as e:
            self.logger.error(f"❌ 年齢特化モデル訓練エラー: {e}")
    
    def _run_multi_year_training(self):
        """📅 複数年統合訓練"""
        print("\n📅 複数年統合訓練")
        print("=" * 50)
        print("複数年度のデータを統合して大規模モデル訓練を行います")
        print()
        
        try:
            # 利用可能な年度を確認
            available_years = []
            import os
            for year in range(2020, 2026):
                race_file = f"output/race_results_{year}.pickle"
                if os.path.exists(race_file):
                    available_years.append(year)
            
            if not available_years:
                print("❌ 利用可能な年度データが見つかりません")
                print("💡 まずデータ収集を実行してください")
                return
            
            print(f"📊 利用可能な年度: {', '.join(map(str, available_years))}")
            
            # 年度範囲選択
            print("\n📅 訓練年度範囲を選択してください:")
            print("1. 全年度統合 (推奨)")
            print("2. カスタム範囲選択")
            print("3. 最新3年のみ")
            print("4. 最新5年のみ")
            
            range_choice = input("\n選択してください (1-4): ").strip()
            
            selected_years = []
            if range_choice == "1":
                selected_years = available_years
                print(f"✅ 全年度統合: {len(selected_years)}年分")
            elif range_choice == "2":
                start_year = input("開始年度 (例: 2020): ").strip()
                end_year = input("終了年度 (例: 2024): ").strip()
                try:
                    start = int(start_year)
                    end = int(end_year)
                    selected_years = [y for y in available_years if start <= y <= end]
                    if not selected_years:
                        print("❌ 指定範囲にデータがありません")
                        return
                    print(f"✅ カスタム範囲: {len(selected_years)}年分")
                except ValueError:
                    print("❌ 無効な年度形式です")
                    return
            elif range_choice == "3":
                selected_years = available_years[-3:] if len(available_years) >= 3 else available_years
                print(f"✅ 最新3年: {len(selected_years)}年分")
            elif range_choice == "4":
                selected_years = available_years[-5:] if len(available_years) >= 5 else available_years
                print(f"✅ 最新5年: {len(selected_years)}年分")
            else:
                print("❌ 無効な選択です")
                return
            
            print(f"📊 選択された年度: {', '.join(map(str, selected_years))}")
            
            # 訓練モード選択
            print("\n🤖 訓練モードを選択してください:")
            print("1. 📊 拡張モード (過去戦績込み、高精度)")
            print("2. ⚡ 高速モード (基本特徴量のみ)")
            print("3. 🎯 年齢特化モード")
            print("4. 🚀 実験モード (全特徴量)")
            
            mode_choice = input("\n選択してください (1-4): ").strip()
            
            # 確認
            print(f"\n📋 訓練設定確認:")
            print(f"   年度: {', '.join(map(str, selected_years))}")
            mode_names = {
                "1": "拡張モード (過去戦績込み)",
                "2": "高速モード (基本特徴量)",
                "3": "年齢特化モード",
                "4": "実験モード (全特徴量)"
            }
            print(f"   モード: {mode_names.get(mode_choice, '不明')}")
            
            confirm = input("この設定で訓練を開始しますか？ (y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ 訓練をキャンセルしました")
                return
            
            # 複数年訓練実行
            self.logger.info(f"📅 複数年統合訓練開始: {len(selected_years)}年分")
            print(f"\n🚀 複数年統合訓練開始...")
            print(f"📊 対象年度: {len(selected_years)}年分")
            print("⏳ 大規模データ処理のため時間がかかります...")
            
            # 複数年訓練スクリプトを作成・実行
            self._execute_multi_year_training(selected_years, mode_choice)
            
        except Exception as e:
            self.logger.error(f"❌ 複数年訓練エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _execute_multi_year_training(self, years, mode):
        """複数年訓練の実行"""
        try:
            # 一時的な訓練スクリプトを生成
            script_content = self._generate_multi_year_script(years, mode)
            script_path = "temp_multi_year_train.py"
            
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # スクリプト実行
            import subprocess
            import sys
            
            print("📝 訓練スクリプト生成完了")
            print("🚀 訓練実行中...")
            
            process = subprocess.Popen(
                [sys.executable, script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            # リアルタイム出力表示
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
            
            return_code = process.wait()
            
            if return_code == 0:
                print("✅ 複数年統合訓練完了!")
                self.logger.info("✅ 複数年統合訓練成功")
            else:
                error_output = process.stderr.read()
                print(f"❌ 訓練中にエラーが発生しました")
                print(f"エラー詳細: {error_output}")
                self.logger.error(f"❌ 複数年訓練エラー: {error_output}")
            
            # 一時ファイル削除
            try:
                os.remove(script_path)
            except:
                pass
                
        except Exception as e:
            self.logger.error(f"❌ 複数年訓練実行エラー: {e}")
            print(f"❌ 訓練実行エラー: {e}")
    
    def _generate_multi_year_script(self, years, mode):
        """複数年訓練スクリプト生成"""
        script_template = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
複数年統合訓練スクリプト (自動生成)
対象年度: {years}
モード: {mode}
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 基本設定
YEARS = {years}
MODE = "{mode}"

def load_multi_year_data():
    """複数年データ読み込み"""
    print(f"📊 {{len(YEARS)}}年分のデータを読み込み中...")
    
    all_race_data = []
    all_horse_data = []
    
    for year in YEARS:
        try:
            # レースデータ
            race_file = f"output/race_results_{{year}}.pickle"
            if os.path.exists(race_file):
                race_df = pd.read_pickle(race_file)
                race_df['data_year'] = year
                all_race_data.append(race_df)
                print(f"  {{year}}年レースデータ: {{len(race_df):,}}件")
            
            # 馬データ（モードに応じて）
            if MODE in ["1", "3", "4"]:  # 過去戦績が必要なモード
                horse_file = f"output/horse_results_{{year}}.pickle"
                if os.path.exists(horse_file):
                    horse_df = pd.read_pickle(horse_file)
                    horse_df['data_year'] = year
                    all_horse_data.append(horse_df)
                    print(f"  {{year}}年馬データ: {{len(horse_df):,}}件")
        except Exception as e:
            print(f"⚠️  {{year}}年データ読み込みエラー: {{e}}")
    
    if not all_race_data:
        raise ValueError("読み込み可能なレースデータがありません")
    
    # データ統合
    race_data = pd.concat(all_race_data, ignore_index=True)
    horse_data = pd.concat(all_horse_data, ignore_index=True) if all_horse_data else pd.DataFrame()
    
    print(f"✅ 統合完了: レース{{len(race_data):,}}件, 馬データ{{len(horse_data):,}}件")
    return race_data, horse_data

def prepare_features(race_data, horse_data, mode):
    """特徴量準備"""
    print(f"🔧 特徴量準備開始 (モード: {{mode}})")
    
    # 基本特徴量
    features = ['course_len', '枠番', '馬番', '斤量', 'race_type_encoded', 
               'ground_state_encoded', 'weather_encoded', 'track_direction_encoded']
    
    # モード別特徴量追加
    if mode == "1":  # 拡張モード
        print("📊 拡張モード: 過去戦績特徴量を追加")
        # 過去戦績特徴量の計算ロジックをここに追加
        pass
    elif mode == "2":  # 高速モード
        print("⚡ 高速モード: 基本特徴量のみ")
        pass
    elif mode == "3":  # 年齢特化モード
        print("🎯 年齢特化モード: 年齢関連特徴量を追加")
        # 年齢特徴量の計算ロジックをここに追加
        pass
    elif mode == "4":  # 実験モード
        print("🚀 実験モード: 全特徴量を追加")
        # 実験的特徴量の計算ロジックをここに追加
        pass
    
    # 基本的なデータ準備
    data = race_data.copy()
    
    # カテゴリエンコーディング
    categorical_mappings = {{
        'race_type': {{'芝': 0, 'ダート': 1, '障害': 2}},
        'ground_state': {{'良': 0, '稍重': 1, '重': 2, '不良': 3}},
        'weather': {{'晴': 0, '曇': 1, '雨': 2, '雪': 3}},
        'track_direction': {{'右': 0, '左': 1, '直線': 2, '障害': 3}}
    }}
    
    for col, mapping in categorical_mappings.items():
        if col in data.columns:
            data[f'{{col}}_encoded'] = data[col].map(mapping).fillna(0)
    
    # 数値特徴量の準備
    numeric_cols = ['course_len', '枠番', '馬番', '斤量']
    for col in numeric_cols:
        if col in data.columns:
            data[col] = pd.to_numeric(data[col], errors='coerce').fillna(0)
    
    # ターゲット変数
    if '着順' in data.columns:
        # 着順を数値型に変換（文字列や無効値を除去）
        data['着順_numeric'] = pd.to_numeric(data['着順'], errors='coerce')
        # 有効な着順データのみを使用（1-18位程度）
        valid_mask = (data['着順_numeric'] >= 1) & (data['着順_numeric'] <= 18)
        data = data[valid_mask].copy()
        y = (data['着順_numeric'] <= 3).astype(int)  # 3着以内を正例
        print(f"📊 有効着順データ: {{len(data):,}}件 (正例率: {{y.mean():.3f}})")
    else:
        raise ValueError("着順カラムが見つかりません")
    
    # 特徴量選択
    available_features = [f for f in features if f in data.columns]
    X = data[available_features].fillna(0)
    
    print(f"✅ 特徴量準備完了: {{X.shape}}")
    return X, y, available_features

def train_model(X, y, features, mode):
    """モデル訓練"""
    print(f"🤖 モデル訓練開始 ({{len(X)}}件)")
    
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
    import lightgbm as lgb
    
    # データ分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # スケーリング
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # LightGBM訓練
    train_data = lgb.Dataset(X_train_scaled, label=y_train)
    
    params = {{
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42
    }}
    
    model = lgb.train(
        params,
        train_data,
        num_boost_round=1000,
        valid_sets=[train_data],
        callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
    )
    
    # 評価
    y_pred = model.predict(X_test_scaled)
    y_pred_binary = (y_pred > 0.5).astype(int)
    
    accuracy = accuracy_score(y_test, y_pred_binary)
    auc = roc_auc_score(y_test, y_pred)
    
    print(f"📊 訓練結果:")
    print(f"   精度: {{accuracy:.4f}}")
    print(f"   AUC: {{auc:.4f}}")
    print(f"   訓練データ: {{len(X_train):,}}件")
    print(f"   テストデータ: {{len(X_test):,}}件")
    
    return model, scaler, accuracy, auc

def save_multi_year_model(model, scaler, features, years, mode, accuracy, auc):
    """複数年モデル保存"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    year_range = f"{{min(years)}}-{{max(years)}}"
    
    model_name = f"multi_year_{{year_range}}_mode{{mode}}_{{timestamp}}"
    
    import joblib
    
    model_path = f"models/{{model_name}}_model.pkl"
    scaler_path = f"models/{{model_name}}_scaler.pkl"
    features_path = f"models/{{model_name}}_features.pkl"
    
    joblib.dump(model, model_path)
    joblib.dump(scaler, scaler_path)
    joblib.dump(features, features_path)
    
    print(f"💾 モデル保存完了:")
    print(f"   モデル: {{model_path}}")
    print(f"   スケーラー: {{scaler_path}}")
    print(f"   特徴量: {{features_path}}")
    
    # メタデータ保存
    metadata = {{
        'years': years,
        'mode': mode,
        'accuracy': accuracy,
        'auc': auc,
        'features': features,
        'trained_at': timestamp,
        'model_type': 'multi_year_lightgbm'
    }}
    
    import json
    metadata_path = f"models/{{model_name}}_metadata.json"
    with open(metadata_path, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    print(f"   メタデータ: {{metadata_path}}")

def main():
    """メイン処理"""
    try:
        print("🚀 複数年統合訓練開始")
        print(f"📅 対象年度: {{YEARS}}")
        print(f"🔧 モード: {{MODE}}")
        print("="*60)
        
        # データ読み込み
        race_data, horse_data = load_multi_year_data()
        
        # 特徴量準備
        X, y, features = prepare_features(race_data, horse_data, MODE)
        
        # モデル訓練
        model, scaler, accuracy, auc = train_model(X, y, features, MODE)
        
        # モデル保存
        save_multi_year_model(model, scaler, features, YEARS, MODE, accuracy, auc)
        
        print("="*60)
        print("✅ 複数年統合訓練完了!")
        print(f"📊 最終精度: {{accuracy:.4f}}")
        print(f"📈 最終AUC: {{auc:.4f}}")
        
    except Exception as e:
        print(f"❌ 訓練エラー: {{e}}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
        return script_template
    
    def _run_time_series_training(self):
        """📊 時系列検証訓練"""
        print("\n📊 時系列検証訓練")
        print("=" * 50)
        print("時系列を考慮した検証方法でモデルの汎化性能を評価します")
        print()
        
        try:
            # 既存の時系列検証ツールを呼び出し
            if os.path.exists("time_series_validator.py"):
                print("🔍 時系列検証ツール実行中...")
                import subprocess
                import sys
                
                result = subprocess.run(
                    [sys.executable, "time_series_validator.py"],
                    capture_output=True,
                    text=True,
                    encoding='utf-8'
                )
                
                if result.returncode == 0:
                    print("✅ 時系列検証完了")
                    print(result.stdout)
                else:
                    print("❌ 時系列検証エラー")
                    print(result.stderr)
            else:
                print("❌ time_series_validator.pyが見つかりません")
                print("💡 基本的な時系列分割検証を実行します")
                self._run_basic_time_series_validation()
                
        except Exception as e:
            self.logger.error(f"❌ 時系列検証エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_basic_time_series_validation(self):
        """基本的な時系列検証"""
        print("📊 基本時系列検証実行中...")
        # 簡単な時系列検証の実装
        print("⏳ 実装中...")
    
    def _run_custom_training(self):
        """カスタム訓練設定"""
        print("⚙️ カスタム訓練設定")
        print("-" * 40)
        print("1. カスタム特徴量設定")
        print("2. ハイパーパラメータ調整")
        print("3. 訓練データ期間設定")
        print("4. クロスバリデーション設定")
        print("5. モデル種類選択")
        print("0. 戻る")
        
        try:
            choice = input("\n選択してください: ").strip()
            
            if choice == "1":
                self._configure_custom_features()
            elif choice == "2":
                self._configure_hyperparameters()
            elif choice == "3":
                self._configure_training_period()
            elif choice == "4":
                self._configure_cross_validation()
            elif choice == "5":
                self._configure_model_type()
            elif choice == "0":
                return
            else:
                print("❌ 無効な選択です")
        except Exception as e:
            self.logger.error(f"❌ カスタム訓練設定エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_ultimate_live_prediction(self):
        """🏆 Ultimate Live Predictor - 最強統合版ライブレース予測"""
        print("\n🏆 Ultimate Live Race Predictor - 最強統合版")
        print("=" * 60)
        print("🔥 リアルタイムデータ取得 + 🤖 Selenium統合 + 🛡️ 最大BAN対策 + 🚀 TFR統合")
        print("= 競馬AI予測システムの最高峰実装 =")
        print()
        
        race_id = input("予測するレースIDを入力してください (例: 202412080101): ").strip()
        if len(race_id) != 12 or not race_id.isdigit():
            print("❌ 無効なレースID形式です")
            return
        
        try:
            self.logger.info(f"🏆 レース{race_id}のUltimate Live Prediction開始...")
            
            # 🏆 Ultimate Live Predictor のインポート
            from ultimate_live_predictor import UltimateLiveRacePredictor
            
            print("\n🔧 システム設定:")
            print("1. 🏆 フル機能モード (リアルタイム + Selenium + TFR)")
            print("2. 🔥 高速モード (リアルタイムのみ)")
            print("3. 🛡️ 安全モード (最大BAN対策)")
            print("4. 🚀 実験モード (TFR重視)")
            
            mode_choice = input("モードを選択してください (1-4, デフォルト:1): ").strip() or "1"
            
            # 🏆 モード別設定
            if mode_choice == "1":
                # フル機能モード
                predictor = UltimateLiveRacePredictor(
                    use_selenium=True,
                    enable_live_scraping=True,
                    enable_tfr=True,
                    max_ban_protection=True
                )
                print("✅ フル機能モードで初期化完了")
            elif mode_choice == "2":
                # 高速モード
                predictor = UltimateLiveRacePredictor(
                    use_selenium=False,
                    enable_live_scraping=True,
                    enable_tfr=False,
                    max_ban_protection=False
                )
                print("⚡ 高速モードで初期化完了")
            elif mode_choice == "3":
                # 安全モード
                predictor = UltimateLiveRacePredictor(
                    use_selenium=False,
                    enable_live_scraping=True,
                    enable_tfr=False,
                    max_ban_protection=True
                )
                print("🛡️ 安全モードで初期化完了")
            elif mode_choice == "4":
                # 実験モード
                predictor = UltimateLiveRacePredictor(
                    use_selenium=True,
                    enable_live_scraping=True,
                    enable_tfr=True,
                    max_ban_protection=False
                )
                print("🚀 実験モードで初期化完了")
            else:
                print("❌ 無効な選択です。フル機能モードで実行します。")
                predictor = UltimateLiveRacePredictor(
                    use_selenium=True,
                    enable_live_scraping=True,
                    enable_tfr=True,
                    max_ban_protection=True
                )
            
            print(f"\n🏆 {race_id} の究極予測を開始...")
            print("🔥 リアルタイムデータ取得中... (BAN対策のため時間がかかります)")
            
            # 🏆 究極の予測実行
            results, race_info = predictor.predict_race_ultimate(race_id)
            
            if not results.empty:
                predictor.display_ultimate_results(results, race_info)
                self.logger.info("✅ Ultimate Live Prediction完了")
                
                # 📊 結果保存オプション
                save_option = input("\n💾 結果を保存しますか？ (y/N): ").strip().lower()
                if save_option in ['y', 'yes']:
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    csv_filename = f"ultimate_prediction_{race_id}_{timestamp}.csv"
                    json_filename = f"ultimate_prediction_{race_id}_{timestamp}.json"
                    
                    # CSV保存
                    results.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                    print(f"📄 CSV保存: {csv_filename}")
                    
                    # JSON保存
                    import json
                    result_data = {
                        'race_id': race_id,
                        'prediction_results': results.to_dict('records'),
                        'race_info': race_info,
                        'predicted_at': timestamp,
                        'system': 'Ultimate Live Race Predictor'
                    }
                    with open(json_filename, 'w', encoding='utf-8') as f:
                        json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
                    print(f"📄 JSON保存: {json_filename}")
                    
            else:
                print("❌ Ultimate予測結果が取得できませんでした")
                print("💡 改善版予測を試してみてください")
                
        except ImportError as e:
            self.logger.error(f"❌ Ultimate Live Predictor インポートエラー: {e}")
            print("❌ Ultimate Live Predictorが見つかりません")
            print("💡 ultimate_live_predictor.pyファイルが存在することを確認してください")
        except Exception as e:
            self.logger.error(f"❌ Ultimate Live Prediction エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
            print("💡 改善版予測を試してみてください")
        finally:
            # 🧹 リソースクリーンアップ
            try:
                if 'predictor' in locals():
                    predictor.cleanup()
            except:
                pass
    
    def _run_improved_live_prediction(self):
        """改善版ライブレース予測"""
        race_id = input("予測するレースIDを入力してください (例: 202401010101): ").strip()
        if len(race_id) != 12 or not race_id.isdigit():
            print("❌ 無効なレースID形式です")
            return
        
        try:
            self.logger.info(f"🚀 レース{race_id}の改善版ライブ予測開始...")
            from improved_live_predictor import ImprovedLiveRacePredictor
            
            predictor = ImprovedLiveRacePredictor()
            results, race_info = predictor.predict_race_improved(race_id)
            
            if not results.empty:
                predictor.display_improved_results(results, race_info)
                self.logger.info("✅ 改善版ライブ予測完了")
            else:
                print("❌ 改善版予測結果が取得できませんでした")
                print("💡 従来版予測を試してみてください")
                
        except Exception as e:
            self.logger.error(f"❌ 改善版ライブ予測エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
            print("💡 従来版予測を試してみてください")
    
    def _run_live_prediction(self):
        """従来版ライブレース予測"""
        race_id = input("予測するレースIDを入力してください (例: 202406010101): ").strip()
        if len(race_id) != 12 or not race_id.isdigit():
            print("❌ 無効なレースID形式です")
            return
        
        try:
            self.logger.info(f"📊 レース{race_id}の従来版ライブ予測開始...")
            import subprocess
            result = subprocess.run([sys.executable, "enhanced_live_predictor.py"], 
                                  input=race_id, capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ 従来版ライブ予測完了")
                print(result.stdout)
            else:
                self.logger.error(f"❌ 予測エラー: {result.stderr}")
        except Exception as e:
            self.logger.error(f"❌ 従来版ライブ予測エラー: {e}")
    
    def _run_specific_race_prediction(self):
        """特定レース予測"""
        race_id = input("予測するレースIDを入力してください: ").strip()
        try:
            self.logger.info(f"🎯 レース{race_id}の予測開始...")
            from prediction.live_predictor import LiveRacePredictor
            predictor = LiveRacePredictor()
            result = predictor.predict_race_complete(race_id)
            if not result.empty:
                print("\n🏆 予測結果:")
                print(result[['馬名', '予測順位', '勝率予測']].head(10))
                self.logger.info("✅ 予測完了")
            else:
                print("❌ 予測結果が取得できませんでした")
        except Exception as e:
            self.logger.error(f"❌ 予測エラー: {e}")
    
    def _run_batch_prediction(self):
        """複数レース一括予測"""
        print("\n📊 複数レース一括予測")
        print("-" * 30)
        print("1. 日付指定一括予測")
        print("2. レースID一覧予測")
        print("3. ファイル指定予測")
        print("0. 戻る")
        
        choice = input("\n選択してください (0-3): ").strip()
        
        if choice == "1":
            self._batch_predict_by_date()
        elif choice == "2":
            self._batch_predict_by_race_ids()
        elif choice == "3":
            self._batch_predict_by_file()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def _batch_predict_by_date(self):
        """日付指定での一括予測"""
        date_str = input("予測する日付を入力してください (例: 2024-12-08): ").strip()
        
        try:
            from datetime import datetime
            target_date = datetime.strptime(date_str, '%Y-%m-%d')
            
            print(f"📅 {date_str} のレース一括予測を開始します...")
            
            # レースID生成（簡易版）
            date_code = target_date.strftime('%Y%m%d')
            race_ids = []
            
            # 一般的な場所・レース番号の組み合わせ
            venues = ['01', '02', '03', '04', '05', '06']  # 主要競馬場
            for venue in venues:
                for race_num in range(1, 13):  # 1R-12R
                    race_id = f"{date_code}{venue}{race_num:02d}"
                    race_ids.append(race_id)
            
            print(f"🎯 対象レース数: {len(race_ids)}レース")
            
            proceed = input("予測を実行しますか？ (y/N): ").strip().lower()
            if proceed not in ['y', 'yes']:
                print("❌ 予測をキャンセルしました")
                return
            
            self._execute_batch_prediction(race_ids, f"batch_{date_code}")
            
        except ValueError:
            print("❌ 無効な日付形式です (YYYY-MM-DD)")
        except Exception as e:
            self.logger.error(f"❌ 日付指定一括予測エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _batch_predict_by_race_ids(self):
        """レースID一覧での予測"""
        print("レースIDを複数入力してください (1行1ID、空行で終了)")
        race_ids = []
        
        while True:
            race_id = input("レースID: ").strip()
            if not race_id:
                break
            
            if len(race_id) == 12 and race_id.isdigit():
                race_ids.append(race_id)
                print(f"✅ 追加: {race_id}")
            else:
                print("❌ 無効なレースID形式です")
        
        if not race_ids:
            print("❌ レースIDが入力されていません")
            return
        
        print(f"\n🎯 対象レース数: {len(race_ids)}レース")
        proceed = input("予測を実行しますか？ (y/N): ").strip().lower()
        if proceed not in ['y', 'yes']:
            print("❌ 予測をキャンセルしました")
            return
        
        self._execute_batch_prediction(race_ids, "batch_manual")
    
    def _batch_predict_by_file(self):
        """ファイル指定での予測"""
        file_path = input("レースIDリストファイルのパスを入力してください: ").strip()
        
        try:
            import os
            if not os.path.exists(file_path):
                print("❌ ファイルが見つかりません")
                return
            
            race_ids = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    race_id = line.strip()
                    if race_id and len(race_id) == 12 and race_id.isdigit():
                        race_ids.append(race_id)
            
            if not race_ids:
                print("❌ 有効なレースIDが見つかりません")
                return
            
            print(f"📄 ファイル読み込み完了: {len(race_ids)}レース")
            proceed = input("予測を実行しますか？ (y/N): ").strip().lower()
            if proceed not in ['y', 'yes']:
                print("❌ 予測をキャンセルしました")
                return
            
            self._execute_batch_prediction(race_ids, f"batch_file")
            
        except Exception as e:
            self.logger.error(f"❌ ファイル指定予測エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _execute_batch_prediction(self, race_ids: list, batch_name: str):
        """一括予測の実行"""
        try:
            from improved_live_predictor import ImprovedLiveRacePredictor
            import pandas as pd
            
            predictor = ImprovedLiveRacePredictor()
            all_results = []
            success_count = 0
            
            print(f"\n🚀 改善版モデルで一括予測開始...")
            print(f"対象: {len(race_ids)}レース")
            
            for i, race_id in enumerate(race_ids, 1):
                try:
                    print(f"\n[{i}/{len(race_ids)}] レース{race_id}を予測中...")
                    
                    results, race_info = predictor.predict_race_improved(race_id)
                    
                    if not results.empty:
                        # 結果にレースIDを追加
                        results['race_id'] = race_id
                        results['batch_name'] = batch_name
                        all_results.append(results)
                        success_count += 1
                        
                        print(f"✅ 予測完了 (予測1位: {results.iloc[0]['馬名']})")
                    else:
                        print(f"❌ 予測失敗")
                    
                    # 進捗表示
                    if i % 10 == 0:
                        print(f"📊 進捗: {i}/{len(race_ids)} ({success_count}成功)")
                
                except Exception as e:
                    print(f"❌ レース{race_id}でエラー: {e}")
                    continue
            
            # 結果をまとめて保存
            if all_results:
                combined_results = pd.concat(all_results, ignore_index=True)
                
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = self.output_dir / f"{batch_name}_results_{timestamp}.csv"
                combined_results.to_csv(output_file, index=False, encoding='utf-8-sig')
                
                print(f"\n🎉 一括予測完了!")
                print(f"成功: {success_count}/{len(race_ids)}レース")
                print(f"📄 結果保存: {output_file}")
                
                # サマリー表示
                if success_count > 0:
                    print(f"\n📊 予測サマリー:")
                    print(f"総レース数: {success_count}")
                    print(f"総予測馬数: {len(combined_results)}")
                    print(f"平均勝率: {combined_results['勝率'].mean():.1f}%")
            else:
                print("❌ 予測に成功したレースがありませんでした")
            
        except Exception as e:
            self.logger.error(f"❌ 一括予測実行エラー: {e}")
            print(f"❌ 一括予測でエラーが発生しました: {e}")
    
    def _run_safe_prediction(self):
        """安全モード予測"""
        try:
            self.logger.info("🛡️ 安全モード予測開始...")
            import subprocess
            result = subprocess.run([sys.executable, "safe_live_predictor.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ 安全モード予測完了")
                print(result.stdout)
            else:
                self.logger.error(f"❌ 予測エラー: {result.stderr}")
        except Exception as e:
            self.logger.error(f"❌ 安全モード予測エラー: {e}")
    
    def _run_tf_ranking_prediction(self):
        """TensorFlow Ranking予測"""
        race_id = input("予測するレースIDを入力してください (例: 202401010101): ").strip()
        if len(race_id) != 12 or not race_id.isdigit():
            print("❌ 無効なレースID形式です")
            return
        
        try:
            print(f"🧠 TensorFlow Ranking予測実行中: {race_id}")
            
            # TensorFlow Ranking予測システムを実行
            import subprocess
            import sys
            
            cmd = [sys.executable, "tensorflow_ranking_optuna_quick_demo.py", "--race_id", race_id]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ TensorFlow Ranking予測完了")
                print("\n=== 予測結果 ===")
                print(result.stdout)
                
                # 結果ファイルが生成されているか確認
                from pathlib import Path
                results_dir = Path("tfr_optuna_quick_results")
                if results_dir.exists():
                    latest_files = sorted(results_dir.glob("*.json"), reverse=True)
                    if latest_files:
                        print(f"\n📊 詳細結果ファイル: {latest_files[0]}")
            else:
                print("❌ TensorFlow Ranking予測に失敗しました")
                if result.stderr:
                    print(f"エラー詳細: {result.stderr}")
                    
        except subprocess.TimeoutExpired:
            print("❌ 予測処理がタイムアウトしました（5分）")
        except Exception as e:
            self.logger.error(f"❌ TensorFlow Ranking予測エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_improved_model_test(self):
        """改善版モデルテスト"""
        try:
            self.logger.info("🧪 改善版モデルテスト開始...")
            
            print("\n🧪 改善版モデルテスト")
            print("=" * 50)
            print("このテストでは人気・オッズ重み調整の効果を確認します")
            print("既存の2024年データを使用して評価を行います")
            
            proceed = input("\nテストを実行しますか？ (y/N): ").strip().lower()
            if proceed not in ['y', 'yes']:
                print("❌ テストをキャンセルしました")
                return
            
            from test_improved_model import ImprovedModelTester
            
            tester = ImprovedModelTester()
            success = tester.test_on_existing_data()
            
            if success:
                self.logger.info("✅ 改善版モデルテスト完了")
                print("\n🎉 テストが正常に完了しました")
                print("📄 詳細結果は improved_model_test_results_*.csv ファイルを確認してください")
            else:
                print("❌ テストに失敗しました")
                
        except Exception as e:
            self.logger.error(f"❌ 改善版モデルテストエラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_weekly_race_prediction(self):
        """週間レース一括予測"""
        try:
            print("\n🗓️ 週間レース一括予測")
            print("=" * 50)
            print("予測したい週を選択してください")
            
            # 週選択メニュー表示
            selected_dates = self._show_week_selection_menu()
            if not selected_dates:
                print("❌ 週選択をキャンセルしました")
                return
            
            # 実行確認
            total_races = self._estimate_race_count(selected_dates)
            print(f"\n📊 予想対象: 約{total_races}レース")
            
            proceed = input("\n週間予測を実行しますか？ (y/N): ").strip().lower()
            if proceed not in ['y', 'yes']:
                print("❌ 週間予測をキャンセルしました")
                return
            
            # 週間予測実行
            self._execute_weekly_prediction(selected_dates)
            
        except Exception as e:
            self.logger.error(f"❌ 週間予測エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _show_week_selection_menu(self):
        """週選択メニュー表示"""
        try:
            from datetime import datetime, timedelta
            import calendar
            
            print("\n📅 週選択メニュー")
            print("-" * 40)
            print("1. 今週")
            print("2. 来週")
            print("3. 再来週")
            print("4. 日付を指定して週を選択")
            print("5. 週間スケジュール確認")
            print("6. カスタム日付範囲")
            print("0. 戻る")
            
            choice = input("\n選択してください (0-6): ").strip()
            
            today = datetime.now()
            
            if choice == "1":
                # 今週
                return self._get_week_dates_from_base(today, "今週")
            elif choice == "2":
                # 来週
                next_week = today + timedelta(days=7)
                return self._get_week_dates_from_base(next_week, "来週")
            elif choice == "3":
                # 再来週
                week_after_next = today + timedelta(days=14)
                return self._get_week_dates_from_base(week_after_next, "再来週")
            elif choice == "4":
                # 日付指定
                return self._get_week_dates_by_input()
            elif choice == "5":
                # スケジュール確認
                return self._show_schedule_and_select()
            elif choice == "6":
                # カスタム範囲
                return self._get_custom_date_range()
            elif choice == "0":
                return None
            else:
                print("❌ 無効な選択です")
                return self._show_week_selection_menu()
                
        except Exception as e:
            self.logger.error(f"❌ 週選択メニューエラー: {e}")
            return None
    
    def _get_week_dates_from_base(self, base_date, week_name):
        """基準日から週の土日を取得"""
        try:
            from datetime import timedelta
            
            # その週の土日を特定
            weekday = base_date.weekday()  # 0=月曜, 6=日曜
            
            # 土曜日を計算 (5 = 土曜)
            days_to_saturday = (5 - weekday) % 7
            if weekday == 6:  # 日曜日の場合は前日の土曜
                days_to_saturday = -1
            
            saturday = base_date + timedelta(days=days_to_saturday)
            sunday = saturday + timedelta(days=1)
            
            print(f"\n🏇 {week_name}の対象日程:")
            print(f"   土曜日: {saturday.strftime('%Y年%m月%d日')}")
            print(f"   日曜日: {sunday.strftime('%Y年%m月%d日')}")
            
            # 開催確認
            race_count = self._check_actual_race_schedule([saturday, sunday])
            if race_count == 0:
                print("⚠️ この週は競馬の開催がない可能性があります")
                proceed = input("それでも予測を実行しますか？ (y/N): ").strip().lower()
                if proceed not in ['y', 'yes']:
                    return None
            
            return [saturday, sunday]
            
        except Exception as e:
            self.logger.error(f"❌ 週日程取得エラー: {e}")
            return None
    
    def _get_week_dates_by_input(self):
        """日付入力による週選択"""
        try:
            from datetime import datetime
            
            date_input = input("\n基準日を入力してください (YYYY-MM-DD): ").strip()
            
            try:
                base_date = datetime.strptime(date_input, '%Y-%m-%d')
            except ValueError:
                print("❌ 無効な日付形式です (YYYY-MM-DD)")
                return None
            
            return self._get_week_dates_from_base(base_date, f"{date_input}の週")
            
        except Exception as e:
            self.logger.error(f"❌ 日付入力エラー: {e}")
            return None
    
    def _show_schedule_and_select(self):
        """スケジュール確認後に週選択"""
        try:
            from datetime import datetime, timedelta
            
            print("\n📋 今後4週間のスケジュールを確認中...")
            
            today = datetime.now()
            weeks_info = []
            
            for week_offset in range(4):
                week_start = today + timedelta(days=week_offset * 7)
                saturday, sunday = self._get_week_saturday_sunday(week_start)
                
                # 実際の開催確認
                race_count = self._check_actual_race_schedule([saturday, sunday])
                
                week_info = {
                    'offset': week_offset,
                    'saturday': saturday,
                    'sunday': sunday,
                    'race_count': race_count
                }
                weeks_info.append(week_info)
                
                week_name = ["今週", "来週", "再来週", "3週間後"][week_offset]
                status = "🏇 開催あり" if race_count > 0 else "⚠️ 開催なし?"
                
                print(f"{week_offset + 1}. {week_name}: {saturday.strftime('%m/%d')}(土)-{sunday.strftime('%m/%d')}(日) - {status}")
            
            choice = input("\n予測する週を選択してください (1-4, 0で戻る): ").strip()
            
            if choice == "0":
                return None
            
            try:
                week_index = int(choice) - 1
                if 0 <= week_index < 4:
                    selected_week = weeks_info[week_index]
                    return [selected_week['saturday'], selected_week['sunday']]
                else:
                    print("❌ 無効な選択です")
                    return None
            except ValueError:
                print("❌ 数字を入力してください")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ スケジュール確認エラー: {e}")
            return None
    
    def _get_custom_date_range(self):
        """カスタム日付範囲選択"""
        try:
            from datetime import datetime
            
            print("\n📅 カスタム日付範囲設定")
            print("平日を含む任意の期間を設定できます")
            
            start_input = input("開始日を入力してください (YYYY-MM-DD): ").strip()
            end_input = input("終了日を入力してください (YYYY-MM-DD): ").strip()
            
            try:
                start_date = datetime.strptime(start_input, '%Y-%m-%d')
                end_date = datetime.strptime(end_input, '%Y-%m-%d')
            except ValueError:
                print("❌ 無効な日付形式です")
                return None
            
            if start_date > end_date:
                print("❌ 開始日が終了日より後になっています")
                return None
            
            # 日付範囲内の全日を取得
            from datetime import timedelta
            dates = []
            current_date = start_date
            
            while current_date <= end_date:
                dates.append(current_date)
                current_date += timedelta(days=1)
            
            print(f"\n🗓️ カスタム期間: {start_date.strftime('%Y年%m月%d日')} - {end_date.strftime('%Y年%m月%d日')} ({len(dates)}日間)")
            
            # 開催確認
            race_count = self._check_actual_race_schedule(dates)
            print(f"📊 予想される開催日数: {race_count}日")
            
            return dates
            
        except Exception as e:
            self.logger.error(f"❌ カスタム範囲設定エラー: {e}")
            return None
    
    def _get_week_saturday_sunday(self, base_date):
        """基準日からその週の土日を取得"""
        from datetime import timedelta
        
        weekday = base_date.weekday()
        days_to_saturday = (5 - weekday) % 7
        if weekday == 6:
            days_to_saturday = -1
        
        saturday = base_date + timedelta(days=days_to_saturday)
        sunday = saturday + timedelta(days=1)
        
        return saturday, sunday
    
    def _check_actual_race_schedule(self, target_dates):
        """実際の開催スケジュール確認"""
        try:
            # 簡易確認（実際のレースID取得は時間がかかるため、概算で判定）
            race_count = 0
            
            for date in target_dates:
                # 土日の場合は開催の可能性が高い
                if date.weekday() in [5, 6]:  # 土曜・日曜
                    race_count += 1
                # 平日でも祝日等で開催の可能性
                elif date.weekday() in [0, 1, 2, 3, 4]:  # 月〜金
                    # 簡易的に月・金曜は開催可能性ありとして扱う
                    if date.weekday() in [0, 4]:  # 月曜・金曜
                        race_count += 0.3  # 30%の確率で加算
            
            return int(race_count)
            
        except Exception as e:
            self.logger.error(f"❌ 開催スケジュール確認エラー: {e}")
            return 0
    
    def _estimate_race_count(self, target_dates):
        """予想レース数を推定"""
        # 一般的に土日で各日6場所×12レース程度
        venues_per_day = 6
        races_per_venue = 12
        total_per_day = venues_per_day * races_per_venue
        return len(target_dates) * total_per_day
    
    def _execute_weekly_prediction(self, target_dates):
        """週間予測実行"""
        try:
            from improved_live_predictor import ImprovedLiveRacePredictor
            
            predictor = ImprovedLiveRacePredictor()
            if not predictor.load_latest_model():
                print("❌ モデル読み込みに失敗しました")
                return
            
            weekly_results = []
            total_races = 0
            successful_predictions = 0
            
            print(f"\n🚀 週間予測実行開始...")
            print("=" * 60)
            
            for date in target_dates:
                date_str = date.strftime('%Y-%m-%d')
                weekday_name = ['月', '火', '水', '木', '金', '土', '日'][date.weekday()]
                
                print(f"\n📅 {date_str} ({weekday_name}曜日) の予測")
                print("-" * 40)
                
                # その日のレースID生成
                race_ids = self._generate_race_ids_for_date(date)
                print(f"🎯 対象レース: {len(race_ids)}レース")
                
                daily_results = []
                
                for i, race_id in enumerate(race_ids, 1):
                    try:
                        print(f"予測中... ({i}/{len(race_ids)}) レース{race_id}", end=" ")
                        
                        # 予測実行
                        results, race_info = predictor.predict_race_improved(race_id)
                        
                        if not results.empty:
                            # 上位3頭を取得
                            top3 = results.head(3)
                            race_result = {
                                'race_id': race_id,
                                'date': date_str,
                                'top1': top3.iloc[0]['馬名'] if len(top3) > 0 else 'N/A',
                                'top1_odds': top3.iloc[0]['単勝オッズ'] if len(top3) > 0 else 0,
                                'top1_popularity': top3.iloc[0]['人気'] if len(top3) > 0 else 0,
                                'top3_horses': top3['馬名'].tolist(),
                                'prediction_scores': top3['予測スコア'].tolist() if len(top3) > 0 else [],
                                'distance': race_info.get('course_len', 'N/A')
                            }
                            daily_results.append(race_result)
                            successful_predictions += 1
                            print("✅")
                        else:
                            print("❌")
                        
                        total_races += 1
                        
                        # 過負荷防止
                        import time
                        time.sleep(0.5)
                        
                    except Exception as e:
                        print(f"❌ ({e})")
                        continue
                
                weekly_results.extend(daily_results)
                print(f"📊 {date_str}: {len(daily_results)}/{len(race_ids)} レース予測成功")
            
            # 結果表示とサマリー
            self._display_weekly_results(weekly_results, total_races, successful_predictions)
            
        except Exception as e:
            self.logger.error(f"❌ 週間予測実行エラー: {e}")
            print(f"❌ 予測実行中にエラーが発生しました: {e}")
    
    def _generate_race_ids_for_date(self, target_date):
        """指定日の実際のレースID取得（scraper.pyを使用）"""
        try:
            from datetime import datetime
            
            # 未来の日付の場合はフォールバック方式を使用
            if target_date > datetime.now():
                self.logger.info(f"🔮 {target_date.strftime('%Y年%m月%d日')} は未来の日付のため、フォールバック方式を使用")
                print(f"🔮 {target_date.strftime('%Y年%m月%d日')} は未来の日付のため、推定レースIDを使用")
                return self._generate_fallback_race_ids(target_date)
            
            date_str = target_date.strftime('%Y%m%d')
            self.logger.info(f"🔍 {target_date.strftime('%Y年%m月%d日')} の実際のレースIDを取得中...")
            
            # scraper.pyを使用して実際のレースIDを取得
            from core.scrapers.scraper import scrape_netkeiba_race_ids
            
            race_ids = scrape_netkeiba_race_ids(
                race_date_list=[date_str],
                debug=False,
                connection_timeout=120,
                max_retries=3
            )
            
            if race_ids:
                # レースIDの年度チェック
                target_year = target_date.year
                valid_race_ids = []
                
                for race_id in race_ids:
                    if len(race_id) >= 4:
                        try:
                            race_year = int(race_id[:4])
                            if race_year == target_year:
                                valid_race_ids.append(race_id)
                        except ValueError:
                            continue
                
                if valid_race_ids:
                    self.logger.info(f"✅ {len(valid_race_ids)}件の正しい年度のレースIDを取得しました")
                    print(f"✅ 実際の開催レース: {len(valid_race_ids)}件")
                    return valid_race_ids
                else:
                    self.logger.warning(f"⚠️ 正しい年度のレースIDが見つかりません（取得: {len(race_ids)}件）")
                    print(f"⚠️ 正しい年度のレースIDが見つかりません")
                    return self._generate_fallback_race_ids(target_date)
            else:
                self.logger.warning(f"⚠️ {target_date.strftime('%Y年%m月%d日')} の開催レースが見つかりません")
                print(f"⚠️ {target_date.strftime('%Y年%m月%d日')} は競馬の開催がない可能性があります")
                return self._generate_fallback_race_ids(target_date)
                
        except Exception as e:
            self.logger.error(f"❌ レースID取得エラー: {e}")
            print(f"❌ レースID取得エラー: {e}")
            return self._generate_fallback_race_ids(target_date)
    
    def _generate_fallback_race_ids(self, target_date):
        """フォールバック用レースID生成（従来方式）"""
        self.logger.info("📋 フォールバック方式でレースIDを生成中...")
        print("📋 フォールバック方式でレースIDを生成しています...")
        
        date_code = target_date.strftime('%Y%m%d')
        race_ids = []
        
        # 主要競馬場コード
        venue_codes = ['01', '02', '03', '04', '05', '06']  # 主要6場所に限定
        
        # 各競馬場×各レース
        for venue in venue_codes:
            for race_num in range(1, 13):  # 1R-12R
                race_id = f"{date_code}{venue}{race_num:02d}"
                race_ids.append(race_id)
        
        self.logger.info(f"📋 フォールバック: {len(race_ids)}件のレースIDを生成")
        print(f"📋 フォールバック: {len(race_ids)}件のレースIDを生成")
        return race_ids
    
    def _display_weekly_results(self, weekly_results, total_races, successful_predictions):
        """週間結果表示"""
        if not weekly_results:
            print("\n❌ 予測結果がありません")
            return
        
        print(f"\n🎉 週間予測完了!")
        print("=" * 80)
        print(f"📊 予測サマリー")
        print(f"   総レース数: {total_races}")
        print(f"   成功予測数: {successful_predictions}")
        print(f"   成功率: {successful_predictions/total_races*100:.1f}%")
        
        # 日別サマリー
        from collections import defaultdict
        daily_summary = defaultdict(list)
        
        for result in weekly_results:
            daily_summary[result['date']].append(result)
        
        print(f"\n📅 日別予測結果")
        print("-" * 80)
        
        for date, day_results in daily_summary.items():
            print(f"\n{date} - {len(day_results)}レース予測")
            print("レースID    | 本命馬           | 人気 | オッズ")
            print("-" * 60)
            
            for result in day_results[:10]:  # 上位10レースのみ表示
                race_id = result['race_id'][-4:]  # 末尾4桁
                horse_name = result['top1'][:12]  # 馬名12文字まで
                popularity = result['top1_popularity']
                odds = result['top1_odds']
                
                print(f"{race_id}     | {horse_name:15} | {popularity:2.0f}番 | {odds:5.1f}")
        
        # 結果ファイル保存
        self._save_weekly_results(weekly_results)
    
    def _save_weekly_results(self, weekly_results):
        """週間結果をファイルに保存"""
        try:
            import pandas as pd
            from datetime import datetime
            
            df = pd.DataFrame(weekly_results)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"weekly_prediction_results_{timestamp}.csv"
            
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"\n💾 詳細結果保存: {filename}")
            
            # JSONでも保存
            import json
            json_filename = f"weekly_prediction_results_{timestamp}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(weekly_results, f, ensure_ascii=False, indent=2)
            print(f"💾 JSON形式でも保存: {json_filename}")
            
        except Exception as e:
            self.logger.error(f"❌ 結果保存エラー: {e}")
            print(f"❌ 結果保存中にエラーが発生しました: {e}")
    
    def _run_comprehensive_integration(self):
        """包括的データ統合"""
        try:
            self.logger.info("📈 包括的データ統合開始...")
            from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
            
            year = input("統合する年度を入力してください (例: 2024): ").strip()
            if not year.isdigit():
                print("❌ 無効な年度です")
                return
            
            integrator = ComprehensiveDataIntegrator()
            data = integrator.generate_comprehensive_table(
                year=year,
                include_race_info=True,
                include_horse_info=True,
                include_past_performance=True
            )
            self.logger.info(f"✅ 統合完了: {len(data)}件のデータ")
            print(f"📊 統合データ件数: {len(data)}件")
        except Exception as e:
            self.logger.error(f"❌ データ統合エラー: {e}")
    
    def _run_race_data_processing(self):
        """レースデータ処理"""
        print("\n🏇 レースデータ処理")
        print("-" * 30)
        print("1. HTML→Pickle変換")
        print("2. レースデータ品質チェック")
        print("3. レース統計レポート")
        print("4. 重複データ除去")
        print("0. 戻る")
        
        choice = input("\n選択してください (0-4): ").strip()
        
        if choice == "1":
            self._convert_html_to_pickle()
        elif choice == "2":
            self._check_race_data_quality()
        elif choice == "3":
            self._generate_race_statistics()
        elif choice == "4":
            self._remove_duplicate_race_data()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def _convert_html_to_pickle(self):
        """HTML→Pickle変換"""
        try:
            year = input("処理する年度を入力してください (例: 2024): ").strip()
            if not year.isdigit() or len(year) != 4:
                print("❌ 無効な年度です")
                return
            
            print(f"📄 {year}年度のHTMLデータをPickle形式に変換中...")
            
            from core.processors.race_processor import RaceProcessor
            from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
            
            processor = RaceProcessor()
            integrator = ComprehensiveDataIntegrator()
            
            # レースデータ処理
            print("🏇 レースデータを処理中...")
            race_data = processor.process_race_info_for_year(year)
            race_results = processor.process_race_results_for_year(year)
            
            if not race_data.empty:
                race_info_path = self.output_dir / f"race_info_{year}.pickle"
                race_data.to_pickle(race_info_path)
                print(f"✅ レース情報保存: {race_info_path} ({len(race_data)}件)")
            
            if not race_results.empty:
                race_results_path = self.output_dir / f"race_results_{year}.pickle"
                race_results.to_pickle(race_results_path)
                print(f"✅ レース結果保存: {race_results_path} ({len(race_results)}件)")
            
            print(f"🎉 {year}年度データ変換完了")
            
        except Exception as e:
            self.logger.error(f"❌ HTML→Pickle変換エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _check_race_data_quality(self):
        """レースデータ品質チェック"""
        try:
            print("🔍 レースデータ品質チェック実行中...")
            
            quality_report = {
                'checked_files': 0,
                'total_records': 0,
                'missing_data': {},
                'duplicate_data': 0,
                'data_types_issues': [],
                'checked_at': datetime.now().isoformat()
            }
            
            # Pickleファイルをチェック
            pickle_files = list(self.output_dir.glob("race_*_*.pickle"))
            
            for pickle_file in pickle_files:
                try:
                    print(f"📄 チェック中: {pickle_file.name}")
                    data = pd.read_pickle(pickle_file)
                    
                    quality_report['checked_files'] += 1
                    quality_report['total_records'] += len(data)
                    
                    # 欠損データチェック
                    missing_counts = data.isnull().sum()
                    for col, count in missing_counts.items():
                        if count > 0:
                            if col not in quality_report['missing_data']:
                                quality_report['missing_data'][col] = 0
                            quality_report['missing_data'][col] += count
                    
                    # 重複データチェック
                    if 'race_id' in data.columns:
                        duplicates = data.duplicated(subset=['race_id']).sum()
                        quality_report['duplicate_data'] += duplicates
                    
                except Exception as e:
                    print(f"❌ {pickle_file.name}でエラー: {e}")
                    continue
            
            # レポート表示
            print("\n📊 データ品質チェック結果")
            print("=" * 50)
            print(f"チェックファイル数: {quality_report['checked_files']}")
            print(f"総レコード数: {quality_report['total_records']:,}")
            print(f"重複データ数: {quality_report['duplicate_data']}")
            
            if quality_report['missing_data']:
                print("\n❌ 欠損データ:")
                for col, count in sorted(quality_report['missing_data'].items(), key=lambda x: x[1], reverse=True)[:10]:
                    print(f"  {col}: {count:,}件")
            else:
                print("✅ 欠損データなし")
            
            # レポート保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"data_quality_report_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("レースデータ品質チェックレポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"チェック実行日時: {quality_report['checked_at']}\n")
                f.write(f"チェックファイル数: {quality_report['checked_files']}\n")
                f.write(f"総レコード数: {quality_report['total_records']:,}\n")
                f.write(f"重複データ数: {quality_report['duplicate_data']}\n\n")
                
                if quality_report['missing_data']:
                    f.write("欠損データ詳細:\n")
                    f.write("-" * 30 + "\n")
                    for col, count in sorted(quality_report['missing_data'].items(), key=lambda x: x[1], reverse=True):
                        f.write(f"{col}: {count:,}件\n")
            
            print(f"📄 詳細レポート保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"❌ データ品質チェックエラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _generate_race_statistics(self):
        """レース統計レポート生成"""
        try:
            print("📊 レース統計レポート生成中...")
            
            statistics = {
                'years_analyzed': [],
                'total_races': 0,
                'total_horses': 0,
                'venue_stats': {},
                'distance_stats': {},
                'generated_at': datetime.now().isoformat()
            }
            
            # 年別データを分析
            for year in range(2020, 2025):
                try:
                    results_path = f"output/race_results_{year}.pickle"
                    if os.path.exists(results_path):
                        race_results = pd.read_pickle(results_path)
                        
                        statistics['years_analyzed'].append(year)
                        
                        # レース数カウント
                        if 'race_id' in race_results.columns:
                            year_races = race_results['race_id'].nunique()
                            statistics['total_races'] += year_races
                            print(f"{year}年: {year_races:,}レース")
                        
                        # 馬数カウント
                        statistics['total_horses'] += len(race_results)
                        
                        # 場所別統計
                        if 'race_id' in race_results.columns:
                            for race_id in race_results['race_id'].unique():
                                venue_code = race_id[8:10] if len(race_id) >= 10 else 'unknown'
                                statistics['venue_stats'][venue_code] = statistics['venue_stats'].get(venue_code, 0) + 1
                        
                except Exception as e:
                    continue
            
            # 統計表示
            print(f"\n📊 レース統計サマリー")
            print("=" * 50)
            print(f"分析年度: {min(statistics['years_analyzed'])}-{max(statistics['years_analyzed'])}")
            print(f"総レース数: {statistics['total_races']:,}")
            print(f"総出走数: {statistics['total_horses']:,}")
            print(f"平均出走頭数/レース: {statistics['total_horses']/statistics['total_races']:.1f}頭")
            
            if statistics['venue_stats']:
                print(f"\n会場別レース数TOP5:")
                sorted_venues = sorted(statistics['venue_stats'].items(), key=lambda x: x[1], reverse=True)[:5]
                for venue, count in sorted_venues:
                    print(f"  場所{venue}: {count:,}レース")
            
            # レポート保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"race_statistics_report_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("レース統計レポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"生成日時: {statistics['generated_at']}\n")
                f.write(f"分析年度: {min(statistics['years_analyzed'])}-{max(statistics['years_analyzed'])}\n")
                f.write(f"総レース数: {statistics['total_races']:,}\n")
                f.write(f"総出走数: {statistics['total_horses']:,}\n")
                f.write(f"平均出走頭数/レース: {statistics['total_horses']/statistics['total_races']:.1f}頭\n\n")
                
                f.write("会場別レース数:\n")
                f.write("-" * 30 + "\n")
                for venue, count in sorted(statistics['venue_stats'].items(), key=lambda x: x[1], reverse=True):
                    f.write(f"場所{venue}: {count:,}レース\n")
            
            print(f"📄 統計レポート保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"❌ レース統計生成エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _remove_duplicate_race_data(self):
        """重複データ除去"""
        try:
            print("🧹 重複データ除去実行中...")
            
            removed_count = 0
            processed_files = 0
            
            pickle_files = list(self.output_dir.glob("race_*_*.pickle"))
            
            for pickle_file in pickle_files:
                try:
                    print(f"📄 処理中: {pickle_file.name}")
                    data = pd.read_pickle(pickle_file)
                    original_count = len(data)
                    
                    # 重複除去
                    if 'race_id' in data.columns:
                        data_cleaned = data.drop_duplicates(subset=['race_id'], keep='first')
                    else:
                        data_cleaned = data.drop_duplicates()
                    
                    duplicates_removed = original_count - len(data_cleaned)
                    
                    if duplicates_removed > 0:
                        # バックアップ作成
                        backup_path = pickle_file.with_suffix('.backup.pickle')
                        data.to_pickle(backup_path)
                        
                        # クリーンデータ保存
                        data_cleaned.to_pickle(pickle_file)
                        
                        print(f"  ✅ {duplicates_removed}件の重複データを除去")
                        removed_count += duplicates_removed
                    else:
                        print(f"  ✅ 重複データなし")
                    
                    processed_files += 1
                    
                except Exception as e:
                    print(f"❌ {pickle_file.name}でエラー: {e}")
                    continue
            
            print(f"\n🎉 重複データ除去完了")
            print(f"処理ファイル数: {processed_files}")
            print(f"除去された重複データ: {removed_count:,}件")
            
        except Exception as e:
            self.logger.error(f"❌ 重複データ除去エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_horse_data_processing(self):
        """馬データ処理"""
        print("\n🐎 馬データ処理")
        print("-" * 30)
        print("1. 馬基本情報統合")
        print("2. 過去戦績統計計算")
        print("3. 馬データ品質チェック")
        print("4. 馬パフォーマンス分析")
        print("0. 戻る")
        
        choice = input("\n選択してください (0-4): ").strip()
        
        if choice == "1":
            self._integrate_horse_basic_info()
        elif choice == "2":
            self._calculate_horse_statistics()
        elif choice == "3":
            self._check_horse_data_quality()
        elif choice == "4":
            self._analyze_horse_performance()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def _integrate_horse_basic_info(self):
        """馬基本情報統合"""
        try:
            print("🐎 馬基本情報を統合中...")
            
            from core.processors.horse_processor import HorseProcessor
            
            processor = HorseProcessor()
            
            # 統合する年度を選択
            year = input("統合する年度を入力してください (例: 2024): ").strip()
            if not year.isdigit() or len(year) != 4:
                print("❌ 無効な年度です")
                return
            
            print(f"📊 {year}年度の馬基本情報を統合中...")
            
            # 馬基本情報処理
            horse_info = processor.process_horse_info_for_year(year)
            
            if not horse_info.empty:
                # 重複除去
                horse_info_clean = horse_info.drop_duplicates(subset=['horse_id'], keep='last')
                
                # 保存
                output_path = self.output_dir / f"horse_info_{year}_integrated.pickle"
                horse_info_clean.to_pickle(output_path)
                
                print(f"✅ 馬基本情報統合完了")
                print(f"  処理前: {len(horse_info):,}件")
                print(f"  処理後: {len(horse_info_clean):,}件")
                print(f"  重複除去: {len(horse_info) - len(horse_info_clean):,}件")
                print(f"📄 保存先: {output_path}")
                
                # サマリー表示
                print(f"\n📊 馬基本情報サマリー:")
                if '生年月日' in horse_info_clean.columns:
                    age_stats = horse_info_clean['生年月日'].value_counts().head(5)
                    print(f"  生年月日分布TOP5:")
                    for birth_year, count in age_stats.items():
                        print(f"    {birth_year}: {count}頭")
                
            else:
                print("❌ 馬基本情報が取得できませんでした")
                
        except Exception as e:
            self.logger.error(f"❌ 馬基本情報統合エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _calculate_horse_statistics(self):
        """過去戦績統計計算"""
        try:
            print("📊 馬の過去戦績統計を計算中...")
            
            # 年度選択
            year = input("統計計算する年度を入力してください (例: 2024): ").strip()
            if not year.isdigit() or len(year) != 4:
                print("❌ 無効な年度です")
                return
            
            # レース結果データ読み込み
            results_path = f"output/race_results_{year}.pickle"
            if not os.path.exists(results_path):
                print(f"❌ {year}年のレース結果データが見つかりません")
                return
            
            race_results = pd.read_pickle(results_path)
            print(f"📄 {year}年レース結果: {len(race_results):,}件")
            
            # 馬別統計計算
            horse_stats = []
            
            if 'horse_id' in race_results.columns:
                unique_horses = race_results['horse_id'].unique()
                print(f"🐎 対象馬数: {len(unique_horses):,}頭")
                
                for i, horse_id in enumerate(unique_horses, 1):
                    if i % 1000 == 0:
                        print(f"  進捗: {i:,}/{len(unique_horses):,}頭")
                    
                    horse_races = race_results[race_results['horse_id'] == horse_id]
                    
                    if len(horse_races) > 0:
                        stats = {
                            'horse_id': horse_id,
                            'total_races': len(horse_races),
                            'win_count': (horse_races['着順'] == 1).sum() if '着順' in horse_races.columns else 0,
                            'place_count': (horse_races['着順'] <= 2).sum() if '着順' in horse_races.columns else 0,
                            'show_count': (horse_races['着順'] <= 3).sum() if '着順' in horse_races.columns else 0,
                        }
                        
                        # 勝率計算
                        stats['win_rate'] = stats['win_count'] / stats['total_races'] if stats['total_races'] > 0 else 0
                        stats['place_rate'] = stats['place_count'] / stats['total_races'] if stats['total_races'] > 0 else 0
                        stats['show_rate'] = stats['show_count'] / stats['total_races'] if stats['total_races'] > 0 else 0
                        
                        # 平均着順
                        if '着順' in horse_races.columns:
                            ranks = pd.to_numeric(horse_races['着順'], errors='coerce').dropna()
                            stats['avg_rank'] = ranks.mean() if len(ranks) > 0 else 0
                            stats['best_rank'] = ranks.min() if len(ranks) > 0 else 0
                        else:
                            stats['avg_rank'] = 0
                            stats['best_rank'] = 0
                        
                        # 平均人気
                        if '人気' in horse_races.columns:
                            popularity = pd.to_numeric(horse_races['人気'], errors='coerce').dropna()
                            stats['avg_popularity'] = popularity.mean() if len(popularity) > 0 else 0
                        else:
                            stats['avg_popularity'] = 0
                        
                        # 総賞金
                        if '賞金(万円)' in horse_races.columns:
                            prize_money = pd.to_numeric(horse_races['賞金(万円)'], errors='coerce').fillna(0)
                            stats['total_prize'] = prize_money.sum()
                            stats['avg_prize'] = prize_money.mean()
                        else:
                            stats['total_prize'] = 0
                            stats['avg_prize'] = 0
                        
                        horse_stats.append(stats)
                
                # 統計データ保存
                if horse_stats:
                    stats_df = pd.DataFrame(horse_stats)
                    
                    output_path = self.output_dir / f"horse_statistics_{year}.pickle"
                    stats_df.to_pickle(output_path)
                    
                    print(f"✅ 馬統計計算完了: {len(stats_df):,}頭")
                    print(f"📄 保存先: {output_path}")
                    
                    # 統計サマリー
                    print(f"\n📊 統計サマリー:")
                    print(f"  平均出走回数: {stats_df['total_races'].mean():.1f}回")
                    print(f"  平均勝率: {stats_df['win_rate'].mean():.1%}")
                    print(f"  平均連対率: {stats_df['place_rate'].mean():.1%}")
                    print(f"  平均複勝率: {stats_df['show_rate'].mean():.1%}")
                    print(f"  平均着順: {stats_df['avg_rank'].mean():.1f}着")
                    
                    # TOP5馬表示
                    top_winners = stats_df.nlargest(5, 'win_rate')
                    print(f"\n🏆 勝率TOP5:")
                    for _, horse in top_winners.iterrows():
                        print(f"  {horse['horse_id']}: {horse['win_rate']:.1%} ({horse['win_count']}/{horse['total_races']})")
                
            else:
                print("❌ horse_idカラムが見つかりません")
                
        except Exception as e:
            self.logger.error(f"❌ 馬統計計算エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _check_horse_data_quality(self):
        """馬データ品質チェック"""
        try:
            print("🔍 馬データ品質チェック実行中...")
            
            quality_report = {
                'checked_files': 0,
                'total_horses': 0,
                'missing_horse_id': 0,
                'missing_horse_name': 0,
                'invalid_birth_dates': 0,
                'checked_at': datetime.now().isoformat()
            }
            
            # 馬関連ファイルをチェック
            horse_files = list(self.output_dir.glob("horse_*_*.pickle"))
            
            for horse_file in horse_files:
                try:
                    print(f"📄 チェック中: {horse_file.name}")
                    data = pd.read_pickle(horse_file)
                    
                    quality_report['checked_files'] += 1
                    quality_report['total_horses'] += len(data)
                    
                    # horse_idチェック
                    if 'horse_id' in data.columns:
                        missing_id = data['horse_id'].isnull().sum()
                        quality_report['missing_horse_id'] += missing_id
                    
                    # 馬名チェック
                    if '馬名' in data.columns:
                        missing_name = data['馬名'].isnull().sum()
                        quality_report['missing_horse_name'] += missing_name
                    
                    # 生年月日チェック
                    if '生年月日' in data.columns:
                        try:
                            pd.to_datetime(data['生年月日'], errors='coerce')
                            invalid_dates = data['生年月日'].isnull().sum()
                            quality_report['invalid_birth_dates'] += invalid_dates
                        except:
                            pass
                    
                except Exception as e:
                    print(f"❌ {horse_file.name}でエラー: {e}")
                    continue
            
            # レポート表示
            print(f"\n📊 馬データ品質チェック結果")
            print("=" * 50)
            print(f"チェックファイル数: {quality_report['checked_files']}")
            print(f"総馬数: {quality_report['total_horses']:,}")
            print(f"horse_id欠損: {quality_report['missing_horse_id']:,}")
            print(f"馬名欠損: {quality_report['missing_horse_name']:,}")
            print(f"無効な生年月日: {quality_report['invalid_birth_dates']:,}")
            
            # データ品質スコア
            total_issues = (quality_report['missing_horse_id'] + 
                          quality_report['missing_horse_name'] + 
                          quality_report['invalid_birth_dates'])
            
            if quality_report['total_horses'] > 0:
                quality_score = max(0, 100 - (total_issues / quality_report['total_horses'] * 100))
                print(f"\n📊 データ品質スコア: {quality_score:.1f}%")
                
                if quality_score >= 90:
                    print("✅ データ品質：優秀")
                elif quality_score >= 70:
                    print("⚠️ データ品質：良好")
                elif quality_score >= 50:
                    print("❌ データ品質：要改善")
                else:
                    print("🚨 データ品質：問題あり")
            
            # レポート保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"horse_data_quality_report_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("馬データ品質チェックレポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"チェック実行日時: {quality_report['checked_at']}\n")
                f.write(f"チェックファイル数: {quality_report['checked_files']}\n")
                f.write(f"総馬数: {quality_report['total_horses']:,}\n")
                f.write(f"horse_id欠損: {quality_report['missing_horse_id']:,}\n")
                f.write(f"馬名欠損: {quality_report['missing_horse_name']:,}\n")
                f.write(f"無効な生年月日: {quality_report['invalid_birth_dates']:,}\n")
                if quality_report['total_horses'] > 0:
                    f.write(f"データ品質スコア: {quality_score:.1f}%\n")
            
            print(f"📄 詳細レポート保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 馬データ品質チェックエラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _analyze_horse_performance(self):
        """馬パフォーマンス分析"""
        try:
            print("📈 馬パフォーマンス分析実行中...")
            
            # 年度選択
            year = input("分析する年度を入力してください (例: 2024): ").strip()
            if not year.isdigit() or len(year) != 4:
                print("❌ 無効な年度です")
                return
            
            # 馬統計データ読み込み
            stats_path = f"output/horse_statistics_{year}.pickle"
            if not os.path.exists(stats_path):
                print(f"❌ {year}年の馬統計データが見つかりません")
                print("まず馬統計計算を実行してください")
                return
            
            horse_stats = pd.read_pickle(stats_path)
            print(f"📊 分析対象: {len(horse_stats):,}頭")
            
            # パフォーマンス分析
            analysis_results = {
                'total_horses': len(horse_stats),
                'analyzed_year': year,
                'performance_categories': {},
                'top_performers': {},
                'analyzed_at': datetime.now().isoformat()
            }
            
            # カテゴリ分類
            high_performers = horse_stats[horse_stats['win_rate'] >= 0.2]  # 勝率20%以上
            moderate_performers = horse_stats[(horse_stats['win_rate'] >= 0.1) & (horse_stats['win_rate'] < 0.2)]  # 勝率10-20%
            low_performers = horse_stats[horse_stats['win_rate'] < 0.1]  # 勝率10%未満
            
            analysis_results['performance_categories'] = {
                'high_performers': len(high_performers),
                'moderate_performers': len(moderate_performers),
                'low_performers': len(low_performers)
            }
            
            # TOP performers
            top_win_rate = horse_stats.nlargest(10, 'win_rate')
            top_total_prize = horse_stats.nlargest(10, 'total_prize')
            most_races = horse_stats.nlargest(10, 'total_races')
            
            analysis_results['top_performers'] = {
                'win_rate': top_win_rate[['horse_id', 'win_rate', 'total_races']].to_dict('records'),
                'total_prize': top_total_prize[['horse_id', 'total_prize', 'total_races']].to_dict('records'),
                'most_active': most_races[['horse_id', 'total_races', 'win_rate']].to_dict('records')
            }
            
            # 結果表示
            print(f"\n📊 パフォーマンス分析結果")
            print("=" * 50)
            print(f"分析年度: {year}")
            print(f"分析対象馬数: {analysis_results['total_horses']:,}頭")
            
            print(f"\n🏆 パフォーマンスカテゴリ:")
            print(f"  優秀馬 (勝率20%以上): {analysis_results['performance_categories']['high_performers']:,}頭")
            print(f"  標準馬 (勝率10-20%): {analysis_results['performance_categories']['moderate_performers']:,}頭")
            print(f"  低調馬 (勝率10%未満): {analysis_results['performance_categories']['low_performers']:,}頭")
            
            print(f"\n🥇 勝率TOP5:")
            for i, horse in enumerate(top_win_rate.head(5).itertuples(), 1):
                print(f"  {i}. {horse.horse_id}: {horse.win_rate:.1%} ({horse.win_count}/{horse.total_races})")
            
            print(f"\n💰 賞金TOP5:")
            for i, horse in enumerate(top_total_prize.head(5).itertuples(), 1):
                print(f"  {i}. {horse.horse_id}: {horse.total_prize:,.0f}万円 ({horse.total_races}戦)")
            
            # レポート保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"horse_performance_analysis_{year}_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"馬パフォーマンス分析レポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"分析実行日時: {analysis_results['analyzed_at']}\n")
                f.write(f"分析年度: {year}\n")
                f.write(f"分析対象馬数: {analysis_results['total_horses']:,}頭\n\n")
                
                f.write("パフォーマンスカテゴリ:\n")
                f.write("-" * 30 + "\n")
                f.write(f"優秀馬 (勝率20%以上): {analysis_results['performance_categories']['high_performers']:,}頭\n")
                f.write(f"標準馬 (勝率10-20%): {analysis_results['performance_categories']['moderate_performers']:,}頭\n")
                f.write(f"低調馬 (勝率10%未満): {analysis_results['performance_categories']['low_performers']:,}頭\n\n")
                
                f.write("勝率TOP10:\n")
                f.write("-" * 30 + "\n")
                for i, horse in enumerate(top_win_rate.head(10).itertuples(), 1):
                    f.write(f"{i:2d}. {horse.horse_id}: {horse.win_rate:.1%} ({horse.win_count}/{horse.total_races})\n")
                
                f.write("\n賞金TOP10:\n")
                f.write("-" * 30 + "\n")
                for i, horse in enumerate(top_total_prize.head(10).itertuples(), 1):
                    f.write(f"{i:2d}. {horse.horse_id}: {horse.total_prize:,.0f}万円 ({horse.total_races}戦)\n")
            
            print(f"📄 分析レポート保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 馬パフォーマンス分析エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_corner_analysis(self):
        """コーナー分析"""
        print("\n📊 コーナー分析")
        print("-" * 30)
        print("1. コーナー通過順位分析")
        print("2. コーナー戦術パターン分析")
        print("3. コーナー別パフォーマンス統計")
        print("4. コーナー特徴量生成")
        print("0. 戻る")
        
        choice = input("\n選択してください (0-4): ").strip()
        
        if choice == "1":
            self._analyze_corner_positions()
        elif choice == "2":
            self._analyze_corner_tactics()
        elif choice == "3":
            self._analyze_corner_performance()
        elif choice == "4":
            self._generate_corner_features()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def _analyze_corner_positions(self):
        """コーナー通過順位分析"""
        try:
            print("🏃 コーナー通過順位分析実行中...")
            
            # 年度選択
            year = input("分析する年度を入力してください (例: 2024): ").strip()
            if not year.isdigit() or len(year) != 4:
                print("❌ 無効な年度です")
                return
            
            # コーナーデータ読み込み
            corner_path = f"output/corner_features_{year}.pickle"
            if not os.path.exists(corner_path):
                print(f"❌ {year}年のコーナーデータが見つかりません")
                return
            
            corner_data = pd.read_pickle(corner_path)
            print(f"📄 コーナーデータ: {len(corner_data):,}件")
            
            # コーナー通過順位分析
            analysis_results = {
                'total_records': len(corner_data),
                'analyzed_year': year,
                'corner_stats': {},
                'position_changes': {},
                'analyzed_at': datetime.now().isoformat()
            }
            
            # 各コーナーの統計
            corners = ['1コーナー', '2コーナー', '3コーナー', '4コーナー']
            
            for corner in corners:
                if corner in corner_data.columns:
                    corner_positions = pd.to_numeric(corner_data[corner], errors='coerce').dropna()
                    
                    if len(corner_positions) > 0:
                        analysis_results['corner_stats'][corner] = {
                            'count': len(corner_positions),
                            'mean_position': corner_positions.mean(),
                            'median_position': corner_positions.median(),
                            'std_position': corner_positions.std(),
                            'min_position': corner_positions.min(),
                            'max_position': corner_positions.max()
                        }
            
            # ポジション変化分析
            if all(col in corner_data.columns for col in ['1コーナー', '4コーナー']):
                corner1_pos = pd.to_numeric(corner_data['1コーナー'], errors='coerce')
                corner4_pos = pd.to_numeric(corner_data['4コーナー'], errors='coerce')
                
                position_change = corner4_pos - corner1_pos
                valid_changes = position_change.dropna()
                
                if len(valid_changes) > 0:
                    analysis_results['position_changes'] = {
                        'improved_count': (valid_changes < 0).sum(),  # 順位上昇
                        'declined_count': (valid_changes > 0).sum(),  # 順位下降
                        'unchanged_count': (valid_changes == 0).sum(),  # 順位変わらず
                        'avg_change': valid_changes.mean(),
                        'max_improvement': valid_changes.min(),  # 最大上昇（負の値）
                        'max_decline': valid_changes.max()  # 最大下降（正の値）
                    }
            
            # 結果表示
            print(f"\n📊 コーナー通過順位分析結果")
            print("=" * 50)
            print(f"分析年度: {year}")
            print(f"分析対象: {analysis_results['total_records']:,}件")
            
            print(f"\n🏃 各コーナー統計:")
            for corner, stats in analysis_results['corner_stats'].items():
                print(f"  {corner}:")
                print(f"    データ数: {stats['count']:,}件")
                print(f"    平均順位: {stats['mean_position']:.1f}位")
                print(f"    中央値: {stats['median_position']:.1f}位")
                print(f"    標準偏差: {stats['std_position']:.1f}")
            
            if analysis_results['position_changes']:
                print(f"\n📈 1→4コーナー順位変化:")
                pc = analysis_results['position_changes']
                print(f"  順位上昇: {pc['improved_count']:,}件")
                print(f"  順位下降: {pc['declined_count']:,}件")
                print(f"  順位維持: {pc['unchanged_count']:,}件")
                print(f"  平均変化: {pc['avg_change']:+.1f}位")
                print(f"  最大上昇: {abs(pc['max_improvement']):.0f}位")
                print(f"  最大下降: {pc['max_decline']:.0f}位")
            
            # レポート保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"corner_position_analysis_{year}_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("コーナー通過順位分析レポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"分析実行日時: {analysis_results['analyzed_at']}\n")
                f.write(f"分析年度: {year}\n")
                f.write(f"分析対象: {analysis_results['total_records']:,}件\n\n")
                
                f.write("各コーナー統計:\n")
                f.write("-" * 30 + "\n")
                for corner, stats in analysis_results['corner_stats'].items():
                    f.write(f"{corner}:\n")
                    f.write(f"  データ数: {stats['count']:,}件\n")
                    f.write(f"  平均順位: {stats['mean_position']:.1f}位\n")
                    f.write(f"  中央値: {stats['median_position']:.1f}位\n")
                    f.write(f"  標準偏差: {stats['std_position']:.1f}\n")
                    f.write(f"  最高位: {stats['min_position']:.0f}位\n")
                    f.write(f"  最低位: {stats['max_position']:.0f}位\n\n")
                
                if analysis_results['position_changes']:
                    f.write("1→4コーナー順位変化:\n")
                    f.write("-" * 30 + "\n")
                    pc = analysis_results['position_changes']
                    f.write(f"順位上昇: {pc['improved_count']:,}件\n")
                    f.write(f"順位下降: {pc['declined_count']:,}件\n")
                    f.write(f"順位維持: {pc['unchanged_count']:,}件\n")
                    f.write(f"平均変化: {pc['avg_change']:+.1f}位\n")
                    f.write(f"最大上昇: {abs(pc['max_improvement']):.0f}位\n")
                    f.write(f"最大下降: {pc['max_decline']:.0f}位\n")
            
            print(f"📄 分析レポート保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"❌ コーナー順位分析エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _analyze_corner_tactics(self):
        """コーナー戦術パターン分析"""
        try:
            print("📋 コーナー戦術パターン分析実行中...")
            
            # 年度選択
            year = input("分析する年度を入力してください (例: 2024): ").strip()
            if not year.isdigit() or len(year) != 4:
                print("❌ 無効な年度です")
                return
            
            # コーナーデータ読み込み
            corner_path = f"output/corner_features_{year}.pickle"
            if not os.path.exists(corner_path):
                print(f"❌ {year}年のコーナーデータが見つかりません")
                return
            
            corner_data = pd.read_pickle(corner_path)
            print(f"📄 コーナーデータ: {len(corner_data):,}件")
            
            # 戦術パターン分析
            tactics_analysis = {
                'total_records': len(corner_data),
                'analyzed_year': year,
                'tactical_patterns': {},
                'analyzed_at': datetime.now().isoformat()
            }
            
            # 戦術パターン分類
            if all(col in corner_data.columns for col in ['1コーナー', '2コーナー', '3コーナー', '4コーナー']):
                corner1 = pd.to_numeric(corner_data['1コーナー'], errors='coerce')
                corner2 = pd.to_numeric(corner_data['2コーナー'], errors='coerce')
                corner3 = pd.to_numeric(corner_data['3コーナー'], errors='coerce')
                corner4 = pd.to_numeric(corner_data['4コーナー'], errors='coerce')
                
                valid_data = pd.DataFrame({
                    '1コーナー': corner1,
                    '2コーナー': corner2,
                    '3コーナー': corner3,
                    '4コーナー': corner4
                }).dropna()
                
                tactics = []
                
                for _, row in valid_data.iterrows():
                    c1, c2, c3, c4 = row['1コーナー'], row['2コーナー'], row['3コーナー'], row['4コーナー']
                    
                    # 戦術パターン分類
                    if c1 <= 3:  # 先行
                        if c4 <= 3:
                            tactic = "先行→好位維持"
                        elif c4 <= 6:
                            tactic = "先行→中団"
                        else:
                            tactic = "先行→後退"
                    elif c1 <= 8:  # 中団
                        if c4 <= 3:
                            tactic = "中団→上昇"
                        elif c4 <= 8:
                            tactic = "中団→中団"
                        else:
                            tactic = "中団→後退"
                    else:  # 後方
                        if c4 <= 3:
                            tactic = "後方→大駆け"
                        elif c4 <= 8:
                            tactic = "後方→上昇"
                        else:
                            tactic = "後方→後方"
                    
                    tactics.append(tactic)
                
                # パターン集計
                from collections import Counter
                pattern_counts = Counter(tactics)
                
                tactics_analysis['tactical_patterns'] = {
                    pattern: {
                        'count': count,
                        'percentage': count / len(tactics) * 100
                    }
                    for pattern, count in pattern_counts.items()
                }
            
            # 結果表示
            print(f"\n📋 コーナー戦術パターン分析結果")
            print("=" * 50)
            print(f"分析年度: {year}")
            print(f"分析対象: {tactics_analysis['total_records']:,}件")
            
            if tactics_analysis['tactical_patterns']:
                print(f"\n🏇 戦術パターン分布:")
                sorted_patterns = sorted(tactics_analysis['tactical_patterns'].items(), 
                                       key=lambda x: x[1]['count'], reverse=True)
                
                for pattern, stats in sorted_patterns:
                    print(f"  {pattern}: {stats['count']:,}件 ({stats['percentage']:.1f}%)")
            
            # レポート保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"corner_tactics_analysis_{year}_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("コーナー戦術パターン分析レポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"分析実行日時: {tactics_analysis['analyzed_at']}\n")
                f.write(f"分析年度: {year}\n")
                f.write(f"分析対象: {tactics_analysis['total_records']:,}件\n\n")
                
                if tactics_analysis['tactical_patterns']:
                    f.write("戦術パターン分布:\n")
                    f.write("-" * 30 + "\n")
                    sorted_patterns = sorted(tactics_analysis['tactical_patterns'].items(), 
                                           key=lambda x: x[1]['count'], reverse=True)
                    
                    for pattern, stats in sorted_patterns:
                        f.write(f"{pattern}: {stats['count']:,}件 ({stats['percentage']:.1f}%)\n")
            
            print(f"📄 分析レポート保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"❌ コーナー戦術分析エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _analyze_corner_performance(self):
        """コーナー別パフォーマンス統計"""
        try:
            print("📈 コーナー別パフォーマンス統計実行中...")
            
            # 年度選択
            year = input("分析する年度を入力してください (例: 2024): ").strip()
            if not year.isdigit() or len(year) != 4:
                print("❌ 無効な年度です")
                return
            
            # データ読み込み
            corner_path = f"output/corner_features_{year}.pickle"
            results_path = f"output/race_results_{year}.pickle"
            
            if not os.path.exists(corner_path) or not os.path.exists(results_path):
                print(f"❌ {year}年の必要なデータが見つかりません")
                return
            
            corner_data = pd.read_pickle(corner_path)
            race_results = pd.read_pickle(results_path)
            
            print(f"📄 コーナーデータ: {len(corner_data):,}件")
            print(f"📄 レース結果: {len(race_results):,}件")
            
            # データ結合
            if 'race_id' in corner_data.columns and 'race_id' in race_results.columns:
                merged_data = pd.merge(corner_data, race_results[['race_id', '着順', 'horse_id']], 
                                     on=['race_id'], how='inner')
                
                print(f"📊 結合データ: {len(merged_data):,}件")
                
                # コーナー別パフォーマンス分析
                performance_stats = {
                    'total_records': len(merged_data),
                    'analyzed_year': year,
                    'corner_performance': {},
                    'analyzed_at': datetime.now().isoformat()
                }
                
                corners = ['1コーナー', '2コーナー', '3コーナー', '4コーナー']
                
                for corner in corners:
                    if corner in merged_data.columns:
                        # コーナー順位別の着順統計
                        corner_pos = pd.to_numeric(merged_data[corner], errors='coerce')
                        final_rank = pd.to_numeric(merged_data['着順'], errors='coerce')
                        
                        valid_mask = corner_pos.notna() & final_rank.notna()
                        valid_corner = corner_pos[valid_mask]
                        valid_rank = final_rank[valid_mask]
                        
                        if len(valid_corner) > 0:
                            # コーナー順位グループ別分析
                            position_groups = {
                                '先頭群(1-3位)': (valid_corner <= 3),
                                '中団(4-8位)': (valid_corner >= 4) & (valid_corner <= 8),
                                '後方(9位以下)': (valid_corner >= 9)
                            }
                            
                            corner_stats = {}
                            
                            for group_name, mask in position_groups.items():
                                if mask.sum() > 0:
                                    group_ranks = valid_rank[mask]
                                    
                                    corner_stats[group_name] = {
                                        'count': len(group_ranks),
                                        'win_rate': (group_ranks == 1).mean(),
                                        'place_rate': (group_ranks <= 2).mean(),
                                        'show_rate': (group_ranks <= 3).mean(),
                                        'avg_rank': group_ranks.mean(),
                                        'top5_rate': (group_ranks <= 5).mean()
                                    }
                            
                            performance_stats['corner_performance'][corner] = corner_stats
                
                # 結果表示
                print(f"\n📈 コーナー別パフォーマンス統計結果")
                print("=" * 70)
                print(f"分析年度: {year}")
                print(f"分析対象: {performance_stats['total_records']:,}件")
                
                for corner, stats in performance_stats['corner_performance'].items():
                    print(f"\n🏃 {corner}:")
                    for group, metrics in stats.items():
                        print(f"  {group} (n={metrics['count']:,}):")
                        print(f"    勝率: {metrics['win_rate']:.1%}")
                        print(f"    連対率: {metrics['place_rate']:.1%}")
                        print(f"    複勝率: {metrics['show_rate']:.1%}")
                        print(f"    平均着順: {metrics['avg_rank']:.1f}着")
                        print(f"    5着以内率: {metrics['top5_rate']:.1%}")
                
                # レポート保存
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                report_path = self.output_dir / f"corner_performance_stats_{year}_{timestamp}.txt"
                
                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write("コーナー別パフォーマンス統計レポート\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"分析実行日時: {performance_stats['analyzed_at']}\n")
                    f.write(f"分析年度: {year}\n")
                    f.write(f"分析対象: {performance_stats['total_records']:,}件\n\n")
                    
                    for corner, stats in performance_stats['corner_performance'].items():
                        f.write(f"{corner}:\n")
                        f.write("-" * 30 + "\n")
                        for group, metrics in stats.items():
                            f.write(f"{group} (n={metrics['count']:,}):\n")
                            f.write(f"  勝率: {metrics['win_rate']:.1%}\n")
                            f.write(f"  連対率: {metrics['place_rate']:.1%}\n")
                            f.write(f"  複勝率: {metrics['show_rate']:.1%}\n")
                            f.write(f"  平均着順: {metrics['avg_rank']:.1f}着\n")
                            f.write(f"  5着以内率: {metrics['top5_rate']:.1%}\n\n")
                
                print(f"📄 統計レポート保存: {report_path}")
            
            else:
                print("❌ データ結合に必要なrace_idが見つかりません")
            
        except Exception as e:
            self.logger.error(f"❌ コーナーパフォーマンス分析エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _generate_corner_features(self):
        """コーナー特徴量生成"""
        try:
            print("🔧 コーナー特徴量生成実行中...")
            
            # 年度選択
            year = input("特徴量生成する年度を入力してください (例: 2024): ").strip()
            if not year.isdigit() or len(year) != 4:
                print("❌ 無効な年度です")
                return
            
            from core.processors.corner_analyzer import CornerAnalyzer
            
            analyzer = CornerAnalyzer()
            
            print(f"🔧 {year}年度のコーナー特徴量を生成中...")
            
            # コーナー特徴量生成
            corner_features = analyzer.generate_corner_features_for_year(year)
            
            if not corner_features.empty:
                # 生成された特徴量情報
                feature_columns = [col for col in corner_features.columns if 'corner' in col.lower()]
                
                print(f"✅ コーナー特徴量生成完了")
                print(f"  データ件数: {len(corner_features):,}件")
                print(f"  特徴量数: {len(feature_columns)}個")
                
                # 特徴量一覧表示
                print(f"\n🔧 生成された特徴量:")
                for i, feature in enumerate(feature_columns[:10], 1):  # 最初の10個を表示
                    print(f"  {i:2d}. {feature}")
                
                if len(feature_columns) > 10:
                    print(f"  ... 他{len(feature_columns) - 10}個")
                
                # 特徴量統計
                print(f"\n📊 特徴量統計サマリー:")
                for feature in feature_columns[:5]:  # 最初の5個の統計
                    if feature in corner_features.columns:
                        values = pd.to_numeric(corner_features[feature], errors='coerce').dropna()
                        if len(values) > 0:
                            print(f"  {feature}:")
                            print(f"    平均: {values.mean():.2f}")
                            print(f"    標準偏差: {values.std():.2f}")
                            print(f"    最小値: {values.min():.2f}")
                            print(f"    最大値: {values.max():.2f}")
                
                # 保存
                output_path = self.output_dir / f"corner_features_enhanced_{year}.pickle"
                corner_features.to_pickle(output_path)
                print(f"📄 コーナー特徴量保存: {output_path}")
                
            else:
                print("❌ コーナー特徴量の生成に失敗しました")
            
        except Exception as e:
            self.logger.error(f"❌ コーナー特徴量生成エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_data_quality_check(self):
        """データ品質チェック"""
        print("\n✅ データ品質チェック")
        print("-" * 30)
        print("1. 全データファイル品質チェック")
        print("2. 年度別データ整合性チェック")
        print("3. 欠損値分析")
        print("4. データ型検証")
        print("5. 総合品質レポート生成")
        print("0. 戻る")
        
        choice = input("\n選択してください (0-5): ").strip()
        
        if choice == "1":
            self._check_all_data_quality()
        elif choice == "2":
            self._check_yearly_data_consistency()
        elif choice == "3":
            self._analyze_missing_values()
        elif choice == "4":
            self._validate_data_types()
        elif choice == "5":
            self._generate_comprehensive_quality_report()
        elif choice == "0":
            return
        else:
            print("❌ 無効な選択です")
    
    def _check_all_data_quality(self):
        """全データファイル品質チェック"""
        try:
            print("🔍 全データファイル品質チェック実行中...")
            
            quality_summary = {
                'total_files': 0,
                'pickle_files': 0,
                'csv_files': 0,
                'total_size_mb': 0,
                'file_issues': [],
                'checked_at': datetime.now().isoformat()
            }
            
            # 全ファイルをチェック
            data_files = list(self.output_dir.glob("*"))
            data_files.extend(list(self.models_dir.glob("*")))
            
            for file_path in data_files:
                if file_path.is_file():
                    quality_summary['total_files'] += 1
                    file_size_mb = file_path.stat().st_size / (1024 * 1024)
                    quality_summary['total_size_mb'] += file_size_mb
                    
                    # ファイル種別チェック
                    if file_path.suffix == '.pickle':
                        quality_summary['pickle_files'] += 1
                        # Pickleファイルの読み込みテスト
                        try:
                            pd.read_pickle(file_path)
                        except Exception as e:
                            quality_summary['file_issues'].append(f"{file_path.name}: {e}")
                    elif file_path.suffix == '.csv':
                        quality_summary['csv_files'] += 1
                        # CSVファイルの読み込みテスト
                        try:
                            pd.read_csv(file_path)
                        except Exception as e:
                            quality_summary['file_issues'].append(f"{file_path.name}: {e}")
            
            # 結果表示
            print(f"\n📊 全データファイル品質チェック結果")
            print("=" * 50)
            print(f"総ファイル数: {quality_summary['total_files']:,}")
            print(f"Pickleファイル: {quality_summary['pickle_files']:,}")
            print(f"CSVファイル: {quality_summary['csv_files']:,}")
            print(f"総サイズ: {quality_summary['total_size_mb']:.1f} MB")
            
            if quality_summary['file_issues']:
                print(f"\n❌ ファイル問題 ({len(quality_summary['file_issues'])}件):")
                for issue in quality_summary['file_issues'][:10]:  # 最初の10件
                    print(f"  {issue}")
                if len(quality_summary['file_issues']) > 10:
                    print(f"  ... 他{len(quality_summary['file_issues']) - 10}件")
            else:
                print("✅ 全ファイル正常")
            
            # レポート保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"all_data_quality_check_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("全データファイル品質チェックレポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"チェック実行日時: {quality_summary['checked_at']}\n")
                f.write(f"総ファイル数: {quality_summary['total_files']:,}\n")
                f.write(f"Pickleファイル: {quality_summary['pickle_files']:,}\n")
                f.write(f"CSVファイル: {quality_summary['csv_files']:,}\n")
                f.write(f"総サイズ: {quality_summary['total_size_mb']:.1f} MB\n\n")
                
                if quality_summary['file_issues']:
                    f.write("ファイル問題:\n")
                    f.write("-" * 30 + "\n")
                    for issue in quality_summary['file_issues']:
                        f.write(f"{issue}\n")
            
            print(f"📄 詳細レポート保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 全データファイル品質チェックエラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _check_yearly_data_consistency(self):
        """年度別データ整合性チェック"""
        try:
            print("📅 年度別データ整合性チェック実行中...")
            
            consistency_report = {
                'years_checked': [],
                'data_consistency': {},
                'cross_year_issues': [],
                'checked_at': datetime.now().isoformat()
            }
            
            # 年度別データをチェック
            for year in range(2020, 2025):
                year_files = {
                    'race_info': f"output/race_info_{year}.pickle",
                    'race_results': f"output/race_results_{year}.pickle",
                    'horse_info': f"output/horse_info_{year}.pickle",
                    'corner_features': f"output/corner_features_{year}.pickle"
                }
                
                year_status = {'year': year, 'files_found': 0, 'total_records': 0, 'issues': []}
                
                for file_type, file_path in year_files.items():
                    if os.path.exists(file_path):
                        try:
                            data = pd.read_pickle(file_path)
                            year_status['files_found'] += 1
                            year_status['total_records'] += len(data)
                            
                            # race_idの整合性チェック
                            if 'race_id' in data.columns:
                                race_ids = data['race_id'].astype(str)
                                expected_year_prefix = str(year)
                                wrong_year_ids = race_ids[~race_ids.str.startswith(expected_year_prefix)]
                                
                                if len(wrong_year_ids) > 0:
                                    year_status['issues'].append(
                                        f"{file_type}: {len(wrong_year_ids)}件の年度不整合race_id"
                                    )
                            
                        except Exception as e:
                            year_status['issues'].append(f"{file_type}: ファイル読み込みエラー - {e}")
                
                if year_status['files_found'] > 0:
                    consistency_report['years_checked'].append(year)
                    consistency_report['data_consistency'][year] = year_status
            
            # 結果表示
            print(f"\n📅 年度別データ整合性チェック結果")
            print("=" * 60)
            print(f"チェック年度数: {len(consistency_report['years_checked'])}")
            
            for year, status in consistency_report['data_consistency'].items():
                print(f"\n{year}年:")
                print(f"  ファイル数: {status['files_found']}/4")
                print(f"  総レコード数: {status['total_records']:,}")
                
                if status['issues']:
                    print(f"  ❌ 問題 ({len(status['issues'])}件):")
                    for issue in status['issues']:
                        print(f"    - {issue}")
                else:
                    print("  ✅ 整合性問題なし")
            
            # レポート保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"yearly_consistency_check_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("年度別データ整合性チェックレポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"チェック実行日時: {consistency_report['checked_at']}\n")
                f.write(f"チェック年度数: {len(consistency_report['years_checked'])}\n\n")
                
                for year, status in consistency_report['data_consistency'].items():
                    f.write(f"{year}年:\n")
                    f.write(f"  ファイル数: {status['files_found']}/4\n")
                    f.write(f"  総レコード数: {status['total_records']:,}\n")
                    
                    if status['issues']:
                        f.write(f"  問題 ({len(status['issues'])}件):\n")
                        for issue in status['issues']:
                            f.write(f"    - {issue}\n")
                    else:
                        f.write("  整合性問題なし\n")
                    f.write("\n")
            
            print(f"📄 詳細レポート保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 年度別整合性チェックエラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _analyze_missing_values(self):
        """欠損値分析"""
        try:
            print("🕳️ 欠損値分析実行中...")
            
            missing_analysis = {
                'files_analyzed': 0,
                'total_records': 0,
                'columns_with_missing': {},
                'missing_patterns': [],
                'analyzed_at': datetime.now().isoformat()
            }
            
            # Pickleファイルの欠損値分析
            pickle_files = list(self.output_dir.glob("*.pickle"))
            
            for pickle_file in pickle_files:
                try:
                    print(f"📄 分析中: {pickle_file.name}")
                    data = pd.read_pickle(pickle_file)
                    
                    missing_analysis['files_analyzed'] += 1
                    missing_analysis['total_records'] += len(data)
                    
                    # 欠損値分析
                    missing_counts = data.isnull().sum()
                    missing_percentages = (missing_counts / len(data)) * 100
                    
                    for col, count in missing_counts.items():
                        if count > 0:
                            key = f"{pickle_file.name}:{col}"
                            missing_analysis['columns_with_missing'][key] = {
                                'count': count,
                                'percentage': missing_percentages[col],
                                'total_rows': len(data)
                            }
                
                except Exception as e:
                    print(f"❌ {pickle_file.name}でエラー: {e}")
                    continue
            
            # 結果表示
            print(f"\n🕳️ 欠損値分析結果")
            print("=" * 60)
            print(f"分析ファイル数: {missing_analysis['files_analyzed']}")
            print(f"総レコード数: {missing_analysis['total_records']:,}")
            print(f"欠損値のあるカラム: {len(missing_analysis['columns_with_missing'])}個")
            
            if missing_analysis['columns_with_missing']:
                print(f"\n🔍 欠損値TOP10:")
                sorted_missing = sorted(missing_analysis['columns_with_missing'].items(), 
                                      key=lambda x: x[1]['count'], reverse=True)[:10]
                
                for col_key, stats in sorted_missing:
                    print(f"  {col_key}:")
                    print(f"    欠損数: {stats['count']:,}/{stats['total_rows']:,} ({stats['percentage']:.1f}%)")
            else:
                print("✅ 欠損値なし")
            
            # レポート保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"missing_values_analysis_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("欠損値分析レポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"分析実行日時: {missing_analysis['analyzed_at']}\n")
                f.write(f"分析ファイル数: {missing_analysis['files_analyzed']}\n")
                f.write(f"総レコード数: {missing_analysis['total_records']:,}\n")
                f.write(f"欠損値のあるカラム: {len(missing_analysis['columns_with_missing'])}個\n\n")
                
                if missing_analysis['columns_with_missing']:
                    f.write("欠損値詳細:\n")
                    f.write("-" * 30 + "\n")
                    sorted_missing = sorted(missing_analysis['columns_with_missing'].items(), 
                                          key=lambda x: x[1]['count'], reverse=True)
                    
                    for col_key, stats in sorted_missing:
                        f.write(f"{col_key}:\n")
                        f.write(f"  欠損数: {stats['count']:,}/{stats['total_rows']:,} ({stats['percentage']:.1f}%)\n\n")
            
            print(f"📄 詳細レポート保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 欠損値分析エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _validate_data_types(self):
        """データ型検証"""
        try:
            print("🔢 データ型検証実行中...")
            
            type_validation = {
                'files_validated': 0,
                'type_issues': [],
                'column_types': {},
                'validated_at': datetime.now().isoformat()
            }
            
            expected_types = {
                'race_id': 'string',
                'horse_id': 'string',
                '馬名': 'string',
                '着順': 'numeric',
                '人気': 'numeric',
                '斤量': 'numeric',
                '単勝': 'numeric',
                '枠番': 'numeric',
                '馬番': 'numeric',
                '賞金(万円)': 'numeric'
            }
            
            # Pickleファイルのデータ型検証
            pickle_files = list(self.output_dir.glob("*.pickle"))
            
            for pickle_file in pickle_files:
                try:
                    print(f"📄 検証中: {pickle_file.name}")
                    data = pd.read_pickle(pickle_file)
                    
                    type_validation['files_validated'] += 1
                    
                    for col in data.columns:
                        if col in expected_types:
                            expected_type = expected_types[col]
                            actual_dtype = str(data[col].dtype)
                            
                            # 型検証
                            if expected_type == 'numeric':
                                try:
                                    pd.to_numeric(data[col], errors='coerce')
                                except:
                                    type_validation['type_issues'].append(
                                        f"{pickle_file.name}:{col} - 数値型変換不可"
                                    )
                            elif expected_type == 'string':
                                if not data[col].dtype == 'object':
                                    type_validation['type_issues'].append(
                                        f"{pickle_file.name}:{col} - 文字列型期待、実際: {actual_dtype}"
                                    )
                            
                            # 型情報記録
                            key = f"{pickle_file.name}:{col}"
                            type_validation['column_types'][key] = {
                                'expected': expected_type,
                                'actual': actual_dtype,
                                'sample_values': data[col].dropna().head(3).tolist()
                            }
                
                except Exception as e:
                    print(f"❌ {pickle_file.name}でエラー: {e}")
                    continue
            
            # 結果表示
            print(f"\n🔢 データ型検証結果")
            print("=" * 50)
            print(f"検証ファイル数: {type_validation['files_validated']}")
            print(f"検証カラム数: {len(type_validation['column_types'])}")
            print(f"型問題数: {len(type_validation['type_issues'])}")
            
            if type_validation['type_issues']:
                print(f"\n❌ データ型問題:")
                for issue in type_validation['type_issues'][:10]:  # 最初の10件
                    print(f"  {issue}")
                if len(type_validation['type_issues']) > 10:
                    print(f"  ... 他{len(type_validation['type_issues']) - 10}件")
            else:
                print("✅ データ型問題なし")
            
            # レポート保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"data_type_validation_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("データ型検証レポート\n")
                f.write("=" * 50 + "\n")
                f.write(f"検証実行日時: {type_validation['validated_at']}\n")
                f.write(f"検証ファイル数: {type_validation['files_validated']}\n")
                f.write(f"検証カラム数: {len(type_validation['column_types'])}\n")
                f.write(f"型問題数: {len(type_validation['type_issues'])}\n\n")
                
                if type_validation['type_issues']:
                    f.write("データ型問題:\n")
                    f.write("-" * 30 + "\n")
                    for issue in type_validation['type_issues']:
                        f.write(f"{issue}\n")
                
                f.write("\nカラム型情報:\n")
                f.write("-" * 30 + "\n")
                for col_key, type_info in type_validation['column_types'].items():
                    f.write(f"{col_key}:\n")
                    f.write(f"  期待型: {type_info['expected']}\n")
                    f.write(f"  実際型: {type_info['actual']}\n")
                    f.write(f"  サンプル: {type_info['sample_values']}\n\n")
            
            print(f"📄 詳細レポート保存: {report_path}")
            
        except Exception as e:
            self.logger.error(f"❌ データ型検証エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _generate_comprehensive_quality_report(self):
        """総合品質レポート生成"""
        try:
            print("📊 総合品質レポート生成中...")
            
            # 各種チェックを実行して統合レポートを作成
            print("1/4 全ファイルチェック実行中...")
            self._check_all_data_quality()
            
            print("2/4 年度別整合性チェック実行中...")
            self._check_yearly_data_consistency()
            
            print("3/4 欠損値分析実行中...")
            self._analyze_missing_values()
            
            print("4/4 データ型検証実行中...")
            self._validate_data_types()
            
            # 統合レポート作成
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            comprehensive_report_path = self.output_dir / f"comprehensive_data_quality_report_{timestamp}.txt"
            
            with open(comprehensive_report_path, 'w', encoding='utf-8') as f:
                f.write("競馬AIシステム 総合データ品質レポート\n")
                f.write("=" * 60 + "\n")
                f.write(f"レポート生成日時: {datetime.now().isoformat()}\n")
                f.write(f"システムバージョン: 改善版競馬AIシステム\n\n")
                
                f.write("📋 実行済みチェック項目:\n")
                f.write("-" * 30 + "\n")
                f.write("✅ 全データファイル品質チェック\n")
                f.write("✅ 年度別データ整合性チェック\n")
                f.write("✅ 欠損値分析\n")
                f.write("✅ データ型検証\n\n")
                
                f.write("📄 詳細レポートファイル:\n")
                f.write("-" * 30 + "\n")
                f.write(f"- all_data_quality_check_{timestamp}.txt\n")
                f.write(f"- yearly_consistency_check_{timestamp}.txt\n")
                f.write(f"- missing_values_analysis_{timestamp}.txt\n")
                f.write(f"- data_type_validation_{timestamp}.txt\n\n")
                
                f.write("🎯 推奨アクション:\n")
                f.write("-" * 30 + "\n")
                f.write("1. 各詳細レポートを確認\n")
                f.write("2. 品質問題があれば対策実施\n")
                f.write("3. 定期的な品質チェック継続\n")
                f.write("4. データ収集プロセスの改善検討\n")
            
            print(f"\n🎉 総合品質レポート生成完了")
            print(f"📄 統合レポート: {comprehensive_report_path}")
            print(f"💡 各詳細レポートも併せて確認してください")
            
        except Exception as e:
            self.logger.error(f"❌ 総合品質レポート生成エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_feature_calculation(self):
        """特徴量計算実行"""
        try:
            self.logger.info("🔧 特徴量計算開始...")
            from core.features.manager import FeatureEngineeringManager
            
            manager = FeatureEngineeringManager()
            summary = manager.get_feature_summary()
            print(f"📊 利用可能特徴量: {summary}")
            self.logger.info("✅ 特徴量計算完了")
        except Exception as e:
            self.logger.error(f"❌ 特徴量計算エラー: {e}")
    
    def _show_feature_config(self):
        """特徴量設定表示"""
        try:
            config_file = self.project_root / "core" / "features" / "config.yaml"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                print("\n📋 特徴量設定:")
                print("-" * 40)
                print(content[:1000] + "..." if len(content) > 1000 else content)
            else:
                print("❌ 特徴量設定ファイルが見つかりません")
        except Exception as e:
            self.logger.error(f"❌ 設定表示エラー: {e}")
    
    def _add_custom_feature(self):
        """カスタム特徴量追加"""
        try:
            print("➕ カスタム特徴量追加")
            print("=" * 40)
            
            print("カスタム特徴量の種類:")
            print("1. 馬の年齢別特徴量")
            print("2. 距離適性特徴量") 
            print("3. 騎手・調教師組み合わせ特徴量")
            print("4. 季節・月別パフォーマンス特徴量")
            print("5. オッズ・人気関連特徴量")
            
            feature_type = input("\n追加する特徴量の種類を選択してください (1-5): ").strip()
            
            if feature_type == "1":
                self._add_age_features()
            elif feature_type == "2":
                self._add_distance_features()
            elif feature_type == "3":
                self._add_combination_features()
            elif feature_type == "4":
                self._add_seasonal_features()
            elif feature_type == "5":
                self._add_odds_features()
            else:
                print("❌ 無効な選択です")
                
        except Exception as e:
            self.logger.error(f"❌ カスタム特徴量追加エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _add_age_features(self):
        """年齢関連特徴量追加"""
        print("🎂 年齢関連特徴量を追加中...")
        
        # サンプル実装
        custom_features = {
            'age_category': "馬の年齢カテゴリ (2歳、3歳、古馬)",
            'age_distance_interaction': "年齢×距離の交互作用特徴量",
            'age_weight_ratio': "年齢に対する斤量の比率",
            'prime_age_flag': "全盛期年齢フラグ (4-6歳)"
        }
        
        print("追加予定の年齢関連特徴量:")
        for feature, description in custom_features.items():
            print(f"  - {feature}: {description}")
        
        print("✅ 年齢関連特徴量の設計完了")
        print("💡 実装は core/features/calculators.py に追加してください")
    
    def _add_distance_features(self):
        """距離適性特徴量追加"""
        print("📏 距離適性特徴量を追加中...")
        
        custom_features = {
            'distance_experience': "該当距離での経験回数",
            'distance_win_rate': "距離別勝率",
            'distance_category_performance': "距離カテゴリ別成績 (短距離、マイル、中距離、長距離)",
            'distance_change_impact': "前走からの距離変更の影響"
        }
        
        print("追加予定の距離適性特徴量:")
        for feature, description in custom_features.items():
            print(f"  - {feature}: {description}")
        
        print("✅ 距離適性特徴量の設計完了")
        print("💡 実装は core/features/calculators.py に追加してください")
    
    def _add_combination_features(self):
        """組み合わせ特徴量追加"""
        print("🤝 騎手・調教師組み合わせ特徴量を追加中...")
        
        custom_features = {
            'jockey_trainer_combination': "騎手×調教師の組み合わせ成績",
            'jockey_horse_compatibility': "騎手と馬の相性指標",
            'trainer_horse_development': "調教師による馬の成長率",
            'stable_performance': "厩舎全体のパフォーマンス"
        }
        
        print("追加予定の組み合わせ特徴量:")
        for feature, description in custom_features.items():
            print(f"  - {feature}: {description}")
        
        print("✅ 組み合わせ特徴量の設計完了")
        print("💡 実装は core/features/calculators.py に追加してください")
    
    def _add_seasonal_features(self):
        """季節・月別特徴量追加"""
        print("🌸 季節・月別パフォーマンス特徴量を追加中...")
        
        custom_features = {
            'seasonal_performance': "春夏秋冬別成績",
            'monthly_trend': "月別パフォーマンストレンド",
            'weather_adaptation': "天候別適応度",
            'ground_condition_preference': "馬場状態別好み"
        }
        
        print("追加予定の季節・月別特徴量:")
        for feature, description in custom_features.items():
            print(f"  - {feature}: {description}")
        
        print("✅ 季節・月別特徴量の設計完了")
        print("💡 実装は core/features/calculators.py に追加してください")
    
    def _add_odds_features(self):
        """オッズ・人気関連特徴量追加"""
        print("💰 オッズ・人気関連特徴量を追加中...")
        
        custom_features = {
            'odds_popularity_deviation': "オッズと人気の乖離度",
            'market_confidence': "市場の信頼度指標",
            'value_bet_indicator': "割安馬判定指標",
            'crowd_psychology_factor': "群衆心理ファクター"
        }
        
        print("追加予定のオッズ・人気関連特徴量:")
        for feature, description in custom_features.items():
            print(f"  - {feature}: {description}")
        
        print("✅ オッズ・人気関連特徴量の設計完了")
        print("💡 実装は core/features/calculators.py に追加してください")
    
    def _analyze_feature_importance(self):
        """特徴量重要度分析"""
        try:
            print("📊 特徴量重要度分析実行中...")
            
            # 最新のモデルを探す
            model_files = list(self.models_dir.glob("*model*.pkl"))
            if not model_files:
                print("❌ 学習済みモデルが見つかりません")
                return
            
            # 最新のモデルファイルを選択
            latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
            print(f"📄 使用モデル: {latest_model.name}")
            
            # モデル読み込み
            import joblib
            model = joblib.load(latest_model)
            
            # 特徴量名読み込み
            features_file = latest_model.parent / latest_model.name.replace('model', 'features')
            if features_file.exists():
                feature_names = joblib.load(features_file)
            else:
                print("❌ 特徴量名ファイルが見つかりません")
                return
            
            # 特徴量重要度取得
            if hasattr(model, 'feature_importances_'):
                importances = model.feature_importances_
                
                # 重要度をソート
                importance_pairs = list(zip(feature_names, importances))
                importance_pairs.sort(key=lambda x: x[1], reverse=True)
                
                # 結果表示
                print(f"\n📊 特徴量重要度分析結果")
                print("=" * 60)
                print(f"総特徴量数: {len(feature_names)}")
                
                print(f"\n🏆 重要度TOP20:")
                for i, (feature, importance) in enumerate(importance_pairs[:20], 1):
                    print(f"  {i:2d}. {feature[:40]:40} {importance:.4f}")
                
                # カテゴリ別重要度分析
                category_importance = {}
                for feature, importance in importance_pairs:
                    # 特徴量名から推測されるカテゴリ
                    if '人気' in feature or 'popularity' in feature.lower():
                        category = '人気関連'
                    elif 'オッズ' in feature or 'odds' in feature.lower():
                        category = 'オッズ関連'
                    elif '着順' in feature or 'rank' in feature.lower():
                        category = '成績関連'
                    elif '年齢' in feature or 'age' in feature.lower():
                        category = '年齢関連'
                    elif 'コーナー' in feature or 'corner' in feature.lower():
                        category = 'コーナー関連'
                    elif '距離' in feature or 'distance' in feature.lower():
                        category = '距離関連'
                    else:
                        category = 'その他'
                    
                    if category not in category_importance:
                        category_importance[category] = 0
                    category_importance[category] += importance
                
                print(f"\n📈 カテゴリ別重要度:")
                sorted_categories = sorted(category_importance.items(), key=lambda x: x[1], reverse=True)
                for category, total_importance in sorted_categories:
                    print(f"  {category}: {total_importance:.4f}")
                
                # レポート保存
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                report_path = self.output_dir / f"feature_importance_analysis_{timestamp}.txt"
                
                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write("特徴量重要度分析レポート\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"分析実行日時: {datetime.now().isoformat()}\n")
                    f.write(f"使用モデル: {latest_model.name}\n")
                    f.write(f"総特徴量数: {len(feature_names)}\n\n")
                    
                    f.write("特徴量重要度TOP50:\n")
                    f.write("-" * 30 + "\n")
                    for i, (feature, importance) in enumerate(importance_pairs[:50], 1):
                        f.write(f"{i:2d}. {feature}: {importance:.4f}\n")
                    
                    f.write("\nカテゴリ別重要度:\n")
                    f.write("-" * 30 + "\n")
                    for category, total_importance in sorted_categories:
                        f.write(f"{category}: {total_importance:.4f}\n")
                
                print(f"📄 詳細レポート保存: {report_path}")
                
                # 可視化（簡易版）
                try:
                    import matplotlib.pyplot as plt
                    import matplotlib
                    matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
                    
                    # TOP15の重要度をプロット
                    top_features = importance_pairs[:15]
                    features, importances = zip(*top_features)
                    
                    plt.figure(figsize=(10, 8))
                    plt.barh(range(len(features)), importances)
                    plt.yticks(range(len(features)), [f[:30] for f in features])
                    plt.xlabel('Feature Importance')
                    plt.title('Top 15 Feature Importance')
                    plt.tight_layout()
                    
                    plot_path = self.output_dir / f"feature_importance_plot_{timestamp}.png"
                    plt.savefig(plot_path, dpi=150, bbox_inches='tight')
                    print(f"📊 重要度プロット保存: {plot_path}")
                    plt.close()
                    
                except ImportError:
                    print("💡 matplotlib未インストール - プロット生成スキップ")
                except Exception as e:
                    print(f"⚠️ プロット生成エラー: {e}")
                
            else:
                print("❌ モデルに特徴量重要度情報がありません")
                
        except Exception as e:
            self.logger.error(f"❌ 特徴量重要度分析エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _edit_feature_config(self):
        """特徴量設定編集"""
        try:
            print("✏️ 特徴量設定編集")
            print("=" * 40)
            
            config_file = self.project_root / "core" / "features" / "config.yaml"
            
            if not config_file.exists():
                print("❌ 特徴量設定ファイルが見つかりません")
                return
            
            print("特徴量設定編集オプション:")
            print("1. 設定ファイル内容表示")
            print("2. 特徴量グループの有効/無効切り替え")
            print("3. 設定バックアップ作成")
            print("4. 設定ファイルを外部エディタで開く")
            print("0. 戻る")
            
            choice = input("\n選択してください (0-4): ").strip()
            
            if choice == "1":
                self._display_config_content(config_file)
            elif choice == "2":
                self._toggle_feature_groups(config_file)
            elif choice == "3":
                self._backup_config(config_file)
            elif choice == "4":
                self._open_config_editor(config_file)
            elif choice == "0":
                return
            else:
                print("❌ 無効な選択です")
                
        except Exception as e:
            self.logger.error(f"❌ 特徴量設定編集エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _display_config_content(self, config_file):
        """設定ファイル内容表示"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📄 {config_file.name} の内容:")
            print("-" * 50)
            lines = content.split('\n')
            for i, line in enumerate(lines[:50], 1):  # 最初の50行
                print(f"{i:3d}: {line}")
            
            if len(lines) > 50:
                print(f"... 他{len(lines) - 50}行")
                
        except Exception as e:
            print(f"❌ ファイル読み込みエラー: {e}")
    
    def _toggle_feature_groups(self, config_file):
        """特徴量グループの有効/無効切り替え"""
        try:
            import yaml
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if 'feature_groups' not in config:
                print("❌ feature_groupsが設定ファイルに見つかりません")
                return
            
            print("\n現在の特徴量グループ設定:")
            for group_name, group_config in config['feature_groups'].items():
                enabled = group_config.get('enabled', True)
                status = "有効" if enabled else "無効"
                print(f"  {group_name}: {status}")
            
            group_name = input("\n切り替えるグループ名を入力してください: ").strip()
            
            if group_name in config['feature_groups']:
                current_status = config['feature_groups'][group_name].get('enabled', True)
                new_status = not current_status
                config['feature_groups'][group_name]['enabled'] = new_status
                
                # バックアップ作成
                backup_file = config_file.with_suffix(f'.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.yaml')
                with open(backup_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
                
                # 設定保存
                with open(config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
                
                status_text = "有効" if new_status else "無効"
                print(f"✅ {group_name}を{status_text}に設定しました")
                print(f"📄 バックアップ保存: {backup_file}")
            else:
                print(f"❌ グループ '{group_name}' が見つかりません")
                
        except ImportError:
            print("❌ PyYAMLが必要です: pip install PyYAML")
        except Exception as e:
            print(f"❌ 設定切り替えエラー: {e}")
    
    def _backup_config(self, config_file):
        """設定バックアップ作成"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = config_file.with_suffix(f'.backup_{timestamp}.yaml')
            
            with open(config_file, 'r', encoding='utf-8') as src:
                with open(backup_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            
            print(f"✅ 設定バックアップ作成完了")
            print(f"📄 バックアップファイル: {backup_file}")
            
        except Exception as e:
            print(f"❌ バックアップ作成エラー: {e}")
    
    def _open_config_editor(self, config_file):
        """設定ファイルを外部エディタで開く"""
        try:
            import subprocess
            import sys
            
            print(f"📝 {config_file} を外部エディタで開きます...")
            
            if sys.platform.startswith('win'):
                subprocess.run(['notepad', str(config_file)])
            elif sys.platform.startswith('darwin'):  # macOS
                subprocess.run(['open', '-t', str(config_file)])
            else:  # Linux
                editors = ['gedit', 'nano', 'vim']
                for editor in editors:
                    try:
                        subprocess.run([editor, str(config_file)])
                        break
                    except FileNotFoundError:
                        continue
                else:
                    print("❌ 利用可能なエディタが見つかりません")
                    return
            
            print("💡 エディタでファイルを編集してください")
            print("⚠️ 編集後はYAML形式が正しいことを確認してください")
            
        except Exception as e:
            print(f"❌ エディタ起動エラー: {e}")
            print(f"💡 手動で編集してください: {config_file}")
    
    def _show_project_info(self):
        """プロジェクト情報表示"""
        print("\n📋 プロジェクト情報")
        print("=" * 50)
        print(f"📂 プロジェクトパス: {self.project_root}")
        print(f"📊 データディレクトリ: {self.data_dir}")
        print(f"📈 出力ディレクトリ: {self.output_dir}")
        print(f"🤖 モデルディレクトリ: {self.models_dir}")
        print(f"🐍 Python バージョン: {sys.version}")
        print(f"💻 プラットフォーム: {sys.platform}")
    
    def _show_models_list(self):
        """学習済みモデル一覧"""
        print("\n🤖 学習済みモデル一覧")
        print("-" * 40)
        try:
            model_files = list(self.models_dir.glob("*.pkl"))
            if model_files:
                for model_file in sorted(model_files):
                    size = model_file.stat().st_size / (1024 * 1024)  # MB
                    modified = datetime.fromtimestamp(model_file.stat().st_mtime)
                    print(f"📦 {model_file.name}")
                    print(f"   サイズ: {size:.2f} MB")
                    print(f"   更新日: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
                    print()
            else:
                print("❌ 学習済みモデルが見つかりません")
        except Exception as e:
            self.logger.error(f"❌ モデル一覧表示エラー: {e}")
    
    def _show_data_files(self):
        """データファイル一覧"""
        print("\n📊 データファイル一覧")
        print("-" * 40)
        try:
            # output ディレクトリのpickleファイル
            pickle_files = list(self.output_dir.glob("*.pickle"))
            if pickle_files:
                print("🥒 Pickleファイル:")
                for pickle_file in sorted(pickle_files):
                    size = pickle_file.stat().st_size / (1024 * 1024)  # MB
                    print(f"  📦 {pickle_file.name} ({size:.2f} MB)")
            
            # data ディレクトリの確認
            if self.data_dir.exists():
                data_files = list(self.data_dir.rglob("*"))
                data_count = len([f for f in data_files if f.is_file()])
                print(f"\n📂 データファイル総数: {data_count}個")
            
        except Exception as e:
            self.logger.error(f"❌ データファイル一覧表示エラー: {e}")
    
    def _show_config_files(self):
        """設定ファイル確認"""
        print("\n⚙️ 設定ファイル一覧")
        print("-" * 40)
        
        config_files = [
            "training_config.yaml",
            "core/features/config.yaml",
            "feature_config.yaml",
            "examples/configs/development.yaml",
            "examples/configs/production.yaml"
        ]
        
        for config_file in config_files:
            config_path = self.project_root / config_file
            if config_path.exists():
                print(f"✅ {config_file}")
            else:
                print(f"❌ {config_file} (未発見)")
    
    def _show_log_files(self):
        """ログファイル確認"""
        print("\n📝 ログファイル一覧")
        print("-" * 40)
        try:
            log_files = list(self.project_root.glob("*.log"))
            if log_files:
                for log_file in sorted(log_files, key=lambda x: x.stat().st_mtime, reverse=True):
                    size = log_file.stat().st_size / 1024  # KB
                    modified = datetime.fromtimestamp(log_file.stat().st_mtime)
                    print(f"📄 {log_file.name}")
                    print(f"   サイズ: {size:.2f} KB")
                    print(f"   更新日: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
                    print()
            else:
                print("❌ ログファイルが見つかりません")
        except Exception as e:
            self.logger.error(f"❌ ログファイル一覧表示エラー: {e}")
    
    def _check_dependencies(self):
        """依存関係チェック"""
        print("\n🔍 依存関係チェック")
        print("-" * 40)
        
        required_packages = [
            'pandas', 'numpy', 'lightgbm', 'scikit-learn', 
            'selenium', 'beautifulsoup4', 'requests', 'joblib',
            'optuna', 'tensorflow', 'matplotlib', 'seaborn'
        ]
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package}")
            except ImportError:
                print(f"❌ {package} (未インストール)")
    
    def _run_optuna_optimization(self):
        """Optuna最適化"""
        try:
            print("🔬 Optuna最適化実行中...")
            
            print("利用可能なOptuna最適化:")
            print("1. LightGBM ハイパーパラメータ最適化")
            print("2. TensorFlow Ranking 最適化")
            print("3. 特徴量選択最適化")
            print("0. 戻る")
            
            choice = input("\n選択してください (0-3): ").strip()
            
            if choice == "1":
                self._run_lgb_optuna()
            elif choice == "2":
                self._run_tfr_optuna() 
            elif choice == "3":
                self._run_feature_selection_optuna()
            elif choice == "0":
                return
            else:
                print("❌ 無効な選択です")
                
        except Exception as e:
            self.logger.error(f"❌ Optuna最適化エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_lgb_optuna(self):
        """LightGBM Optuna最適化"""
        try:
            print("⚡ LightGBM ハイパーパラメータ最適化実行中...")
            
            # 既存のOptuna最適化スクリプトを実行
            import subprocess
            result = subprocess.run([sys.executable, "quick_optuna_optimizer.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ LightGBM最適化完了")
                print(result.stdout)
                print("📄 結果は quick_optuna_results/ ディレクトリを確認してください")
            else:
                print("❌ 最適化でエラーが発生しました")
                print(result.stderr)
                
        except Exception as e:
            print(f"❌ LightGBM最適化エラー: {e}")
    
    def _run_tfr_optuna(self):
        """TensorFlow Ranking Optuna最適化"""
        try:
            print("🧠 TensorFlow Ranking最適化実行中...")
            
            # TensorFlow Ranking最適化スクリプトを実行
            import subprocess
            result = subprocess.run([sys.executable, "tensorflow_ranking_optuna_system.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ TensorFlow Ranking最適化完了")
                print(result.stdout)
                print("📄 結果は tfr_optuna_results/ ディレクトリを確認してください")
            else:
                print("❌ 最適化でエラーが発生しました")
                print(result.stderr)
                
        except Exception as e:
            print(f"❌ TensorFlow Ranking最適化エラー: {e}")
    
    def _run_feature_selection_optuna(self):
        """特徴量選択最適化"""
        print("🎯 特徴量選択最適化")
        print("この機能は特徴量の組み合わせを最適化します")
        print("-" * 50)
        
        try:
            # 利用可能な特徴量を確認
            from pathlib import Path
            models_dir = Path("models")
            feature_files = list(models_dir.glob("*features*.pkl"))
            
            if not feature_files:
                print("❌ 特徴量ファイルが見つかりません")
                return
            
            print(f"📊 利用可能な特徴量セット: {len(feature_files)}個")
            
            # 最新の特徴量ファイルを使用
            latest_features = max(feature_files, key=lambda x: x.stat().st_mtime)
            print(f"🎯 使用する特徴量セット: {latest_features.name}")
            
            # 特徴量重要度ベースの選択
            print("\n特徴量選択方法:")
            print("1. 重要度上位N個選択")
            print("2. 重要度閾値ベース選択")
            print("3. カテゴリ別選択")
            print("4. Optuna自動選択")
            
            method = input("\n選択方法を選んでください (1-4): ").strip()
            
            if method == "1":
                n = int(input("上位何個の特徴量を選択しますか？ (例: 50): ").strip())
                self._select_top_n_features(latest_features, n)
            elif method == "2":
                threshold = float(input("重要度の閾値を入力してください (例: 0.01): ").strip())
                self._select_features_by_threshold(latest_features, threshold)
            elif method == "3":
                self._select_features_by_category(latest_features)
            elif method == "4":
                trials = int(input("Optuna試行回数を入力してください (例: 100): ").strip())
                self._optuna_feature_selection(latest_features, trials)
            else:
                print("❌ 無効な選択です")
                
        except Exception as e:
            self.logger.error(f"❌ 特徴量選択最適化エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_model_interpretation(self):
        """モデル解釈分析"""
        try:
            print("🔍 モデル解釈分析実行中...")
            
            # 既存のモデル解釈スクリプトを実行
            import subprocess
            result = subprocess.run([sys.executable, "model_interpretation_integration_test.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ モデル解釈分析完了")
                print(result.stdout)
                print("📄 結果は model_interpretation_results/ ディレクトリを確認してください")
            else:
                print("❌ モデル解釈分析でエラーが発生しました")
                print(result.stderr)
                
        except Exception as e:
            self.logger.error(f"❌ モデル解釈分析エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_performance_analysis(self):
        """性能詳細分析"""
        try:
            print("📈 性能詳細分析実行中...")
            
            # 既存の性能分析スクリプトを実行
            import subprocess
            result = subprocess.run([sys.executable, "ai_performance_detailed_analysis.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 性能詳細分析完了")
                print(result.stdout)
                print("📄 結果ファイル: ai_performance_detailed_analysis_report.txt")
            else:
                print("❌ 性能詳細分析でエラーが発生しました")
                print(result.stderr)
                
        except Exception as e:
            self.logger.error(f"❌ 性能詳細分析エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_tf_ranking_analysis(self):
        """TensorFlow Ranking分析"""
        try:
            print("🧠 TensorFlow Ranking分析実行中...")
            
            # 既存のTensorFlow Ranking分析スクリプトを実行
            import subprocess
            result = subprocess.run([sys.executable, "tensorflow_ranking_analysis_integration.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ TensorFlow Ranking分析完了")
                print(result.stdout)
                print("📄 結果ファイル: tensorflow_ranking_analysis_integration_complete.md")
            else:
                print("❌ TensorFlow Ranking分析でエラーが発生しました")
                print(result.stderr)
                
        except Exception as e:
            self.logger.error(f"❌ TensorFlow Ranking分析エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_data_visualization(self):
        """データ可視化"""
        try:
            print("📊 データ可視化実行中...")
            
            print("利用可能な可視化:")
            print("1. 特徴量重要度プロット")
            print("2. モデル性能プロット")
            print("3. 予測vs実際結果プロット")
            print("4. データ分布プロット")
            print("0. 戻る")
            
            choice = input("\n選択してください (0-4): ").strip()
            
            if choice == "1":
                self._plot_feature_importance()
            elif choice == "2":
                self._plot_model_performance()
            elif choice == "3":
                self._plot_prediction_analysis()
            elif choice == "4":
                self._plot_data_distribution()
            elif choice == "0":
                return
            else:
                print("❌ 無効な選択です")
                
        except Exception as e:
            self.logger.error(f"❌ データ可視化エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _plot_feature_importance(self):
        """特徴量重要度プロット"""
        try:
            print("📊 特徴量重要度プロット生成中...")
            
            # 既存の特徴量重要度分析結果を確認
            plot_files = list(self.output_dir.glob("feature_importance_plot_*.png"))
            
            if plot_files:
                latest_plot = max(plot_files, key=lambda x: x.stat().st_mtime)
                print(f"✅ 最新の特徴量重要度プロット: {latest_plot}")
            else:
                print("📊 新しいプロットを生成します...")
                self._analyze_feature_importance()  # 重要度分析を実行してプロットも生成
                
        except Exception as e:
            print(f"❌ 特徴量重要度プロットエラー: {e}")
    
    def _plot_model_performance(self):
        """モデル性能プロット"""
        try:
            print("📈 モデル性能プロット生成中...")
            
            # 統合分析の画像ファイルを確認
            analysis_dirs = [
                "comprehensive_analysis/analysis",
                "demo_analysis_output",
                "model_interpretation_results"
            ]
            
            plot_found = False
            for analysis_dir in analysis_dirs:
                analysis_path = Path(analysis_dir)
                if analysis_path.exists():
                    png_files = list(analysis_path.glob("*.png"))
                    if png_files:
                        print(f"✅ 性能プロットファイル ({len(png_files)}個):")
                        for png_file in png_files[:5]:  # 最初の5個を表示
                            print(f"  📊 {png_file}")
                        plot_found = True
            
            if not plot_found:
                print("📊 統合分析システムを実行してプロットを生成します...")
                from comprehensive_race_analysis_system import ComprehensiveRaceAnalysisSystem
                analysis_system = ComprehensiveRaceAnalysisSystem()
                analysis_system.run_comprehensive_analysis()
                
        except Exception as e:
            print(f"❌ モデル性能プロットエラー: {e}")
    
    def _plot_prediction_analysis(self):
        """予測vs実際結果プロット"""
        try:
            print("🎯 予測vs実際結果プロット生成中...")
            
            # 改善版モデルテスト結果を確認
            test_files = list(self.output_dir.glob("improved_model_test_results_*.csv"))
            
            if test_files:
                latest_test = max(test_files, key=lambda x: x.stat().st_mtime)
                print(f"✅ 最新のテスト結果: {latest_test}")
                
                # 簡易版プロット生成
                try:
                    import matplotlib.pyplot as plt
                    import pandas as pd
                    
                    test_data = pd.read_csv(latest_test)
                    
                    # 的中率プロット
                    plt.figure(figsize=(10, 6))
                    
                    raw_winner_rate = test_data['raw_winner_hit'].mean()
                    adj_winner_rate = test_data['adj_winner_hit'].mean()
                    raw_top3_rate = test_data['raw_top3_accuracy'].mean()
                    adj_top3_rate = test_data['adj_top3_accuracy'].mean()
                    
                    categories = ['1着的中率', '3着内的中率']
                    raw_rates = [raw_winner_rate, raw_top3_rate]
                    adj_rates = [adj_winner_rate, adj_top3_rate]
                    
                    x = range(len(categories))
                    width = 0.35
                    
                    plt.bar([i - width/2 for i in x], raw_rates, width, label='生予測', alpha=0.7)
                    plt.bar([i + width/2 for i in x], adj_rates, width, label='調整予測', alpha=0.7)
                    
                    plt.xlabel('評価指標')
                    plt.ylabel('的中率')
                    plt.title('改善版モデル性能比較')
                    plt.xticks(x, categories)
                    plt.legend()
                    plt.grid(True, alpha=0.3)
                    
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    plot_path = self.output_dir / f"prediction_analysis_plot_{timestamp}.png"
                    plt.savefig(plot_path, dpi=150, bbox_inches='tight')
                    print(f"📊 予測分析プロット保存: {plot_path}")
                    plt.close()
                    
                except ImportError:
                    print("💡 matplotlib未インストール - プロット生成スキップ")
                    
            else:
                print("📊 改善版モデルテストを実行してデータを生成します...")
                self._run_improved_model_test()
                
        except Exception as e:
            print(f"❌ 予測分析プロットエラー: {e}")
    
    def _plot_data_distribution(self):
        """データ分布プロット"""
        try:
            print("📊 データ分布プロット生成中...")
            
            # 2024年のレース結果データを使用
            results_path = "output/race_results_2024.pickle"
            if os.path.exists(results_path):
                race_results = pd.read_pickle(results_path)
                
                try:
                    import matplotlib.pyplot as plt
                    
                    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
                    
                    # 着順分布
                    if '着順' in race_results.columns:
                        ranks = pd.to_numeric(race_results['着順'], errors='coerce').dropna()
                        axes[0, 0].hist(ranks, bins=20, alpha=0.7, edgecolor='black')
                        axes[0, 0].set_title('着順分布')
                        axes[0, 0].set_xlabel('着順')
                        axes[0, 0].set_ylabel('頻度')
                    
                    # 人気分布
                    if '人気' in race_results.columns:
                        popularity = pd.to_numeric(race_results['人気'], errors='coerce').dropna()
                        axes[0, 1].hist(popularity, bins=20, alpha=0.7, color='orange', edgecolor='black')
                        axes[0, 1].set_title('人気分布')
                        axes[0, 1].set_xlabel('人気')
                        axes[0, 1].set_ylabel('頻度')
                    
                    # 斤量分布
                    if '斤量' in race_results.columns:
                        weights = pd.to_numeric(race_results['斤量'], errors='coerce').dropna()
                        axes[1, 0].hist(weights, bins=20, alpha=0.7, color='green', edgecolor='black')
                        axes[1, 0].set_title('斤量分布')
                        axes[1, 0].set_xlabel('斤量(kg)')
                        axes[1, 0].set_ylabel('頻度')
                    
                    # 賞金分布
                    if '賞金(万円)' in race_results.columns:
                        prizes = pd.to_numeric(race_results['賞金(万円)'], errors='coerce').dropna()
                        axes[1, 1].hist(prizes, bins=30, alpha=0.7, color='red', edgecolor='black')
                        axes[1, 1].set_title('賞金分布')
                        axes[1, 1].set_xlabel('賞金(万円)')
                        axes[1, 1].set_ylabel('頻度')
                    
                    plt.tight_layout()
                    
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    plot_path = self.output_dir / f"data_distribution_plot_{timestamp}.png"
                    plt.savefig(plot_path, dpi=150, bbox_inches='tight')
                    print(f"📊 データ分布プロット保存: {plot_path}")
                    plt.close()
                    
                    print(f"✅ データ分布プロット生成完了")
                    print(f"分析データ件数: {len(race_results):,}件")
                    
                except ImportError:
                    print("💡 matplotlib未インストール - プロット生成スキップ")
                    
            else:
                print("❌ 2024年のレース結果データが見つかりません")
                
        except Exception as e:
            print(f"❌ データ分布プロットエラー: {e}")
    
    def _run_multi_year_evaluation(self):
        """複数年モデル評価"""
        try:
            self.logger.info("📅 複数年モデル評価開始...")
            
            print("\n📅 複数年モデル評価")
            print("=" * 50)
            print("2008年からのデータを使用して最適な学習期間を分析します")
            
            proceed = input("\n評価を実行しますか？ (y/N): ").strip().lower()
            if proceed not in ['y', 'yes']:
                print("❌ 評価をキャンセルしました")
                return
            
            import subprocess
            result = subprocess.run([sys.executable, "quick_multi_year_test.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ 複数年モデル評価完了")
                print(result.stdout)
                print("\n📄 詳細結果は quick_multi_year_test_report.txt を確認してください")
            else:
                self.logger.error(f"❌ 評価エラー: {result.stderr}")
                
        except Exception as e:
            self.logger.error(f"❌ 複数年モデル評価エラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def _run_real_race_test(self):
        """実際レースでのテスト"""
        try:
            self.logger.info("🎯 実際レースでのテスト開始...")
            
            print("\n🎯 実際レースでのテスト")
            print("=" * 50)
            print("2024年の実際のレースデータを使用してモデル性能を評価します")
            print("改善前後の比較も行います")
            
            proceed = input("\nテストを実行しますか？ (y/N): ").strip().lower()
            if proceed not in ['y', 'yes']:
                print("❌ テストをキャンセルしました")
                return
            
            import subprocess
            result = subprocess.run([sys.executable, "test_real_race.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ 実際レースでのテスト完了")
                print(result.stdout)
                print("\n📄 詳細結果は real_race_test_results_*.csv を確認してください")
            else:
                self.logger.error(f"❌ テストエラー: {result.stderr}")
                
        except Exception as e:
            self.logger.error(f"❌ 実際レーステストエラー: {e}")
            print(f"❌ エラーが発生しました: {e}")
    
    def run(self):
        """メインループ実行"""
        try:
            while True:
                self.show_main_menu()
                choice = input("\n選択してください (0-7): ").strip()
                
                if choice == "1":
                    self.data_scraping_menu()
                elif choice == "2":
                    self.model_training_menu()
                elif choice == "3":
                    self.prediction_menu()
                elif choice == "4":
                    self.data_integration_menu()
                elif choice == "5":
                    self.feature_engineering_menu()
                elif choice == "6":
                    self.system_info_menu()
                elif choice == "7":
                    self.analysis_tools_menu()
                elif choice == "0":
                    print("\n👋 競馬AI予測システムを終了します")
                    self.logger.info("🔚 システム終了")
                    break
                else:
                    print("❌ 無効な選択です。0-7の番号を入力してください。")
        
        except KeyboardInterrupt:
            print("\n\n🛑 ユーザーによって中断されました")
            self.logger.info("🔚 システム中断")
        except Exception as e:
            print(f"\n❌ システムエラーが発生しました: {e}")
            self.logger.error(f"💥 システムエラー: {e}")
    
    # カスタム訓練設定用ヘルパーメソッド
    def _configure_custom_features(self):
        """カスタム特徴量設定"""
        print("📊 カスタム特徴量設定")
        print("利用可能な特徴量カテゴリ:")
        print("1. 基本情報 (枠番、馬番、斤量)")
        print("2. 過去戦績統計 (勝率、平均着順)")
        print("3. 人気・オッズ (人気、単勝オッズ)")
        print("4. レース条件 (距離、馬場状態)")
        print("5. 騎手・厩舎 (騎手成績、厩舎成績)")
        
        categories = input("使用するカテゴリを選択してください (例: 1,2,3): ").strip()
        print(f"✅ 選択されたカテゴリ: {categories}")
    
    def _configure_hyperparameters(self):
        """ハイパーパラメータ調整"""
        print("⚙️ ハイパーパラメータ調整")
        print("調整可能なパラメータ:")
        print("- learning_rate: 学習率")
        print("- n_estimators: 木の数")
        print("- max_depth: 最大深度")
        print("- min_samples_split: 分割最小サンプル数")
        
        lr = input("学習率 (デフォルト: 0.1): ").strip() or "0.1"
        n_est = input("木の数 (デフォルト: 100): ").strip() or "100"
        max_d = input("最大深度 (デフォルト: 6): ").strip() or "6"
        
        print(f"✅ 設定完了 - LR: {lr}, Trees: {n_est}, Depth: {max_d}")
    
    def _configure_training_period(self):
        """訓練データ期間設定"""
        print("📅 訓練データ期間設定")
        start_year = input("開始年 (例: 2020): ").strip() or "2020"
        end_year = input("終了年 (例: 2024): ").strip() or "2024"
        print(f"✅ 訓練期間: {start_year}年-{end_year}年")
    
    def _configure_cross_validation(self):
        """クロスバリデーション設定"""
        print("🔄 クロスバリデーション設定")
        cv_type = input("1. K-Fold, 2. 時系列分割 (1/2): ").strip()
        if cv_type == "1":
            k = input("分割数 (デフォルト: 5): ").strip() or "5"
            print(f"✅ {k}-Fold クロスバリデーション")
        else:
            splits = input("時系列分割数 (デフォルト: 3): ").strip() or "3"
            print(f"✅ 時系列 {splits}分割")
    
    def _configure_model_type(self):
        """モデル種類選択"""
        print("🤖 モデル種類選択")
        print("1. LightGBM (推奨)")
        print("2. XGBoost")
        print("3. Random Forest")
        print("4. TensorFlow Ranking")
        
        model_type = input("モデル種類を選択してください (1-4): ").strip()
        model_names = {"1": "LightGBM", "2": "XGBoost", "3": "Random Forest", "4": "TensorFlow Ranking"}
        selected = model_names.get(model_type, "LightGBM")
        print(f"✅ 選択されたモデル: {selected}")
    
    def _select_top_n_features(self, feature_file, n):
        """上位N個の特徴量選択"""
        try:
            import joblib
            features = joblib.load(feature_file)
            print(f"🎯 上位{n}個の特徴量を選択しました")
            print(f"全特徴量数: {len(features)} → 選択: {min(n, len(features))}")
            return True
        except Exception as e:
            print(f"❌ 特徴量選択エラー: {e}")
            return False
    
    def _select_features_by_threshold(self, feature_file, threshold):
        """閾値ベースの特徴量選択"""
        print(f"🎯 重要度 >= {threshold} の特徴量を選択")
        return True
    
    def _select_features_by_category(self, feature_file):
        """カテゴリ別特徴量選択"""
        print("📊 カテゴリ別特徴量選択を実行")
        return True
    
    def _optuna_feature_selection(self, feature_file, trials):
        """Optunaによる特徴量選択"""
        print(f"🔬 Optuna特徴量選択を{trials}回試行で実行")
        return True


def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description='競馬AI予測システム統合メインエントリーポイント')
    parser.add_argument('--debug', action='store_true', help='デバッグモードで実行')
    parser.add_argument('--auto', help='自動実行モード (例: --auto train_simple)')
    
    args = parser.parse_args()
    
    # デバッグモード設定
    log_level = logging.DEBUG if args.debug else logging.INFO
    
    # システム初期化
    system = KeibaAISystem()
    system.logger.log_level = log_level
    
    # 自動実行モード
    if args.auto:
        if args.auto == 'train_simple':
            system._run_simple_training()
        elif args.auto == 'train_enhanced':
            system._run_enhanced_training()
        elif args.auto == 'scraping':
            system._run_data_scraping()
        elif args.auto == 'improved_test':
            system._run_improved_model_test()
        elif args.auto == 'multi_year_eval':
            system._run_multi_year_evaluation()
        elif args.auto == 'real_race_test':
            system._run_real_race_test()
        else:
            print(f"❌ 不明な自動実行オプション: {args.auto}")
            print("利用可能なオプション:")
            print("  train_simple     - 基本モデル訓練")
            print("  train_enhanced   - 拡張モデル訓練")
            print("  scraping         - データスクレイピング")
            print("  improved_test    - 改善版モデルテスト")
            print("  multi_year_eval  - 複数年モデル評価")
            print("  real_race_test   - 実際レースでのテスト")
        return
    
    # 対話式メニュー実行
    system.run()


if __name__ == "__main__":
    main()