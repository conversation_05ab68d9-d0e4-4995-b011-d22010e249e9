#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
レース情報のbinファイルから表データを生成し、前処理やマージを行うスクリプト
"""

import argparse
import logging
import os
import pandas as pd # all_past_results_for_merge の前処理で pd.DataFrame() を使う可能性のため

from core.processors.race_processor import RaceProcessor
from core.utils.constants import ResultsCols # merge_past_performance の target_performance_cols で使用

def main():
    # ロガーの基本設定
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                        handlers=[logging.StreamHandler()])

    parser = argparse.ArgumentParser(description="レース情報のbinファイルから表データを生成")
    parser.add_argument("--year", type=str, help="処理する年")
    parser.add_argument("--race-id", type=str, help="処理する特定のレースID")
    parser.add_argument("--save", action="store_true", help="CSVとして保存する")
    parser.add_argument("--no-parallel", action="store_true", help="並列処理を使用しない")
    parser.add_argument("--workers", type=int, default=os.cpu_count() or 1, help="並列処理の最大ワーカー数")
    parser.add_argument(
        "--merge-past-performance", action="store_true", help="レース結果に馬の過去の戦績をマージする"
    )
    parser.add_argument(
        "--preprocess", action="store_true", help="データの前処理を行う"
    )
    args = parser.parse_args()

    # RaceProcessorのインスタンスを作成
    race_processor = RaceProcessor()

    # レース情報を処理
    race_processor.process_race_bin_files(
        year=args.year, race_id=args.race_id,
        parallel=not args.no_parallel, max_workers=args.workers
    )
    # インスタンス変数から取得
    race_info_df = race_processor._race_info_df
    race_results_df = race_processor._race_results_df

    # 結果を表示
    if not race_info_df.empty:
        print(f"\nレース情報 ({len(race_info_df)}件):")
        print(race_info_df.head())
    else:
        print("レース情報を取得できませんでした")

    if not race_results_df.empty:
        print(f"\n出走馬のレース結果 ({len(race_results_df)}件):")
        print(race_results_df.head())
    else:
        print("出走馬のレース結果を取得できませんでした")

    # データの前処理
    if args.preprocess:
        print("\nデータの前処理を実行中...")
        preprocessed_df = race_processor.preprocess_data()
        if not preprocessed_df.empty:
            print("\n前処理後のデータ:")
            print(preprocessed_df.head())
        else:
            print("データの前処理に失敗しました。")

    # レース結果に馬の過去の戦績をマージ
    if args.merge_past_performance:
        print("\nレース結果に馬の過去の戦績をマージする機能は RaceFeatureEngineer に移動予定です。")
        # RaceFeatureEngineer が実装されたら、以下のコメントを解除し、
        # race_processor.merge_past_horse_performance(...) を呼び出すように修正してください。
        # (その際、all_past_results_for_merge の準備も適切に行ってください)

    # CSVとして保存
    if args.save:
        race_processor.save_race_data_to_csv(year=args.year, race_id=args.race_id)

if __name__ == "__main__":
    main()