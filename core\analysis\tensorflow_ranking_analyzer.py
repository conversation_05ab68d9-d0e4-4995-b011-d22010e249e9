#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TensorFlow Ranking + Optuna 分析システム

このモジュールはTensorFlow Rankingモデルの詳細分析を提供します。
- ランキング予測の解釈
- Optunaハイパーパラメータ分析
- モデル比較と性能評価
- 順位予測の可視化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
import shap
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import pickle
import json
import warnings
import japanize_matplotlib
from sklearn.metrics import ndcg_score
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

warnings.filterwarnings('ignore')

class TensorFlowRankingAnalyzer:
    """TensorFlow Ranking + Optuna 分析クラス"""
    
    def __init__(self, 
                 model=None, 
                 feature_names: List[str] = None,
                 optuna_results: Dict[str, Any] = None,
                 output_dir: str = "tfr_analysis_output"):
        """
        初期化
        
        Parameters
        ----------
        model : tf.keras.Model
            学習済みTensorFlow Rankingモデル
        feature_names : List[str]
            特徴量名リスト
        optuna_results : Dict[str, Any]
            Optuna最適化結果
        output_dir : str
            分析結果出力ディレクトリ
        """
        self.model = model
        self.feature_names = feature_names or []
        self.optuna_results = optuna_results or {}
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # 分析結果保存用
        self.last_predictions = None
        self.last_features = None
        self.last_analysis = None
    
    def analyze_ranking_predictions(self, 
                                  X: np.ndarray, 
                                  y_true: np.ndarray = None,
                                  horse_names: List[str] = None,
                                  race_id: str = None) -> Dict[str, Any]:
        """
        ランキング予測の詳細分析
        
        Parameters
        ----------
        X : np.ndarray
            特徴量データ
        y_true : np.ndarray, optional
            実際の順位・スコア
        horse_names : List[str], optional
            馬名リスト
        race_id : str, optional
            レースID
            
        Returns
        -------
        Dict[str, Any]
            分析結果
        """
        if self.model is None:
            self.logger.error("モデルが設定されていません")
            return {}
        
        try:
            # 予測実行
            predictions = self.model.predict(X, verbose=0)
            if len(predictions.shape) > 1:
                predictions = predictions.flatten()
            
            # 予測順位計算
            predicted_ranks = np.argsort(-predictions) + 1
            
            # 分析結果構築
            analysis_result = {
                'basic_info': {
                    'race_id': race_id or 'unknown',
                    'horse_count': len(X),
                    'feature_count': len(self.feature_names),
                    'model_type': 'TensorFlow Ranking'
                },
                'predictions': self._format_predictions(
                    predictions, predicted_ranks, horse_names, y_true),
                'ranking_analysis': self._analyze_ranking_quality(
                    predictions, y_true, predicted_ranks),
                'feature_analysis': self._analyze_feature_contributions(X, predictions),
                'confidence_analysis': self._analyze_prediction_confidence(predictions),
                'comparative_analysis': self._comparative_ranking_analysis(
                    predictions, y_true, predicted_ranks)
            }
            
            # 結果を保存
            self.last_predictions = predictions
            self.last_features = X
            self.last_analysis = analysis_result
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"ランキング予測分析エラー: {e}")
            return {}
    
    def _format_predictions(self, 
                          predictions: np.ndarray,
                          predicted_ranks: np.ndarray, 
                          horse_names: List[str] = None,
                          y_true: np.ndarray = None) -> List[Dict[str, Any]]:
        """予測結果のフォーマット"""
        
        formatted_predictions = []
        
        for i in range(len(predictions)):
            horse_data = {
                'horse_id': i + 1,
                'horse_name': horse_names[i] if horse_names and i < len(horse_names) else f"Horse_{i+1}",
                'predicted_score': float(predictions[i]),
                'predicted_rank': int(predicted_ranks[i]),
                'confidence_level': self._calculate_confidence(predictions[i], predictions)
            }
            
            if y_true is not None and i < len(y_true):
                horse_data['true_score'] = float(y_true[i])
                true_ranks = np.argsort(-y_true) + 1
                horse_data['true_rank'] = int(true_ranks[i])
                horse_data['rank_error'] = int(predicted_ranks[i] - true_ranks[i])
                horse_data['score_error'] = float(predictions[i] - y_true[i])
            
            formatted_predictions.append(horse_data)
        
        # 予測スコアでソート
        formatted_predictions.sort(key=lambda x: x['predicted_score'], reverse=True)
        
        return formatted_predictions
    
    def _calculate_confidence(self, score: float, all_scores: np.ndarray) -> str:
        """予測信頼度計算"""
        
        score_std = np.std(all_scores)
        score_mean = np.mean(all_scores)
        
        # 標準化スコア
        z_score = (score - score_mean) / score_std if score_std > 0 else 0
        
        if z_score > 1.5:
            return "高"
        elif z_score > 0.5:
            return "中"
        elif z_score > -0.5:
            return "普通"
        else:
            return "低"
    
    def _analyze_ranking_quality(self, 
                               predictions: np.ndarray,
                               y_true: np.ndarray = None,
                               predicted_ranks: np.ndarray = None) -> Dict[str, Any]:
        """ランキング品質分析"""
        
        quality_metrics = {
            'prediction_spread': {
                'max_score': float(np.max(predictions)),
                'min_score': float(np.min(predictions)),
                'score_range': float(np.max(predictions) - np.min(predictions)),
                'score_std': float(np.std(predictions)),
                'score_distribution': 'normal' if np.std(predictions) > 0.1 else 'concentrated'
            },
            'ranking_separation': {
                'clear_winner': float(predictions[0]) > float(np.mean(predictions)) + np.std(predictions),
                'competitive_field': float(np.std(predictions)) < 0.2,
                'outliers_count': int(np.sum(np.abs(predictions - np.mean(predictions)) > 2 * np.std(predictions)))
            }
        }
        
        if y_true is not None:
            try:
                # NDCG計算
                ndcg_5 = ndcg_score([y_true], [predictions], k=5)
                ndcg_10 = ndcg_score([y_true], [predictions], k=10)
                
                # 順位相関
                spearman_corr = np.corrcoef(predicted_ranks, np.argsort(-y_true) + 1)[0, 1]
                
                quality_metrics['accuracy_metrics'] = {
                    'ndcg_5': float(ndcg_5),
                    'ndcg_10': float(ndcg_10),
                    'rank_correlation': float(spearman_corr),
                    'top_3_accuracy': self._calculate_top_k_accuracy(predicted_ranks, y_true, k=3),
                    'top_5_accuracy': self._calculate_top_k_accuracy(predicted_ranks, y_true, k=5)
                }
            except Exception as e:
                self.logger.warning(f"精度指標計算エラー: {e}")
                quality_metrics['accuracy_metrics'] = {}
        
        return quality_metrics
    
    def _calculate_top_k_accuracy(self, predicted_ranks: np.ndarray, 
                                y_true: np.ndarray, k: int) -> float:
        """Top-K精度計算"""
        
        true_ranks = np.argsort(-y_true) + 1
        predicted_top_k = np.where(predicted_ranks <= k)[0]
        true_top_k = np.where(true_ranks <= k)[0]
        
        intersection = len(set(predicted_top_k) & set(true_top_k))
        return intersection / k
    
    def _analyze_feature_contributions(self, X: np.ndarray, predictions: np.ndarray) -> Dict[str, Any]:
        """特徴量寄与分析"""
        
        try:
            # 簡易的な特徴量重要度分析
            feature_correlations = []
            
            for i, feature_name in enumerate(self.feature_names):
                if i < X.shape[1]:
                    correlation = np.corrcoef(X[:, i], predictions)[0, 1]
                    feature_correlations.append({
                        'feature': feature_name,
                        'correlation': float(correlation) if not np.isnan(correlation) else 0.0,
                        'mean_value': float(np.mean(X[:, i])),
                        'std_value': float(np.std(X[:, i]))
                    })
            
            # 相関でソート
            feature_correlations.sort(key=lambda x: abs(x['correlation']), reverse=True)
            
            return {
                'feature_correlations': feature_correlations,
                'top_positive_features': [f for f in feature_correlations if f['correlation'] > 0][:5],
                'top_negative_features': [f for f in feature_correlations if f['correlation'] < 0][:5],
                'feature_summary': {
                    'high_impact_count': len([f for f in feature_correlations if abs(f['correlation']) > 0.3]),
                    'avg_correlation': float(np.mean([abs(f['correlation']) for f in feature_correlations]))
                }
            }
            
        except Exception as e:
            self.logger.error(f"特徴量寄与分析エラー: {e}")
            return {}
    
    def _analyze_prediction_confidence(self, predictions: np.ndarray) -> Dict[str, Any]:
        """予測信頼度分析"""
        
        confidence_levels = {
            'high': np.sum(predictions > np.mean(predictions) + np.std(predictions)),
            'medium': np.sum((predictions > np.mean(predictions)) & 
                           (predictions <= np.mean(predictions) + np.std(predictions))),
            'low': np.sum(predictions <= np.mean(predictions))
        }
        
        return {
            'confidence_distribution': {k: int(v) for k, v in confidence_levels.items()},
            'confidence_metrics': {
                'prediction_variance': float(np.var(predictions)),
                'confidence_spread': float(np.max(predictions) - np.min(predictions)),
                'normalized_entropy': self._calculate_normalized_entropy(predictions)
            },
            'risk_assessment': {
                'high_confidence_picks': int(confidence_levels['high']),
                'uncertain_predictions': int(confidence_levels['medium'] + confidence_levels['low']),
                'overall_certainty': 'high' if confidence_levels['high'] > len(predictions) * 0.3 else 'medium'
            }
        }
    
    def _calculate_normalized_entropy(self, predictions: np.ndarray) -> float:
        """正規化エントロピー計算"""
        
        # 予測値を確率分布に変換
        exp_preds = np.exp(predictions - np.max(predictions))  # 数値安定性のため
        probabilities = exp_preds / np.sum(exp_preds)
        
        # エントロピー計算
        entropy = -np.sum(probabilities * np.log(probabilities + 1e-8))
        max_entropy = np.log(len(predictions))
        
        return float(entropy / max_entropy) if max_entropy > 0 else 0.0
    
    def _comparative_ranking_analysis(self, 
                                    predictions: np.ndarray,
                                    y_true: np.ndarray = None,
                                    predicted_ranks: np.ndarray = None) -> Dict[str, Any]:
        """比較ランキング分析"""
        
        analysis = {
            'ranking_patterns': {
                'clear_hierarchy': np.std(predictions) > 0.2,
                'close_competition': np.std(predictions) < 0.1,
                'prediction_consensus': len(np.unique(np.round(predictions, 2))) / len(predictions)
            }
        }
        
        if y_true is not None:
            true_ranks = np.argsort(-y_true) + 1
            
            # 順位変動分析
            rank_differences = predicted_ranks - true_ranks
            
            analysis['rank_accuracy'] = {
                'perfect_predictions': int(np.sum(rank_differences == 0)),
                'close_predictions': int(np.sum(np.abs(rank_differences) <= 2)),
                'major_errors': int(np.sum(np.abs(rank_differences) > 5)),
                'avg_rank_error': float(np.mean(np.abs(rank_differences))),
                'rank_correlation': float(np.corrcoef(predicted_ranks, true_ranks)[0, 1])
            }
            
            # 着順グループ分析
            analysis['position_group_analysis'] = {
                'top_3_precision': self._calculate_position_group_precision(
                    predicted_ranks, true_ranks, group_size=3),
                'middle_group_precision': self._calculate_position_group_precision(
                    predicted_ranks, true_ranks, group_start=4, group_size=5),
                'bottom_group_precision': self._calculate_position_group_precision(
                    predicted_ranks, true_ranks, group_start=9, group_size=None)
            }
        
        return analysis
    
    def _calculate_position_group_precision(self, 
                                         predicted_ranks: np.ndarray,
                                         true_ranks: np.ndarray,
                                         group_start: int = 1,
                                         group_size: int = None) -> float:
        """着順グループ精度計算"""
        
        if group_size is None:
            group_end = len(predicted_ranks)
        else:
            group_end = group_start + group_size - 1
        
        # 予測と実際のグループメンバー
        pred_group = set(np.where((predicted_ranks >= group_start) & 
                                (predicted_ranks <= group_end))[0])
        true_group = set(np.where((true_ranks >= group_start) & 
                                (true_ranks <= group_end))[0])
        
        if len(true_group) == 0:
            return 0.0
        
        intersection = len(pred_group & true_group)
        return intersection / len(true_group)
    
    def analyze_optuna_optimization(self) -> Dict[str, Any]:
        """Optuna最適化結果分析"""
        
        if not self.optuna_results:
            self.logger.warning("Optuna結果が設定されていません")
            return {}
        
        try:
            analysis = {
                'optimization_summary': {
                    'best_score': self.optuna_results.get('best_score', 0),
                    'total_trials': len(self.optuna_results.get('optimization_history', [])),
                    'best_params': self.optuna_results.get('best_params', {})
                },
                'parameter_analysis': self._analyze_parameter_importance(),
                'convergence_analysis': self._analyze_optimization_convergence(),
                'parameter_correlations': self._analyze_parameter_correlations()
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Optuna分析エラー: {e}")
            return {}
    
    def _analyze_parameter_importance(self) -> Dict[str, Any]:
        """パラメータ重要度分析"""
        
        history = self.optuna_results.get('optimization_history', [])
        if not history:
            return {}
        
        # パラメータ別のスコア分析
        param_analysis = {}
        all_params = set()
        
        for trial in history:
            params = trial.get('params', {})
            all_params.update(params.keys())
        
        for param in all_params:
            param_values = []
            param_scores = []
            
            for trial in history:
                if param in trial.get('params', {}):
                    param_values.append(trial['params'][param])
                    param_scores.append(trial.get('cv_score', 0))
            
            if param_values and param_scores:
                param_analysis[param] = {
                    'correlation_with_score': float(np.corrcoef(param_values, param_scores)[0, 1]) 
                                            if len(set(param_values)) > 1 else 0.0,
                    'best_value': param_values[np.argmax(param_scores)],
                    'value_range': {
                        'min': min(param_values),
                        'max': max(param_values),
                        'unique_count': len(set(param_values))
                    }
                }
        
        # 重要度でソート
        sorted_params = sorted(param_analysis.items(), 
                             key=lambda x: abs(x[1]['correlation_with_score']), 
                             reverse=True)
        
        return {
            'parameter_importance': dict(sorted_params),
            'most_important_params': [p[0] for p in sorted_params[:5]],
            'parameter_summary': {
                'total_parameters': len(param_analysis),
                'high_impact_params': len([p for p in param_analysis.values() 
                                         if abs(p['correlation_with_score']) > 0.3])
            }
        }
    
    def _analyze_optimization_convergence(self) -> Dict[str, Any]:
        """最適化収束分析"""
        
        history = self.optuna_results.get('optimization_history', [])
        if not history:
            return {}
        
        scores = [trial.get('cv_score', 0) for trial in history]
        trial_numbers = [trial.get('trial_number', i) for i, trial in enumerate(history)]
        
        # 収束分析
        best_scores = []
        current_best = float('-inf')
        
        for score in scores:
            if score > current_best:
                current_best = score
            best_scores.append(current_best)
        
        # 改善率分析
        improvements = []
        for i in range(1, len(best_scores)):
            if best_scores[i-1] > 0:
                improvement = (best_scores[i] - best_scores[i-1]) / best_scores[i-1]
                improvements.append(improvement)
        
        return {
            'convergence_metrics': {
                'final_best_score': float(best_scores[-1]) if best_scores else 0,
                'initial_score': float(scores[0]) if scores else 0,
                'total_improvement': float(best_scores[-1] - scores[0]) if best_scores and scores else 0,
                'improvement_rate': float(np.mean(improvements)) if improvements else 0,
                'convergence_trial': self._find_convergence_trial(best_scores)
            },
            'optimization_trajectory': {
                'trial_numbers': trial_numbers,
                'scores': scores,
                'best_scores': best_scores,
                'plateau_detection': self._detect_optimization_plateau(best_scores)
            }
        }
    
    def _find_convergence_trial(self, best_scores: List[float]) -> int:
        """収束試行回数検出"""
        
        if len(best_scores) < 3:
            return len(best_scores)
        
        # 最後の改善から現在までの試行数
        last_improvement = 0
        for i in range(len(best_scores) - 1, 0, -1):
            if best_scores[i] > best_scores[i-1]:
                last_improvement = i
                break
        
        return len(best_scores) - last_improvement
    
    def _detect_optimization_plateau(self, best_scores: List[float]) -> Dict[str, Any]:
        """最適化プラトー検出"""
        
        if len(best_scores) < 5:
            return {'plateau_detected': False}
        
        # 最後の5試行での改善をチェック
        recent_scores = best_scores[-5:]
        is_plateau = recent_scores[-1] == recent_scores[0]  # 改善なし
        
        return {
            'plateau_detected': is_plateau,
            'plateau_length': self._find_convergence_trial(best_scores),
            'stability_score': 1.0 - (np.std(recent_scores) / np.mean(recent_scores)) 
                             if np.mean(recent_scores) > 0 else 0.0
        }
    
    def _analyze_parameter_correlations(self) -> Dict[str, Any]:
        """パラメータ相関分析"""
        
        history = self.optuna_results.get('optimization_history', [])
        if not history:
            return {}
        
        # パラメータデータフレーム作成
        param_data = []
        for trial in history:
            trial_params = trial.get('params', {})
            trial_params['cv_score'] = trial.get('cv_score', 0)
            param_data.append(trial_params)
        
        if not param_data:
            return {}
        
        df = pd.DataFrame(param_data)
        
        # 数値パラメータのみで相関計算
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 1:
            correlation_matrix = df[numeric_cols].corr()
            
            # 高相関ペア抽出
            high_corr_pairs = []
            for i in range(len(correlation_matrix.columns)):
                for j in range(i+1, len(correlation_matrix.columns)):
                    corr_value = correlation_matrix.iloc[i, j]
                    if abs(corr_value) > 0.5:  # 閾値を下げて検出
                        high_corr_pairs.append({
                            'param1': correlation_matrix.columns[i],
                            'param2': correlation_matrix.columns[j],
                            'correlation': float(corr_value)
                        })
            
            return {
                'correlation_matrix': correlation_matrix.to_dict(),
                'high_correlation_pairs': high_corr_pairs,
                'parameter_interactions': self._identify_parameter_interactions(df)
            }
        
        return {}
    
    def _identify_parameter_interactions(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """パラメータ相互作用識別"""
        
        interactions = []
        numeric_cols = [col for col in df.columns if col != 'cv_score' and df[col].dtype in [np.number]]
        
        if 'cv_score' in df.columns and len(numeric_cols) >= 2:
            # 主要パラメータペアの相互作用をチェック
            for i, param1 in enumerate(numeric_cols[:3]):  # 計算量制限
                for param2 in numeric_cols[i+1:4]:
                    try:
                        # 両パラメータの組み合わせでグループ化してスコア分析
                        groups = df.groupby([
                            pd.cut(df[param1], bins=3, labels=['low', 'mid', 'high']),
                            pd.cut(df[param2], bins=3, labels=['low', 'mid', 'high'])
                        ])['cv_score'].mean()
                        
                        if not groups.empty:
                            interaction_strength = groups.std() / groups.mean() if groups.mean() > 0 else 0
                            
                            interactions.append({
                                'param1': param1,
                                'param2': param2,
                                'interaction_strength': float(interaction_strength),
                                'best_combination': groups.idxmax() if not groups.empty else None
                            })
                    except Exception:
                        continue
        
        return sorted(interactions, key=lambda x: x['interaction_strength'], reverse=True)
    
    def create_ranking_visualizations(self, analysis_result: Dict[str, Any]) -> Dict[str, str]:
        """ランキング分析可視化"""
        
        visualization_files = {}
        
        try:
            # 1. 予測順位vs実際順位（実際順位がある場合）
            if self._has_true_ranks(analysis_result):
                rank_comparison_path = self.create_rank_comparison_chart(analysis_result)
                if rank_comparison_path:
                    visualization_files['rank_comparison'] = rank_comparison_path
            
            # 2. 予測スコア分布
            score_dist_path = self.create_score_distribution_chart(analysis_result)
            if score_dist_path:
                visualization_files['score_distribution'] = score_dist_path
            
            # 3. 特徴量寄与度
            feature_contrib_path = self.create_feature_contribution_chart(analysis_result)
            if feature_contrib_path:
                visualization_files['feature_contribution'] = feature_contrib_path
            
            # 4. 信頼度分析
            confidence_path = self.create_confidence_analysis_chart(analysis_result)
            if confidence_path:
                visualization_files['confidence_analysis'] = confidence_path
            
        except Exception as e:
            self.logger.error(f"可視化作成エラー: {e}")
        
        return visualization_files
    
    def _has_true_ranks(self, analysis_result: Dict[str, Any]) -> bool:
        """実際順位データの存在確認"""
        predictions = analysis_result.get('predictions', [])
        return any('true_rank' in pred for pred in predictions)
    
    def create_rank_comparison_chart(self, analysis_result: Dict[str, Any]) -> Optional[str]:
        """順位比較チャート作成"""
        
        try:
            predictions = analysis_result.get('predictions', [])
            if not predictions or not self._has_true_ranks(analysis_result):
                return None
            
            predicted_ranks = [p['predicted_rank'] for p in predictions]
            true_ranks = [p['true_rank'] for p in predictions]
            horse_names = [p['horse_name'] for p in predictions]
            
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 散布図
            scatter = ax.scatter(true_ranks, predicted_ranks, 
                               alpha=0.7, s=100, c='skyblue', edgecolors='navy')
            
            # 完璧な予測線
            max_rank = max(max(predicted_ranks), max(true_ranks))
            ax.plot([1, max_rank], [1, max_rank], 'r--', label='完璧な予測', linewidth=2)
            
            # 馬名ラベル（上位5頭のみ）
            for i, (pred, true, name) in enumerate(zip(predicted_ranks, true_ranks, horse_names)):
                if i < 5:  # 上位5頭のみ
                    ax.annotate(name, (true, pred), xytext=(5, 5), 
                              textcoords='offset points', fontsize=8)
            
            ax.set_xlabel('実際の順位', fontsize=12)
            ax.set_ylabel('予測順位', fontsize=12)
            ax.set_title('予測順位 vs 実際順位', fontsize=14, fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 軸を反転（1位が上/左）
            ax.invert_xaxis()
            ax.invert_yaxis()
            
            plt.tight_layout()
            
            file_path = self.output_dir / "rank_comparison.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"順位比較チャート作成エラー: {e}")
            return None
    
    def create_score_distribution_chart(self, analysis_result: Dict[str, Any]) -> Optional[str]:
        """スコア分布チャート作成"""
        
        try:
            predictions = analysis_result.get('predictions', [])
            if not predictions:
                return None
            
            scores = [p['predicted_score'] for p in predictions]
            horse_names = [p['horse_name'] for p in predictions]
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # ヒストグラム
            ax1.hist(scores, bins=10, alpha=0.7, color='lightblue', edgecolor='navy')
            ax1.set_xlabel('予測スコア', fontsize=12)
            ax1.set_ylabel('頻度', fontsize=12)
            ax1.set_title('予測スコア分布', fontsize=14, fontweight='bold')
            ax1.grid(True, alpha=0.3)
            
            # バーチャート（順位順）
            top_10 = sorted(predictions, key=lambda x: x['predicted_score'], reverse=True)[:10]
            names = [p['horse_name'] for p in top_10]
            scores_top = [p['predicted_score'] for p in top_10]
            
            bars = ax2.barh(range(len(names)), scores_top, color='lightcoral')
            ax2.set_yticks(range(len(names)))
            ax2.set_yticklabels(names)
            ax2.set_xlabel('予測スコア', fontsize=12)
            ax2.set_title('予測上位10頭', fontsize=14, fontweight='bold')
            
            # スコア値を表示
            for i, (bar, score) in enumerate(zip(bars, scores_top)):
                ax2.text(bar.get_width() + max(scores_top) * 0.01, 
                        bar.get_y() + bar.get_height()/2,
                        f'{score:.3f}', va='center', fontsize=9)
            
            plt.tight_layout()
            
            file_path = self.output_dir / "score_distribution.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"スコア分布チャート作成エラー: {e}")
            return None
    
    def create_feature_contribution_chart(self, analysis_result: Dict[str, Any]) -> Optional[str]:
        """特徴量寄与度チャート作成"""
        
        try:
            feature_analysis = analysis_result.get('feature_analysis', {})
            correlations = feature_analysis.get('feature_correlations', [])
            
            if not correlations:
                return None
            
            # 上位10特徴量
            top_features = correlations[:10]
            
            features = [self._translate_feature_name(f['feature']) for f in top_features]
            corr_values = [f['correlation'] for f in top_features]
            
            # 色分け（正負）
            colors = ['green' if c > 0 else 'red' for c in corr_values]
            
            fig, ax = plt.subplots(figsize=(12, 8))
            
            bars = ax.barh(range(len(features)), corr_values, color=colors, alpha=0.7)
            ax.set_yticks(range(len(features)))
            ax.set_yticklabels(features)
            ax.set_xlabel('予測スコアとの相関', fontsize=12)
            ax.set_title('特徴量寄与度分析 (Top 10)', fontsize=14, fontweight='bold')
            ax.axvline(x=0, color='black', linestyle='-', linewidth=0.8)
            ax.grid(True, alpha=0.3)
            
            # 相関値を表示
            for bar, corr in zip(bars, corr_values):
                x_pos = bar.get_width() + (0.01 if corr >= 0 else -0.01)
                ha = 'left' if corr >= 0 else 'right'
                ax.text(x_pos, bar.get_y() + bar.get_height()/2,
                       f'{corr:.3f}', va='center', ha=ha, fontsize=9)
            
            plt.tight_layout()
            
            file_path = self.output_dir / "feature_contribution.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"特徴量寄与度チャート作成エラー: {e}")
            return None
    
    def create_confidence_analysis_chart(self, analysis_result: Dict[str, Any]) -> Optional[str]:
        """信頼度分析チャート作成"""
        
        try:
            confidence_analysis = analysis_result.get('confidence_analysis', {})
            confidence_dist = confidence_analysis.get('confidence_distribution', {})
            
            if not confidence_dist:
                return None
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 信頼度分布（円グラフ）
            labels = list(confidence_dist.keys())
            sizes = list(confidence_dist.values())
            colors = ['green', 'orange', 'red']
            
            ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax1.set_title('予測信頼度分布', fontsize=14, fontweight='bold')
            
            # 予測vs信頼度（散布図）
            predictions = analysis_result.get('predictions', [])
            if predictions:
                scores = [p['predicted_score'] for p in predictions]
                confidence_levels = [p['confidence_level'] for p in predictions]
                
                # 信頼度レベルを数値に変換
                conf_mapping = {'高': 3, '中': 2, '普通': 1, '低': 0}
                conf_numeric = [conf_mapping.get(c, 1) for c in confidence_levels]
                
                scatter = ax2.scatter(scores, conf_numeric, alpha=0.7, s=100, c='skyblue')
                ax2.set_xlabel('予測スコア', fontsize=12)
                ax2.set_ylabel('信頼度レベル', fontsize=12)
                ax2.set_yticks([0, 1, 2, 3])
                ax2.set_yticklabels(['低', '普通', '中', '高'])
                ax2.set_title('予測スコア vs 信頼度', fontsize=14, fontweight='bold')
                ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            file_path = self.output_dir / "confidence_analysis.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"信頼度分析チャート作成エラー: {e}")
            return None
    
    def create_optuna_visualizations(self, optuna_analysis: Dict[str, Any]) -> Dict[str, str]:
        """Optuna分析可視化"""
        
        visualization_files = {}
        
        try:
            # 1. 最適化履歴
            convergence_path = self.create_optimization_convergence_chart(optuna_analysis)
            if convergence_path:
                visualization_files['optimization_convergence'] = convergence_path
            
            # 2. パラメータ重要度
            param_importance_path = self.create_parameter_importance_chart(optuna_analysis)
            if param_importance_path:
                visualization_files['parameter_importance'] = param_importance_path
            
            # 3. パラメータ相関
            param_correlation_path = self.create_parameter_correlation_chart(optuna_analysis)
            if param_correlation_path:
                visualization_files['parameter_correlation'] = param_correlation_path
            
        except Exception as e:
            self.logger.error(f"Optuna可視化作成エラー: {e}")
        
        return visualization_files
    
    def create_optimization_convergence_chart(self, optuna_analysis: Dict[str, Any]) -> Optional[str]:
        """最適化収束チャート作成"""
        
        try:
            convergence = optuna_analysis.get('convergence_analysis', {})
            trajectory = convergence.get('optimization_trajectory', {})
            
            trial_numbers = trajectory.get('trial_numbers', [])
            scores = trajectory.get('scores', [])
            best_scores = trajectory.get('best_scores', [])
            
            if not trial_numbers or not scores:
                return None
            
            fig, ax = plt.subplots(figsize=(12, 6))
            
            # 各試行のスコア
            ax.scatter(trial_numbers, scores, alpha=0.6, s=50, c='lightblue', 
                      label='各試行スコア', edgecolors='navy')
            
            # 最高スコア推移
            ax.plot(trial_numbers, best_scores, 'r-', linewidth=2, 
                   label='最高スコア推移', marker='o', markersize=4)
            
            ax.set_xlabel('試行回数', fontsize=12)
            ax.set_ylabel('NDCG@5 スコア', fontsize=12)
            ax.set_title('Optuna最適化収束', fontsize=14, fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 収束情報を注釈
            final_score = best_scores[-1] if best_scores else 0
            ax.text(0.02, 0.98, f'最終スコア: {final_score:.4f}', 
                   transform=ax.transAxes, va='top', 
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
            
            plt.tight_layout()
            
            file_path = self.output_dir / "optimization_convergence.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"最適化収束チャート作成エラー: {e}")
            return None
    
    def create_parameter_importance_chart(self, optuna_analysis: Dict[str, Any]) -> Optional[str]:
        """パラメータ重要度チャート作成"""
        
        try:
            param_analysis = optuna_analysis.get('parameter_analysis', {})
            importance = param_analysis.get('parameter_importance', {})
            
            if not importance:
                return None
            
            # 重要度上位パラメータ
            params = list(importance.keys())[:8]  # 上位8個
            correlations = [abs(importance[p]['correlation_with_score']) for p in params]
            
            fig, ax = plt.subplots(figsize=(12, 6))
            
            bars = ax.bar(range(len(params)), correlations, color='steelblue', alpha=0.7)
            ax.set_xticks(range(len(params)))
            ax.set_xticklabels(params, rotation=45, ha='right')
            ax.set_ylabel('スコアとの相関（絶対値）', fontsize=12)
            ax.set_title('パラメータ重要度分析', fontsize=14, fontweight='bold')
            ax.grid(True, alpha=0.3)
            
            # 相関値を表示
            for bar, corr in zip(bars, correlations):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(correlations) * 0.01,
                       f'{corr:.3f}', ha='center', va='bottom', fontsize=9)
            
            plt.tight_layout()
            
            file_path = self.output_dir / "parameter_importance.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"パラメータ重要度チャート作成エラー: {e}")
            return None
    
    def create_parameter_correlation_chart(self, optuna_analysis: Dict[str, Any]) -> Optional[str]:
        """パラメータ相関チャート作成"""
        
        try:
            param_analysis = optuna_analysis.get('parameter_analysis', {})
            correlation_data = param_analysis.get('parameter_correlations', {})
            correlation_matrix = correlation_data.get('correlation_matrix', {})
            
            if not correlation_matrix:
                return None
            
            # DataFrameに変換
            df_corr = pd.DataFrame(correlation_matrix)
            
            # 数値パラメータのみフィルタ
            numeric_cols = [col for col in df_corr.columns if df_corr[col].dtype in [np.number]]
            if len(numeric_cols) < 2:
                return None
            
            subset_corr = df_corr.loc[numeric_cols, numeric_cols]
            
            fig, ax = plt.subplots(figsize=(10, 8))
            
            # ヒートマップ
            sns.heatmap(subset_corr, annot=True, cmap='coolwarm', center=0,
                       square=True, ax=ax, fmt='.2f', cbar_kws={'label': '相関係数'})
            
            ax.set_title('パラメータ相関マトリックス', fontsize=14, fontweight='bold')
            
            plt.tight_layout()
            
            file_path = self.output_dir / "parameter_correlation.png"
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"パラメータ相関チャート作成エラー: {e}")
            return None
    
    def _translate_feature_name(self, feature_name: str) -> str:
        """特徴量名の日本語変換（拡張版）"""
        
        translations = {
            '枠番': '枠番',
            '馬番': '馬番', 
            '斤量': '斤量',
            'course_len': '距離',
            '着順_last_5R_mean': '過去5戦平均着順',
            '人気_last_5R_mean': '過去5戦平均人気',
            'オッズ_last_5R_mean': '過去5戦平均オッズ',
            '上り_last_5R_mean': '過去5戦平均上り',
            'interval_days': '休養日数',
            'race_class': 'レースクラス',
            'jockey_skill': '騎手技量',
            'trainer_skill': '調教師技量',
            'hidden_size': '隠れ層サイズ',
            'learning_rate': '学習率',
            'dropout_rate': 'ドロップアウト率',
            'activation': '活性化関数',
            'use_dropout': 'ドロップアウト使用',
            'use_batch_norm': 'バッチ正規化'
        }
        
        # 完全一致を優先
        if feature_name in translations:
            return translations[feature_name]
        
        # 部分一致チェック
        for eng, jp in translations.items():
            if eng.lower() in feature_name.lower():
                return jp
        
        return feature_name
    
    def generate_comprehensive_report(self, 
                                    ranking_analysis: Dict[str, Any],
                                    optuna_analysis: Dict[str, Any] = None) -> str:
        """総合分析レポート生成"""
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("TensorFlow Ranking + Optuna 総合分析レポート")
        report_lines.append("=" * 80)
        
        # 基本情報
        basic_info = ranking_analysis.get('basic_info', {})
        report_lines.append(f"\n📊 基本情報:")
        report_lines.append(f"  レースID: {basic_info.get('race_id', 'N/A')}")
        report_lines.append(f"  出走頭数: {basic_info.get('horse_count', 0)}頭")
        report_lines.append(f"  特徴量数: {basic_info.get('feature_count', 0)}個")
        report_lines.append(f"  モデル種類: {basic_info.get('model_type', 'TensorFlow Ranking')}")
        
        # 予測結果サマリー
        predictions = ranking_analysis.get('predictions', [])
        if predictions:
            report_lines.append(f"\n🏇 予測結果 (上位5頭):")
            report_lines.append(f"{'順位':>4} {'馬名':>15} {'予測スコア':>10} {'信頼度':>8}")
            report_lines.append("-" * 45)
            
            for i, pred in enumerate(predictions[:5]):
                rank = i + 1
                name = pred['horse_name']
                score = pred['predicted_score']
                confidence = pred['confidence_level']
                
                report_lines.append(f"{rank:>4} {name:>15} {score:>10.3f} {confidence:>8}")
        
        # ランキング品質分析
        quality = ranking_analysis.get('ranking_analysis', {})
        if quality:
            prediction_spread = quality.get('prediction_spread', {})
            
            report_lines.append(f"\n📈 ランキング品質:")
            report_lines.append(f"  スコア範囲: {prediction_spread.get('score_range', 0):.3f}")
            report_lines.append(f"  予測分散: {prediction_spread.get('score_std', 0):.3f}")
            report_lines.append(f"  分布特性: {prediction_spread.get('score_distribution', 'N/A')}")
            
            accuracy = quality.get('accuracy_metrics', {})
            if accuracy:
                report_lines.append(f"\n🎯 精度指標:")
                report_lines.append(f"  NDCG@5: {accuracy.get('ndcg_5', 0):.3f}")
                report_lines.append(f"  NDCG@10: {accuracy.get('ndcg_10', 0):.3f}")
                report_lines.append(f"  順位相関: {accuracy.get('rank_correlation', 0):.3f}")
                report_lines.append(f"  Top3精度: {accuracy.get('top_3_accuracy', 0):.1%}")
        
        # 特徴量分析
        feature_analysis = ranking_analysis.get('feature_analysis', {})
        if feature_analysis:
            correlations = feature_analysis.get('feature_correlations', [])
            
            report_lines.append(f"\n🔍 特徴量分析:")
            if correlations:
                report_lines.append(f"  最重要特徴量:")
                for i, feature in enumerate(correlations[:3]):
                    name = self._translate_feature_name(feature['feature'])
                    corr = feature['correlation']
                    report_lines.append(f"    {i+1}. {name}: {corr:+.3f}")
            
            summary = feature_analysis.get('feature_summary', {})
            high_impact = summary.get('high_impact_count', 0)
            avg_corr = summary.get('avg_correlation', 0)
            report_lines.append(f"  高影響特徴量数: {high_impact}個")
            report_lines.append(f"  平均相関: {avg_corr:.3f}")
        
        # 信頼度分析
        confidence_analysis = ranking_analysis.get('confidence_analysis', {})
        if confidence_analysis:
            conf_dist = confidence_analysis.get('confidence_distribution', {})
            conf_metrics = confidence_analysis.get('confidence_metrics', {})
            
            report_lines.append(f"\n🔒 信頼度分析:")
            if conf_dist:
                total = sum(conf_dist.values())
                for level, count in conf_dist.items():
                    percentage = (count / total * 100) if total > 0 else 0
                    report_lines.append(f"  {level}信頼度: {count}頭 ({percentage:.1f}%)")
            
            if conf_metrics:
                entropy = conf_metrics.get('normalized_entropy', 0)
                uncertainty_level = '低' if entropy < 0.7 else '中' if entropy < 0.9 else '高'
                report_lines.append(f"  予測不確実性: {uncertainty_level} ({entropy:.3f})")
        
        # Optuna分析（データがある場合）
        if optuna_analysis:
            opt_summary = optuna_analysis.get('optimization_summary', {})
            report_lines.append(f"\n⚙️ Optuna最適化:")
            report_lines.append(f"  最高スコア: {opt_summary.get('best_score', 0):.4f}")
            report_lines.append(f"  試行回数: {opt_summary.get('total_trials', 0)}回")
            
            param_analysis = optuna_analysis.get('parameter_analysis', {})
            important_params = param_analysis.get('most_important_params', [])
            if important_params:
                report_lines.append(f"  重要パラメータ:")
                for param in important_params[:3]:
                    translated = self._translate_feature_name(param)
                    report_lines.append(f"    • {translated}")
            
            convergence = optuna_analysis.get('convergence_analysis', {})
            convergence_metrics = convergence.get('convergence_metrics', {})
            if convergence_metrics:
                improvement = convergence_metrics.get('total_improvement', 0)
                convergence_trial = convergence_metrics.get('convergence_trial', 0)
                report_lines.append(f"  総改善幅: {improvement:+.4f}")
                report_lines.append(f"  収束試行数: {convergence_trial}回")
        
        # 推奨事項
        report_lines.append(f"\n💡 推奨事項:")
        
        # 品質に基づく推奨
        if quality:
            spread = quality.get('prediction_spread', {})
            score_std = spread.get('score_std', 0)
            
            if score_std < 0.1:
                report_lines.append(f"  • 予想が僅差のため、複数の馬での馬券購入を推奨")
            elif score_std > 0.3:
                report_lines.append(f"  • 明確な実力差があり、上位馬重視の戦略を推奨")
            
            accuracy = quality.get('accuracy_metrics', {})
            if accuracy and accuracy.get('ndcg_5', 0) > 0.8:
                report_lines.append(f"  • 高精度予測のため、予想通りの結果の可能性が高い")
        
        # 特徴量に基づく推奨
        if feature_analysis:
            top_features = feature_analysis.get('top_positive_features', [])
            if top_features:
                top_feature = top_features[0]
                feature_name = self._translate_feature_name(top_feature['feature'])
                report_lines.append(f"  • {feature_name}が重要要因：この点を重視して判断")
        
        # 信頼度に基づく推奨
        if confidence_analysis:
            risk_assessment = confidence_analysis.get('risk_assessment', {})
            overall_certainty = risk_assessment.get('overall_certainty', 'medium')
            
            if overall_certainty == 'high':
                report_lines.append(f"  • 高い予測信頼度：積極的な投資戦略を推奨")
            elif overall_certainty == 'medium':
                report_lines.append(f"  • 中程度の信頼度：堅実な馬券戦略を推奨")
            else:
                report_lines.append(f"  • 予測不確実性が高い：少額での様子見を推奨")
        
        report_lines.append(f"\n" + "=" * 80)
        report_lines.append("TensorFlow Ranking + Optuna 総合分析完了")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
    
    def save_analysis_results(self, 
                            ranking_analysis: Dict[str, Any],
                            optuna_analysis: Dict[str, Any] = None,
                            visualizations: Dict[str, str] = None) -> str:
        """分析結果の保存"""
        
        timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
        
        # JSON形式で詳細データ保存
        analysis_data = {
            'timestamp': timestamp,
            'ranking_analysis': ranking_analysis,
            'optuna_analysis': optuna_analysis or {},
            'visualizations': visualizations or {}
        }
        
        json_file = self.output_dir / f"tfr_analysis_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False, default=str)
        
        # レポート保存
        report = self.generate_comprehensive_report(ranking_analysis, optuna_analysis)
        report_file = self.output_dir / f"tfr_analysis_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info(f"分析結果保存完了:")
        self.logger.info(f"  詳細データ: {json_file}")
        self.logger.info(f"  レポート: {report_file}")
        
        return str(report_file)