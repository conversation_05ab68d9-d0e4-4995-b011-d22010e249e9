# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.
日本語で答える。
絵文字は必ず使わない。

## Important Instructions

- **Language**: Always respond in Japanese (日本語で答える)
- **Emoji Policy**: Never use emojis in code or text output (絵文字は必ず使わない)
- **Main Entry Point**: Always recommend `keiba_ai_main.py` for new users - it provides guided menu access to all functionality
- **Ultimate Live Predictor**: For race prediction, recommend the Ultimate Live Predictor as the most advanced option

## Project Overview

This is a comprehensive horse racing prediction system (`keiba_ai_system`) that integrates data scraping, processing, feature engineering, and machine learning models for predicting race outcomes. The system processes data from Japanese horse racing (netkeiba) and is primarily documented in Japanese.

**Key Technologies**: Python, LightGBM, TensorFlow Ranking, Selenium, Optuna, Pandas, Scikit-learn

## Important Notes

- **Data Leakage Prevention**: The system has built-in protection against data leakage with `leakage_risk_columns` in training_config.yaml
- **Japanese Documentation**: Most code comments and documentation are in Japanese
- **Large Dataset**: Project contains ~22万files (19.5GB), primarily HTML scraped data and processed pickles

## Environment Setup

```bash
# Activate virtual environment (Windows)
.\venv\Scripts\activate
# or call activate_env.bat

# Install dependencies
pip install -r requirements.txt
```

## Quick Start - Unified Entry Point

The system provides a unified entry point for all major functionality through keiba_ai_main.py:

```bash
# Launch integrated menu system (RECOMMENDED)
python keiba_ai_main.py

# Or use batch files for easy startup
start_keiba_ai.bat          # Windows batch
start_keiba_ai.ps1          # PowerShell

# Quick one-command operations
python quick_predict.py 202406010101           # Quick prediction
python quick_train.py --enhanced               # Quick training

# Ultimate Live Predictor (最強統合版)
python ultimate_live_predictor.py             # TFR + Real-time scraping

# Automated execution mode
python keiba_ai_main.py --auto train_simple
python keiba_ai_main.py --auto scraping
```

## Running Tests

```bash
# Run specific test file 
python -m pytest core/processors/test_comprehensive_integrator.py -v

# Run all tests (if pytest is properly configured)
python -m pytest -v

# Run individual test functions
python -m pytest core/processors/test_comprehensive_integrator.py::test_function_name -v
```

## Code Quality Tools

The project includes these tools in requirements.txt:
```bash
# Code formatting
black .

# Linting
flake8 .

# Type checking
mypy .

# Import sorting
isort .
```

## Common Commands

### Model Training
```bash
# Simple training (basic features)
python simple_train.py

# Enhanced training (with feature engineering)
python enhanced_train.py

# Quick enhanced training
python quick_enhanced_train.py

# Age-enhanced training
python age_enhanced_train.py

# Multi-year integrated training (via keiba_ai_main.py)
python keiba_ai_main.py
# Then select: 2 (モデル訓練) → 5 (複数年統合訓練)
# Supports 2008-2024 data with 4 training modes
```

### Data Scraping
```bash
# Run netkeiba scraping tool
python netkeiba_scraping_tool.py
```

### Live Race Prediction
```bash
# Ultimate Live Predictor (最強統合版) - RECOMMENDED
python ultimate_live_predictor.py
# Features: Real-time data scraping + Selenium + BAN protection + TFR integration

# Via main menu system (easiest)
python keiba_ai_main.py
# Then select: 3 (リアルタイム予測) → 1 (Ultimate Live Predictor)

# Traditional predictors
python -m prediction.live_predictor RACE_ID --model MODEL_PATH --scaler SCALER_PATH
python safe_live_predictor.py           # BAN-safe version
python enhanced_live_predictor.py       # Selenium integrated
python improved_live_predictor.py       # Enhanced features

# Example with specific model:
python -m prediction.live_predictor 202406010101 --model models/lgb_model.pkl --scaler models/scaler.pkl
```

### Data Processing
```python
# Using the comprehensive data integrator
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator

integrator = ComprehensiveDataIntegrator()
data = integrator.generate_comprehensive_table(
    year="2024",
    include_race_info=True,
    include_horse_info=True,
    include_past_performance=True
)
```

## Architecture

### Core Design Principles

- **Modular Architecture**: Separated concerns across scraping, processing, feature engineering, and prediction
- **Configuration-Driven**: YAML-based configs for training parameters, feature engineering, and data processing
- **Data Mode Flexibility**: Supports both HTML parsing (from scraped files) and pickle-based processing for performance
- **Type Safety**: Extensive use of type hints and dataclasses for configuration management

### Core Components

1. **Data Scrapers** (`core/scrapers/`)
   - `scraper.py`: Main scraping functions for netkeiba website with anti-detection measures
   - Extracts race data, horse information, and past performance using Selenium + BeautifulSoup
   - Built-in retry logic and user-agent rotation from `core/utils/constants.py`

2. **Data Processors** (`core/processors/`)
   - `comprehensive_integrator.py`: **Central integration engine** - orchestrates all data merging
   - `race_processor.py`: HTML parsing for race information and results
   - `horse_processor.py`: Horse information and past performance extraction
   - `race_feature_engineer.py`: Race-specific feature engineering
   - `corner_analyzer.py`: Corner position analysis for racing strategy features

3. **Feature Engineering** (`core/features/`)
   - `manager.py`: **`FeatureEngineeringManager`** - centralized feature calculation orchestrator
   - `definitions.py`: Structured feature categories with priority-based loading
   - `calculators.py`: Actual feature calculation implementations
   - `config.yaml`: **Comprehensive feature configuration** with 7 main categories and 268 lines of settings

4. **Prediction Systems** (`prediction/`)
   - `live_predictor.py`: Real-time race prediction with live web scraping
   - `race_predictor.py`: TensorFlow Ranking-based prediction system for ranking horses

5. **Unified Entry Point** 
   - `keiba_ai_main.py`: **Main system orchestrator** - provides menu-driven access to all functionality
   - `ultimate_live_predictor.py`: **Ultimate prediction system** - TFR + real-time scraping integration
   - `quick_predict.py`: One-command race prediction
   - `quick_train.py`: One-command model training

### Configuration Architecture

The system uses a sophisticated configuration hierarchy:

- **`ComprehensiveIntegratorConfig`** (dataclass): Main data processing configuration
- **`training_config.yaml`**: Model training parameters and data leakage protection
- **`core/features/config.yaml`**: Feature engineering settings with 7 categories
- **`core/utils/constants.py`**: System constants, URL patterns, and dataclass definitions

### Data Flow

1. **Scraping**: `netkeiba_scraping_tool.py` → HTML files in `data/html/` (22万+ files)
2. **Processing**: HTML → structured pandas DataFrames via `RaceProcessor` and `HorseProcessor`
3. **Integration**: `ComprehensiveDataIntegrator` → unified dataset combining race info, results, horse data
4. **Feature Engineering**: `FeatureEngineeringManager` → enhanced features across 7 categories
5. **Training**: LightGBM/TensorFlow models with data leakage protection
6. **Prediction**: `LiveRacePredictor` → real-time race outcome predictions

### Data Processing Modes

- **HTML Mode** (default): Processes from scraped HTML files in `data/html/`
- **Pickle Mode**: Uses pre-processed pickle files from `output/` (faster, use `use_pickle_source=True`)
- **Live Mode**: Real-time scraping and prediction for current races

### Key Data Structures

The system defines comprehensive dataclasses for type safety:
- **`ResultsCols`**: Race result table column mappings
- **`RaceInfoCols`**: Race information table columns  
- **`HorseInfoCols`**: Horse basic information columns
- **`HorseResultsCols`**: Horse past performance columns

## Configuration System

### Main Configuration Files

- **`training_config.yaml`**: Model training parameters, LightGBM config, data leakage protection
- **`core/features/config.yaml`**: 268-line comprehensive feature engineering configuration
  - 7 feature categories: basic, performance, pedigree, jockey, trainer, race_condition, age_features, advanced
  - Performance features: lookback_races, statistics calculations, weight decay
  - Age features: days_old, category thresholds, interaction features
- **`ComprehensiveIntegratorConfig`** (dataclass): Data processing configuration with pickle/HTML mode switching

### Key Configuration Patterns

```python
# Enable pickle mode for faster processing
config = ComprehensiveIntegratorConfig(
    use_pickle_source=True,
    include_corner_features=True,
    parallel=True,
    max_workers=4
)

# Feature engineering categories can be enabled/disabled
feature_groups:
  basic: {enabled: true, priority: 1}
  performance: {enabled: true, priority: 2}
  advanced: {enabled: false, priority: 10}
```

## Key Classes and Integration Points

### Data Integration Layer
- **`ComprehensiveDataIntegrator`**: Main orchestrator for all data merging operations
- **`RaceProcessor`**: Processes race information from HTML with robust CSS selectors
- **`HorseProcessor`**: Extracts horse information and past performance data

### Feature Engineering Layer  
- **`FeatureEngineeringManager`**: Central coordinator for feature calculation across categories
- **`FeatureCalculators`**: Implementation of actual feature calculation functions
- **`FeatureDefinition`**: Type-safe feature definitions with categories and priorities

### Prediction Layer
- **`LiveRacePredictor`**: Real-time prediction with web scraping integration
- **Various Training Scripts**: Multiple specialized training approaches (simple, enhanced, quick, age-based)

## Development Patterns

### Modular Training Scripts
The project contains multiple training scripts for different approaches:
- `simple_train.py`: Basic feature set
- `enhanced_train.py`: Full feature engineering  
- `quick_enhanced_train.py`: Optimized for speed
- `age_enhanced_train.py`: Age-focused features

Choose the appropriate script based on your needs and available processing time.

### Data Processing Strategy
- **HTML First**: Default mode processes from scraped HTML for maximum data freshness
- **Pickle Fallback**: Use `use_pickle_source=True` for faster iteration during development
- **Parallel Processing**: Enable with `parallel=True` and `max_workers` configuration

### Anti-Detection Measures
The scraping system includes sophisticated anti-detection:
- User-agent rotation from predefined list
- Request delays with randomization
- Retry logic with exponential backoff
- Headers that mimic real browser requests

## Development Notes

- **Japanese-First Codebase**: All documentation, comments, and configurations are in Japanese
- **Type Safety**: Extensive use of dataclasses and type hints throughout
- **Configuration-Driven Development**: Modify behavior through YAML configs rather than code changes
- **Multi-Year Support**: System designed to handle data across multiple racing seasons (2008-2024)
- **Performance Optimization**: Built-in parallel processing and caching mechanisms

## Advanced Features

### Ultimate Live Predictor (最強統合版)
The `ultimate_live_predictor.py` represents the pinnacle of the prediction system:

```python
from ultimate_live_predictor import UltimateLiveRacePredictor

# Initialize with full capabilities
predictor = UltimateLiveRacePredictor(
    use_selenium=True,           # Selenium integration
    enable_live_scraping=True,   # Real-time data acquisition
    enable_tfr=True,            # TensorFlow Ranking
    max_ban_protection=True     # Maximum BAN protection
)

# Execute ultimate prediction
results, race_info = predictor.predict_race_ultimate(race_id)
```

**Key Features:**
- **Real-time Data Acquisition**: Live scraping of latest horse performance data
- **Selenium Integration**: Advanced web scraping with browser automation
- **BAN Protection**: Sophisticated anti-detection measures with user-agent rotation
- **TensorFlow Ranking Integration**: Hybrid LightGBM + TFR prediction system
- **13 Types of High-Quality Features**: Automatically generated from live data

### Multi-Year Training System
The system supports training on massive datasets spanning multiple years:

**Available Data Range**: 2008-2024 (17 years, 200K+ races)

**Training Modes:**
1. **Enhanced Mode**: Past performance + advanced features (high accuracy)
2. **Fast Mode**: Basic features only (quick training)
3. **Age-focused Mode**: Age-related feature specialization
4. **Experimental Mode**: All available features

**Execution:**
```bash
python keiba_ai_main.py
# Select: 2 (モデル訓練) → 5 (複数年統合訓練)
# Choose year range and training mode
# Example result: 236K training samples, 78% accuracy
```

### Data Leakage Prevention
Critical safeguards against temporal data leakage:

- **`leakage_risk_columns`** in `training_config.yaml`: Automatic detection of risky features
- **Temporal validation**: Ensures past-only data in feature calculation
- **Date filtering**: Automatic exclusion of future information
- **Validation checks**: Built-in data integrity verification

### Character Encoding Handling
For Windows development environments:

```python
# Safe character encoding (避ける絵文字使用)
print("[競馬AI] 学習完了")  # Good
print("🐎 競馬AI学習完了")   # May cause cp932 encoding errors
```