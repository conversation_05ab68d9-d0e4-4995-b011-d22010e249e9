2025-06-09 14:37:24,845 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-09 14:37:24,846 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-09 14:42:12,057 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-09 14:42:12,058 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-09 14:42:22,049 - KeibaAI - INFO - 🚀 拡張モデル訓練開始...
2025-06-09 14:42:23,691 - KeibaAI - ERROR - ❌ 訓練エラー: 2025-06-09 14:42:23,508 - INFO - レースデータ読み込み完了: 48282件
2025-06-09 14:42:23,508 - INFO - 馬戦績データ読み込み完了: 336142件
2025-06-09 14:42:23,512 - INFO - 馬の過去戦績統計を計算中...
H:\AI\keiba_ai_system\enhanced_train.py:60: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  race_data['date'] = pd.to_datetime(race_data['date'])
2025-06-09 14:42:23,516 - ERROR - 学習中にエラー: Unknown datetime string format, unable to parse: 2020年7月25日, at position 0
Traceback (most recent call last):
  File "H:\AI\keiba_ai_system\enhanced_train.py", line 444, in <module>
    main()
  File "H:\AI\keiba_ai_system\enhanced_train.py", line 402, in main
    enhanced_data = calculate_past_performance(race_data, horse_results)
  File "H:\AI\keiba_ai_system\enhanced_train.py", line 60, in calculate_past_performance
    race_data['date'] = pd.to_datetime(race_data['date'])
  File "H:\AI\keiba_ai_system\venv\lib\site-packages\pandas\core\tools\datetimes.py", line 1063, in to_datetime
    cache_array = _maybe_cache(arg, format, cache, convert_listlike)
  File "H:\AI\keiba_ai_system\venv\lib\site-packages\pandas\core\tools\datetimes.py", line 247, in _maybe_cache
    cache_dates = convert_listlike(unique_dates, format)
  File "H:\AI\keiba_ai_system\venv\lib\site-packages\pandas\core\tools\datetimes.py", line 435, in _convert_listlike_datetimes
    result, tz_parsed = objects_to_datetime64(
  File "H:\AI\keiba_ai_system\venv\lib\site-packages\pandas\core\arrays\datetimes.py", line 2398, in objects_to_datetime64
    result, tz_parsed = tslib.array_to_datetime(
  File "tslib.pyx", line 414, in pandas._libs.tslib.array_to_datetime
  File "tslib.pyx", line 596, in pandas._libs.tslib.array_to_datetime
  File "tslib.pyx", line 553, in pandas._libs.tslib.array_to_datetime
  File "conversion.pyx", line 641, in pandas._libs.tslibs.conversion.convert_str_to_tsobject
  File "parsing.pyx", line 336, in pandas._libs.tslibs.parsing.parse_datetime_string
  File "parsing.pyx", line 666, in pandas._libs.tslibs.parsing.dateutil_parse
pandas._libs.tslibs.parsing.DateParseError: Unknown datetime string format, unable to parse: 2020年7月25日, at position 0

2025-06-09 14:44:20,812 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-09 14:44:20,813 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-09 14:44:24,674 - KeibaAI - INFO - 🚀 拡張モデル訓練開始...
2025-06-09 14:44:54,087 - KeibaAI - ERROR - ❌ 訓練エラー: 2025-06-09 14:44:26,111 - INFO - レースデータ読み込み完了: 48282件
2025-06-09 14:44:26,111 - INFO - 馬戦績データ読み込み完了: 336142件
2025-06-09 14:44:26,116 - INFO - 馬の過去戦績統計を計算中...
2025-06-09 14:44:26,446 - INFO - 過去戦績計算進捗: 0/11702 (0.0%)
2025-06-09 14:44:28,784 - INFO - 過去戦績計算進捗: 1000/11702 (8.5%)
2025-06-09 14:44:31,121 - INFO - 過去戦績計算進捗: 2000/11702 (17.1%)
2025-06-09 14:44:33,359 - INFO - 過去戦績計算進捗: 3000/11702 (25.6%)
2025-06-09 14:44:35,611 - INFO - 過去戦績計算進捗: 4000/11702 (34.2%)
2025-06-09 14:44:37,867 - INFO - 過去戦績計算進捗: 5000/11702 (42.7%)
2025-06-09 14:44:40,211 - INFO - 過去戦績計算進捗: 6000/11702 (51.3%)
2025-06-09 14:44:42,467 - INFO - 過去戦績計算進捗: 7000/11702 (59.8%)
2025-06-09 14:44:44,703 - INFO - 過去戦績計算進捗: 8000/11702 (68.4%)
2025-06-09 14:44:47,037 - INFO - 過去戦績計算進捗: 9000/11702 (76.9%)
2025-06-09 14:44:49,395 - INFO - 過去戦績計算進捗: 10000/11702 (85.5%)
2025-06-09 14:44:51,851 - INFO - 過去戦績計算進捗: 11000/11702 (94.0%)
2025-06-09 14:44:53,379 - INFO - === 過去戦績統計サマリー ===
2025-06-09 14:44:53,379 - INFO - 過去レース数 - 平均: 0.0, 最大: 0.0
2025-06-09 14:44:53,379 - INFO - 勝率 - 平均: 0.000
2025-06-09 14:44:53,379 - INFO - 3着以内率 - 平均: 0.000
2025-06-09 14:44:53,379 - INFO - 平均着順 - 全体平均: 0.00
2025-06-09 14:44:53,379 - INFO - 特徴量準備開始（過去戦績込み）
2025-06-09 14:44:53,496 - INFO - 特徴量数: 28
2025-06-09 14:44:53,496 - INFO - 基本特徴量: ['course_len', '枠番', '馬番', '斤量', 'days_since_last_race', 'race_type_encoded', 'ground_state_encoded', 'weather_encoded', 'track_direction_encoded', '年齢', '性別_牡', '性別_牝', '距離_短距離', '距離_マイル', '距離_中距離', '距離_長距離', '経験豊富', '好調', '連続出走']
2025-06-09 14:44:53,496 - INFO - 過去戦績特徴量: ['past_races_count', 'past_win_rate', 'past_top3_rate', 'past_avg_rank', 'past_std_rank', 'past_distance_similar_races', 'past_distance_win_rate', 'past_surface_races', 'past_surface_win_rate']
2025-06-09 14:44:53,496 - INFO - データ形状: X=(47876, 28), y=(47876,)
2025-06-09 14:44:53,496 - INFO - 正例率: 0.217
2025-06-09 14:44:53,496 - INFO - モデル学習開始
2025-06-09 14:44:53,865 - INFO - === 学習結果（過去戦績込み） ===
2025-06-09 14:44:53,865 - INFO - 精度: 0.7832
2025-06-09 14:44:53,865 - INFO - AUC: 0.5808
2025-06-09 14:44:53,865 - INFO - 適中率@10%: 0.3260
2025-06-09 14:44:53,865 - INFO - 適中率@20%: 0.3044
2025-06-09 14:44:53,865 - INFO - 学習データ: 38300件
2025-06-09 14:44:53,865 - INFO - テストデータ: 9576件
2025-06-09 14:44:53,873 - INFO - 強化モデル保存完了:
2025-06-09 14:44:53,873 - INFO -   モデル: models\enhanced_lgb_model_20250609_144453.pkl
2025-06-09 14:44:53,874 - INFO -   スケーラー: models\enhanced_scaler_20250609_144453.pkl
2025-06-09 14:44:53,874 - INFO -   特徴量: models\enhanced_features_20250609_144453.pkl
2025-06-09 14:44:53,874 - ERROR - 学習中にエラー: 'cp932' codec can't encode character '\U0001f40e' in position 0: illegal multibyte sequence
Traceback (most recent call last):
  File "H:\AI\keiba_ai_system\enhanced_train.py", line 452, in <module>
    main()
  File "H:\AI\keiba_ai_system\enhanced_train.py", line 422, in main
    print("\U0001f40e 競馬AI学習完了（過去戦績強化版）!")
UnicodeEncodeError: 'cp932' codec can't encode character '\U0001f40e' in position 0: illegal multibyte sequence

2025-06-09 14:49:10,320 - KeibaAI - INFO - 📈 包括的データ統合開始...
2025-06-09 22:04:21,340 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-09 22:04:21,344 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-09 22:04:21,344 - KeibaAI - INFO - 🧪 改善版モデルテスト開始...
2025-06-09 22:04:21,344 - KeibaAI - ERROR - ❌ 改善版モデルテストエラー: EOF when reading a line
2025-06-09 22:05:19,005 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-09 22:05:19,006 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-09 22:28:17,342 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-09 22:28:17,346 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-09 22:33:36,322 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-09 22:33:36,326 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-09 22:39:35,854 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-09 22:39:35,858 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-09 23:46:41,410 - KeibaAI - INFO - 🏇 競馬AI予測システム初期化中...
2025-06-09 23:46:41,411 - KeibaAI - INFO - ✅ システム初期化完了
2025-06-09 23:47:12,136 - KeibaAI - INFO - 🚀 レース202505021211の改善版ライブ予測開始...
