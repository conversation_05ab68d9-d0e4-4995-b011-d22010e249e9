#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
データリーケージ検証テストスクリプト

特徴量計算でデータリーケージが発生していないことを確認するテスト
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# テスト用のサンプルデータを作成
def create_test_data():
    """テスト用のサンプルデータを作成"""
    
    # レース結果データ（過去データ）
    horse_results = []
    base_date = datetime(2024, 1, 1)
    
    # 馬1の過去レース
    for i in range(10):
        race_date = base_date + timedelta(days=i*30)
        horse_results.append({
            'horse_id': 'horse_001',
            'jockey_id': 'jockey_001',
            'trainer_id': 'trainer_001',
            '日付': race_date,
            '着順': np.random.randint(1, 10),
            '賞金': np.random.randint(0, 1000) * 10000
        })
    
    # 馬2の過去レース
    for i in range(8):
        race_date = base_date + timedelta(days=i*30 + 15)
        horse_results.append({
            'horse_id': 'horse_002',
            'jockey_id': 'jockey_002',
            'trainer_id': 'trainer_001',
            '日付': race_date,
            '着順': np.random.randint(1, 10),
            '賞金': np.random.randint(0, 500) * 10000
        })
    
    horse_results_df = pd.DataFrame(horse_results)
    
    # 予測対象レースデータ
    target_date = datetime(2024, 6, 1)
    target_races = pd.DataFrame([
        {
            'horse_id': 'horse_001',
            'jockey_id': 'jockey_001',
            'trainer_id': 'trainer_001',
            'date': target_date,
            '馬番': 1,
            '枠番': 1,
            '性齢': '牡3',
            '斤量': 56.0,
            '人気': 1
        },
        {
            'horse_id': 'horse_002',
            'jockey_id': 'jockey_002',
            'trainer_id': 'trainer_001',
            'date': target_date,
            '馬番': 2,
            '枠番': 1,
            '性齢': '牝3',
            '斤量': 54.0,
            '人気': 2
        }
    ])
    
    # 未来のレースデータ（リーケージテスト用）
    future_date = datetime(2024, 7, 1)
    future_results = pd.DataFrame([
        {
            'horse_id': 'horse_001',
            'jockey_id': 'jockey_001',
            'trainer_id': 'trainer_001',
            '日付': future_date,
            '着順': 1,  # 未来の1着
            '賞金': 5000000
        },
        {
            'horse_id': 'horse_002',
            'jockey_id': 'jockey_002',
            'trainer_id': 'trainer_001',
            '日付': future_date,
            '着順': 2,
            '賞金': 2000000
        }
    ])
    
    return horse_results_df, target_races, future_results


def test_data_leakage_original():
    """元の実装でのデータリーケージをテスト"""
    print("=" * 60)
    print("元の実装でのデータリーケージテスト")
    print("=" * 60)
    
    # テストデータを作成
    horse_results_df, target_races, future_results = create_test_data()
    
    # 未来のデータを含めた全データ
    all_results = pd.concat([horse_results_df, future_results], ignore_index=True)
    
    # 元の実装をシミュレート（全データで集計）
    print("\n1. 全データでの勝率計算（データリーケージあり）:")
    for horse_id in ['horse_001', 'horse_002']:
        horse_data = all_results[all_results['horse_id'] == horse_id]
        wins = (horse_data['着順'] == 1).sum()
        total = len(horse_data)
        win_rate = wins / total if total > 0 else 0
        print(f"  {horse_id}: {wins}勝/{total}戦 = {win_rate:.2%}")
        
        # 未来のレースが含まれているかチェック
        future_races = horse_data[horse_data['日付'] >= target_races['date'].iloc[0]]
        if len(future_races) > 0:
            print(f"    ⚠️ 警告: 未来のレース{len(future_races)}件が含まれています！")
    
    print("\n2. 予測時点（2024-06-01）での正しい勝率:")
    for horse_id in ['horse_001', 'horse_002']:
        # 予測時点より前のデータのみ
        past_data = horse_results_df[
            (horse_results_df['horse_id'] == horse_id) & 
            (horse_results_df['日付'] < target_races['date'].iloc[0])
        ]
        wins = (past_data['着順'] == 1).sum()
        total = len(past_data)
        win_rate = wins / total if total > 0 else 0
        print(f"  {horse_id}: {wins}勝/{total}戦 = {win_rate:.2%}")


def test_data_leakage_fixed():
    """修正版実装でのデータリーケージ防止をテスト"""
    print("\n" + "=" * 60)
    print("修正版実装でのデータリーケージ防止テスト")
    print("=" * 60)
    
    # パスを追加
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from calculators_fixed import FeatureCalculatorsFixed
        calc_fixed = FeatureCalculatorsFixed()
        
        # テストデータを作成
        horse_results_df, target_races, future_results = create_test_data()
        all_results = pd.concat([horse_results_df, future_results], ignore_index=True)
        
        print("\n修正版での勝率計算:")
        
        # 修正版で勝率を計算
        win_rates = calc_fixed.calculate_win_rate(
            target_races, 
            all_results,  # 未来データを含む全データを渡す
            column='horse_id',
            race_date_column='date'
        )
        
        for idx, (horse_id, win_rate) in enumerate(zip(target_races['horse_id'], win_rates)):
            print(f"  {horse_id}: 勝率 = {win_rate:.2%}")
            
            # 使用されたデータを確認
            filtered_data = calc_fixed.filter_past_data(
                all_results,
                target_races.iloc[idx]['date'],
                horse_id=horse_id,
                exclude_current_date=True
            )
            
            print(f"    使用データ件数: {len(filtered_data)}")
            if len(filtered_data) > 0:
                print(f"    最新レース日: {filtered_data['日付'].max()}")
                print(f"    予測日: {target_races.iloc[idx]['date']}")
                
                # 未来データが含まれていないことを確認
                if filtered_data['日付'].max() >= target_races.iloc[idx]['date']:
                    print("    ❌ エラー: 未来のデータが含まれています！")
                else:
                    print("    ✅ OK: 過去データのみ使用")
                    
    except ImportError:
        print("⚠️ 修正版のcalculators_fixed.pyが見つかりません")


def test_edge_cases():
    """エッジケースのテスト"""
    print("\n" + "=" * 60)
    print("エッジケースのテスト")
    print("=" * 60)
    
    # 1. 同日に複数レースがある場合
    print("\n1. 同日複数レースのケース:")
    same_day_results = pd.DataFrame([
        {'horse_id': 'horse_001', '日付': datetime(2024, 1, 1, 10, 0), '着順': 1},
        {'horse_id': 'horse_001', '日付': datetime(2024, 1, 1, 14, 0), '着順': 3},
        {'horse_id': 'horse_001', '日付': datetime(2024, 1, 1, 16, 0), '着順': 2},
    ])
    
    print(f"  同日のレース数: {len(same_day_results)}")
    print("  ⚠️ 注意: 同日の他レースを除外する必要があります")
    
    # 2. データが存在しない馬
    print("\n2. 過去データがない馬のケース:")
    print("  新馬の場合、勝率 = 0.0 となるべき")
    
    # 3. 日付形式の異なるデータ
    print("\n3. 日付形式の違い:")
    date_formats = [
        "2024-01-01",
        "2024/01/01", 
        "2024年1月1日",
        "20240101"
    ]
    print("  以下の形式に対応する必要があります:")
    for fmt in date_formats:
        print(f"    - {fmt}")


def generate_validation_report():
    """検証レポートの生成"""
    print("\n" + "=" * 60)
    print("データリーケージ検証サマリー")
    print("=" * 60)
    
    issues = [
        {
            'severity': '🔴 重大',
            'issue': '過去成績の集計で未来データを使用',
            'impact': 'モデル性能の過大評価',
            'status': '修正版で対応済み'
        },
        {
            'severity': '🟡 中程度',
            'issue': '同日複数レースの処理',
            'impact': '特徴量の不正確さ',
            'status': '要追加対応'
        },
        {
            'severity': '🟢 軽微',
            'issue': '日付形式の不統一',
            'impact': 'データ処理エラー',
            'status': '変換関数で対応可能'
        }
    ]
    
    print("\n発見された問題:")
    for issue in issues:
        print(f"\n{issue['severity']} {issue['issue']}")
        print(f"  影響: {issue['impact']}")
        print(f"  状態: {issue['status']}")
    
    print("\n推奨アクション:")
    print("1. calculators.pyをcalculators_fixed.pyに置き換える")
    print("2. 全ての特徴量計算関数でrace_dateパラメータを必須にする")
    print("3. 既存モデルの再学習を実施する")
    print("4. CI/CDパイプラインにデータリーケージチェックを追加する")


if __name__ == "__main__":
    # 各テストを実行
    test_data_leakage_original()
    test_data_leakage_fixed()
    test_edge_cases()
    generate_validation_report()
