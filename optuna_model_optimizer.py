#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna ハイパーパラメータ最適化システム
競馬予想モデルの性能を最大化するための自動最適化
"""

import sys
sys.path.append('.')

import pandas as pd
import numpy as np
import optuna
import lightgbm as lgb
from sklearn.model_selection import KFold, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import roc_auc_score, accuracy_score, log_loss
import pickle
import logging
import warnings
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Tuple
import json

warnings.filterwarnings('ignore')

# 既存のデータ処理クラス
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
from core.features.manager import FeatureEngineeringManager

class OptunaModelOptimizer:
    """Optunaを使用したモデル最適化クラス"""
    
    def __init__(self, 
                 data_years: List[str] = ["2020"],
                 output_dir: str = "optuna_results",
                 n_trials: int = 100,
                 random_seed: int = 42):
        """
        初期化
        
        Parameters
        ----------
        data_years : List[str]
            使用するデータの年
        output_dir : str
            結果出力ディレクトリ
        n_trials : int
            最適化試行回数
        random_seed : int
            乱数シード
        """
        self.data_years = data_years
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.n_trials = n_trials
        self.random_seed = random_seed
        
        # ログ設定
        self.logger = self._setup_logging()
        
        # データとモデル用の変数
        self.X_train = None
        self.y_train = None
        self.feature_names = None
        self.label_encoders = {}
        self.scaler = None
        
        # 最適化結果
        self.best_params = None
        self.best_score = None
        self.optimization_history = []
        
    def _setup_logging(self) -> logging.Logger:
        """ログ設定"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        # ファイルハンドラー
        log_file = self.output_dir / f"optuna_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # フォーマッター
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        if not logger.handlers:
            logger.addHandler(file_handler)
        
        return logger
    
    def load_and_prepare_data(self) -> bool:
        """データの読み込みと前処理"""
        
        try:
            self.logger.info("データ読み込み開始")
            
            # データ統合
            integrator = ComprehensiveDataIntegrator()
            
            # 複数年のデータを統合
            all_data = []
            for year in self.data_years:
                self.logger.info(f"{year}年のデータを読み込み中...")
                yearly_data = integrator.generate_comprehensive_table(
                    year=year,
                    include_race_info=True,
                    include_horse_info=True,
                    include_past_performance=True
                )
                
                if not yearly_data.empty:
                    all_data.append(yearly_data)
                    self.logger.info(f"{year}年: {len(yearly_data)}件のデータ")
                else:
                    self.logger.warning(f"{year}年: データが空です")
            
            if not all_data:
                self.logger.error("使用可能なデータがありません")
                return False
            
            # データ結合
            combined_data = pd.concat(all_data, ignore_index=True)
            self.logger.info(f"統合データ: {len(combined_data)}件")
            
            # 特徴量エンジニアリング
            self.logger.info("特徴量エンジニアリング実行中...")
            feature_manager = FeatureEngineeringManager()
            
            # データリーケージ防止のための日付フィルタリング
            if 'date' in combined_data.columns:
                combined_data = combined_data.sort_values('date')
            
            # 特徴量生成（データリーケージ防止版）
            enhanced_data = feature_manager.engineer_features(
                combined_data,
                race_date_column='date' if 'date' in combined_data.columns else None
            )
            
            self.logger.info(f"特徴量エンジニアリング完了: {enhanced_data.shape}")
            
            # ターゲット変数の準備
            if '着順' in enhanced_data.columns:
                # 3着以内を1、それ以外を0とする二値分類
                enhanced_data['target'] = (enhanced_data['着順'] <= 3).astype(int)
            else:
                self.logger.error("着順カラムが見つかりません")
                return False
            
            # 不要なカラムを除外
            exclude_cols = ['着順', 'date', 'race_id', 'horse_id', '馬名', 'レース名']
            feature_cols = [col for col in enhanced_data.columns 
                          if col not in exclude_cols + ['target']]
            
            # 特徴量とターゲットの分離
            X = enhanced_data[feature_cols].copy()
            y = enhanced_data['target'].copy()
            
            # カテゴリカル変数のエンコーディング
            categorical_cols = X.select_dtypes(include=['object']).columns
            for col in categorical_cols:
                self.label_encoders[col] = LabelEncoder()
                X[col] = self.label_encoders[col].fit_transform(X[col].astype(str))
            
            # 無限値とNaNの処理
            X = X.replace([np.inf, -np.inf], np.nan)
            X = X.fillna(X.median())
            
            # 高カーディナリティ特徴量の除外
            high_cardinality_cols = []
            for col in X.columns:
                if X[col].nunique() > 100:
                    high_cardinality_cols.append(col)
            
            if high_cardinality_cols:
                X = X.drop(columns=high_cardinality_cols)
                self.logger.info(f"高カーディナリティ特徴量を除外: {high_cardinality_cols}")
            
            # スケーリング
            self.scaler = StandardScaler()
            X_scaled = self.scaler.fit_transform(X)
            
            # 最終データの保存
            self.X_train = pd.DataFrame(X_scaled, columns=X.columns)
            self.y_train = y
            self.feature_names = list(X.columns)
            
            self.logger.info(f"データ準備完了:")
            self.logger.info(f"  特徴量数: {len(self.feature_names)}")
            self.logger.info(f"  サンプル数: {len(self.X_train)}")
            self.logger.info(f"  正例率: {self.y_train.mean():.3f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"データ準備エラー: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def objective(self, trial) -> float:
        """
        Optuna最適化の目的関数
        
        Parameters
        ----------
        trial : optuna.Trial
            Optunaの試行オブジェクト
            
        Returns
        -------
        float
            最適化スコア（AUC）
        """
        
        # LightGBMハイパーパラメータの提案
        params = {
            'objective': 'binary',
            'metric': 'auc',
            'boosting_type': 'gbdt',
            'num_leaves': trial.suggest_int('num_leaves', 10, 100),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
            'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
            'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
            'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
            'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
            'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 10.0, log=True),
            'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 10.0, log=True),
            'random_state': self.random_seed,
            'verbosity': -1
        }
        
        # クロスバリデーション
        kfold = KFold(n_splits=5, shuffle=True, random_state=self.random_seed)
        scores = []
        
        for train_idx, val_idx in kfold.split(self.X_train):
            X_train_fold = self.X_train.iloc[train_idx]
            X_val_fold = self.X_train.iloc[val_idx]
            y_train_fold = self.y_train.iloc[train_idx]
            y_val_fold = self.y_train.iloc[val_idx]
            
            # LightGBMデータセット作成
            train_data = lgb.Dataset(X_train_fold, label=y_train_fold)
            val_data = lgb.Dataset(X_val_fold, label=y_val_fold, reference=train_data)
            
            # モデル訓練
            model = lgb.train(
                params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=1000,
                callbacks=[
                    lgb.early_stopping(stopping_rounds=50),
                    lgb.log_evaluation(0)  # ログ出力を抑制
                ]
            )
            
            # 予測とスコア計算
            y_pred = model.predict(X_val_fold, num_iteration=model.best_iteration)
            score = roc_auc_score(y_val_fold, y_pred)
            scores.append(score)
        
        cv_score = np.mean(scores)
        
        # 最適化履歴の記録
        self.optimization_history.append({
            'trial_number': trial.number,
            'params': params,
            'cv_score': cv_score,
            'cv_std': np.std(scores)
        })
        
        # 進捗ログ
        if trial.number % 10 == 0:
            self.logger.info(f"Trial {trial.number}: AUC = {cv_score:.4f} (±{np.std(scores):.4f})")
        
        return cv_score
    
    def optimize_hyperparameters(self) -> bool:
        """ハイパーパラメータ最適化の実行"""
        
        try:
            self.logger.info(f"Optuna最適化開始: {self.n_trials}回試行")
            
            # Optunaスタディ作成
            study = optuna.create_study(
                direction='maximize',  # AUCを最大化
                sampler=optuna.samplers.TPESampler(seed=self.random_seed),
                pruner=optuna.pruners.MedianPruner(n_startup_trials=10)
            )
            
            # 最適化実行
            study.optimize(
                self.objective,
                n_trials=self.n_trials,
                timeout=3600,  # 1時間でタイムアウト
                show_progress_bar=True
            )
            
            # 最適化結果の保存
            self.best_params = study.best_params
            self.best_score = study.best_value
            
            self.logger.info(f"最適化完了:")
            self.logger.info(f"  最高スコア: {self.best_score:.4f}")
            self.logger.info(f"  最適パラメータ: {self.best_params}")
            
            # 結果の保存
            self._save_optimization_results(study)
            
            return True
            
        except Exception as e:
            self.logger.error(f"最適化エラー: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _save_optimization_results(self, study):
        """最適化結果の保存"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 最適パラメータの保存
        best_params_file = self.output_dir / f"best_params_{timestamp}.json"
        with open(best_params_file, 'w', encoding='utf-8') as f:
            json.dump({
                'best_params': self.best_params,
                'best_score': self.best_score,
                'n_trials': len(study.trials)
            }, f, indent=2, ensure_ascii=False)
        
        # 最適化履歴の保存
        history_file = self.output_dir / f"optimization_history_{timestamp}.json"
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(self.optimization_history, f, indent=2, ensure_ascii=False)
        
        # 試行結果のDataFrame保存
        trials_df = study.trials_dataframe()
        trials_csv = self.output_dir / f"trials_dataframe_{timestamp}.csv"
        trials_df.to_csv(trials_csv, index=False, encoding='utf-8')
        
        self.logger.info(f"最適化結果保存完了:")
        self.logger.info(f"  最適パラメータ: {best_params_file}")
        self.logger.info(f"  最適化履歴: {history_file}")
        self.logger.info(f"  試行データ: {trials_csv}")
    
    def train_final_model(self) -> bool:
        """最適パラメータでの最終モデル訓練"""
        
        try:
            if self.best_params is None:
                self.logger.error("最適パラメータが見つかりません。先に最適化を実行してください。")
                return False
            
            self.logger.info("最適パラメータで最終モデルを訓練中...")
            
            # 最適パラメータの設定
            final_params = self.best_params.copy()
            final_params.update({
                'objective': 'binary',
                'metric': 'auc',
                'boosting_type': 'gbdt',
                'random_state': self.random_seed,
                'verbosity': -1
            })
            
            # 全データでモデル訓練
            train_data = lgb.Dataset(self.X_train, label=self.y_train)
            
            final_model = lgb.train(
                final_params,
                train_data,
                num_boost_round=1000,
                callbacks=[lgb.log_evaluation(100)]
            )
            
            # モデルの評価
            y_pred = final_model.predict(self.X_train)
            train_auc = roc_auc_score(self.y_train, y_pred)
            train_acc = accuracy_score(self.y_train, (y_pred > 0.5).astype(int))
            
            self.logger.info(f"最終モデル性能:")
            self.logger.info(f"  訓練AUC: {train_auc:.4f}")
            self.logger.info(f"  訓練精度: {train_acc:.4f}")
            
            # モデルとスケーラーの保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            model_file = self.output_dir / f"optuna_optimized_model_{timestamp}.pkl"
            scaler_file = self.output_dir / f"optuna_optimized_scaler_{timestamp}.pkl"
            features_file = self.output_dir / f"optuna_optimized_features_{timestamp}.pkl"
            encoders_file = self.output_dir / f"optuna_optimized_encoders_{timestamp}.pkl"
            
            # 保存
            with open(model_file, 'wb') as f:
                pickle.dump(final_model, f)
            
            with open(scaler_file, 'wb') as f:
                pickle.dump(self.scaler, f)
            
            with open(features_file, 'wb') as f:
                pickle.dump(self.feature_names, f)
            
            with open(encoders_file, 'wb') as f:
                pickle.dump(self.label_encoders, f)
            
            self.logger.info(f"最終モデル保存完了:")
            self.logger.info(f"  モデル: {model_file}")
            self.logger.info(f"  スケーラー: {scaler_file}")
            self.logger.info(f"  特徴量: {features_file}")
            self.logger.info(f"  エンコーダー: {encoders_file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"最終モデル訓練エラー: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def generate_optimization_report(self) -> str:
        """最適化結果レポートの生成"""
        
        if not self.optimization_history:
            return "最適化履歴がありません。"
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("Optuna ハイパーパラメータ最適化レポート")
        report_lines.append("=" * 80)
        
        # 基本情報
        report_lines.append(f"\n📊 最適化概要:")
        report_lines.append(f"  試行回数: {len(self.optimization_history)}")
        report_lines.append(f"  データ年: {', '.join(self.data_years)}")
        report_lines.append(f"  特徴量数: {len(self.feature_names) if self.feature_names else 'N/A'}")
        
        # 最高スコア
        if self.best_score is not None:
            report_lines.append(f"\n🎯 最適化結果:")
            report_lines.append(f"  最高AUC: {self.best_score:.4f}")
            
            if self.best_params:
                report_lines.append(f"  最適パラメータ:")
                for param, value in self.best_params.items():
                    report_lines.append(f"    {param}: {value}")
        
        # 最適化推移
        scores = [h['cv_score'] for h in self.optimization_history]
        if scores:
            report_lines.append(f"\n📈 最適化推移:")
            report_lines.append(f"  初期スコア: {scores[0]:.4f}")
            report_lines.append(f"  最終スコア: {scores[-1]:.4f}")
            report_lines.append(f"  改善幅: {max(scores) - scores[0]:+.4f}")
            report_lines.append(f"  平均スコア: {np.mean(scores):.4f}")
            report_lines.append(f"  標準偏差: {np.std(scores):.4f}")
        
        # 上位試行
        sorted_history = sorted(self.optimization_history, 
                              key=lambda x: x['cv_score'], reverse=True)
        
        report_lines.append(f"\n🏆 上位5試行:")
        report_lines.append(f"{'順位':>4} {'試行':>6} {'AUC':>8} {'主要パラメータ'}")
        report_lines.append("-" * 60)
        
        for i, trial in enumerate(sorted_history[:5]):
            rank = i + 1
            trial_num = trial['trial_number']
            score = trial['cv_score']
            
            # 主要パラメータを抜粋
            key_params = []
            params = trial['params']
            for key in ['num_leaves', 'learning_rate', 'feature_fraction']:
                if key in params:
                    if isinstance(params[key], float):
                        key_params.append(f"{key}:{params[key]:.3f}")
                    else:
                        key_params.append(f"{key}:{params[key]}")
            
            param_str = ", ".join(key_params)
            report_lines.append(f"{rank:>4} {trial_num:>6} {score:>8.4f} {param_str}")
        
        report_lines.append(f"\n" + "=" * 80)
        report_lines.append("最適化完了")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)

def main():
    """メイン実行関数"""
    
    print("=" * 80)
    print("Optuna ハイパーパラメータ最適化システム")
    print("=" * 80)
    
    # 最適化設定
    optimizer = OptunaModelOptimizer(
        data_years=["2020"],  # 利用可能なデータ年
        output_dir="optuna_results",
        n_trials=50,  # 50回の試行（時間を考慮して調整）
        random_seed=42
    )
    
    try:
        # 1. データ準備
        print("\n📊 データ準備中...")
        if not optimizer.load_and_prepare_data():
            print("❌ データ準備に失敗しました。")
            return
        
        print("✅ データ準備完了")
        print(f"  特徴量数: {len(optimizer.feature_names)}")
        print(f"  サンプル数: {len(optimizer.X_train)}")
        
        # 2. ハイパーパラメータ最適化
        print("\n🔍 ハイパーパラメータ最適化中...")
        if not optimizer.optimize_hyperparameters():
            print("❌ 最適化に失敗しました。")
            return
        
        print("✅ 最適化完了")
        print(f"  最高AUC: {optimizer.best_score:.4f}")
        
        # 3. 最終モデル訓練
        print("\n🎯 最終モデル訓練中...")
        if not optimizer.train_final_model():
            print("❌ 最終モデル訓練に失敗しました。")
            return
        
        print("✅ 最終モデル訓練完了")
        
        # 4. レポート生成
        print("\n📄 最適化レポート生成中...")
        report = optimizer.generate_optimization_report()
        
        # レポート表示
        print("\n" + report)
        
        # レポート保存
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = optimizer.output_dir / f"optimization_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📁 レポート保存: {report_file}")
        print("\n🎉 Optuna最適化完了！")
        
    except KeyboardInterrupt:
        print("\n⚠️ ユーザーによって中断されました。")
    except Exception as e:
        print(f"\n❌ エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()