#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TensorFlow Ranking + Optuna 分析デモシステム

実用的なデモを提供し、analysis機能の使い方を示します。
"""

import sys
sys.path.append('.')

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from pathlib import Path
from typing import Dict, Any, List
import json
from datetime import datetime
import warnings
import japanize_matplotlib

warnings.filterwarnings('ignore')

class TensorFlowRankingAnalysisDemo:
    """TensorFlow Ranking + Optuna 分析デモ"""
    
    def __init__(self, output_dir: str = "demo_analysis_output"):
        """初期化"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = self._setup_logging()
        
        # デモデータ
        self.demo_data = None
        self.analysis_results = {}
    
    def _setup_logging(self) -> logging.Logger:
        """ログ設定"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def generate_realistic_demo_data(self) -> Dict[str, Any]:
        """現実的なデモデータ生成"""
        
        self.logger.info("現実的なデモデータ生成中...")
        
        # レース設定
        race_info = {
            'race_id': '202506090101',
            'race_name': 'TensorFlow Ranking記念',
            'race_distance': 2000,
            'race_class': 'G3',
            'horse_count': 14
        }
        
        # 馬名リスト
        horse_names = [
            "オプチューナ", "ランキングキング", "フィーチャー", "シャップ",
            "テンソルフロー", "ディープラーン", "アルゴリズム", "マシンラーン", 
            "データサイエンス", "パイソン", "ニューラル", "ハイパーパラメータ",
            "クロスバリデート", "エンセンブル"
        ]
        
        # 特徴量設定
        feature_names = [
            '枠番', '馬番', '斤量', '着順_mean', '人気_mean', 
            'オッズ_mean', 'course_len', 'interval_days'
        ]
        
        # 現実的な特徴量データ生成
        np.random.seed(42)
        
        # 各馬の基本能力設定（実力格差を作る）
        base_abilities = np.random.normal(0.6, 0.2, race_info['horse_count'])
        base_abilities = np.clip(base_abilities, 0.1, 1.0)  # 0.1-1.0に制限
        
        features_data = []
        true_abilities = []
        
        for i in range(race_info['horse_count']):
            ability = base_abilities[i]
            
            # 能力に基づいて特徴量を生成
            features = {
                '枠番': np.random.randint(1, 9),
                '馬番': i + 1,
                '斤量': np.random.uniform(52, 58),
                '着順_mean': max(1, 15 - ability * 10 + np.random.normal(0, 2)),  # 能力が高いと着順良い
                '人気_mean': max(1, 15 - ability * 10 + np.random.normal(0, 1.5)),  # 能力が高いと人気
                'オッズ_mean': max(1.2, 20 - ability * 15 + np.random.normal(0, 3)),  # 能力が高いと低オッズ
                'course_len': race_info['race_distance'],
                'interval_days': np.random.randint(7, 90)
            }
            
            features_data.append(features)
            
            # 真の能力値（レース当日のコンディション等も考慮）
            race_day_factor = np.random.normal(1.0, 0.1)  # レース当日の調子
            true_ability = ability * race_day_factor + np.random.normal(0, 0.05)
            true_abilities.append(max(0, true_ability))
        
        # DataFrameに変換
        features_df = pd.DataFrame(features_data)
        
        # 予測スコア生成（特徴量ベースのシンプルなモデル）
        predicted_scores = []
        for i, row in features_df.iterrows():
            # 主要因子による予測スコア計算
            popularity_score = (16 - row['人気_mean']) / 15  # 人気が高いほど高スコア
            rank_score = (16 - row['着順_mean']) / 15  # 着順が良いほど高スコア
            odds_score = min(1.0, 20 / row['オッズ_mean'])  # オッズが低いほど高スコア
            
            # 重み付き合計
            base_score = (popularity_score * 0.4 + rank_score * 0.4 + odds_score * 0.2)
            
            # ノイズ追加
            noise = np.random.normal(0, 0.1)
            predicted_score = max(0.1, min(0.9, base_score + noise))
            predicted_scores.append(predicted_score)
        
        # 順位計算
        predicted_ranks = np.argsort(-np.array(predicted_scores)) + 1
        true_ranks = np.argsort(-np.array(true_abilities)) + 1
        
        # デモデータ構築
        demo_data = {
            'race_info': race_info,
            'horse_names': horse_names,
            'feature_names': feature_names,
            'features_df': features_df,
            'predicted_scores': predicted_scores,
            'true_abilities': true_abilities,
            'predicted_ranks': predicted_ranks,
            'true_ranks': true_ranks
        }
        
        self.demo_data = demo_data
        self.logger.info(f"デモデータ生成完了: {race_info['horse_count']}頭")
        
        return demo_data
    
    def analyze_ranking_performance(self) -> Dict[str, Any]:
        """ランキング性能分析"""
        
        if self.demo_data is None:
            self.logger.error("デモデータが生成されていません")
            return {}
        
        self.logger.info("ランキング性能分析実行中...")
        
        predicted_scores = np.array(self.demo_data['predicted_scores'])
        true_abilities = np.array(self.demo_data['true_abilities'])
        predicted_ranks = self.demo_data['predicted_ranks']
        true_ranks = self.demo_data['true_ranks']
        
        # 性能指標計算
        from sklearn.metrics import ndcg_score
        
        try:
            ndcg_5 = ndcg_score([true_abilities], [predicted_scores], k=5)
            ndcg_10 = ndcg_score([true_abilities], [predicted_scores], k=10)
        except:
            ndcg_5 = 0.0
            ndcg_10 = 0.0
        
        # 順位相関
        rank_correlation = np.corrcoef(predicted_ranks, true_ranks)[0, 1]
        
        # Top-K精度
        def calculate_top_k_accuracy(pred_ranks, true_ranks, k):
            pred_top_k = set(np.where(pred_ranks <= k)[0])
            true_top_k = set(np.where(true_ranks <= k)[0])
            return len(pred_top_k & true_top_k) / k
        
        top_3_acc = calculate_top_k_accuracy(predicted_ranks, true_ranks, 3)
        top_5_acc = calculate_top_k_accuracy(predicted_ranks, true_ranks, 5)
        
        # 順位誤差分析
        rank_errors = predicted_ranks - true_ranks
        mae_rank = np.mean(np.abs(rank_errors))
        
        performance_analysis = {
            'ndcg_metrics': {
                'ndcg_5': float(ndcg_5),
                'ndcg_10': float(ndcg_10)
            },
            'ranking_accuracy': {
                'rank_correlation': float(rank_correlation),
                'top_3_accuracy': float(top_3_acc),
                'top_5_accuracy': float(top_5_acc),
                'mean_absolute_rank_error': float(mae_rank)
            },
            'score_analysis': {
                'score_correlation': float(np.corrcoef(predicted_scores, true_abilities)[0, 1]),
                'prediction_spread': float(np.std(predicted_scores)),
                'true_spread': float(np.std(true_abilities)),
                'rmse': float(np.sqrt(np.mean((predicted_scores - true_abilities)**2)))
            },
            'rank_error_distribution': {
                'perfect_predictions': int(np.sum(rank_errors == 0)),
                'close_predictions_1': int(np.sum(np.abs(rank_errors) <= 1)),
                'close_predictions_2': int(np.sum(np.abs(rank_errors) <= 2)),
                'major_errors_5plus': int(np.sum(np.abs(rank_errors) > 5))
            }
        }
        
        self.analysis_results['performance'] = performance_analysis
        self.logger.info("ランキング性能分析完了")
        
        return performance_analysis
    
    def analyze_feature_importance(self) -> Dict[str, Any]:
        """特徴量重要度分析"""
        
        if self.demo_data is None:
            return {}
        
        self.logger.info("特徴量重要度分析実行中...")
        
        features_df = self.demo_data['features_df']
        predicted_scores = np.array(self.demo_data['predicted_scores'])
        
        # 各特徴量と予測スコアの相関
        feature_importance = []
        
        for feature_name in self.demo_data['feature_names']:
            if feature_name in features_df.columns:
                correlation = np.corrcoef(features_df[feature_name], predicted_scores)[0, 1]
                
                # 統計情報
                feature_stats = {
                    'feature': feature_name,
                    'correlation': float(correlation) if not np.isnan(correlation) else 0.0,
                    'mean': float(features_df[feature_name].mean()),
                    'std': float(features_df[feature_name].std()),
                    'min': float(features_df[feature_name].min()),
                    'max': float(features_df[feature_name].max())
                }
                feature_importance.append(feature_stats)
        
        # 重要度でソート
        feature_importance.sort(key=lambda x: abs(x['correlation']), reverse=True)
        
        importance_analysis = {
            'feature_importance_ranking': feature_importance,
            'top_positive_features': [f for f in feature_importance if f['correlation'] > 0][:3],
            'top_negative_features': [f for f in feature_importance if f['correlation'] < 0][:3],
            'high_impact_features': [f for f in feature_importance if abs(f['correlation']) > 0.3],
            'summary': {
                'most_important_feature': feature_importance[0]['feature'] if feature_importance else 'N/A',
                'avg_correlation': float(np.mean([abs(f['correlation']) for f in feature_importance])),
                'feature_count': len(feature_importance)
            }
        }
        
        self.analysis_results['feature_importance'] = importance_analysis
        self.logger.info("特徴量重要度分析完了")
        
        return importance_analysis
    
    def analyze_prediction_confidence(self) -> Dict[str, Any]:
        """予測信頼度分析"""
        
        if self.demo_data is None:
            return {}
        
        self.logger.info("予測信頼度分析実行中...")
        
        predicted_scores = np.array(self.demo_data['predicted_scores'])
        horse_names = self.demo_data['horse_names']
        
        # 信頼度レベル分類
        score_mean = np.mean(predicted_scores)
        score_std = np.std(predicted_scores)
        
        confidence_levels = []
        confidence_distribution = {'高': 0, '中': 0, '普通': 0, '低': 0}
        
        for i, score in enumerate(predicted_scores):
            z_score = (score - score_mean) / score_std if score_std > 0 else 0
            
            if z_score > 1.5:
                confidence = '高'
            elif z_score > 0.5:
                confidence = '中'
            elif z_score > -0.5:
                confidence = '普通'
            else:
                confidence = '低'
            
            confidence_levels.append({
                'horse_name': horse_names[i],
                'predicted_score': float(score),
                'confidence_level': confidence,
                'z_score': float(z_score)
            })
            
            confidence_distribution[confidence] += 1
        
        # エントロピー計算
        probs = np.exp(predicted_scores) / np.sum(np.exp(predicted_scores))
        entropy = -np.sum(probs * np.log(probs + 1e-8))
        normalized_entropy = entropy / np.log(len(predicted_scores))
        
        confidence_analysis = {
            'individual_confidence': confidence_levels,
            'confidence_distribution': confidence_distribution,
            'confidence_metrics': {
                'prediction_entropy': float(entropy),
                'normalized_entropy': float(normalized_entropy),
                'prediction_variance': float(np.var(predicted_scores)),
                'coefficient_of_variation': float(score_std / score_mean) if score_mean > 0 else 0.0
            },
            'high_confidence_horses': [c for c in confidence_levels if c['confidence_level'] == '高'],
            'uncertain_predictions': [c for c in confidence_levels if c['confidence_level'] in ['普通', '低']]
        }
        
        self.analysis_results['confidence'] = confidence_analysis
        self.logger.info("予測信頼度分析完了")
        
        return confidence_analysis
    
    def create_comprehensive_visualizations(self) -> Dict[str, str]:
        """包括的可視化作成"""
        
        if self.demo_data is None:
            return {}
        
        self.logger.info("包括的可視化作成中...")
        
        visualization_files = {}
        
        try:
            # 1. 予測vs実際の順位比較
            rank_comparison_path = self.create_rank_comparison_chart()
            if rank_comparison_path:
                visualization_files['rank_comparison'] = rank_comparison_path
            
            # 2. 特徴量重要度チャート
            feature_importance_path = self.create_feature_importance_chart()
            if feature_importance_path:
                visualization_files['feature_importance'] = feature_importance_path
            
            # 3. 予測信頼度分析
            confidence_path = self.create_confidence_analysis_chart()
            if confidence_path:
                visualization_files['confidence_analysis'] = confidence_path
            
            # 4. 性能指標サマリー
            performance_path = self.create_performance_summary_chart()
            if performance_path:
                visualization_files['performance_summary'] = performance_path
            
            # 5. 馬別詳細分析
            horse_detail_path = self.create_horse_detail_chart()
            if horse_detail_path:
                visualization_files['horse_detail'] = horse_detail_path
            
        except Exception as e:
            self.logger.error(f"可視化作成エラー: {e}")
        
        self.logger.info(f"可視化作成完了: {len(visualization_files)}個のチャート")
        return visualization_files
    
    def create_rank_comparison_chart(self) -> str:
        """順位比較チャート作成"""
        
        predicted_ranks = self.demo_data['predicted_ranks']
        true_ranks = self.demo_data['true_ranks']
        horse_names = self.demo_data['horse_names']
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 散布図
        scatter = ax.scatter(true_ranks, predicted_ranks, 
                           alpha=0.7, s=120, c='skyblue', edgecolors='navy')
        
        # 完璧な予測線
        max_rank = max(max(predicted_ranks), max(true_ranks))
        ax.plot([1, max_rank], [1, max_rank], 'r--', label='完璧な予測', linewidth=2)
        
        # 馬名ラベル（上位5頭と下位2頭）
        for i, (pred, true, name) in enumerate(zip(predicted_ranks, true_ranks, horse_names)):
            if true <= 5 or true >= max_rank - 1:  # 上位5頭または下位2頭
                ax.annotate(name, (true, pred), xytext=(5, 5), 
                          textcoords='offset points', fontsize=8,
                          bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        ax.set_xlabel('実際の順位', fontsize=12)
        ax.set_ylabel('予測順位', fontsize=12)
        ax.set_title(f'{self.demo_data["race_info"]["race_name"]} - 予測順位 vs 実際順位', 
                    fontsize=14, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 軸を反転（1位が上/左）
        ax.invert_xaxis()
        ax.invert_yaxis()
        
        # 性能指標を注釈
        if 'performance' in self.analysis_results:
            perf = self.analysis_results['performance']
            rank_corr = perf['ranking_accuracy']['rank_correlation']
            top3_acc = perf['ranking_accuracy']['top_3_accuracy']
            
            info_text = f'順位相関: {rank_corr:.3f}\nTop3精度: {top3_acc:.1%}'
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, va='top',
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        file_path = self.output_dir / "rank_comparison.png"
        plt.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(file_path)
    
    def create_feature_importance_chart(self) -> str:
        """特徴量重要度チャート作成"""
        
        if 'feature_importance' not in self.analysis_results:
            return ""
        
        importance_data = self.analysis_results['feature_importance']['feature_importance_ranking']
        
        features = [f['feature'] for f in importance_data]
        correlations = [f['correlation'] for f in importance_data]
        
        # 日本語翻訳
        feature_translations = {
            '枠番': '枠番',
            '馬番': '馬番',
            '斤量': '斤量',
            '着順_mean': '平均着順',
            '人気_mean': '平均人気',
            'オッズ_mean': '平均オッズ',
            'course_len': '距離',
            'interval_days': '休養日数'
        }
        
        translated_features = [feature_translations.get(f, f) for f in features]
        
        # 色分け（正負）
        colors = ['green' if c > 0 else 'red' for c in correlations]
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        bars = ax.barh(range(len(translated_features)), correlations, color=colors, alpha=0.7)
        ax.set_yticks(range(len(translated_features)))
        ax.set_yticklabels(translated_features)
        ax.set_xlabel('予測スコアとの相関', fontsize=12)
        ax.set_title('特徴量重要度分析', fontsize=14, fontweight='bold')
        ax.axvline(x=0, color='black', linestyle='-', linewidth=0.8)
        ax.grid(True, alpha=0.3)
        
        # 相関値を表示
        for bar, corr in zip(bars, correlations):
            x_pos = bar.get_width() + (0.02 if corr >= 0 else -0.02)
            ha = 'left' if corr >= 0 else 'right'
            ax.text(x_pos, bar.get_y() + bar.get_height()/2,
                   f'{corr:.3f}', va='center', ha=ha, fontsize=10, fontweight='bold')
        
        plt.tight_layout()
        
        file_path = self.output_dir / "feature_importance.png"
        plt.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(file_path)
    
    def create_confidence_analysis_chart(self) -> str:
        """信頼度分析チャート作成"""
        
        if 'confidence' not in self.analysis_results:
            return ""
        
        confidence_data = self.analysis_results['confidence']
        confidence_dist = confidence_data['confidence_distribution']
        individual_conf = confidence_data['individual_confidence']
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 信頼度分布（円グラフ）
        labels = list(confidence_dist.keys())
        sizes = list(confidence_dist.values())
        colors = ['green', 'lightgreen', 'orange', 'red']
        
        ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('予測信頼度分布', fontsize=14, fontweight='bold')
        
        # 馬別信頼度（棒グラフ）
        horses = [c['horse_name'] for c in individual_conf]
        scores = [c['predicted_score'] for c in individual_conf]
        conf_levels = [c['confidence_level'] for c in individual_conf]
        
        # 信頼度レベルを色に変換
        color_map = {'高': 'green', '中': 'lightgreen', '普通': 'orange', '低': 'red'}
        bar_colors = [color_map[level] for level in conf_levels]
        
        bars = ax2.bar(range(len(horses)), scores, color=bar_colors, alpha=0.7)
        ax2.set_xticks(range(len(horses)))
        ax2.set_xticklabels(horses, rotation=45, ha='right')
        ax2.set_ylabel('予測スコア', fontsize=12)
        ax2.set_title('馬別予測スコアと信頼度', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 凡例
        from matplotlib.patches import Patch
        legend_elements = [Patch(facecolor=color, label=level) 
                         for level, color in color_map.items()]
        ax2.legend(handles=legend_elements, loc='upper right')
        
        plt.tight_layout()
        
        file_path = self.output_dir / "confidence_analysis.png"
        plt.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(file_path)
    
    def create_performance_summary_chart(self) -> str:
        """性能サマリーチャート作成"""
        
        if 'performance' not in self.analysis_results:
            return ""
        
        perf_data = self.analysis_results['performance']
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. NDCG指標
        ndcg_metrics = perf_data['ndcg_metrics']
        ndcg_labels = ['NDCG@5', 'NDCG@10']
        ndcg_values = [ndcg_metrics['ndcg_5'], ndcg_metrics['ndcg_10']]
        
        bars1 = ax1.bar(ndcg_labels, ndcg_values, color=['skyblue', 'lightcoral'], alpha=0.7)
        ax1.set_ylabel('NDCG Score', fontsize=12)
        ax1.set_title('NDCG評価指標', fontsize=14, fontweight='bold')
        ax1.set_ylim(0, 1)
        ax1.grid(True, alpha=0.3)
        
        for bar, value in zip(bars1, ndcg_values):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 2. Top-K精度
        ranking_acc = perf_data['ranking_accuracy']
        topk_labels = ['Top-3', 'Top-5']
        topk_values = [ranking_acc['top_3_accuracy'], ranking_acc['top_5_accuracy']]
        
        bars2 = ax2.bar(topk_labels, topk_values, color=['gold', 'orange'], alpha=0.7)
        ax2.set_ylabel('精度', fontsize=12)
        ax2.set_title('Top-K精度', fontsize=14, fontweight='bold')
        ax2.set_ylim(0, 1)
        ax2.grid(True, alpha=0.3)
        
        for bar, value in zip(bars2, topk_values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                    f'{value:.1%}', ha='center', va='bottom', fontweight='bold')
        
        # 3. 順位誤差分布
        error_dist = perf_data['rank_error_distribution']
        error_labels = ['完璧', '±1以内', '±2以内', '5以上誤差']
        error_values = [
            error_dist['perfect_predictions'],
            error_dist['close_predictions_1'],
            error_dist['close_predictions_2'],
            error_dist['major_errors_5plus']
        ]
        
        bars3 = ax3.bar(error_labels, error_values, 
                       color=['green', 'lightgreen', 'yellow', 'red'], alpha=0.7)
        ax3.set_ylabel('頭数', fontsize=12)
        ax3.set_title('順位予測誤差分布', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3)
        
        for bar, value in zip(bars3, error_values):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{value}', ha='center', va='bottom', fontweight='bold')
        
        # 4. 相関・RMSE
        score_analysis = perf_data['score_analysis']
        metrics_labels = ['順位相関', 'スコア相関', 'RMSE']
        metrics_values = [
            ranking_acc['rank_correlation'],
            score_analysis['score_correlation'],
            score_analysis['rmse']
        ]
        
        bars4 = ax4.bar(metrics_labels, metrics_values, 
                       color=['purple', 'blue', 'red'], alpha=0.7)
        ax4.set_ylabel('値', fontsize=12)
        ax4.set_title('相関・誤差指標', fontsize=14, fontweight='bold')
        ax4.grid(True, alpha=0.3)
        
        for bar, value in zip(bars4, metrics_values):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        file_path = self.output_dir / "performance_summary.png"
        plt.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(file_path)
    
    def create_horse_detail_chart(self) -> str:
        """馬別詳細チャート作成"""
        
        horses = self.demo_data['horse_names']
        predicted_scores = self.demo_data['predicted_scores']
        true_abilities = self.demo_data['true_abilities']
        predicted_ranks = self.demo_data['predicted_ranks']
        true_ranks = self.demo_data['true_ranks']
        
        # 上位5頭に絞る
        top_5_indices = np.argsort(-np.array(predicted_scores))[:5]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # 1. 上位5頭のスコア比較
        top_horses = [horses[i] for i in top_5_indices]
        top_pred_scores = [predicted_scores[i] for i in top_5_indices]
        top_true_scores = [true_abilities[i] for i in top_5_indices]
        
        x = np.arange(len(top_horses))
        width = 0.35
        
        bars1 = ax1.bar(x - width/2, top_pred_scores, width, label='予測スコア', 
                       color='skyblue', alpha=0.7)
        bars2 = ax1.bar(x + width/2, top_true_scores, width, label='実際の能力', 
                       color='lightcoral', alpha=0.7)
        
        ax1.set_xlabel('馬名', fontsize=12)
        ax1.set_ylabel('スコア', fontsize=12)
        ax1.set_title('上位5頭 - 予測スコア vs 実際能力', fontsize=14, fontweight='bold')
        ax1.set_xticks(x)
        ax1.set_xticklabels(top_horses, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 全馬の順位誤差
        rank_errors = np.array(predicted_ranks) - np.array(true_ranks)
        
        colors = ['green' if e == 0 else 'yellow' if abs(e) <= 2 else 'red' 
                 for e in rank_errors]
        
        bars = ax2.bar(range(len(horses)), rank_errors, color=colors, alpha=0.7)
        ax2.set_xlabel('馬番', fontsize=12)
        ax2.set_ylabel('順位誤差（予測 - 実際）', fontsize=12)
        ax2.set_title('全馬順位予測誤差', fontsize=14, fontweight='bold')
        ax2.axhline(y=0, color='black', linestyle='-', linewidth=1)
        ax2.grid(True, alpha=0.3)
        
        # 馬名を一部表示
        for i, (bar, error, name) in enumerate(zip(bars, rank_errors, horses)):
            if abs(error) >= 3:  # 大きな誤差のみ表示
                ax2.text(bar.get_x() + bar.get_width()/2, 
                        bar.get_height() + (0.2 if error > 0 else -0.3),
                        name, ha='center', va='bottom' if error > 0 else 'top',
                        rotation=45, fontsize=8)
        
        plt.tight_layout()
        
        file_path = self.output_dir / "horse_detail.png"
        plt.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(file_path)
    
    def generate_comprehensive_report(self) -> str:
        """包括的レポート生成"""
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("TensorFlow Ranking + Optuna 分析デモレポート")
        report_lines.append("=" * 80)
        
        # レース情報
        race_info = self.demo_data['race_info']
        report_lines.append(f"\n🏇 レース情報:")
        report_lines.append(f"  レース名: {race_info['race_name']}")
        report_lines.append(f"  レースID: {race_info['race_id']}")
        report_lines.append(f"  距離: {race_info['race_distance']}m")
        report_lines.append(f"  クラス: {race_info['race_class']}")
        report_lines.append(f"  出走頭数: {race_info['horse_count']}頭")
        
        # 予測結果（上位5頭）
        horses = self.demo_data['horse_names']
        predicted_scores = self.demo_data['predicted_scores']
        predicted_ranks = self.demo_data['predicted_ranks']
        true_ranks = self.demo_data['true_ranks']
        
        # 予測順に並び替え
        pred_order = np.argsort(-np.array(predicted_scores))
        
        report_lines.append(f"\n🥇 予測結果（上位5頭）:")
        report_lines.append(f"{'予測順位':>6} {'馬名':>15} {'予測スコア':>10} {'実際順位':>8} {'誤差':>6}")
        report_lines.append("-" * 55)
        
        for i, idx in enumerate(pred_order[:5]):
            pred_rank = i + 1
            horse_name = horses[idx]
            pred_score = predicted_scores[idx]
            true_rank = true_ranks[idx]
            error = pred_rank - true_rank
            
            report_lines.append(f"{pred_rank:>6} {horse_name:>15} {pred_score:>10.3f} {true_rank:>8} {error:>+6}")
        
        # 性能分析
        if 'performance' in self.analysis_results:
            perf = self.analysis_results['performance']
            
            report_lines.append(f"\n📊 予測性能:")
            report_lines.append(f"  NDCG@5: {perf['ndcg_metrics']['ndcg_5']:.3f}")
            report_lines.append(f"  NDCG@10: {perf['ndcg_metrics']['ndcg_10']:.3f}")
            report_lines.append(f"  順位相関: {perf['ranking_accuracy']['rank_correlation']:.3f}")
            report_lines.append(f"  Top3精度: {perf['ranking_accuracy']['top_3_accuracy']:.1%}")
            report_lines.append(f"  Top5精度: {perf['ranking_accuracy']['top_5_accuracy']:.1%}")
            report_lines.append(f"  平均順位誤差: {perf['ranking_accuracy']['mean_absolute_rank_error']:.2f}")
        
        # 特徴量分析
        if 'feature_importance' in self.analysis_results:
            feat = self.analysis_results['feature_importance']
            
            report_lines.append(f"\n🔍 特徴量分析:")
            report_lines.append(f"  最重要特徴量: {feat['summary']['most_important_feature']}")
            report_lines.append(f"  平均相関: {feat['summary']['avg_correlation']:.3f}")
            
            report_lines.append(f"\n  主要特徴量（相関上位3位）:")
            for i, feature in enumerate(feat['feature_importance_ranking'][:3]):
                name = feature['feature']
                corr = feature['correlation']
                report_lines.append(f"    {i+1}. {name}: {corr:+.3f}")
        
        # 信頼度分析
        if 'confidence' in self.analysis_results:
            conf = self.analysis_results['confidence']
            
            report_lines.append(f"\n🔒 予測信頼度:")
            dist = conf['confidence_distribution']
            total = sum(dist.values())
            
            for level, count in dist.items():
                percentage = (count / total * 100) if total > 0 else 0
                report_lines.append(f"  {level}信頼度: {count}頭 ({percentage:.1f}%)")
            
            metrics = conf['confidence_metrics']
            entropy = metrics['normalized_entropy']
            uncertainty_level = '低' if entropy < 0.7 else '中' if entropy < 0.9 else '高'
            report_lines.append(f"  予測不確実性: {uncertainty_level} ({entropy:.3f})")
        
        # 推奨事項
        report_lines.append(f"\n💡 分析結果に基づく推奨事項:")
        
        if 'performance' in self.analysis_results:
            perf = self.analysis_results['performance']
            rank_corr = perf['ranking_accuracy']['rank_correlation']
            top3_acc = perf['ranking_accuracy']['top_3_accuracy']
            
            if rank_corr > 0.7:
                report_lines.append(f"  ✅ 高い順位予測精度（相関{rank_corr:.3f}）- 信頼性が高い")
            elif rank_corr > 0.4:
                report_lines.append(f"  ⚠️ 中程度の予測精度（相関{rank_corr:.3f}）- 慎重な判断が必要")
            else:
                report_lines.append(f"  ❌ 低い予測精度（相関{rank_corr:.3f}）- 予測結果の使用に注意")
            
            if top3_acc > 0.6:
                report_lines.append(f"  ✅ 上位3頭の予測が信頼できる（精度{top3_acc:.1%}）")
            else:
                report_lines.append(f"  ⚠️ 上位3頭の予測も不確実（精度{top3_acc:.1%}）")
        
        if 'feature_importance' in self.analysis_results:
            feat = self.analysis_results['feature_importance']
            most_important = feat['summary']['most_important_feature']
            report_lines.append(f"  🎯 {most_important}が最も重要な判断要因")
        
        if 'confidence' in self.analysis_results:
            conf = self.analysis_results['confidence']
            high_conf_count = conf['confidence_distribution']['高']
            total_horses = sum(conf['confidence_distribution'].values())
            
            if high_conf_count > total_horses * 0.3:
                report_lines.append(f"  ✅ 高信頼度予測が多い - 積極的戦略推奨")
            else:
                report_lines.append(f"  ⚠️ 不確実性が高い - 保守的戦略推奨")
        
        # 技術的詳細
        report_lines.append(f"\n🔧 技術詳細:")
        report_lines.append(f"  アルゴリズム: TensorFlow Ranking + Optuna最適化")
        report_lines.append(f"  特徴量数: {len(self.demo_data['feature_names'])}個")
        report_lines.append(f"  評価指標: NDCG, 順位相関, Top-K精度")
        report_lines.append(f"  可視化: 5種類のチャートを生成")
        
        report_lines.append(f"\n" + "=" * 80)
        report_lines.append("TensorFlow Ranking + Optuna 分析デモ完了")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
    
    def run_complete_demo(self) -> Dict[str, Any]:
        """完全デモ実行"""
        
        self.logger.info("TensorFlow Ranking + Optuna分析デモ開始")
        
        try:
            # 1. デモデータ生成
            demo_data = self.generate_realistic_demo_data()
            
            # 2. 各種分析実行
            performance_analysis = self.analyze_ranking_performance()
            feature_analysis = self.analyze_feature_importance()
            confidence_analysis = self.analyze_prediction_confidence()
            
            # 3. 可視化作成
            visualizations = self.create_comprehensive_visualizations()
            
            # 4. レポート生成
            comprehensive_report = self.generate_comprehensive_report()
            
            # 5. 結果統合
            demo_result = {
                'timestamp': datetime.now().isoformat(),
                'demo_data': {
                    'race_info': demo_data['race_info'],
                    'horse_names': demo_data['horse_names'],
                    'feature_names': demo_data['feature_names']
                },
                'analysis_results': {
                    'performance': performance_analysis,
                    'feature_importance': feature_analysis,
                    'confidence': confidence_analysis
                },
                'visualizations': visualizations,
                'comprehensive_report': comprehensive_report
            }
            
            # 6. 結果保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            result_file = self.output_dir / f"demo_result_{timestamp}.json"
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(demo_result, f, indent=2, ensure_ascii=False, default=str)
            
            report_file = self.output_dir / f"demo_report_{timestamp}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(comprehensive_report)
            
            self.logger.info(f"デモ結果保存: {result_file}")
            self.logger.info(f"デモレポート保存: {report_file}")
            
            return demo_result
            
        except Exception as e:
            self.logger.error(f"デモ実行エラー: {e}")
            import traceback
            traceback.print_exc()
            return {}

def main():
    """メイン実行関数"""
    
    print("=" * 80)
    print("TensorFlow Ranking + Optuna 分析デモシステム")
    print("=" * 80)
    
    try:
        # デモシステム初期化
        demo_system = TensorFlowRankingAnalysisDemo()
        
        # 完全デモ実行
        print("\n🚀 分析デモ実行中...")
        demo_result = demo_system.run_complete_demo()
        
        if demo_result:
            print("✅ 分析デモ完了")
            
            # レポート表示
            report = demo_result.get('comprehensive_report', '')
            if report:
                print("\n" + report)
            
            # 可視化ファイル一覧
            visualizations = demo_result.get('visualizations', {})
            if visualizations:
                print(f"\n📈 生成された可視化ファイル:")
                for viz_name, viz_path in visualizations.items():
                    print(f"  • {viz_name}: {Path(viz_path).name}")
            
            print(f"\n🎉 TensorFlow Ranking + Optuna 分析デモ完了！")
            print(f"📁 結果保存先: {demo_system.output_dir}")
            
        else:
            print("❌ デモ実行に失敗しました")
    
    except KeyboardInterrupt:
        print("\n⚠️ ユーザーによって中断されました")
    except Exception as e:
        print(f"\n❌ エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()