#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
レース情報の処理を行うモジュール
- HTMLファイルからのデータ抽出
- レース情報と結果の前処理
- データの保存と分析
"""

import datetime
import glob
import io
import logging
import os
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import numpy as np
import pandas as pd
from bs4 import BeautifulSoup
from tqdm.auto import tqdm
from keiba_ai_system.core.utils.constants import LocalPaths, Master, RaceInfoCols, RaceProcessorConstants, ResultsCols

# TODO: ResultsCols, RaceInfoCols, Master は constants.py のような共通ファイルに一元化することを強く推奨します。
class RaceProcessor: # RaceProcessorConstants から正規表現をインポートして利用
    """
    レース情報と結果を統合して処理するクラス
    HTMLファイルからのデータ抽出と前処理を行う
    """
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初期化

        Parameters
        ----------
        config : Dict[str, Any], optional
            処理の設定情報
        """
        self._config = config or {}
        self._race_info_df = pd.DataFrame()
        self._race_results_df = pd.DataFrame()
        self.logger = logging.getLogger(__name__)

    def extract_horse_ids_from_html(self, html_path: str) -> List[str]:
        """
        単一のレースHTMLファイルから馬IDのリストを抽出する。
        parse_race_html の馬ID抽出ロジックを基に、より直接的にIDを抽出する。

        Args:
            html_path (str): HTMLファイルのパス。

        Returns:
            List[str]: 抽出された馬IDのリスト（重複なし、ソート済み）。
        """
        if not os.path.exists(html_path):
            self.logger.warning(f"ファイルが見つかりません: {html_path}")
            return []

        with open(html_path, "rb") as f:
            html_content = f.read()

        soup: Optional[BeautifulSoup] = None
        # HTMLのパース試行 (euc-jp -> utf-8)
        try:
            soup = BeautifulSoup(html_content, "lxml", from_encoding="euc-jp")
        except Exception as e_eucjp:
            self.logger.warning(f"euc-jpでのデコードに失敗したため、utf-8で試行します: {html_path}, エラー: {e_eucjp}")
            try:
                soup = BeautifulSoup(html_content, "lxml", from_encoding="utf-8")
            except Exception as e_utf8:
                self.logger.error(f"HTMLのデコード/パースに失敗しました: {html_path}, エラー: {e_utf8}", exc_info=True)
                return []

        if not soup or not soup.body:  # パース成功しても中身がない場合
            self.logger.warning(f"HTMLのパース結果が空です: {html_path}")
            return []

        horse_ids: Set[str] = set()

        # レース結果テーブルと思われる箇所から馬IDを持つリンクを抽出
        search_area: Union[BeautifulSoup, Any] = soup # type: ignore # デフォルトはHTML全体

        # 最も確実なのは summary="レース結果" または "出馬表" のテーブル
        main_result_table = soup.find("table", summary=RaceProcessorConstants._RE_TABLE_SUMMARY) # attrs は不要

        if main_result_table:
            search_area = main_result_table
            self.logger.debug(f"メインのレース結果/出馬表テーブルを発見: {html_path}")
        else:
            # フォールバックとして他の可能性のある要素を探す
            possible_selectors = [
                "table.race_table_01", "table.Shutuba_Table", "table.Result_Table", ".HorseList"
            ]
            for selector in possible_selectors:
                candidate_area = soup.select_one(selector)
                if candidate_area and candidate_area.find("a", href=RaceProcessorConstants._RE_HORSE_ID):
                    search_area = candidate_area
                    self.logger.debug(f"フォールバックセレクタ '{selector}' で馬IDリンクを含むエリアを発見: {html_path}")
                    break
            if search_area is soup: # 特定のエリアが見つからなかった場合
                self.logger.warning(f"特定のレース関連エリアが見つかりず、HTML全体から馬IDを検索します: {html_path}")

        for a_tag in search_area.find_all("a", href=RaceProcessorConstants._RE_HORSE_ID):
            match = RaceProcessorConstants._RE_HORSE_ID.search(a_tag.get("href", "")) # .get()でNoneを避ける
            if match:
                horse_ids.add(match.group(1))

        if not horse_ids:
            self.logger.info(f"馬IDが見つかりませんでした: {html_path}")

        return sorted(list(horse_ids))

    def parse_race_html(self, html_path: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """レース情報のHTMLファイルを解析して、レース情報とレース結果のDataFrameを返す"""
        if not os.path.exists(html_path):
            self.logger.warning(f"ファイルが見つかりません: {html_path}")
            return pd.DataFrame(), pd.DataFrame()

        with open(html_path, "rb") as f:
            html_content = f.read()

        soup = self._get_soup_from_html(html_content, html_path)
        if not soup:
            return pd.DataFrame(), pd.DataFrame()

        race_id = os.path.basename(html_path).replace(".bin", "")

        # レース情報の抽出
        race_info_data = self._parse_race_info_block(soup, race_id)
        race_info_df = pd.DataFrame([race_info_data]) if race_info_data else pd.DataFrame()

        # レース結果の抽出
        # 1. テーブルをパースしてDataFrame化
        race_results_df = self._parse_race_results_table(soup, html_content, race_id)
        # 2. ID抽出・カラム整形を必ず実施 # type: ignore
        if not race_results_df.empty:
            # soupからレース結果テーブルを再取得（ID抽出のため）
            table_soup_for_ids = soup.find("table", summary=RaceProcessorConstants._RE_TABLE_SUMMARY)
            if not table_soup_for_ids:
                table_soup_for_ids = soup.find("table", class_=re.compile(r"(race_table_01|Shutuba_Table|Result_Table)"))
            race_results_df = self._format_and_extract_ids_from_results_df(race_results_df, table_soup_for_ids, race_id)

        # TODO: 払い戻し情報の抽出も同様にメソッド化する
        # payoff_df = self._parse_payoff_tables(soup, race_id)

        return race_info_df, race_results_df

    def _get_soup_from_html(self, html_content: bytes, file_path_for_log: str) -> Optional[BeautifulSoup]:
        """HTMLコンテンツからBeautifulSoupオブジェクトを取得する"""
        soup: Optional[BeautifulSoup] = None
        try:
            soup = BeautifulSoup(html_content, "lxml", from_encoding="euc-jp")
        except Exception as e_eucjp:
            self.logger.warning(f"euc-jpでのデコードに失敗したため、utf-8で試行します: {file_path_for_log}, エラー: {e_eucjp}")
            try:
                soup = BeautifulSoup(html_content, "lxml", from_encoding="utf-8")
            except Exception as e_utf8:
                self.logger.error(f"HTMLのデコード/パースに失敗しました: {file_path_for_log}, エラー: {e_utf8}", exc_info=True)
                return None

        if not soup or not soup.body:
            self.logger.warning(f"HTMLのパースに失敗しました（bodyタグなし）: {file_path_for_log}")
            return None
        return soup

    def _extract_ids_from_cell_content(self, cell_content: Any) -> Dict[str, Optional[str]]:
        """
        テーブルのセル内容 (文字列またはBeautifulSoup要素) から関連するIDを抽出する。
        馬、騎手、調教師のIDを想定。
        """
        ids = {
            "horse_id": None,
            "jockey_id": None,
            "trainer_id": None,
        }

        links = []
        if isinstance(cell_content, str): # pd.read_htmlの結果など、セルが文字列の場合
            # 文字列内にHTMLタグが含まれている場合を考慮
            temp_soup = BeautifulSoup(cell_content, 'html.parser')
            links = temp_soup.find_all("a", href=True)
        elif hasattr(cell_content, 'find_all'): # BeautifulSoupのTagオブジェクトの場合
            links = cell_content.find_all("a", href=True)

        for link_tag in links:
            href = link_tag.get("href", "")
            if "/horse/" in href and not ids["horse_id"]:
                match = RaceProcessorConstants._RE_HORSE_ID.search(href)
                if match:
                    ids["horse_id"] = match.group(1)
            elif "/jockey/" in href and not ids["jockey_id"]:
                match = RaceProcessorConstants._RE_JOCKEY_ID.search(href)
                if match:
                    ids["jockey_id"] = match.group(1)
            elif "/trainer/" in href and not ids["trainer_id"]:
                match = RaceProcessorConstants._RE_TRAINER_ID.search(href)
                if match:
                    ids["trainer_id"] = match.group(1)
        return ids

    def _parse_race_info_block(self, soup: BeautifulSoup, race_id: str) -> Dict[str, Any]:
        """
        HTML(soup)からレース基本情報を抽出する。

        Parameters
        ----------
        soup : BeautifulSoup
            解析済みのHTMLオブジェクト
        race_id : str
            レースID

        Returns
        -------
        Dict[str, Any]
            抽出されたレース情報の辞書
        """
        self.logger.debug(f"レース情報ブロックのパースを開始: {race_id}")

        # レース情報を取得
        race_info = {}
        race_info["race_id"] = race_id

        # 開催場所をレースIDから取得（より確実）
        try:
            venue_id = race_id[4:6]
            race_info[RaceInfoCols.VENUE] = venue_id
        except IndexError:
            self.logger.warning(f"レースID '{race_id}' から開催場所を特定できませんでした。")
            race_info[RaceInfoCols.VENUE] = ""

        # 日付はHTMLから抽出することを優先。ここでは初期化しない。
        # race_info[RaceInfoCols.DATE] = "" # type: ignore # デフォルト値設定は後段で行う

        # レース名を取得
        race_name_elem = None
        for selector in RaceProcessorConstants.RACE_NAME_SELECTORS:
            race_name_elem = soup.select_one(selector)
            if race_name_elem:
                break

        if race_name_elem:
            race_info[RaceInfoCols.RACE_NAME] = race_name_elem.text.strip()
        else:
            race_info[RaceInfoCols.RACE_NAME] = ""

        # 参照ファイルの方法を使用して天候、レースの種類、コースの長さ、馬場の状態などを抽出
        try:
            # data_introクラスから情報を取得
            data_intro = soup.find("div", attrs={"class": "data_intro"})
            if data_intro:
                # 全てのテキストを結合
                texts = ""
                p_tags = data_intro.find_all("p")
                if p_tags:
                    texts = " ".join([p.text for p in p_tags])

                # data_intro から日付を抽出 (YYYY年MM月DD日 形式)
                # まず全てのpタグから日付を検索
                all_p_text = " ".join([p.get_text(strip=True) for p in p_tags])
                date_match_in_intro = RaceProcessorConstants._RE_DATE_IN_DETAILS.search(all_p_text)
                if date_match_in_intro:
                    race_info[RaceInfoCols.DATE] = f"{date_match_in_intro.group(1)}年{date_match_in_intro.group(2)}月{date_match_in_intro.group(3)}日"
                    self.logger.debug(f"data_introから日付を取得: {race_info[RaceInfoCols.DATE]}")
                else:
                    # RaceData01 クラスを持つ p タグからも探す（フォールバック）
                    race_data01_text = " ".join([p.get_text(strip=True) for p in data_intro.find_all("p", class_="RaceData01")])
                    date_match_in_intro = RaceProcessorConstants._RE_DATE_IN_DETAILS.search(race_data01_text)
                    if date_match_in_intro:
                        race_info[RaceInfoCols.DATE] = f"{date_match_in_intro.group(1)}年{date_match_in_intro.group(2)}月{date_match_in_intro.group(3)}日"
                        self.logger.debug(f"RaceData01から日付を取得: {race_info[RaceInfoCols.DATE]}")

                # 単語に分割
                info = re.findall(r'\w+', texts)

                # レース種別（芝・ダート・障害）を抽出
                for text in info:
                    if text in ["芝", "ダート"]:
                        race_info[RaceInfoCols.RACE_TYPE] = text
                    if "障" in text:
                        race_info[RaceInfoCols.RACE_TYPE] = "障害"

                    # コース距離を抽出
                    if "0m" in text:
                        try:
                            distance = int(re.findall(r"\d+", text)[-1])
                            # 距離は10の位を切り捨てる（参照ファイルの処理に合わせる）
                            race_info[RaceInfoCols.DISTANCE] = distance
                        except (ValueError, IndexError):
                            race_info[RaceInfoCols.DISTANCE] = 0 # type: ignore

                    # 馬場状態を抽出
                    if text in Master.GROUND_STATE_LIST:
                        race_info[RaceInfoCols.GROUND_STATE] = text

                    # 天気を抽出
                    if text in Master.WEATHER_LIST:
                        race_info[RaceInfoCols.WEATHER] = text

                    # 回り（右・左・直線・障害）
                    if text in Master.AROUND_LIST:
                        race_info[RaceInfoCols.AROUND] = text


            # 上記の方法で取得できなかった場合のバックアップ方法
            if RaceInfoCols.RACE_TYPE not in race_info or not race_info[RaceInfoCols.RACE_TYPE]:
                race_data_text = ""
                for selector in RaceProcessorConstants.RACE_DATA_SELECTORS:
                    race_data_elem = soup.select_one(selector)
                    if race_data_elem:
                        race_data_text = race_data_elem.text.strip()
                        break

                # バックアップ方法で日付を抽出
                if RaceInfoCols.DATE not in race_info or not race_info[RaceInfoCols.DATE]:
                    date_match_backup = RaceProcessorConstants._RE_DATE_IN_DETAILS.search(race_data_text)
                    if date_match_backup:
                        race_info[RaceInfoCols.DATE] = f"{date_match_backup.group(1)}年{date_match_backup.group(2)}月{date_match_backup.group(3)}日"
                        self.logger.debug(f"バックアップ方法で日付を取得: {race_info[RaceInfoCols.DATE]}")

                if '芝' in race_data_text:
                    race_info[RaceInfoCols.RACE_TYPE] = '芝'
                elif 'ダ' in race_data_text:
                    race_info[RaceInfoCols.RACE_TYPE] = 'ダート'
                elif '障' in race_data_text:
                    race_info[RaceInfoCols.RACE_TYPE] = '障害'
                else:
                    race_info[RaceInfoCols.RACE_TYPE] = ''

                # コース距離を抽出（バックアップ）
                if RaceInfoCols.DISTANCE not in race_info or not race_info[RaceInfoCols.DISTANCE]:
                    distance_match = re.search(r'(\d+)m', race_data_text)
                    if distance_match:
                        try:
                            distance = int(distance_match.group(1))
                            race_info[RaceInfoCols.DISTANCE] = distance
                        except (ValueError, AttributeError):
                            race_info[RaceInfoCols.DISTANCE] = 0 # type: ignore
                    else:
                        race_info[RaceInfoCols.DISTANCE] = 0 # type: ignore

                # 天気と馬場状態を抽出（バックアップ）
                if RaceInfoCols.WEATHER not in race_info or not race_info[RaceInfoCols.WEATHER]:
                    weather_match = RaceProcessorConstants._RE_WEATHER_IN_DETAILS.search(race_data_text) # type: ignore
                    if weather_match:
                        race_info[RaceInfoCols.WEATHER] = weather_match.group(1)
                        self.logger.debug(f"バックアップ方法で天候を取得: {race_info[RaceInfoCols.WEATHER]}")

                if RaceInfoCols.GROUND_STATE not in race_info or not race_info[RaceInfoCols.GROUND_STATE]:
                    track_match = RaceProcessorConstants._RE_TRACK_CONDITION_IN_DETAILS.search(race_data_text) # type: ignore
                    if track_match:
                        race_info[RaceInfoCols.GROUND_STATE] = track_match.group(1)
                        self.logger.debug(f"バックアップ方法で馬場状態を取得: {race_info[RaceInfoCols.GROUND_STATE]}")

        except Exception as e:
            self.logger.error(f"レース情報の抽出中にエラーが発生しました: {e}", exc_info=True)

        # 必須項目が取得できなかった場合のデフォルト値設定
        if RaceInfoCols.DATE not in race_info or not race_info[RaceInfoCols.DATE]: # 日付が最終的に取れなかった場合
            # レースIDから日付を推測（フォールバック）
            try:
                if len(race_id) >= 8:
                    year = race_id[:4]
                    month = race_id[4:6]
                    day = race_id[6:8]
                    race_info[RaceInfoCols.DATE] = f"{year}年{int(month)}月{int(day)}日"
                    self.logger.debug(f"レースIDから日付を推測: {race_info[RaceInfoCols.DATE]}")
                else:
                    race_info[RaceInfoCols.DATE] = ""
            except (ValueError, IndexError):
                race_info[RaceInfoCols.DATE] = "" # または pd.NaT や None
        for col_const in [RaceInfoCols.RACE_TYPE, RaceInfoCols.WEATHER, RaceInfoCols.GROUND_STATE, RaceInfoCols.AROUND]:
            if col_const not in race_info:
                race_info[col_const] = ""
        if RaceInfoCols.DISTANCE not in race_info:
            race_info[RaceInfoCols.DISTANCE] = 0

        # レース情報の内容をログに出力
        self.logger.debug(f"抽出されたレース情報 ({race_id}): {race_info}")
        return race_info

    def _parse_race_results_table(self, soup: BeautifulSoup, html_content_bytes: bytes, race_id: str) -> pd.DataFrame:
        """
        HTML(soup)からレース結果テーブルをパースし、DataFrameを生成する。
        ID抽出は _format_and_extract_ids_from_results_df で行う。
        """
        self.logger.debug(f"レース結果テーブルの特定を開始: {race_id}")
        # summary属性または特定のクラス名でテーブルを特定
        table_soup = soup.find("table", summary=RaceProcessorConstants._RE_TABLE_SUMMARY)
        if not table_soup:
            # フォールバックとして他の可能性のあるテーブルを探す
            table_soup = soup.find("table", class_=re.compile(r"(race_table_01|Shutuba_Table|Result_Table)"))
            if not table_soup:
                self.logger.warning(f"レース結果/出馬表テーブルが見つかりません: {race_id}")
                return pd.DataFrame()
        self.logger.debug(f"レース結果テーブルを特定完了: {race_id}")

        # DataFrame化
        html_str = str(table_soup)
        html_str = html_str.replace('<br>', ' ').replace('<br/>', ' ').replace('<br />', ' ')
        try:
            self.logger.debug(f"pd.read_html でテーブルをパース中: {race_id}")
            df = pd.read_html(io.StringIO(html_str), flavor='bs4', header=0)[0]
            self.logger.debug(f"pd.read_html でのパース成功: {race_id}, {len(df)}行")
        except Exception as e:
            self.logger.error(f"pd.read_htmlでのレース結果テーブルのパースに失敗: {race_id}, エラー: {e}")
            return pd.DataFrame()

        # ID抽出とフォーマット処理
        df = self._format_and_extract_ids_from_results_df(df, table_soup, race_id)

        df["race_id"] = race_id
        return df

    def _map_header_to_standard_col(self, header_text: str, col_index: int) -> str:
        """ヘッダーテキストをResultsColsの標準的なカラム名にマッピングする"""
        # 完全一致または部分一致でマッピング
        # (このロジックは _format_and_extract_ids_from_results_df のカラム名変更ロジックと類似)
        header_text_norm = header_text.strip().replace(' ', '').replace('　', '')
        mapping_priority = [ # ResultsCols の参照を修正
            (ResultsCols.RANK, ['着順', '順位']), (ResultsCols.WAKUBAN, ['枠番', '枠']),
            (ResultsCols.UMABAN, ['馬番', '番号']), (ResultsCols.HORSE_NAME, ['馬名', '名前']),
            (ResultsCols.SEX_AGE, ['性齢', '性/齢', '性別', '年齢']), (ResultsCols.KINRYO, ['斤量']),
            (ResultsCols.JOCKEY, ['騎手']), (ResultsCols.TIME, ['タイム']),
            (ResultsCols.RANK_DIFF, ['着差']), (ResultsCols.POPULARITY, ['人気']),
            (ResultsCols.TANSHO_ODDS, ['単勝']), (ResultsCols.WEIGHT_AND_DIFF, ['馬体重', '体重']),
            (ResultsCols.TRAINER, ['調教師'])
        ]
        for standard_col, keywords in mapping_priority:
            if any(kw in header_text_norm for kw in keywords):
                return standard_col

        # インデックスベースのフォールバック (限定的)
        if col_index == 0 and not header_text_norm.isdigit(): return ResultsCols.RANK # ResultsCols の参照を修正
        if col_index == 1 and '枠' in header_text_norm: return ResultsCols.WAKUBAN
        # ...
        return header_text # マッピングできなかった場合は元のヘッダーを返す

    def _format_and_extract_ids_from_results_df(self, df: pd.DataFrame, table_soup_for_ids: Any, race_id: str) -> pd.DataFrame:
        """
        pd.read_htmlで取得したDataFrameを整形し、馬・騎手・調教師IDを抽出・追加する。
        ID抽出には、元のテーブルのHTML構造(table_soup_for_ids)を参照する。
        """
        if df.empty:
            return df

        # df = df.copy() # _parse_race_results_table から渡されるdfは既にコピーまたは新規作成されている想定
        df['race_id'] = race_id

        # カラム名を標準化
        new_columns = {}
        for i, col_original in enumerate(df.columns):
            col_str_norm = str(col_original).strip().replace(' ', '').replace('　', '')
            standard_name = self._map_header_to_standard_col(col_str_norm, i)
            # 重複を避けるため、既に新しい名前がある場合はサフィックスをつける
            final_name = standard_name
            counter = 1
            while final_name in new_columns.values():
                final_name = f"{standard_name}_{counter}"
                counter += 1
            new_columns[col_original] = final_name
        df = df.rename(columns=new_columns)
        self.logger.debug(f"pd.read_html後のカラム名変更後 ({race_id}): {df.columns.tolist()}")

        # ID抽出用の列を準備
        for id_col in [ResultsCols.HORSE_ID, ResultsCols.JOCKEY_ID, ResultsCols.TRAINER_ID]: # ResultsCols の参照を修正
            if id_col not in df.columns:
                df[id_col] = None

        # 元のHTMLテーブルの行とDataFrameの行を対応させてIDを抽出
        if table_soup_for_ids:
            self.logger.debug(f"元のHTMLテーブル構造からID抽出を開始: {race_id}")
            rows_in_soup = table_soup_for_ids.find_all('tr')
            # ヘッダー行を除外 (通常は最初の行)
            data_rows_in_soup = rows_in_soup[1:] if len(rows_in_soup) > 1 else []

            if len(data_rows_in_soup) == len(df):
                horse_ids_extracted = []
                jockey_ids_extracted = []
                trainer_ids_extracted = []

                for soup_row in data_rows_in_soup:
                    ids_in_row = self._extract_ids_from_cell_content(soup_row) # soup_row全体を渡す
                    horse_ids_extracted.append(ids_in_row.get("horse_id"))
                    jockey_ids_extracted.append(ids_in_row.get("jockey_id"))
                    trainer_ids_extracted.append(ids_in_row.get("trainer_id"))

                df[ResultsCols.HORSE_ID] = horse_ids_extracted
                df[ResultsCols.JOCKEY_ID] = jockey_ids_extracted
                df[ResultsCols.TRAINER_ID] = trainer_ids_extracted
                self.logger.debug(f"HTMLテーブル構造からのID抽出・マッピング完了 ({race_id})")
            else:
                self.logger.warning(f"HTMLテーブルの行数 ({len(data_rows_in_soup)}) とDataFrameの行数 ({len(df)}) が一致しません。セルごとのID抽出にフォールバックします。 ({race_id})")
                # フォールバック: 各セルのHTML文字列からIDを抽出 (元のロジックに近いが、より汎用的に)
                # 馬名、騎手、調教師カラムが特定できているか確認
                horse_name_col = ResultsCols.HORSE_NAME if ResultsCols.HORSE_NAME in df.columns else None
                jockey_col = ResultsCols.JOCKEY if ResultsCols.JOCKEY in df.columns else None
                trainer_col = ResultsCols.TRAINER if ResultsCols.TRAINER in df.columns else None

                for index, row_series in df.iterrows():
                    # 各セルのHTML文字列 (pd.read_htmlは通常文字列として返す) からIDを抽出
                    if horse_name_col and pd.notna(row_series[horse_name_col]):
                        ids = self._extract_ids_from_cell_content(str(row_series[horse_name_col]))
                        if ids["horse_id"]: df.loc[index, ResultsCols.HORSE_ID] = ids["horse_id"]

                    if jockey_col and pd.notna(row_series[jockey_col]):
                        ids = self._extract_ids_from_cell_content(str(row_series[jockey_col]))
                        if ids["jockey_id"]: df.loc[index, ResultsCols.JOCKEY_ID] = ids["jockey_id"]

                    if trainer_col and pd.notna(row_series[trainer_col]):
                        ids = self._extract_ids_from_cell_content(str(row_series[trainer_col]))
                        if ids["trainer_id"]: df.loc[index, ResultsCols.TRAINER_ID] = ids["trainer_id"]

        else:
            self.logger.warning(f"ID抽出のための元テーブルHTML(table_soup_for_ids)が提供されませんでした。 ({race_id})")


        # 不要なマルチレベルカラムの削除 (pd.read_htmlが生成することがある)
        if isinstance(df.columns, pd.MultiIndex):
            self.logger.debug(f"マルチインデックスカラムをフラット化: {race_id}")
            df.columns = df.columns.get_level_values(0) # 通常は最初のレベルでOK

        # 'Unnamed' カラムの削除
        df = df.loc[:, ~df.columns.str.contains('^Unnamed')]
        return df

    def merge_past_horse_performance(self,
                                     current_race_results_df: pd.DataFrame,
                                     all_horse_past_results_df: pd.DataFrame,
                                     target_cols: List[str],
                                     n_races_list: List[int] = [5, 9],
                                     group_cols: Optional[List[str]] = None
                                     ) -> pd.DataFrame:
        """
        レース結果データに、各出走馬のそのレース時点での過去戦績をマージする。

        Parameters
        ----------
        current_race_results_df : pd.DataFrame
            マージ対象の現在のレース結果DataFrame。'horse_id'と'date'(datetime型)列が必要。
        all_horse_past_results_df : pd.DataFrame
            全馬の過去の全レース結果を含むDataFrame。
            'horse_id', 'date'(datetime型), および target_cols に指定する成績列が必要。
        target_cols : List[str]
            all_horse_past_results_df の中で集計対象とする列名のリスト (例: ['着順', '賞金'])。
        n_races_list : List[int], optional
            直近Nレースとして集計するレース数のリスト。デフォルトは [5, 9]。
        group_cols : Optional[List[str]], optional
            追加のグルーピング列。指定された場合、これらの列ごとにも集計。デフォルトは None。

        Returns
        -------
        pd.DataFrame
            過去戦績がマージされたDataFrame。
        """
        if current_race_results_df.empty:
            self.logger.warning("マージ対象の現在のレース結果データが空です。")
            return pd.DataFrame()
        if all_horse_past_results_df.empty:
            self.logger.warning("過去戦績の計算に必要な全馬の過去成績データが空です。")
            return current_race_results_df

        # 'date' 列の型チェックと変換 (all_horse_past_results_df)
        if 'date' not in all_horse_past_results_df.columns or \
           not pd.api.types.is_datetime64_any_dtype(all_horse_past_results_df['date']):
            date_col_name_past = 'date' # デフォルト
            if '日付' in all_horse_past_results_df.columns: date_col_name_past = '日付'
            if date_col_name_past in all_horse_past_results_df:
                 all_horse_past_results_df['date'] = pd.to_datetime(all_horse_past_results_df[date_col_name_past], errors='coerce')
            if 'date' not in all_horse_past_results_df.columns or not pd.api.types.is_datetime64_any_dtype(all_horse_past_results_df['date']):
                self.logger.error("全馬の過去成績データに有効な 'date' 列 (datetime型) が見つかりません。")
                return current_race_results_df

        # 'date' 列の型チェックと変換 (current_race_results_df)
        if 'date' not in current_race_results_df.columns or \
           not pd.api.types.is_datetime64_any_dtype(current_race_results_df['date']):
            date_col_name_current = RaceInfoCols.DATE # scraping_constants から
            if date_col_name_current not in current_race_results_df.columns and '日付' in current_race_results_df.columns:
                date_col_name_current = '日付'

            if date_col_name_current in current_race_results_df:
                current_race_results_df['date'] = pd.to_datetime(current_race_results_df[date_col_name_current], errors='coerce')
            if 'date' not in current_race_results_df.columns or not pd.api.types.is_datetime64_any_dtype(current_race_results_df['date']):
                self.logger.error("現在のレース結果データに有効な 'date' 列 (datetime型) が見つかりません。")
                return current_race_results_df

        if 'horse_id' not in current_race_results_df.columns:
            self.logger.error("現在のレース結果データに 'horse_id' 列が見つかりません。")
            return current_race_results_df
        if 'horse_id' not in all_horse_past_results_df.columns:
            self.logger.error("全馬の過去成績データに 'horse_id' 列が見つかりません。")
            return current_race_results_df

        # horse_id の型を文字列に統一
        current_race_results_df['horse_id'] = current_race_results_df['horse_id'].astype(str)
        all_horse_past_results_df['horse_id'] = all_horse_past_results_df['horse_id'].astype(str)
        self.logger.debug("horse_idカラムを文字列型に統一しました。")

        processed_results_list = []
        unique_dates = sorted(current_race_results_df['date'].unique())

        for race_date in tqdm(unique_dates, desc="過去戦績マージ中"):
            results_on_date_df = current_race_results_df[current_race_results_df['date'] == race_date].copy()
            if results_on_date_df.empty:
                continue

            horse_id_list_on_date = results_on_date_df['horse_id'].unique()

            past_horse_results_for_date = all_horse_past_results_df[
                (all_horse_past_results_df['horse_id'].isin(horse_id_list_on_date)) &
                (all_horse_past_results_df['date'] < race_date)
            ].copy()

            if past_horse_results_for_date.empty:
                processed_results_list.append(results_on_date_df)
                continue

            # 集計対象となる有効な target_cols をフィルタリング
            valid_target_cols = [col for col in target_cols if col in past_horse_results_for_date.columns]
            if not valid_target_cols:
                self.logger.warning(f"レース日 {race_date}: 有効な集計対象カラム (target_cols) が過去成績データに存在しません。この日付の集計をスキップします。")
                processed_results_list.append(results_on_date_df)
                continue
            if len(valid_target_cols) < len(target_cols):
                self.logger.debug(f"レース日 {race_date}: target_colsの一部が存在しません。存在するカラムのみ使用: {valid_target_cols}")

            # 直近Nレースの集計
            for n_races in n_races_list:
                n_race_filtered_results = past_horse_results_for_date.sort_values('date', ascending=False)\
                                                                    .groupby('horse_id').head(n_races)
                if not n_race_filtered_results.empty:
                    summarized_n = n_race_filtered_results.groupby('horse_id')[valid_target_cols].mean()\
                                                        .add_suffix(f'_last_{n_races}R_mean')
                    results_on_date_df = results_on_date_df.merge(summarized_n, on='horse_id', how='left')

                    if group_cols:
                        valid_group_cols_n = [
                            gc for gc in group_cols
                            if gc in n_race_filtered_results.columns and gc in results_on_date_df.columns
                        ]
                        if not valid_group_cols_n and group_cols:
                             self.logger.debug(f"レース日 {race_date}, 直近{n_races}R: 指定されたgroup_cols ({group_cols}) のいずれもデータに存在しません。")
                        for group_col_valid in valid_group_cols_n:
                                summarized_n_with = n_race_filtered_results.groupby(['horse_id', group_col_valid])[valid_target_cols].mean()\
                                                                        .add_suffix(f'_{group_col}_last_{n_races}R_mean')
                                results_on_date_df = results_on_date_df.merge(summarized_n_with, on=['horse_id', group_col_valid], how='left', suffixes=('', f'_gcol_{group_col_valid}'))

            # 全期間の集計
            if not past_horse_results_for_date.empty:
                summarized_all = past_horse_results_for_date.groupby('horse_id')[valid_target_cols].mean()\
                                                            .add_suffix('_all_R_mean')
                results_on_date_df = results_on_date_df.merge(summarized_all, on='horse_id', how='left')

                if group_cols:
                    valid_group_cols_all = [
                        gc for gc in group_cols
                        if gc in past_horse_results_for_date.columns and gc in results_on_date_df.columns
                    ]
                    if not valid_group_cols_all and group_cols:
                        self.logger.debug(f"レース日 {race_date}, 全期間: 指定されたgroup_cols ({group_cols}) のいずれもデータに存在しません。")
                    for group_col_valid in valid_group_cols_all:
                            summarized_all_with = past_horse_results_for_date.groupby(['horse_id', group_col_valid])[valid_target_cols].mean()\
                                                                            .add_suffix(f'_{group_col}_all_R_mean')
                            results_on_date_df = results_on_date_df.merge(summarized_all_with, on=['horse_id', group_col_valid], how='left', suffixes=('', f'_gcol_{group_col_valid}'))

            # 前走の日付と経過日数
            if not past_horse_results_for_date.empty:
                latest_race_date_series = past_horse_results_for_date.groupby('horse_id')['date'].max().rename('last_race_date')
                results_on_date_df = results_on_date_df.merge(latest_race_date_series, on='horse_id', how='left')

                # 'interval_days' の計算を安全に行う
                if 'last_race_date' in results_on_date_df.columns:
                    # 'last_race_date' が NaT でない行のみ計算対象とする
                    valid_last_race_dates = results_on_date_df['last_race_date'].notna() & \
                                            pd.api.types.is_datetime64_any_dtype(results_on_date_df['last_race_date'])

                    if valid_last_race_dates.any(): # 有効な日付が一つでもあれば
                        # 'date' も datetime型であることを確認
                        if pd.api.types.is_datetime64_any_dtype(results_on_date_df['date']):
                            results_on_date_df.loc[valid_last_race_dates, 'interval_days'] = \
                                (results_on_date_df.loc[valid_last_race_dates, 'date'] - results_on_date_df.loc[valid_last_race_dates, 'last_race_date']).dt.days
                        else:
                            results_on_date_df['interval_days'] = pd.NA
                    else: # 有効な last_race_date がない場合
                        results_on_date_df['interval_days'] = pd.NA
                else:
                    results_on_date_df['interval_days'] = pd.NA # 前走がない、または日付型でない場合

            processed_results_list.append(results_on_date_df)

        if not processed_results_list:
            self.logger.info("過去戦績をマージする対象レースがありませんでした。")
            return current_race_results_df

        final_merged_df = pd.concat(processed_results_list, ignore_index=True)
        self.logger.info(f"過去戦績のマージが完了しました。処理後の行数: {len(final_merged_df)}")
        return final_merged_df

    def extract_horse_sex_age_mapping(self, year: Optional[int] = None, race_id: Optional[str] = None,
                                     html_path_list: Optional[List[str]] = None) -> pd.DataFrame:
        """
        レース結果から馬IDと性齢情報のマッピングを抽出する

        Parameters
        ----------
        year : int, optional
            対象年度。html_path_listがNoneの場合に使用
        race_id : str, optional
            特定のレースIDを指定する場合
        html_path_list : List[str], optional
            処理対象のHTMLファイルパスのリスト

        Returns
        -------
        pd.DataFrame
            馬IDと性齢情報のマッピング（列: horse_id, 性齢, 性, 年齢, race_id, 日付）
        """
        self.logger.info("馬IDと性齢情報のマッピング抽出を開始")

        # HTMLファイルパスのリストを取得
        if html_path_list is None:
            import glob
            html_path_list = []
            if year:
                # 年からファイルリストを取得
                pattern = os.path.join(LocalPaths.HTML_RACE_DIR, "race_by_year", str(year), "*.bin")
                html_path_list.extend(glob.glob(pattern))
                if not html_path_list:
                    # フォールバック
                    pattern_fallback = os.path.join(LocalPaths.HTML_RACE_DIR, f"*{year}*.bin")
                    html_path_list.extend(glob.glob(pattern_fallback))
            elif race_id:
                # race_idから年を抽出
                year_from_id = race_id[:4]
                path = os.path.join(LocalPaths.HTML_RACE_DIR, "race_by_year", year_from_id, f"{race_id}.bin")
                if os.path.exists(path):
                    html_path_list.append(path)

        if not html_path_list:
            self.logger.warning("処理対象のHTMLファイルが見つかりません")
            return pd.DataFrame()

        mapping_list = []

        for html_path in tqdm(html_path_list, desc="性齢情報抽出"):
            try:
                # レース結果をパース
                race_info_df, results_df = self.parse_race_html(html_path)
                if results_df is None or results_df.empty:
                    continue

                race_id_current = os.path.basename(html_path).replace('.bin', '')
                race_date = None
                if not race_info_df.empty and RaceInfoCols.DATE in race_info_df.columns:
                    race_date = race_info_df[RaceInfoCols.DATE].iloc[0] if len(race_info_df) > 0 else None

                # 性齢情報が含まれているかチェック
                if ResultsCols.SEX_AGE not in results_df.columns:
                    self.logger.debug(f"性齢情報が見つかりません: {html_path}")
                    continue

                # horse_idが含まれているかチェック
                if ResultsCols.HORSE_ID not in results_df.columns:
                    self.logger.debug(f"馬ID情報が見つかりません: {html_path}")
                    continue

                # 必要な列のみ抽出
                mapping_df = results_df[[ResultsCols.HORSE_ID, ResultsCols.SEX_AGE]].copy()
                mapping_df = mapping_df.dropna(subset=[ResultsCols.HORSE_ID, ResultsCols.SEX_AGE])

                if mapping_df.empty:
                    continue

                # 性齢を性と年齢に分離
                sex_age_str = mapping_df[ResultsCols.SEX_AGE].astype(str)
                mapping_df["性"] = sex_age_str.apply(lambda x: x[0] if len(x) > 0 else None)
                mapping_df["年齢"] = pd.to_numeric(sex_age_str.apply(lambda x: x[1:] if len(x) > 1 else None),
                                                errors='coerce').astype('Int64')

                # 性別のバリデーション
                valid_sexes = ['牡', '牝', 'セ']
                mapping_df.loc[~mapping_df["性"].isin(valid_sexes), "性"] = pd.NA

                # レース情報を追加
                mapping_df['race_id'] = race_id_current
                mapping_df['日付'] = race_date

                # 列名を整理
                mapping_df = mapping_df.rename(columns={
                    ResultsCols.HORSE_ID: 'horse_id',
                    ResultsCols.SEX_AGE: '性齢'
                })

                mapping_list.append(mapping_df)

            except Exception as e:
                self.logger.error(f"性齢情報抽出エラー ({html_path}): {e}")
                continue

        if not mapping_list:
            self.logger.warning("性齢情報を抽出できませんでした")
            return pd.DataFrame()

        # 全てのマッピングを結合
        final_mapping_df = pd.concat(mapping_list, ignore_index=True)

        # 重複を除去（同じ馬が複数のレースに出走している場合）
        # 最新の情報を優先（日付順でソートして重複除去）
        if '日付' in final_mapping_df.columns:
            final_mapping_df = final_mapping_df.sort_values('日付', ascending=False)

        # horse_idごとに最新の性齢情報を保持
        final_mapping_df = final_mapping_df.drop_duplicates(subset=['horse_id'], keep='first')

        self.logger.info(f"性齢情報マッピング抽出完了: {len(final_mapping_df)}件")
        return final_mapping_df

    def _preprocess_race_info(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        レース情報の前処理

        Parameters
        ----------
        df : pd.DataFrame
            処理対象のデータフレーム

        Returns
        -------
        pd.DataFrame
            処理後のデータフレーム
        """
        # 距離の前処理
        if RaceInfoCols.DISTANCE in df.columns:
            df[RaceInfoCols.DISTANCE] = pd.to_numeric(df[RaceInfoCols.DISTANCE], errors='coerce')
            # 100の位で丸める (例: 1600m -> 16)
            df[RaceInfoCols.DISTANCE] = (df[RaceInfoCols.DISTANCE] // 100).astype('Int64')

        # 日付型に変換 (YYYY年MM月DD日 または YYYY-MM-DD 形式を想定)
        if RaceInfoCols.DATE in df.columns:
            try:
                # 日付形式が「2025年01月05日」の場合
                df[RaceInfoCols.DATE] = pd.to_datetime(df[RaceInfoCols.DATE], format="%Y年%m月%d日", errors='coerce')
            except:
                try:
                    # ISO形式の場合（例：2025-01-05）
                    df[RaceInfoCols.DATE] = pd.to_datetime(df[RaceInfoCols.DATE], errors='coerce')
                except Exception: # 更なるフォールバック
                    # 変換できない場合はそのまま
                    pass

        # 開催場所
        if RaceInfoCols.VENUE in df.columns:
            df[RaceInfoCols.VENUE] = df[RaceInfoCols.VENUE].astype(str) # 文字列型に統一

        # 開催場所（レースIDから取得したものを優先、なければ既存のVENUE列を使用）
        if 'race_id' in df.columns:
            # '開催' というカラム名で上書き、または新規作成
            df[RaceInfoCols.VENUE] = df['race_id'].astype(str).str[4:6] # 文字列変換とスライス

        # 天候、レース種別、馬場状態などの前処理
        if RaceInfoCols.WEATHER in df.columns:
            df[RaceInfoCols.WEATHER] = df[RaceInfoCols.WEATHER].astype(str)
            df[RaceInfoCols.WEATHER] = pd.Categorical(df[RaceInfoCols.WEATHER], Master.WEATHER_LIST)

        if RaceInfoCols.GROUND_STATE in df.columns: # parse_race_htmlでこのキーで格納
            df[RaceInfoCols.GROUND_STATE] = df[RaceInfoCols.GROUND_STATE].astype(str) # 文字列型に統一
            df[RaceInfoCols.GROUND_STATE] = pd.Categorical(df[RaceInfoCols.GROUND_STATE], Master.GROUND_STATE_LIST)

        return df

    def _preprocess_results(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        レース結果の前処理

        Parameters
        ----------
        df : pd.DataFrame
            処理対象のデータフレーム

        Returns
        -------
        pd.DataFrame
            処理後のデータフレーム
        """
        # 着順の前処理
        df = self._preprocess_rank(df)

        # 性齢を性と年齢に分ける
        if ResultsCols.SEX_AGE in df.columns:
            sex_age_str = df[ResultsCols.SEX_AGE].astype(str)
            # 先頭1文字を性別とし、存在しない場合はNone (str[0]は文字列が空だとエラーになるため、より安全な方法を検討)
            df["性"] = sex_age_str.apply(lambda x: x[0] if len(x) > 0 else None)
            # 2文字目以降を年齢とし、数値に変換できない場合や存在しない場合はNA(Int64対応) (str[1:]も同様)
            df["年齢"] = pd.to_numeric(sex_age_str.apply(lambda x: x[1:] if len(x) > 1 else None), errors='coerce').astype('Int64')
            df.loc[sex_age_str.str.len() <= 1, "年齢"] = pd.NA

        # 各列を数値型に変換 (元のキー名が定数と異なる場合があるため、元のキー名でチェック)
        if ResultsCols.TANSHO_ODDS in df.columns:
            df[ResultsCols.TANSHO_ODDS] = pd.to_numeric(df[ResultsCols.TANSHO_ODDS], errors='coerce').astype(float)
        if ResultsCols.KINRYO in df.columns:
            df[ResultsCols.KINRYO] = pd.to_numeric(df[ResultsCols.KINRYO], errors='coerce').astype(float)
        if ResultsCols.WAKUBAN in df.columns:
            df[ResultsCols.WAKUBAN] = pd.to_numeric(df[ResultsCols.WAKUBAN], errors='coerce').astype('Int64')
        if ResultsCols.UMABAN in df.columns:
            df[ResultsCols.UMABAN] = pd.to_numeric(df[ResultsCols.UMABAN], errors='coerce').astype('Int64')

        # 着差の処理（1着の着差を0にする）
        if ResultsCols.RANK_DIFF in df.columns:
            df[ResultsCols.RANK_DIFF] = pd.to_numeric(df[ResultsCols.RANK_DIFF], errors='coerce')
            # 1着（着順が1）の場合は着差を0に
            df.loc[df[ResultsCols.RANK] == 1, ResultsCols.RANK_DIFF] = 0

        # タイムの値を秒単位に変換
        if ResultsCols.TIME in df.columns:
            try:
                # 準備
                baseformat = '%M:%S.%f' # ResultsCols.TIME の参照を修正
                basetime = pd.to_datetime("00:00.0", format=baseformat)
                to_datetime = lambda x: pd.to_datetime(df[ResultsCols.TIME], format=x, errors='coerce') # .name を削除

                # 秒単位へのフォーマット変換処理
                datetime_s = to_datetime(baseformat)

                # 「x:xx.x」フォーマット以外、許容するフォーマットを定義
                formats_additional = ['%M.%S.%f', '%M:%S:%f']
                for format_ in formats_additional:
                    # 秒単位へのフォーマット変換処理
                    datetime_s = datetime_s.fillna(to_datetime(format_))

                # フォーマット例外は欠損値になる
                df['time_seconds'] = (datetime_s - basetime).dt.total_seconds()
            except Exception as e:
                self.logger.warning(f"タイムの変換中にエラーが発生しました: {e}")

        # 出走数追加
        if 'race_id' in df.columns:
            df['n_horses'] = df.groupby('race_id')['race_id'].transform('count')

        return df

    def _preprocess_rank(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        着順の前処理

        Parameters
        ----------
        df : pd.DataFrame
            処理対象のデータフレーム

        Returns
        -------
        pd.DataFrame
            処理後のデータフレーム
        """ # ResultsCols.RANK の参照を修正
        if ResultsCols.RANK in df.columns:
            # 着順に数字以外の文字列が含まれているものは、欠損値（NaN）に置き換える
            df = df.copy()  # SettingWithCopyWarning対策
            df[ResultsCols.RANK] = pd.to_numeric(df[ResultsCols.RANK], errors='coerce')

            # 着順が欠損値（NaN）となったものを取り除く
            df.dropna(subset=[ResultsCols.RANK], inplace=True)

            # 整数型に変換
            df[ResultsCols.RANK] = df[ResultsCols.RANK].astype(int)

            # 3着以内を1、それ以外を0とする
            df['rank_binary'] = np.where((df[ResultsCols.RANK] > 0) & (df[ResultsCols.RANK] < 4), 1, 0)

        return df

    def preprocess_data(self) -> pd.DataFrame:
        """
        レースデータの前処理を行い、結果を返す

        Returns
        -------
        pd.DataFrame
            前処理後の結合データ
        """
        if self._race_info_df.empty or self._race_results_df.empty:
            self.logger.warning("前処理するデータがありません。process_race_bin_filesメソッドを先に実行してください。")
            return pd.DataFrame()

        # レース情報の前処理
        race_info_df = self._preprocess_race_info(self._race_info_df.copy())

        # 出走馬のレース結果の前処理
        race_results_df = self._preprocess_results(self._race_results_df.copy())

        # レース情報とレース結果を結合
        # race_idをキーとして結合
        if 'race_id' in race_results_df.columns and 'race_id' in race_info_df.columns:
            merged_df = race_results_df.merge(
                race_info_df,
                on='race_id',
                how='left'
            )
            return merged_df
        else:
            self.logger.error("結合に必要なカラム(race_id)がありません。")
            return pd.DataFrame()

    def get_race_features(self) -> pd.DataFrame:
        """
        レース特徴量を取得

        Returns
        -------
        pd.DataFrame
            レース特徴量
        """
        if self._race_info_df.empty:
            self.logger.warning("レース情報がありません。process_race_bin_filesメソッドを先に実行してください。")
            return pd.DataFrame()

        # レース特徴量を抽出
        race_features = self._race_info_df.copy()

        # レース特徴量として使用するカラム
        race_feature_cols = [
            'race_id',
            RaceInfoCols.DATE,
            RaceInfoCols.VENUE,
            RaceInfoCols.WEATHER,
            RaceInfoCols.GROUND_STATE,
        ]

        # 存在するカラムのみを選択
        existing_columns = [col for col in race_feature_cols if col in race_features.columns]

        return race_features[existing_columns]

    def select_columns(self, df=None, columns=None) -> pd.DataFrame:
        """
        必要なカラムを選択

        Parameters
        ----------
        df : pd.DataFrame, optional
            選択対象のDataFrame。指定しない場合はpreprocess_dataメソッドの結果を使用。
        columns : List[str], optional
            選択するカラムのリスト。Noneの場合はデフォルトのカラムを使用

        Returns
        -------
        pd.DataFrame
            選択後のデータフレーム
        """
        if df is None:
            df = self.preprocess_data()

        if df.empty:
            return pd.DataFrame()

        # 選択するカラムが指定されていない場合はデフォルトのカラムを使用
        if columns is None:
            columns = self._config.get('selected_columns', [
                ResultsCols.WAKUBAN,
                ResultsCols.UMABAN,
                ResultsCols.KINRYO,
                ResultsCols.TANSHO_ODDS,
                'horse_id',
                '性',
                '年齢',
                'n_horses',
                'rank_binary' # _preprocess_rank で生成される列名 'rank_binary'
            ])

        # 存在するカラムのみを選択
        existing_columns = [col for col in columns if col in df.columns]

        return df[existing_columns]

    def process_race_bin_files(self, year: Optional[str] = None, race_id: Optional[str] = None,
                               parallel: bool = True, max_workers: Optional[int] = None,
                               max_files: Optional[int] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        指定された年またはレースIDのレース情報binファイルを処理し、
        レース情報とレース結果のDataFrameを生成・更新する。

        Parameters
        ----------
        year : str, optional
            処理する年。Noneの場合はrace_idが必須。
        race_id : str, optional
            処理する特定のレースID。Noneの場合はyearが必須。
        parallel : bool, default True
            並列処理を使用するかどうか。
        max_workers : int, optional
            並列処理の最大ワーカー数。Noneの場合はCPUコア数。
        max_files : int, optional
            処理するファイルの最大数（年度指定時）。Noneの場合は全ファイルを処理。

        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            (レース情報のDataFrame, レース結果のDataFrame)
        """
        self.logger.info(f"レースbinファイルの処理を開始: year={year}, race_id={race_id}")

        # HTMLファイルパスのリストを取得
        html_path_list = []
        if race_id:
            # race_idから年を抽出
            year_from_id = race_id[:4]
            # パスを構築 (LocalPaths.HTML_RACE_DIR は scraping_constants から)
            # 実際のファイル構造に合わせて調整が必要
            # 例: data/html/race/race_by_year/2023/2023xxxxxxxx.bin
            path = os.path.join(LocalPaths.HTML_RACE_DIR, year_from_id, f"{race_id}.bin")
            if os.path.exists(path):
                html_path_list.append(path)
            else:
                # フォールバック: data/html/race/2023xxxxxxxx.bin
                path_fallback = os.path.join(LocalPaths.HTML_RACE_DIR, f"{race_id}.bin")
                if os.path.exists(path_fallback):
                    html_path_list.append(path_fallback)
                else:
                    self.logger.warning(f"レースID {race_id} のHTMLファイルが見つかりません。")
        elif year:
            # 年からファイルリストを取得
            # 例: data/html/race/race_by_year/2023/*.bin
            pattern = os.path.join(LocalPaths.HTML_RACE_DIR, year, "*.bin")
            html_path_list.extend(glob.glob(pattern))
            if not html_path_list:
                # フォールバック: data/html/race/*2023*.bin
                pattern_fallback = os.path.join(LocalPaths.HTML_RACE_DIR, f"*{year}*.bin")
                html_path_list.extend(glob.glob(pattern_fallback))
            if not html_path_list:
                self.logger.warning(f"{year}年のレースHTMLファイルが見つかりません。")

        if not html_path_list:
            self.logger.warning("処理対象のHTMLファイルがありません。")
            return pd.DataFrame(), pd.DataFrame()

        if max_files is not None and len(html_path_list) > max_files:
            html_path_list = html_path_list[:max_files]
            self.logger.info(f"処理ファイル数を{max_files}に制限しました。")

        all_race_info = []
        all_race_results = []

        if not max_workers:
            max_workers = os.cpu_count() or 1

        if parallel and len(html_path_list) > 1:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(self.parse_race_html, path): path for path in html_path_list}
                for future in tqdm(as_completed(futures), total=len(html_path_list), desc="レースHTMLパース(並列)"):
                    path_completed = futures[future]
                    try:
                        info_df, results_df = future.result()
                        if not info_df.empty:
                            all_race_info.append(info_df)
                        if not results_df.empty:
                            all_race_results.append(results_df)
                    except Exception as e:
                        self.logger.error(f"ファイル {path_completed} のパース中にエラー: {e}", exc_info=True)
        else:
            for path in tqdm(html_path_list, desc="レースHTMLパース(逐次)"):
                try:
                    info_df, results_df = self.parse_race_html(path)
                    if not info_df.empty:
                        all_race_info.append(info_df)
                    if not results_df.empty:
                        all_race_results.append(results_df)
                except Exception as e:
                    self.logger.error(f"ファイル {path} のパース中にエラー: {e}", exc_info=True)

        # 結果を結合してインスタンス変数に格納
        if all_race_info:
            self._race_info_df = pd.concat(all_race_info, ignore_index=True)
            self.logger.info(f"レース情報: {len(self._race_info_df)}件")
        else:
            self._race_info_df = pd.DataFrame()
            self.logger.info("レース情報データは空です。")

        if all_race_results:
            self._race_results_df = pd.concat(all_race_results, ignore_index=True)
            self.logger.info(f"レース結果: {len(self._race_results_df)}件")
        else:
            self._race_results_df = pd.DataFrame()
            self.logger.info("レース結果データは空です。")

        return self._race_info_df.copy(), self._race_results_df.copy()

    def save_race_data_to_csv(self, year: Optional[str] = None, race_id: Optional[str] = None,
                              prefix: str = "race_data") -> None:
        """
        処理されたレース情報と結果をCSVファイルに保存する。
        ファイル名には年またはレースID、およびタイムスタンプが含まれる。

        Parameters
        ----------
        year : str, optional
            ファイル名に含める年。
        race_id : str, optional
            ファイル名に含めるレースID。yearより優先される。
        prefix : str, default "race_data"
            ファイル名のプレフィックス。
        """
        if self._race_info_df.empty and self._race_results_df.empty:
            self.logger.warning("保存するデータがありません。")
            return

        # 保存先ディレクトリを作成
        csv_dir = os.path.join(LocalPaths.DATA_DIR, "csv") # LocalPaths は scraping_constants から
        os.makedirs(csv_dir, exist_ok=True)

        # ファイル名を生成
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        suffix = ""
        if race_id:
            suffix = f"_{race_id}"
        elif year:
            suffix = f"_{year}"

        # レース情報を保存
        if not self._race_info_df.empty:
            info_filename = f"{prefix}_info{suffix}_{timestamp}.csv"
            info_path = os.path.join(csv_dir, info_filename)
            self._race_info_df.to_csv(info_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"レース情報をCSVファイルに保存しました: {info_path}")

        # レース結果を保存
        if not self._race_results_df.empty:
            results_filename = f"{prefix}_results{suffix}_{timestamp}.csv"
            results_path = os.path.join(csv_dir, results_filename)
            self._race_results_df.to_csv(results_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"レース結果をCSVファイルに保存しました: {results_path}")

    def process_race_pickle_files(self,
                                 year: Optional[str] = None,
                                 race_id: Optional[str] = None,
                                 pickle_base_dir: str = os.path.join(LocalPaths.DATA_DIR, "processed"),
                                 race_info_filename_pattern: str = "race_info_{year}.pickle",
                                 race_results_filename_pattern: str = "race_results_{year}.pickle"
                                 ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        指定された年またはレースIDのレース情報pickleファイルを処理し、
        レース情報とレース結果のDataFrameを生成・更新する。

        Parameters
        ----------
        year : str, optional
            処理する年。race_idが指定されていない場合は必須。
        race_id : str, optional
            処理する特定のレースID。指定された場合、yearのデータからフィルタリングする。
        pickle_base_dir : str, optional
            pickleファイルが保存されているベースディレクトリ。
            デフォルトは LocalPaths.DATA_DIR / "processed"
        race_info_filename_pattern : str, optional
            レース情報pickleファイル名のパターン。'{year}'プレースホルダを含む。
            デフォルトは "race_info_{year}.pickle"
        race_results_filename_pattern : str, optional
            レース結果pickleファイル名のパターン。'{year}'プレースホルダを含む。
            デフォルトは "race_results_{year}.pickle"

        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            (レース情報のDataFrame, レース結果のDataFrame)
        """
        self.logger.info(f"レースpickleファイルの処理を開始: year={year}, race_id={race_id}, base_dir={pickle_base_dir}")

        if not year and not race_id:
            self.logger.error("yearまたはrace_idのどちらかを指定する必要があります。")
            return pd.DataFrame(), pd.DataFrame()

        target_year = year
        if race_id and not year:
            target_year = race_id[:4]
            self.logger.info(f"race_idから年を抽出: {target_year}")
        elif race_id and year and year != race_id[:4]:
            self.logger.warning(f"指定されたyear ({year}) と race_id ({race_id}) の年が異なります。race_idの年 ({race_id[:4]}) を使用します。")
            target_year = race_id[:4]

        if not target_year:
            self.logger.error("処理対象の年を特定できませんでした。")
            return pd.DataFrame(), pd.DataFrame()

        info_file_path_pattern = os.path.join(pickle_base_dir, race_info_filename_pattern.format(year=target_year))
        results_file_path_pattern = os.path.join(pickle_base_dir, race_results_filename_pattern.format(year=target_year))

        info_files = glob.glob(info_file_path_pattern)
        results_files = glob.glob(results_file_path_pattern)

        race_info_df = pd.DataFrame()
        race_results_df = pd.DataFrame()

        if not info_files:
            self.logger.warning(f"レース情報pickleファイルが見つかりません: {info_file_path_pattern}")
        else:
            info_file_path = info_files[0] # 最初に見つかったファイルを使用
            try:
                self.logger.info(f"レース情報pickleファイルを読み込み中: {info_file_path}")
                race_info_df = pd.read_pickle(info_file_path)
                self.logger.info(f"レース情報読み込み完了: {len(race_info_df)}件")
            except Exception as e:
                self.logger.error(f"レース情報pickleファイルの読み込みに失敗: {info_file_path}, エラー: {e}", exc_info=True)

        if not results_files:
            self.logger.warning(f"レース結果pickleファイルが見つかりません: {results_file_path_pattern}")
        else:
            results_file_path = results_files[0] # 最初に見つかったファイルを使用
            try:
                self.logger.info(f"レース結果pickleファイルを読み込み中: {results_file_path}")
                race_results_df = pd.read_pickle(results_file_path)
                self.logger.info(f"レース結果読み込み完了: {len(race_results_df)}件")
            except Exception as e:
                self.logger.error(f"レース結果pickleファイルの読み込みに失敗: {results_file_path}, エラー: {e}", exc_info=True)

        if race_id:
            if not race_info_df.empty and 'race_id' in race_info_df.columns:
                race_info_df = race_info_df[race_info_df['race_id'] == race_id].copy()
            if not race_results_df.empty and 'race_id' in race_results_df.columns:
                race_results_df = race_results_df[race_results_df['race_id'] == race_id].copy()

        self._race_info_df = race_info_df
        self._race_results_df = race_results_df

        return self._race_info_df.copy(), self._race_results_df.copy()


# 以下は直接実行された場合の処理
if __name__ == "__main__":
    # scraping_constants.py から定数をインポートし直す (グローバルスコープでのインポートのため)
    # このブロックは __main__ の中なので、通常は不要だが、
    # dataclassの定義がグローバルスコープで行われているため、ここで再インポートする
    from scraping_constants import ResultsCols as MainResultsCols
    from scraping_constants import RaceInfoCols as MainRaceInfoCols
    from scraping_constants import Master as MainMaster
    # グローバル変数を上書きしないように別名でインポートし、必要に応じて使い分ける
    # ただし、このスクリプトの構造上、クラス内の定数参照はスクリプト冒頭のインポートに依存する



    # コマンドライン引数を解析
    # ロガーの基本設定 (直接実行時のみ)
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                        handlers=[logging.StreamHandler()])

    import argparse

    parser = argparse.ArgumentParser(description="レース情報のbinファイルから表データを生成")
    parser.add_argument("--year", type=str, help="処理する年")
    parser.add_argument("--race-id", type=str, help="処理する特定のレースID")
    parser.add_argument("--save", action="store_true", help="CSVとして保存する")
    parser.add_argument("--no-parallel", action="store_true", help="並列処理を使用しない")
    parser.add_argument("--workers", type=int, default=os.cpu_count() or 1, help="並列処理の最大ワーカー数")
    parser.add_argument(
        "--merge-past-performance", action="store_true", help="レース結果に馬の過去の戦績をマージする"
    )
    parser.add_argument(
        "--preprocess", action="store_true", help="データの前処理を行う"
    )
    args = parser.parse_args()

    # RaceProcessorのインスタンスを作成
    race_processor = RaceProcessor()

    # レース情報を処理 (process_race_bin_files を呼び出すように変更)
    race_processor.process_race_bin_files( # 返り値は使わず、インスタンス変数を参照
        year=args.year, race_id=args.race_id,
        parallel=not args.no_parallel, max_workers=args.workers
    )
    # インスタンス変数から取得
    race_info_df = race_processor._race_info_df
    race_results_df = race_processor._race_results_df

    # 結果を表示
    if not race_info_df.empty:
        print(f"\nレース情報 ({len(race_info_df)}件):")
        print(race_info_df.head())
    else:
        print("レース情報を取得できませんでした")

    if not race_results_df.empty:
        print(f"\n出走馬のレース結果 ({len(race_results_df)}件):")
        print(race_results_df.head())
    else:
        print("出走馬のレース結果を取得できませんでした")

    # データの前処理
    if args.preprocess:
        print("\nデータの前処理を実行中...")
        preprocessed_df = race_processor.preprocess_data()
        if not preprocessed_df.empty:
            print("\n前処理後のデータ:")
            print(preprocessed_df.head())
        else:
            print("データの前処理に失敗しました。")

    # レース結果に馬の過去の戦績をマージ
    if args.merge_past_performance:
        print("\nレース結果に馬の過去の戦績をマージ中...")
        # この例では、全馬の過去成績データ (all_horse_past_results_df) を
        # process_race_bin_files で取得した race_results_df で代用しています。
        # 実際には、より広範な過去データを用意する必要があります。
        if not race_results_df.empty:
            # preprocess_data を呼び出して、マージ対象の current_race_results_df を準備
            current_results_for_merge = race_processor.preprocess_data()
            if not current_results_for_merge.empty:
                # 集計対象のカラム（例として '着順' と '斤量'。実際のデータに合わせて調整してください）
                target_performance_cols = [MainResultsCols.RANK, MainResultsCols.KINRYO]
                # all_horse_past_results_df も前処理が必要な場合がある
                # ここでは race_results_df をそのまま使うが、実際は全期間のデータで前処理済みのものを使う
                all_past_results_for_merge = race_processor._preprocess_results(race_results_df.copy())

                merged_performance_df = race_processor.merge_past_horse_performance(
                    current_race_results_df=current_results_for_merge,
                    all_horse_past_results_df=all_past_results_for_merge, # 本来は全期間のデータ
                    target_cols=target_performance_cols
                )
                if not merged_performance_df.empty:
                    print("\n過去戦績マージ後のデータ:")
                    print(merged_performance_df.head())
                else:
                    print("過去戦績のマージに失敗しました。")
            else:
                print("マージ対象のレース結果データの準備に失敗しました。")
        else:
            print("マージ処理に必要なレース結果データがありません。")

    # CSVとして保存
    if args.save:
        race_processor.save_race_data_to_csv(year=args.year)