# 改良版ライブ予測システム - リアルタイムスクレイピング対応

## 概要
`improved_live_predictor.py`は、リアルタイムで馬の最新過去戦績をスクレイピングし、より正確な競馬予測を行うシステムです。

## 主な改良点

### 1. リアルタイムスクレイピング機能
- `core/scrapers/scraper.py`を使用して、馬の最新過去戦績をリアルタイムで取得
- 過去の保存データがない馬でも、netkeiba.comから直接データを取得
- スクレイピングしたデータは自動的にキャッシュされ、次回以降の処理が高速化

### 2. 高度な過去戦績分析
```python
# スクレイピングで取得したデータから詳細な統計を計算
- 勝率、連対率、複勝率
- 平均着順、着順の安定性
- 人気と実績の関係性分析
- 最近の調子（直近5戦の成績）
- オッズの傾向分析
```

### 3. データ取得の流れ
```
1. 既存のピックルファイルから過去戦績を検索
2. データがない馬はリアルタイムでスクレイピング
3. 取得したHTMLから過去戦績を解析
4. ターゲット日付以前のデータのみを使用（データリーケージ防止）
5. 詳細な統計計算を実行
```

## 使用方法

### 基本的な実行
```bash
python improved_live_predictor.py
```

### レースIDを指定して実行
```
レースIDを入力 (例: 202401010101): 202407020203
```

### 実行時の注意点
- 初回実行時は、馬の過去戦績をスクレイピングするため時間がかかります（1頭あたり1-2秒）
- スクレイピングしたデータは自動的に保存され、次回以降は高速に処理されます
- netkeiba.comへのアクセスは適切な待機時間を設けて行われます

## 新機能の詳細

### _scrape_realtime_horse_data メソッド
```python
def _scrape_realtime_horse_data(self, horse_ids: List[str], target_date: pd.Timestamp) -> Dict[str, pd.DataFrame]:
    """
    リアルタイムで馬の過去戦績をスクレイピング
    - core.scrapers.scraperを使用してHTMLを取得
    - 取得したHTMLから過去戦績データを解析
    - ターゲット日付以前のデータのみを返す
    """
```

### _parse_horse_race_history メソッド
```python
def _parse_horse_race_history(self, html_content: bytes, horse_id: str, target_date: pd.Timestamp) -> pd.DataFrame:
    """
    馬のHTMLから過去戦績を解析
    - 競走成績テーブルを探索
    - 各レースの詳細データを抽出
    - 日付、着順、人気、オッズ、賞金など
    """
```

### 強化された統計計算
```python
# 人気と実績の関係性
stats['performance_vs_popularity'] = (valid_data['着順'] < valid_data['人気']).mean()

# 最近の調子（失格等を除外）
recent_ranks = recent_ranks[recent_ranks < 50]
stats['recent_form'] = max(0, 5 - recent_ranks.mean())

# オッズ統計
stats['avg_odds'] = odds.mean()
stats['recent_odds'] = odds.head(5).mean()
```

## 必要な環境

### 依存パッケージ
- pandas
- numpy
- requests
- beautifulsoup4
- selenium
- webdriver-manager
- scikit-learn
- joblib

### ディレクトリ構造
```
keiba_ai_system/
├── improved_live_predictor.py
├── core/
│   ├── scrapers/
│   │   └── scraper.py
│   └── utils/
│       └── constants.py
├── data/
│   └── html/
│       └── horse/
│           └── horse_by_year/
│               └── [年]/
│                   └── [馬ID].bin
└── models/
    └── fixed_leakage_model_*.pkl
```

## トラブルシューティング

### エラー: "Chrome WebDriverの初期化に失敗しました"
- ChromeDriverが正しくインストールされているか確認
- `pip install webdriver-manager`を実行

### エラー: "必要なモデルファイルが見つかりません"
- modelsディレクトリに学習済みモデルファイルが存在するか確認
- モデルファイル名がコード内の指定と一致しているか確認

### スクレイピングが遅い
- ネットワーク接続を確認
- 同時に多数の馬をスクレイピングする場合は、適切な待機時間が設定されています

## 今後の改善予定
- 並列スクレイピングによる高速化
- より詳細な過去戦績分析（上がりタイム、通過順位など）
- 血統情報の統合
- リアルタイム予測精度の向上
