# 簡易版PowerShell起動スクリプト - 実行ポリシー問題を回避
param(
    [switch]$BypassPolicy
)

# 実行ポリシーバイパス版
if ($BypassPolicy) {
    Write-Host "🏇 競馬AI予測システム起動中（ポリシーバイパス）..." -ForegroundColor Green
    & powershell.exe -ExecutionPolicy Bypass -File "start_keiba_ai.ps1"
    return
}

# 通常版
Write-Host "🏇 競馬AI予測システム起動中..." -ForegroundColor Green

# 最小限の環境チェック
if (-not (Test-Path "keiba_ai_main.py")) {
    Write-Host "❌ keiba_ai_main.pyが見つかりません" -ForegroundColor Red
    Read-Host "Enterキーを押して終了"
    exit 1
}

# 仮想環境の簡易チェック
$VenvFound = $false
if (Test-Path "venv\Scripts\python.exe") {
    Write-Host "✅ venv仮想環境を使用" -ForegroundColor Green
    $PythonExe = "venv\Scripts\python.exe"
    $VenvFound = $true
} elseif (Test-Path "venv\Scripts\Activate.ps1") {
    Write-Host "✅ venv仮想環境をアクティベート中..." -ForegroundColor Green
    try {
        . "venv\Scripts\Activate.ps1"
        $PythonExe = "python"
        $VenvFound = $true
    } catch {
        Write-Host "⚠️  仮想環境アクティベートに失敗、システムPythonを使用" -ForegroundColor Yellow
        $PythonExe = "python"
    }
} else {
    Write-Host "⚠️  仮想環境が見つかりません、システムPythonを使用" -ForegroundColor Yellow
    $PythonExe = "python"
}

# Pythonバージョンチェック
try {
    $Version = & $PythonExe --version 2>&1
    Write-Host "🐍 $Version" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Python実行エラー: $_" -ForegroundColor Red
    Read-Host "Enterキーを押して終了"
    exit 1
}

Write-Host ""
Write-Host "🚀 メインシステム起動中..." -ForegroundColor Green
Write-Host ""

# メインプログラム実行
try {
    & $PythonExe keiba_ai_main.py
    Write-Host ""
    Write-Host "✅ システム正常終了" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "❌ エラー発生: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "👋 終了します" -ForegroundColor Blue