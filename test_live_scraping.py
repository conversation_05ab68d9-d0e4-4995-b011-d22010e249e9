#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最新馬戦績スクレイピング機能のテストスクリプト
"""

import sys
import os
import pandas as pd
import logging

# パスを追加
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# ロガー設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_live_scraping():
    """最新馬戦績スクレイピング機能のテスト"""
    try:
        print("=== 最新馬戦績スクレイピング機能テスト ===")
        
        from enhanced_live_predictor_with_scraping import EnhancedLiveRacePredictorWithScraping
        
        # スクレイピング機能付きプレディクターを初期化
        predictor = EnhancedLiveRacePredictorWithScraping(
            use_selenium=False,
            enable_live_scraping=True
        )
        
        # モデル読み込み
        model_loaded = predictor.load_latest_model()
        if not model_loaded:
            print("❌ モデル読み込み失敗")
            return False
        
        print("✅ モデル読み込み成功")
        
        # テスト用馬IDで最新戦績スクレイピング
        test_horse_ids = ["2020100101", "2019105555"]  # 仮のテスト用馬ID
        
        print(f"\n最新戦績スクレイピングテスト開始: {test_horse_ids}")
        
        # 実際のスクレイピングテスト
        horse_stats = predictor.get_live_horse_performance(test_horse_ids, '2024-12-01')
        
        if horse_stats:
            print(f"✅ 最新戦績取得成功: {len(horse_stats)}頭")
            
            for horse_id, stats in horse_stats.items():
                scraped_races = stats.get('scraped_races', 0)
                win_rate = stats.get('win_rate', 0)
                avg_rank = stats.get('avg_rank', 0)
                
                if scraped_races > 0:
                    print(f"   馬ID {horse_id}: 実際にスクレイピング成功")
                    print(f"     - 取得レース数: {scraped_races}")
                    print(f"     - 勝率: {win_rate:.3f}")
                    print(f"     - 平均着順: {avg_rank:.1f}")
                else:
                    print(f"   馬ID {horse_id}: フォールバックデータ使用")
            
            return True
        else:
            print("❌ 最新戦績取得失敗")
            return False
        
    except Exception as e:
        print(f"❌ テストエラー: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_horse_scraping():
    """単一馬の戦績スクレイピングテスト"""
    try:
        print("\n=== 単一馬戦績スクレイピングテスト ===")
        
        from enhanced_live_predictor_with_scraping import EnhancedLiveRacePredictorWithScraping
        
        predictor = EnhancedLiveRacePredictorWithScraping(enable_live_scraping=True)
        
        # 実在する馬IDでテスト（例：ディープインパクト）
        test_horse_id = "2002103924"  # 実在の馬ID例
        
        print(f"馬ID {test_horse_id} の戦績をスクレイピング中...")
        
        # 個別スクレイピング機能をテスト
        horse_data = predictor.scrape_live_horse_results(test_horse_id, limit_races=5)
        
        if not horse_data.empty:
            print(f"✅ 個別スクレイピング成功: {len(horse_data)}件のレースデータを取得")
            print("\n取得データのサンプル:")
            print(horse_data[['日付', '着順', 'レース名', '人気', 'オッズ']].head())
            return True
        else:
            print("❌ 個別スクレイピング失敗または該当データなし")
            return False
        
    except Exception as e:
        print(f"❌ 単一馬テストエラー: {e}")
        return False

def main():
    """メインテスト実行"""
    print("最新馬戦績スクレイピング機能テスト")
    print("=" * 50)
    
    # 基本スクレイピング機能テスト
    basic_test = test_live_scraping()
    
    # 単一馬スクレイピングテスト
    single_test = test_single_horse_scraping()
    
    print("\n" + "=" * 50)
    print("テスト結果まとめ:")
    print(f"  基本スクレイピング機能: {'✅ 成功' if basic_test else '❌ 失敗'}")
    print(f"  単一馬スクレイピング: {'✅ 成功' if single_test else '❌ 失敗'}")
    
    overall_success = basic_test or single_test  # どちらか一つでも成功すれば OK
    print(f"\n総合結果: {'✅ スクレイピング機能動作確認' if overall_success else '❌ スクレイピング機能に問題'}")
    
    if overall_success:
        print("\n🎉 最新馬戦績スクレイピング機能が動作しています！")
        print("   - リアルタイムでWebから最新戦績を取得")
        print("   - 戦績データの統計計算")
        print("   - 予測モデルとの統合")
    else:
        print("\n⚠️  スクレイピング機能の確認ができませんでした")
        print("   - ネットワーク接続を確認してください")
        print("   - 対象サイトの仕様変更の可能性があります")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)