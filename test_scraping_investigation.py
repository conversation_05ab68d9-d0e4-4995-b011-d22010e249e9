#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
enhanced_live_predictorとHorseProcessorのスクレイピング機能調査スクリプト
"""

import logging
import sys
import os
from datetime import datetime

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_horse_processor_scraping():
    """HorseProcessorのスクレイピング機能をテスト"""
    print("="*80)
    print("HorseProcessor スクレイピング機能調査")
    print("="*80)
    
    try:
        from core.processors.horse_processor import HorseProcessor
        
        # HorseProcessorインスタンス作成
        processor = HorseProcessor()
        
        # 1. HTMLファイルインデックスの確認
        print(f"\n1. HTMLファイルインデックス確認")
        print(f"   インデックス済みファイル数: {len(processor._horse_html_index):,}件")
        
        # 2. テスト用馬IDでHTMLファイル存在確認
        test_horse_ids = ['2021105522', '2020104923', '2022100001']
        print(f"\n2. テスト馬IDのHTMLファイル存在確認")
        for horse_id in test_horse_ids:
            html_path = processor._find_horse_info_html_by_id(horse_id)
            exists = html_path and os.path.exists(html_path) if html_path else False
            print(f"   馬ID {horse_id}: 存在={exists}, パス={html_path}")
        
        # 3. 実際のHTMLファイル処理テスト（存在するファイルのみ）
        valid_horse_ids = []
        for horse_id in test_horse_ids:
            html_path = processor._find_horse_info_html_by_id(horse_id)
            if html_path and os.path.exists(html_path):
                valid_horse_ids.append(horse_id)
        
        if valid_horse_ids:
            print(f"\n3. 実際のHTMLファイル処理テスト")
            print(f"   有効な馬ID: {valid_horse_ids}")
            
            # 馬基本情報の取得
            print(f"   馬基本情報取得中...")
            horse_info_df = processor.process_horse_info_for_ids(
                horse_ids=valid_horse_ids[:1],  # 1件のみテスト
                parallel=False,
                max_workers=1
            )
            print(f"   結果: {len(horse_info_df)}件取得")
            if not horse_info_df.empty:
                print(f"   カラム: {list(horse_info_df.columns)}")
            
            # 馬過去戦績の取得
            print(f"   馬過去戦績取得中...")
            horse_results_df = processor.process_horse_results_for_ids(
                horse_ids=valid_horse_ids[:1],  # 1件のみテスト
                parallel=False,
                max_workers=1
            )
            print(f"   結果: {len(horse_results_df)}件取得")
            if not horse_results_df.empty:
                print(f"   カラム: {list(horse_results_df.columns)}")
        else:
            print(f"\n3. スキップ（有効なHTMLファイルなし）")
        
        # 結論
        print(f"\n【HorseProcessor 分析結果】")
        print(f"・データソース: 既存のHTMLファイル (.binファイル)")
        print(f"・スクレイピング: リアルタイムWebスクレイピングは行わない")
        print(f"・処理方式: 事前にダウンロード済みのHTMLファイルを解析")
        print(f"・データ取得: ローカルファイルシステムからのみ")
        
    except Exception as e:
        logger.error(f"HorseProcessorテストエラー: {e}")
        import traceback
        traceback.print_exc()

def test_enhanced_live_predictor_scraping():
    """EnhancedLivePredictorのスクレイピング機能をテスト"""
    print("\n" + "="*80)
    print("EnhancedLivePredictor スクレイピング機能調査")
    print("="*80)
    
    try:
        from enhanced_live_predictor import EnhancedLiveRacePredictor
        
        # Enhanced Live Predictorインスタンス作成（Seleniumは無効化）
        predictor = EnhancedLiveRacePredictor(use_selenium=False)
        
        print(f"\n1. Enhanced Live Predictor 設定確認")
        print(f"   Selenium使用: {predictor.use_selenium}")
        print(f"   コーナー特徴量: {predictor.enable_corner_features}")
        print(f"   馬情報: {predictor.enable_horse_info}")
        
        # 2. 出馬表スクレイピング機能の確認
        print(f"\n2. 出馬表スクレイピング機能確認")
        test_race_id = "202406080101"  # テスト用レースID
        
        print(f"   テスト対象レースID: {test_race_id}")
        print(f"   スクレイピング方式: requests/BeautifulSoup")
        print(f"   対象URL: https://race.netkeiba.com/race/shutuba.html?race_id={test_race_id}")
        
        # 実際にスクレイピングテストは行わない（リアルアクセスを避ける）
        print(f"   ※実際のWebアクセスは行いません（テスト環境）")
        
        # 3. 過去戦績取得機能の確認
        print(f"\n3. 過去戦績取得機能確認")
        test_horse_ids = ['test_horse_1', 'test_horse_2']
        print(f"   テスト用馬ID: {test_horse_ids}")
        
        # HorseProcessorを使用した過去戦績取得の確認
        if predictor.horse_processor is None:
            predictor.horse_processor = predictor.horse_processor or __import__('core.processors.horse_processor', fromlist=['HorseProcessor']).HorseProcessor()
        
        print(f"   過去戦績取得方式: HorseProcessorを使用")
        print(f"   データソース: ローカルHTMLファイル")
        print(f"   リアルタイムスクレイピング: なし")
        
        # 結論
        print(f"\n【EnhancedLivePredictor 分析結果】")
        print(f"・出馬表取得: リアルタイムWebスクレイピング（Selenium/requests）")
        print(f"・過去戦績取得: HorseProcessorを使用（ローカルHTMLファイル）")
        print(f"・最新戦績: リアルタイム取得は行わない")
        print(f"・データ混合: Webスクレイピング + ローカルファイル")
        
    except Exception as e:
        logger.error(f"EnhancedLivePredictorテストエラー: {e}")
        import traceback
        traceback.print_exc()

def analyze_data_flow():
    """データフローの分析"""
    print("\n" + "="*80)
    print("データフロー分析")
    print("="*80)
    
    print(f"\n1. enhanced_live_predictor.py のデータフロー:")
    print(f"   ┌─────────────────────────────────────┐")
    print(f"   │  1. 出馬表取得 (リアルタイム)        │")
    print(f"   │     - Selenium または requests      │")
    print(f"   │     - URL: netkeiba.com/shutuba     │")
    print(f"   │     - 最新の出馬情報を取得          │")
    print(f"   └─────────────────────────────────────┘")
    print(f"                    ↓")
    print(f"   ┌─────────────────────────────────────┐")
    print(f"   │  2. 馬ID抽出                        │")
    print(f"   │     - 出馬表から馬IDを取得          │")
    print(f"   └─────────────────────────────────────┘")
    print(f"                    ↓")
    print(f"   ┌─────────────────────────────────────┐")
    print(f"   │  3. 過去戦績取得 (ローカル)         │")
    print(f"   │     - HorseProcessor使用             │")
    print(f"   │     - ローカルHTMLファイル解析      │")
    print(f"   │     - リアルタイム取得なし          │")
    print(f"   └─────────────────────────────────────┘")
    print(f"                    ↓")
    print(f"   ┌─────────────────────────────────────┐")
    print(f"   │  4. 特徴量計算・予測実行            │")
    print(f"   └─────────────────────────────────────┘")
    
    print(f"\n2. 重要な発見:")
    print(f"   ❌ 最新の馬戦績は取得していない")
    print(f"   ❌ リアルタイムで馬の過去戦績を更新していない")
    print(f"   ✅ 出馬表のみリアルタイム取得")
    print(f"   ✅ 過去戦績は事前にダウンロード済みのデータを使用")
    
    print(f"\n3. データの鮮度:")
    print(f"   - 出馬表: リアルタイム（最新）")
    print(f"   - 馬基本情報: ローカルファイル（更新日に依存）")
    print(f"   - 馬過去戦績: ローカルファイル（更新日に依存）")
    print(f"   - コーナー特徴量: ローカルファイル（更新日に依存）")

def main():
    """メイン実行関数"""
    print("競馬AIシステム スクレイピング機能調査")
    print(f"調査日時: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. HorseProcessorのテスト
    test_horse_processor_scraping()
    
    # 2. EnhancedLivePredictorのテスト
    test_enhanced_live_predictor_scraping()
    
    # 3. データフロー分析
    analyze_data_flow()
    
    print("\n" + "="*80)
    print("調査完了")
    print("="*80)

if __name__ == "__main__":
    main()