#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡易版モデル学習スクリプト - 既存データを使用
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
import joblib
import logging
from datetime import datetime
from pathlib import Path

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data():
    """既存のpickleファイルからデータを読み込み"""
    try:
        # レース情報とレース結果を結合
        race_info = pd.read_pickle('output/race_info_2020.pickle')
        race_results = pd.read_pickle('output/race_results_2020.pickle')
        
        # データを結合
        data = pd.merge(race_results, race_info, on='race_id', how='left')
        logger.info(f"データ読み込み完了: {len(data)}件")
        
        return data
        
    except Exception as e:
        logger.error(f"データ読み込みエラー: {e}")
        raise

def prepare_features(data):
    """特徴量の準備"""
    logger.info("特徴量準備開始")
    
    # 必要なカラムの確認
    if '着順' not in data.columns:
        raise ValueError("着順カラムが見つかりません")
    
    # 着順を数値に変換
    data['着順'] = pd.to_numeric(data['着順'], errors='coerce')
    data = data.dropna(subset=['着順'])
    
    # ターゲット変数：3着以内かどうか
    data['target'] = (data['着順'] <= 3).astype(int)
    
    # 基本的な特徴量を選択
    feature_cols = []
    
    # 数値特徴量
    numeric_cols = ['course_len', '枠番', '馬番', '斤量']
    for col in numeric_cols:
        if col in data.columns:
            data[col] = pd.to_numeric(data[col], errors='coerce')
            feature_cols.append(col)
    
    # カテゴリカル特徴量のエンコーディング
    categorical_cols = ['race_type', 'ground_state', 'weather', 'track_direction']
    le_dict = {}
    
    for col in categorical_cols:
        if col in data.columns:
            le = LabelEncoder()
            data[f'{col}_encoded'] = le.fit_transform(data[col].astype(str))
            feature_cols.append(f'{col}_encoded')
            le_dict[col] = le
    
    # 年齢特徴量（性齢から抽出）
    if '性齢' in data.columns:
        data['年齢'] = data['性齢'].str.extract(r'(\d+)').astype(float)
        feature_cols.append('年齢')
        
        # 性別
        data['性別_牡'] = data['性齢'].str.contains('牡', na=False).astype(int)
        data['性別_牝'] = data['性齢'].str.contains('牝', na=False).astype(int)
        feature_cols.extend(['性別_牡', '性別_牝'])
    
    # 距離カテゴリ
    if 'course_len' in data.columns:
        data['距離_短距離'] = (data['course_len'] <= 1400).astype(int)
        data['距離_マイル'] = ((data['course_len'] > 1400) & (data['course_len'] <= 1800)).astype(int)
        data['距離_中距離'] = ((data['course_len'] > 1800) & (data['course_len'] <= 2200)).astype(int)
        data['距離_長距離'] = (data['course_len'] > 2200).astype(int)
        feature_cols.extend(['距離_短距離', '距離_マイル', '距離_中距離', '距離_長距離'])
    
    # 欠損値を埋める
    for col in feature_cols:
        if col in data.columns:
            data[col] = data[col].fillna(0)
    
    # 使用可能な特徴量のみを選択
    available_features = [col for col in feature_cols if col in data.columns]
    
    X = data[available_features]
    y = data['target']
    
    logger.info(f"特徴量数: {len(available_features)}")
    logger.info(f"特徴量: {available_features}")
    logger.info(f"データ形状: X={X.shape}, y={y.shape}")
    logger.info(f"正例率: {y.mean():.3f}")
    
    return X, y, available_features, data

def train_model(X, y):
    """モデル学習"""
    logger.info("モデル学習開始")
    
    # データ分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # データ正規化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # LightGBM学習
    lgb_train = lgb.Dataset(X_train_scaled, label=y_train)
    lgb_valid = lgb.Dataset(X_test_scaled, label=y_test, reference=lgb_train)
    
    params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': 0
    }
    
    model = lgb.train(
        params,
        lgb_train,
        valid_sets=[lgb_valid],
        num_boost_round=100,
        callbacks=[lgb.early_stopping(stopping_rounds=10), lgb.log_evaluation(0)]
    )
    
    # 予測と評価
    y_proba = model.predict(X_test_scaled)
    y_pred = (y_proba > 0.5).astype(int)
    
    # 評価指標
    accuracy = accuracy_score(y_test, y_pred)
    auc = roc_auc_score(y_test, y_proba)
    
    # 上位予測の適中率
    def precision_at_k(y_true, y_proba, k):
        threshold_idx = int(len(y_proba) * k)
        if threshold_idx == 0:
            return 0.0
        top_k_indices = np.argsort(y_proba)[-threshold_idx:]
        return y_true.iloc[top_k_indices].mean()
    
    precision_at_10 = precision_at_k(y_test, y_proba, 0.1)
    precision_at_20 = precision_at_k(y_test, y_proba, 0.2)
    
    results = {
        'model': model,
        'scaler': scaler,
        'accuracy': accuracy,
        'auc': auc,
        'precision_at_10': precision_at_10,
        'precision_at_20': precision_at_20,
        'train_size': len(X_train),
        'test_size': len(X_test)
    }
    
    logger.info(f"=== 学習結果 ===")
    logger.info(f"精度: {accuracy:.4f}")
    logger.info(f"AUC: {auc:.4f}")
    logger.info(f"適中率@10%: {precision_at_10:.4f}")
    logger.info(f"適中率@20%: {precision_at_20:.4f}")
    logger.info(f"学習データ: {len(X_train)}件")
    logger.info(f"テストデータ: {len(X_test)}件")
    
    return results

def save_model(model, scaler, features):
    """モデル保存"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # ディレクトリ作成
    models_dir = Path('models')
    models_dir.mkdir(exist_ok=True)
    
    # ファイル保存
    model_path = models_dir / f"simple_lgb_model_{timestamp}.pkl"
    scaler_path = models_dir / f"simple_scaler_{timestamp}.pkl"
    features_path = models_dir / f"simple_features_{timestamp}.pkl"
    
    joblib.dump(model, model_path)
    joblib.dump(scaler, scaler_path)
    joblib.dump(features, features_path)
    
    logger.info(f"モデル保存完了:")
    logger.info(f"  モデル: {model_path}")
    logger.info(f"  スケーラー: {scaler_path}")
    logger.info(f"  特徴量: {features_path}")
    
    return {
        'model': str(model_path),
        'scaler': str(scaler_path),
        'features': str(features_path)
    }

def main():
    """メイン関数"""
    try:
        # データ読み込み
        data = load_data()
        
        # 特徴量準備
        X, y, features, processed_data = prepare_features(data)
        
        # モデル学習
        results = train_model(X, y)
        
        # モデル保存
        saved_paths = save_model(results['model'], results['scaler'], features)
        
        print("\n" + "="*50)
        print("🎯 競馬AI学習完了!")
        print("="*50)
        print(f"精度: {results['accuracy']:.4f}")
        print(f"AUC: {results['auc']:.4f}")
        print(f"適中率@10%: {results['precision_at_10']:.4f}")
        print(f"適中率@20%: {results['precision_at_20']:.4f}")
        print(f"学習データ: {results['train_size']:,}件")
        print(f"テストデータ: {results['test_size']:,}件")
        print("\n保存ファイル:")
        for key, path in saved_paths.items():
            print(f"  {key}: {path}")
        
        return results
        
    except Exception as e:
        logger.error(f"学習中にエラー: {e}")
        raise

if __name__ == "__main__":
    main()