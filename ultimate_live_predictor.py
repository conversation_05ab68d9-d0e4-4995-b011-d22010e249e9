#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏆 Ultimate Live Race Predictor 🏆
最強統合版実際のレース予測システム

統合機能:
🔥 ベース: enhanced_live_predictor_with_scraping.py (リアルタイムデータ取得)
🛡️ BAN対策: safe_live_predictor.py (高度なBAN対策)
🤖 Selenium: enhanced_live_predictor.py (Selenium統合)
📊 特徴量: improved_live_predictor.py (豊富な特徴量)
🔗 TFR統合: enhanced_live_predictor_with_tfr.py (TensorFlow Ranking)

= 競馬AI予測システムの最高峰実装 =
"""

import sys
import os
import pandas as pd
import numpy as np
import requests
from bs4 import BeautifulSoup
import joblib
import logging
import re
import time
import random
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

# coreモジュール統合（リアルタイムデータ取得の要）
from core.processors.horse_processor import HorseProcessor
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
from core.features.manager import FeatureEngineeringManager
from core.processors.corner_analyzer import CornerAnalyzer

# TensorFlow Ranking統合
try:
    from core.prediction.hybrid_predictor import HybridRacePredictionSystem
    TFR_AVAILABLE = True
except ImportError:
    TFR_AVAILABLE = False

# SSL証明書検証の環境変数設定
os.environ['PYTHONWARNINGS'] = 'ignore:Unverified HTTPS request'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''

# Selenium関連のインポート（enhanced_live_predictor.pyより）
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltimateLiveRacePredictor:
    """🏆 最強統合版実際のレース予測クラス"""
    
    def __init__(self, model_dir="models", use_selenium=True, enable_live_scraping=True, 
                 enable_tfr=True, max_ban_protection=True):
        """
        初期化
        
        Parameters
        ----------
        model_dir : str
            学習済みモデルのディレクトリ
        use_selenium : bool
            Selenium統合を使用するか
        enable_live_scraping : bool
            🔥リアルタイムスクレイピングを有効にするか（最重要機能）
        enable_tfr : bool
            TensorFlow Ranking統合を有効にするか
        max_ban_protection : bool
            最大BAN対策を有効にするか
        """
        self.model_dir = Path(model_dir)
        self.model = None
        self.scaler = None
        self.features = None
        self.label_encoders = {}
        
        # 🔥 リアルタイムスクレイピング（最重要）
        self.enable_live_scraping = enable_live_scraping
        
        # 🤖 Selenium統合
        self.use_selenium = use_selenium and SELENIUM_AVAILABLE
        self.driver = None
        
        # 🚀 TensorFlow Ranking統合
        self.enable_tfr = enable_tfr and TFR_AVAILABLE
        self.hybrid_system = None
        
        # 🛡️ 最大BAN対策
        self.max_ban_protection = max_ban_protection
        
        # 🔥 最新戦績スクレイピング用キャッシュ（重要機能）
        self.scraped_horse_data = {}
        
        # 📊 高度セッション設定（safe_live_predictor.pyより改良）
        self._setup_advanced_session()
        
        # 🗂️ マスターデータ
        self._setup_master_data()
        
        # 🔧 プロセッサ初期化
        self.horse_processor = None
        
        logger.info(f"🏆 Ultimate Live Race Predictor 初期化完了")
        logger.info(f"🔥 リアルタイムスクレイピング: {self.enable_live_scraping}")
        logger.info(f"🤖 Selenium統合: {self.use_selenium}")
        logger.info(f"🚀 TensorFlow Ranking: {self.enable_tfr}")
        logger.info(f"🛡️ 最大BAN対策: {self.max_ban_protection}")
    
    def _setup_advanced_session(self):
        """🛡️ 高度なセッション設定（safe_live_predictor.pyより強化）"""
        self.session = requests.Session()
        
        # 🛡️ より高度なUser-Agent プール（safe_live_predictor.pyより）
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        # 🛡️ より自然なヘッダー設定
        self.session.headers.update({
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        })
    
    def _setup_master_data(self):
        """🗂️ マスターデータ設定"""
        self.WEATHER_LIST = ['晴', '曇', '雨', '雪']
        self.GROUND_STATE_LIST = ['良', '稍重', '重', '不良']
        self.RACE_TYPE_DICT = {'芝': 0, 'ダート': 1, '障害': 2}
        self.AROUND_LIST = ['右', '左', '直線', '障害']
        self.RACE_CLASS_LIST = ['新馬', '未勝利', '１勝クラス', '２勝クラス', '３勝クラス', 'オープン', 'G3', 'G2', 'G1', '障害']
    
    def safe_request(self, url: str, min_delay: float = None, max_delay: float = None) -> requests.Response:
        """
        🛡️ 最高レベルBAN対策リクエスト（safe_live_predictor.pyより強化）
        
        Parameters
        ----------
        url : str
            リクエストURL
        min_delay : float, optional
            最小待機時間（秒）
        max_delay : float, optional  
            最大待機時間（秒）
            
        Returns
        -------
        requests.Response
            レスポンス
        """
        # 🛡️ BAN対策レベル調整
        if self.max_ban_protection:
            min_delay = min_delay or 4.0
            max_delay = max_delay or 8.0
        else:
            min_delay = min_delay or 2.0
            max_delay = max_delay or 4.0
        
        # 🛡️ ランダム待機時間
        delay = random.uniform(min_delay, max_delay)
        logger.info(f"🛡️ BAN対策: {delay:.1f}秒待機中...")
        time.sleep(delay)
        
        # 🛡️ User-Agent ローテーション
        if random.random() < 0.3:  # 30%の確率でUser-Agent変更
            self.session.headers.update({
                'User-Agent': random.choice(self.user_agents)
            })
        
        try:
            # 🛡️ SSL検証無効化（safe_live_predictor.pyより）
            response = self.session.get(url, timeout=15, verify=False)
            response.raise_for_status()
            
            logger.debug(f"✅ リクエスト成功: {url}")
            return response
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ リクエストエラー: {e}")
            # 🛡️ エラー時はさらに長く待機
            error_delay = random.uniform(10, 20)
            logger.warning(f"🛡️ エラー対応: {error_delay:.1f}秒待機...")
            time.sleep(error_delay)
            raise
    
    def prepare_chrome_driver(self):
        """🤖 Seleniumドライバー準備（enhanced_live_predictor.pyより）"""
        if not SELENIUM_AVAILABLE:
            logger.warning("❌ Seleniumが利用できません")
            return None
        
        try:
            logger.info("🤖 Chromeドライバーを準備中...")
            
            options = Options()
            # 🛡️ 検出回避設定
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')
            options.add_argument('--disable-javascript')
            options.add_argument('--user-agent=' + random.choice(self.user_agents))
            
            # 🛡️ ヘッドレスモード（BAN対策）
            if self.max_ban_protection:
                options.add_argument('--headless')
            
            self.driver = webdriver.Chrome(options=options)
            self.driver.implicitly_wait(10)
            
            # 🛡️ WebDriver検出回避
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("✅ Chromeドライバー準備完了")
            return self.driver
            
        except Exception as e:
            logger.error(f"❌ Chromeドライバー準備失敗: {e}")
            self.use_selenium = False
            return None
    
    def load_latest_model(self, model_timestamp="20250608_212220"):
        """📚 データリーケージ修正版モデル読み込み"""
        try:
            # データリーケージ修正版モデルファイルを優先的に読み込み
            model_path = self.model_dir / f"fixed_leakage_model_{model_timestamp}.pkl"
            scaler_path = self.model_dir / f"fixed_leakage_scaler_{model_timestamp}.pkl"
            features_path = self.model_dir / f"fixed_leakage_features_{model_timestamp}.pkl"
            encoders_path = self.model_dir / f"fixed_leakage_encoders_{model_timestamp}.pkl"
            
            # ファイル存在確認
            if not all(path.exists() for path in [model_path, scaler_path, features_path, encoders_path]):
                logger.warning("⚠️ データリーケージ修正版モデルが見つかりません。従来のモデルを探します...")
                # フォールバック：従来のモデル検索
                model_files = list(self.model_dir.glob("*enhanced*model*.pkl"))
                if not model_files:
                    model_files = list(self.model_dir.glob("*model*.pkl"))
                
                if not model_files:
                    raise FileNotFoundError("モデルファイルが見つかりません")
                
                latest_model = max(model_files, key=lambda f: f.stat().st_mtime)
                model_path = latest_model
                scaler_path = self.model_dir / f"{latest_model.stem.replace('model', 'scaler')}.pkl"
                features_path = self.model_dir / f"{latest_model.stem.replace('model', 'features')}.pkl"
                encoders_path = None
            
            # モデル読み込み
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            self.features = joblib.load(features_path)
            
            # エンコーダー読み込み（存在する場合）
            if encoders_path and encoders_path.exists():
                self.label_encoders = joblib.load(encoders_path)
                logger.info(f"📊 ラベルエンコーダー読み込み完了: {len(self.label_encoders)}個")
            
            logger.info(f"✅ モデル読み込み完了: {model_path.name}")
            logger.info(f"📊 特徴量数: {len(self.features)}")
            
            # 🚀 TensorFlow Ranking統合
            if self.enable_tfr:
                self._initialize_tfr_system()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ モデル読み込みエラー: {e}")
            return False
    
    def _initialize_tfr_system(self):
        """🚀 TensorFlow Ranking システム初期化"""
        try:
            if TFR_AVAILABLE:
                config = {
                    'ensemble': {
                        'lgb_weight': 0.7,  # LightGBMを重視（安定性）
                        'tfr_weight': 0.3,  # TFRは補助的役割
                        'enable_dynamic_weights': True
                    }
                }
                self.hybrid_system = HybridRacePredictionSystem(
                    model_dir=str(self.model_dir),
                    config=config
                )
                logger.info("🚀 TensorFlow Ranking統合システム初期化完了")
            else:
                logger.warning("⚠️ TensorFlow Rankingが利用できません")
        except Exception as e:
            logger.error(f"❌ TFR システム初期化エラー: {e}")
    
    def scrape_live_horse_results(self, horse_id: str, limit_races: int = 10) -> pd.DataFrame:
        """
        🔥 リアルタイムで最新の馬戦績をスクレイピング（最重要機能）
        enhanced_live_predictor_with_scraping.pyより
        
        Parameters
        ----------
        horse_id : str
            馬ID
        limit_races : int
            取得する最大レース数
            
        Returns
        -------
        pd.DataFrame
            最新の馬戦績データ
        """
        try:
            if not self.enable_live_scraping:
                logger.debug(f"🔥 馬ID {horse_id}: リアルタイムスクレイピング無効のためスキップ")
                return pd.DataFrame()
            
            # 🔥 キャッシュ確認（パフォーマンス向上）
            if horse_id in self.scraped_horse_data:
                logger.debug(f"🔥 馬ID {horse_id}: キャッシュからデータを取得")
                return self.scraped_horse_data[horse_id]
            
            logger.info(f"🔥 馬ID {horse_id} の最新戦績をリアルタイムスクレイピング中...")
            
            url = f"https://db.netkeiba.com/horse/{horse_id}"
            
            # 🛡️ BAN対策リクエスト
            response = self.safe_request(url)
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 🔍 複数セレクタ対応（堅牢性向上）
            table_selectors = [
                'table.race_table_01',
                'table.nk_tb_common', 
                'table[summary*="競走成績"]',
                'table.db_h_race_results'
            ]
            
            results_table = None
            for selector in table_selectors:
                results_table = soup.select_one(selector)
                if results_table:
                    logger.debug(f"🔍 セレクタ '{selector}' でテーブル発見")
                    break
            
            if not results_table:
                logger.warning(f"⚠️ 馬ID {horse_id}: 戦績テーブルが見つかりません。汎用検索を試行...")
                tables = soup.find_all('table')
                for table in tables:
                    headers = table.find_all(['th', 'td'])
                    if headers and any('着順' in header.get_text() for header in headers[:10]):
                        results_table = table
                        logger.debug("🔍 汎用検索でテーブル発見")
                        break
            
            if not results_table:
                logger.warning(f"❌ 馬ID {horse_id}: 戦績テーブルが見つかりませんでした")
                return pd.DataFrame()
            
            # 📊 戦績データ抽出
            horse_data = []
            rows = results_table.find_all('tr')[1:]  # ヘッダー除外
            
            for i, row in enumerate(rows[:limit_races]):
                cols = row.find_all(['td', 'th'])
                if len(cols) < 5:
                    continue
                
                col_texts = [col.get_text(strip=True) for col in cols]
                
                row_data = {}
                
                # 🔍 柔軟なデータ抽出（カラム数に応じて）
                if len(col_texts) >= 10:
                    # 📊 標準形式の詳細データ
                    try:
                        row_data = {
                            '日付': col_texts[0] if col_texts[0] else '',
                            '競馬場': col_texts[1] if len(col_texts) > 1 else '',
                            'レース名': col_texts[4] if len(col_texts) > 4 else '',
                            '着順': col_texts[11] if len(col_texts) > 11 else '',
                            '人気': col_texts[10] if len(col_texts) > 10 else '',
                            'オッズ': col_texts[12] if len(col_texts) > 12 else '',
                            '頭数': col_texts[7] if len(col_texts) > 7 else '',
                            '距離': col_texts[14] if len(col_texts) > 14 else '',
                        }
                    except IndexError:
                        continue
                elif len(col_texts) >= 5:
                    # 📊 簡略版データ  
                    try:
                        row_data = {
                            '日付': col_texts[0],
                            '競馬場': col_texts[1] if len(col_texts) > 1 else '',
                            'レース名': col_texts[2] if len(col_texts) > 2 else '',
                            '着順': col_texts[3] if len(col_texts) > 3 else '',
                            '人気': col_texts[4] if len(col_texts) > 4 else '',
                        }
                    except IndexError:
                        continue
                else:
                    continue
                
                # 🔢 数値変換（安全処理）
                try:
                    if '着順' in row_data and row_data['着順']:
                        rank_text = re.sub(r'[^0-9]', '', row_data['着順'])
                        row_data['着順'] = int(rank_text) if rank_text else 99
                    else:
                        row_data['着順'] = 99
                except:
                    row_data['着順'] = 99
                
                try:
                    if '人気' in row_data and row_data['人気']:
                        pop_text = re.sub(r'[^0-9]', '', row_data['人気'])
                        row_data['人気'] = int(pop_text) if pop_text else 10
                    else:
                        row_data['人気'] = 10
                except:
                    row_data['人気'] = 10
                
                try:
                    if 'オッズ' in row_data and row_data['オッズ']:
                        odds_text = re.sub(r'[^0-9.]', '', row_data['オッズ'])
                        row_data['オッズ'] = float(odds_text) if odds_text else 10.0
                    else:
                        row_data['オッズ'] = 10.0
                except:
                    row_data['オッズ'] = 10.0
                
                # 📅 データリーケージ防止（重要）
                if '日付' in row_data and row_data['日付']:
                    try:
                        # 日付フォーマット変換
                        date_str = row_data['日付'].replace('/', '').replace('-', '')[:8]
                        if len(date_str) == 8:
                            data_date = datetime.strptime(date_str, '%Y%m%d')
                            # 現在日付より未来のデータは除外
                            if data_date > datetime.now():
                                continue
                    except:
                        pass
                
                horse_data.append(row_data)
            
            df = pd.DataFrame(horse_data)
            
            # 🔥 キャッシュに保存
            self.scraped_horse_data[horse_id] = df
            
            logger.info(f"✅ 馬ID {horse_id}: {len(df)}戦のリアルタイムデータ取得完了")
            return df
            
        except Exception as e:
            logger.error(f"❌ 馬ID {horse_id} のリアルタイムスクレイピングエラー: {e}")
            return pd.DataFrame()
    
    def get_live_horse_performance(self, horse_id: str) -> Dict[str, float]:
        """
        🔥 リアルタイム馬戦績から統計を計算（13種類の高品質特徴量）
        enhanced_live_predictor_with_scraping.pyより
        
        Parameters
        ----------
        horse_id : str
            馬ID
            
        Returns
        -------
        Dict[str, float]
            馬戦績統計
        """
        try:
            horse_data = self.scrape_live_horse_results(horse_id)
            
            if horse_data.empty:
                logger.debug(f"⚠️ 馬ID {horse_id}: データなし、デフォルト統計を使用")
                return self._get_default_horse_stats()
            
            logger.debug(f"📊 馬ID {horse_id}: {len(horse_data)}戦から統計計算中...")
            
            # 📊 基本統計
            total_races = len(horse_data)
            ranks = pd.to_numeric(horse_data['着順'], errors='coerce').dropna()
            valid_ranks = ranks[ranks <= 18]  # 有効着順のみ
            
            # 🏆 勝率・連対率・複勝率
            win_rate = (valid_ranks == 1).sum() / len(valid_ranks) if len(valid_ranks) > 0 else 0.0
            place_rate = (valid_ranks <= 2).sum() / len(valid_ranks) if len(valid_ranks) > 0 else 0.0
            show_rate = (valid_ranks <= 3).sum() / len(valid_ranks) if len(valid_ranks) > 0 else 0.0
            
            # 📊 平均着順
            avg_rank = valid_ranks.mean() if len(valid_ranks) > 0 else 8.0
            
            # 🔥 直近5戦平均（重要指標）
            recent_ranks = valid_ranks.head(5)
            recent_avg_rank = recent_ranks.mean() if len(recent_ranks) > 0 else avg_rank
            
            # ⏰ レース間隔
            last_race_days = 30  # デフォルト
            if '日付' in horse_data.columns and len(horse_data) > 0:
                try:
                    last_date = horse_data['日付'].iloc[0]
                    last_date_clean = re.sub(r'[^0-9]', '', str(last_date))[:8]
                    if len(last_date_clean) == 8:
                        last_race_date = datetime.strptime(last_date_clean, '%Y%m%d')
                        last_race_days = (datetime.now() - last_race_date).days
                except:
                    pass
            
            # 📈 人気・オッズ統計
            popularities = pd.to_numeric(horse_data['人気'], errors='coerce').dropna()
            avg_popularity = popularities.mean() if len(popularities) > 0 else 8.0
            
            odds_data = pd.to_numeric(horse_data['オッズ'], errors='coerce').dropna()
            avg_odds = odds_data.mean() if len(odds_data) > 0 else 10.0
            
            # 🎯 距離・馬場適性（simplified）
            distance_avg = avg_rank  # 距離別の詳細分析は簡略化
            surface_avg = avg_rank   # 馬場別の詳細分析は簡略化
            track_avg = avg_rank     # 競馬場別の詳細分析は簡略化
            
            # 📊 13種類の高品質特徴量を構築
            stats = {
                'total_races': total_races,
                'win_rate': float(win_rate),
                'place_rate': float(place_rate), 
                'show_rate': float(show_rate),
                'avg_rank': float(avg_rank),
                'recent_avg_rank': float(recent_avg_rank),  # 🔥重要
                'days_since_last_race': int(last_race_days), # 🔥重要
                'avg_popularity': float(avg_popularity),
                'avg_odds': float(avg_odds),
                'distance_avg_rank': float(distance_avg),
                'surface_avg_rank': float(surface_avg), 
                'track_avg_rank': float(track_avg),
                'scraped_races': len(horse_data)
            }
            
            logger.debug(f"✅ 馬ID {horse_id}: 統計計算完了 - 勝率{win_rate:.2f}, 平均着順{avg_rank:.1f}")
            return stats
            
        except Exception as e:
            logger.error(f"❌ 馬ID {horse_id} の統計計算エラー: {e}")
            return self._get_default_horse_stats()
    
    def _get_default_horse_stats(self) -> Dict[str, float]:
        """📊 デフォルト馬統計"""
        return {
            'total_races': 10,
            'win_rate': 0.05,
            'place_rate': 0.15,
            'show_rate': 0.25,
            'avg_rank': 8.0,
            'recent_avg_rank': 8.0,
            'days_since_last_race': 30,
            'avg_popularity': 8.0,
            'avg_odds': 10.0,
            'distance_avg_rank': 8.0,
            'surface_avg_rank': 8.0,
            'track_avg_rank': 8.0,
            'scraped_races': 0
        }
    
    def scrape_race_card_ultimate(self, race_id: str) -> pd.DataFrame:
        """
        🏆 究極の出馬表取得（Selenium + Requests ハイブリッド）
        
        Parameters
        ----------
        race_id : str
            レースID
            
        Returns
        -------
        pd.DataFrame
            出馬表データ
        """
        logger.info(f"🏆 究極の出馬表取得開始: {race_id}")
        
        # 🤖 Method 1: Selenium (最高品質)
        if self.use_selenium:
            try:
                logger.info("🤖 Method 1: Selenium統合で出馬表取得を試行...")
                result = self._scrape_with_selenium(race_id)
                if not result.empty:
                    logger.info(f"✅ Selenium取得成功: {len(result)}頭")
                    return result
            except Exception as e:
                logger.warning(f"⚠️ Selenium取得失敗: {e}")
        
        # 🌐 Method 2: Requests (安定性重視)
        try:
            logger.info("🌐 Method 2: Requests安全取得を試行...")
            result = self._scrape_with_requests(race_id)
            if not result.empty:
                logger.info(f"✅ Requests取得成功: {len(result)}頭")
                return result
        except Exception as e:
            logger.warning(f"⚠️ Requests取得失敗: {e}")
        
        logger.error(f"❌ 全ての出馬表取得方法が失敗: {race_id}")
        return pd.DataFrame()
    
    def _scrape_with_selenium(self, race_id: str) -> pd.DataFrame:
        """🤖 Selenium による出馬表取得"""
        if not self.driver:
            self.driver = self.prepare_chrome_driver()
            if not self.driver:
                raise Exception("Seleniumドライバー準備失敗")
        
        url = f"https://race.netkeiba.com/race/shutuba.html?race_id={race_id}"
        
        # 🛡️ BAN対策待機
        delay = random.uniform(3, 7) if self.max_ban_protection else random.uniform(1, 3)
        time.sleep(delay)
        
        self.driver.get(url)
        
        # ページ読み込み待機
        try:
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, 'table'))
            )
        except TimeoutException:
            raise Exception("ページ読み込みタイムアウト")
        
        # テーブル検索・抽出（enhanced_live_predictor.pyのロジック）
        horse_elements = self.driver.find_elements(By.CLASS_NAME, 'HorseList')
        
        if not horse_elements:
            # フォールバック: テーブル行を直接検索
            tables = self.driver.find_elements(By.TAG_NAME, 'table')
            for table in tables:
                rows = table.find_elements(By.TAG_NAME, 'tr')
                if len(rows) > 5:
                    horse_elements = rows[1:]
                    break
        
        if not horse_elements:
            raise Exception("馬データ要素が見つかりません")
        
        df = pd.DataFrame()
        for tr in horse_elements:
            row = []
            td_elements = tr.find_elements(By.TAG_NAME, 'td')
            
            for j, td in enumerate(td_elements):
                text = td.text.strip()
                if j in [0, 1]:  # 枠番・馬番
                    text = re.sub(r'[^\d]', '', text)
                row.append(text)
            
            if len(row) >= 5 and any(cell for cell in row[:5]):
                df = pd.concat([df, pd.DataFrame([row])], ignore_index=True)
        
        # カラム名設定
        if len(df.columns) >= 5:
            col_names = ['枠番', '馬番', '馬名', '性齢', '斤量']
            if len(df.columns) > 5:
                col_names.extend(['単勝オッズ', '人気', '体重・増減', '騎手'])
            df.columns = col_names[:len(df.columns)]
        
        return df
    
    def _scrape_with_requests(self, race_id: str) -> pd.DataFrame:
        """🌐 Requests による出馬表取得（BAN対策強化）"""
        url = f"https://race.netkeiba.com/race/shutuba.html?race_id={race_id}"
        
        # 🛡️ BAN対策リクエスト
        response = self.safe_request(url)
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # テーブル検索
        table = soup.find('table', class_='race_table_01')
        if not table:
            table = soup.find('table')
        
        if not table:
            raise Exception("出馬表テーブルが見つかりません")
        
        rows = []
        for tr in table.find_all('tr')[1:]:  # ヘッダー行をスキップ
            cols = tr.find_all(['td', 'th'])
            if len(cols) >= 8:
                row_data = {}
                
                row_data['枠番'] = self._extract_text(cols[0])
                row_data['馬番'] = self._extract_text(cols[1])
                
                # 馬名とhorse_id抽出
                horse_link = cols[2].find('a')
                if horse_link:
                    horse_href = horse_link.get('href', '')
                    horse_id_match = re.search(r'horse_id=(\w+)', horse_href)
                    row_data['horse_id'] = horse_id_match.group(1) if horse_id_match else ''
                    row_data['馬名'] = horse_link.get_text(strip=True)
                else:
                    row_data['horse_id'] = ''
                    row_data['馬名'] = self._extract_text(cols[2])
                
                row_data['性齢'] = self._extract_text(cols[3])
                row_data['斤量'] = self._extract_text(cols[4])
                row_data['騎手'] = self._extract_text(cols[5])
                row_data['調教師'] = self._extract_text(cols[6])
                
                if len(cols) > 7:
                    row_data['馬主'] = self._extract_text(cols[7])
                
                rows.append(row_data)
        
        return pd.DataFrame(rows)
    
    def _extract_text(self, element):
        """🔍 要素からテキストを安全に抽出"""
        if element:
            return element.get_text(strip=True)
        return ""
    
    def prepare_ultimate_features(self, race_data: pd.DataFrame, race_info: Dict[str, Any], 
                                horse_stats: Dict[str, Dict] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        🏆 究極の特徴量準備（improved_live_predictor.pyより豊富な特徴量）
        
        Parameters
        ----------
        race_data : pd.DataFrame
            出馬表データ
        race_info : Dict[str, Any]
            レース情報
        horse_stats : Dict[str, Dict], optional
            馬統計辞書
            
        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            (特徴量データ, 処理済みデータ)
        """
        try:
            logger.info("🏆 究極の特徴量準備開始...")
            
            data = race_data.copy()
            
            # レース情報追加
            for key, value in race_info.items():
                if key not in data.columns:
                    data[key] = value
            
            # 📊 基本特徴量
            data['枠番'] = pd.to_numeric(data.get('枠番', 1), errors='coerce').fillna(1)
            data['馬番'] = pd.to_numeric(data.get('馬番', 1), errors='coerce').fillna(1)
            data['斤量'] = pd.to_numeric(data.get('斤量', 57.0), errors='coerce').fillna(57.0)
            data['course_len'] = pd.to_numeric(data.get('course_len', 1600), errors='coerce').fillna(1600)
            
            # 📈 人気・オッズ特徴量（improved_live_predictor.pyより）
            data['人気'] = pd.to_numeric(data.get('人気', 8), errors='coerce').fillna(8)
            data['単勝オッズ'] = pd.to_numeric(data.get('単勝オッズ', 10.0), errors='coerce').fillna(10.0)
            
            # 📊 人気・オッズ由来の高度な特徴量
            data['人気逆数'] = 1.0 / data['人気']
            data['オッズ逆数'] = 1.0 / data['単勝オッズ']
            data['人気順位正規化'] = (len(data) + 1 - data['人気']) / len(data)
            data['log_オッズ'] = np.log(data['単勝オッズ'])
            
            # 🔥 リアルタイム過去戦績特徴量（最重要）
            if horse_stats:
                logger.info("🔥 リアルタイム過去戦績データを使用（高精度）")
                
                for idx in range(len(data)):
                    horse_id = data.iloc[idx].get('horse_id', '')
                    
                    if horse_id and horse_id in horse_stats:
                        stats = horse_stats[horse_id]
                        
                        # 🏆 基本統計（リアルタイムデータベース）
                        data.loc[data.index[idx], '過去勝率'] = stats.get('win_rate', 0.05)
                        data.loc[data.index[idx], '過去連対率'] = stats.get('place_rate', 0.15)
                        data.loc[data.index[idx], '過去複勝率'] = stats.get('show_rate', 0.25)
                        data.loc[data.index[idx], '平均着順'] = stats.get('avg_rank', 8.0)
                        
                        # 🔥 重要指標
                        data.loc[data.index[idx], '着順_last_5R_mean'] = stats.get('recent_avg_rank', 8.0)
                        data.loc[data.index[idx], '人気_last_5R_mean'] = stats.get('avg_popularity', 8.0)
                        data.loc[data.index[idx], 'オッズ_last_5R_mean'] = stats.get('avg_odds', 10.0)
                        data.loc[data.index[idx], 'interval_days'] = stats.get('days_since_last_race', 30)
                        
                        # 📊 適性特徴量
                        data.loc[data.index[idx], '距離適性'] = stats.get('distance_avg_rank', 8.0)
                        data.loc[data.index[idx], '馬場適性'] = stats.get('surface_avg_rank', 8.0)
                        data.loc[data.index[idx], '競馬場適性'] = stats.get('track_avg_rank', 8.0)
                        
                        # 📈 その他統計
                        data.loc[data.index[idx], '総レース数'] = stats.get('total_races', 10)
                        data.loc[data.index[idx], 'スクレイプ成功数'] = stats.get('scraped_races', 0)
                        
                        # improved_live_predictor.pyからの追加特徴量
                        data.loc[data.index[idx], '着順_last_10R_mean'] = stats.get('avg_rank', 8.0)
                        data.loc[data.index[idx], '人気_last_10R_mean'] = stats.get('avg_popularity', 8.0)
                        data.loc[data.index[idx], 'オッズ_last_10R_mean'] = stats.get('avg_odds', 10.0)
                        data.loc[data.index[idx], '賞金_last_5R_mean'] = 500000  # デフォルト
                        data.loc[data.index[idx], '賞金_last_10R_mean'] = 500000
                        data.loc[data.index[idx], '斤量_last_5R_mean'] = 57.0
                        data.loc[data.index[idx], '上り_last_5R_mean'] = 35.0
                        data.loc[data.index[idx], '体重_last_5R_mean'] = 480
                        data.loc[data.index[idx], '体重変化_last_5R_mean'] = 0.0
                        
                        # all_R統計
                        data.loc[data.index[idx], '着順_all_R_mean'] = stats.get('avg_rank', 8.0)
                        data.loc[data.index[idx], '人気_all_R_mean'] = stats.get('avg_popularity', 8.0)
                        data.loc[data.index[idx], 'オッズ_all_R_mean'] = stats.get('avg_odds', 10.0)
                        data.loc[data.index[idx], '賞金_all_R_mean'] = 500000
                        data.loc[data.index[idx], '斤量_all_R_mean'] = 57.0
                        data.loc[data.index[idx], '上り_all_R_mean'] = 35.0
                        data.loc[data.index[idx], '体重_all_R_mean'] = 480
                        data.loc[data.index[idx], '体重変化_all_R_mean'] = 0.0
                    else:
                        # デフォルト値設定
                        self._set_default_ultimate_stats(data, idx)
            else:
                logger.warning("⚠️ 過去戦績データなし。人気・オッズベースで予測")
                for idx in range(len(data)):
                    self._set_default_ultimate_stats(data, idx)
            
            # 🏷️ カテゴリカル特徴量処理
            if self.label_encoders:
                for col, encoder in self.label_encoders.items():
                    if col in data.columns:
                        known_classes = set(encoder.classes_)
                        data[col] = data[col].fillna('unknown').astype(str)
                        mask = ~data[col].isin(known_classes)
                        if mask.any():
                            data.loc[mask, col] = encoder.classes_[0]
                        data[col] = encoder.transform(data[col])
                    else:
                        data[col] = 0
            
            # 📊 学習時特徴量に合わせる
            missing_features = []
            for col in self.features:
                if col not in data.columns:
                    if 'rank' in col.lower() or '着順' in col:
                        data[col] = data['人気'].fillna(8.0)
                    elif 'odds' in col.lower() or 'オッズ' in col:
                        data[col] = data['単勝オッズ'].fillna(10.0)
                    elif 'popularity' in col.lower() or '人気' in col:
                        data[col] = data['人気'].fillna(8.0)
                    else:
                        data[col] = 0
                    missing_features.append(col)
            
            if missing_features:
                logger.info(f"📊 推定値で補完した特徴量: {len(missing_features)}個")
            
            # 🎯 最終特徴量
            X = data[self.features]
            X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
            
            logger.info(f"✅ 究極の特徴量準備完了: {X.shape}")
            return X, data
            
        except Exception as e:
            logger.error(f"❌ 特徴量準備エラー: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def _set_default_ultimate_stats(self, data: pd.DataFrame, idx: int):
        """📊 デフォルト統計値設定"""
        default_values = {
            '過去勝率': 0.05,
            '過去連対率': 0.15,
            '過去複勝率': 0.25,
            '平均着順': 8.0,
            '着順_last_5R_mean': 8.0,
            '人気_last_5R_mean': 8.0,
            'オッズ_last_5R_mean': 10.0,
            'interval_days': 30,
            '距離適性': 8.0,
            '馬場適性': 8.0,
            '競馬場適性': 8.0,
            '総レース数': 10,
            'スクレイプ成功数': 0,
            '着順_last_10R_mean': 8.0,
            '人気_last_10R_mean': 8.0,
            'オッズ_last_10R_mean': 10.0,
            '賞金_last_5R_mean': 500000,
            '賞金_last_10R_mean': 500000,
            '斤量_last_5R_mean': 57.0,
            '上り_last_5R_mean': 35.0,
            '体重_last_5R_mean': 480,
            '体重変化_last_5R_mean': 0.0,
            '着順_all_R_mean': 8.0,
            '人気_all_R_mean': 8.0,
            'オッズ_all_R_mean': 10.0,
            '賞金_all_R_mean': 500000,
            '斤量_all_R_mean': 57.0,
            '上り_all_R_mean': 35.0,
            '体重_all_R_mean': 480,
            '体重変化_all_R_mean': 0.0,
        }
        
        for col, value in default_values.items():
            data.loc[data.index[idx], col] = value
    
    def predict_race_ultimate(self, race_id: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        🏆 究極のレース予測（全機能統合）
        
        Parameters
        ----------
        race_id : str
            レースID
            
        Returns
        -------
        Tuple[pd.DataFrame, Dict[str, Any]]
            (予測結果, レース情報)
        """
        try:
            logger.info(f"🏆 究極のレース予測開始: {race_id}")
            
            # 📚 モデル読み込み確認
            if not self.model:
                if not self.load_latest_model():
                    raise RuntimeError("モデル読み込み失敗")
            
            # 🏆 究極の出馬表取得
            race_data = self.scrape_race_card_ultimate(race_id)
            if race_data.empty:
                raise ValueError("出馬表データ取得失敗")
            
            logger.info(f"📋 出馬表取得: {len(race_data)}頭")
            
            # 🔥 リアルタイム馬戦績取得（最重要）
            horse_stats = {}
            if self.enable_live_scraping:
                logger.info("🔥 リアルタイム馬戦績取得開始...")
                
                horse_ids = []
                if 'horse_id' in race_data.columns:
                    horse_ids = race_data['horse_id'].dropna().tolist()
                else:
                    logger.warning("⚠️ horse_idカラムが見つかりません")
                
                for horse_id in horse_ids[:18]:  # 最大18頭まで
                    if horse_id:
                        stats = self.get_live_horse_performance(horse_id)
                        horse_stats[horse_id] = stats
                        
                        # 🛡️ BAN対策（馬戦績取得間の待機）
                        if self.max_ban_protection:
                            time.sleep(random.uniform(1, 3))
                
                logger.info(f"✅ {len(horse_stats)}頭のリアルタイム戦績取得完了")
            
            # 📊 レース情報構築
            race_info = {
                'race_id': race_id,
                'course_len': 1600,  # デフォルト
                'prediction_method': 'ultimate',
                'scraped_horses': len(horse_stats),
                'live_scraping_enabled': self.enable_live_scraping,
                'selenium_enabled': self.use_selenium,
                'tfr_enabled': self.enable_tfr
            }
            
            # 🚀 TensorFlow Ranking統合予測
            if self.enable_tfr and self.hybrid_system:
                try:
                    logger.info("🚀 TensorFlow Ranking統合予測を試行...")
                    results, tfr_info = self.hybrid_system.predict_hybrid(race_id)
                    if not results.empty:
                        race_info['prediction_method'] = 'tfr_hybrid'
                        race_info.update(tfr_info)
                        logger.info("✅ TFR統合予測成功")
                        return results, race_info
                except Exception as e:
                    logger.warning(f"⚠️ TFR統合予測失敗: {e}")
            
            # 🤖 LightGBM予測（メイン）
            logger.info("🤖 LightGBM予測実行...")
            
            # 🏆 究極の特徴量準備
            X, processed_data = self.prepare_ultimate_features(race_data, race_info, horse_stats)
            
            if X.empty:
                raise ValueError("特徴量準備失敗")
            
            # 📏 スケーリング
            if self.scaler:
                X_scaled = self.scaler.transform(X)
            else:
                X_scaled = X.values
            
            # 🎯 予測実行
            predictions = self.model.predict(X_scaled)
            
            # 📊 結果整理
            results = processed_data[['枠番', '馬番', '馬名']].copy()
            
            if 'horse_id' in processed_data.columns:
                results['horse_id'] = processed_data['horse_id']
            
            results['予測スコア'] = predictions
            results['予測順位'] = predictions.argsort()[::-1].argsort() + 1
            
            # 勝率正規化
            total_score = predictions.sum()
            if total_score > 0:
                results['勝率'] = (predictions / total_score * 100).round(1)
            else:
                results['勝率'] = (np.ones(len(predictions)) / len(predictions) * 100).round(1)
            
            results['3着以内確率'] = (predictions * 100).round(1)
            
            # 🔥 リアルタイムデータ使用情報
            results['データ品質'] = results.apply(
                lambda row: 'リアルタイム' if (row.get('horse_id', '') in horse_stats and 
                                       horse_stats[row.get('horse_id', '')].get('scraped_races', 0) > 0) 
                else 'デフォルト', axis=1
            )
            
            # 順位でソート
            results = results.sort_values('予測順位')
            
            race_info.update({
                'prediction_method': 'ultimate_lightgbm',
                'horses_predicted': len(results),
                'live_data_quality': (results['データ品質'] == 'リアルタイム').sum()
            })
            
            logger.info(f"✅ 究極のレース予測完了: {len(results)}頭（リアルタイムデータ: {race_info['live_data_quality']}頭）")
            return results, race_info
            
        except Exception as e:
            logger.error(f"❌ 究極のレース予測エラー: {e}")
            return pd.DataFrame(), {'error': str(e), 'prediction_method': 'failed'}
    
    def display_ultimate_results(self, results: pd.DataFrame, race_info: Dict[str, Any]):
        """🏆 究極の予測結果表示"""
        if results.empty:
            print("❌ [エラー] 予測結果がありません")
            return
        
        print("\\n" + "="*100)
        print("🏆 [競馬AI] Ultimate Live Race Predictor - 最強統合版予測結果 🏆")
        print("="*100)
        
        # 基本情報
        print(f"📋 レースID: {race_info.get('race_id', 'N/A')}")
        print(f"🎯 予測方法: {race_info.get('prediction_method', 'unknown')}")
        print(f"⏰ 予測時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # システム情報
        print(f"🔥 リアルタイムスクレイピング: {'✅' if race_info.get('live_scraping_enabled') else '❌'}")
        print(f"🤖 Selenium統合: {'✅' if race_info.get('selenium_enabled') else '❌'}")
        print(f"🚀 TensorFlow Ranking: {'✅' if race_info.get('tfr_enabled') else '❌'}")
        
        # データ品質情報
        if 'live_data_quality' in race_info:
            print(f"📊 リアルタイムデータ: {race_info['live_data_quality']}/{race_info.get('horses_predicted', 0)}頭")
        
        # 勝率合計確認
        total_win_rate = results['勝率'].sum() if '勝率' in results.columns else 0
        
        print(f"\\n🎯 [予測結果] (予測順位順) - 出馬頭数: {len(results)}頭")
        print(f"📊 勝率合計: {total_win_rate:.1f}% (正規化済み)")
        print("-" * 100)
        
        # 結果表示
        for i, (_, row) in enumerate(results.iterrows()):
            枠番 = row.get('枠番', 0)
            馬番 = row.get('馬番', 0)
            horse_name = row.get('馬名', 'N/A')
            
            if (枠番 == 0 or 馬番 == 0 or 
                horse_name in ['N/A', '', '0', '--'] or 
                pd.isna(horse_name)):
                continue
            
            予測順位 = row.get('予測順位', 0)
            勝率 = row.get('勝率', 0)
            確率 = row.get('3着以内確率', 0)
            データ品質 = row.get('データ品質', '')
            
            # データ品質表示
            quality_icon = "🔥" if データ品質 == 'リアルタイム' else "📊"
            
            try:
                print(f"{予測順位:2.0f}位 {枠番:2.0f}-{馬番:2.0f} {str(horse_name):15s} "
                      f"勝率:{勝率:5.1f}% 3着内:{確率:5.1f}% {quality_icon}{データ品質}")
            except:
                print(f"{int(予測順位)}位 {int(枠番)}-{int(馬番)} {str(horse_name)} "
                      f"勝率:{float(勝率):.1f}% 3着内:{float(確率):.1f}% {quality_icon}{データ品質}")
        
        # 🎯 買い目候補
        self._display_ultimate_betting_suggestions(results)
        
        # システム情報
        print("\\n🏆 [Ultimate System Information]")
        print("-" * 50)
        print("🔥 リアルタイムデータ取得による最新戦績反映")
        print("🤖 Selenium + Requests ハイブリッド出馬表取得")
        print("🛡️ 最高レベルBAN対策（User-Agent ローテーション・待機時間）")
        print("📊 13種類の高品質特徴量自動生成")
        print("🚀 TensorFlow Ranking統合対応")
        print("🏆 競馬AI予測システムの最高峰実装")
        print("⚠️ 投資は自己責任で行ってください")
        
        print("\\n" + "="*100)
    
    def _display_ultimate_betting_suggestions(self, results: pd.DataFrame):
        """🎯 究極の買い目候補表示"""
        print("\\n🎯 [買い目候補]")
        print("-" * 50)
        
        if len(results) >= 3:
            top3 = results.head(3)
            print("🏆 [3連複候補]")
            try:
                print(f"   {top3.iloc[0].get('枠番', 0):.0f}-{top3.iloc[0].get('馬番', 0):.0f}-"
                      f"{top3.iloc[1].get('枠番', 0):.0f}-{top3.iloc[1].get('馬番', 0):.0f}-"
                      f"{top3.iloc[2].get('枠番', 0):.0f}-{top3.iloc[2].get('馬番', 0):.0f}")
            except:
                print("   データが不完全です")
        
        if len(results) >= 1:
            winner_candidate = results.iloc[0]
            try:
                horse_name = winner_candidate.get('馬名', 'N/A')
                if pd.isna(horse_name):
                    horse_name = 'N/A'
                
                データ品質 = winner_candidate.get('データ品質', '')
                quality_info = f" ({データ品質}データ)" if データ品質 else ""
                
                print(f"\\n🏆 [単勝候補] {winner_candidate.get('枠番', 0):.0f}-{winner_candidate.get('馬番', 0):.0f} {horse_name}{quality_info}")
                print(f"   勝率: {winner_candidate.get('勝率', 0):.1f}%")
                print(f"   3着以内確率: {winner_candidate.get('3着以内確率', 0):.1f}%")
            except:
                print("\\n🏆 [単勝候補] データが不完全です")
    
    def cleanup(self):
        """🧹 リソースクリーンアップ"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("🧹 Seleniumドライバーをクリーンアップしました")
            except Exception as e:
                logger.warning(f"⚠️ ドライバークリーンアップエラー: {e}")
        
        if self.session:
            self.session.close()
    
    def __del__(self):
        """デストラクタ"""
        self.cleanup()


def main():
    """🏆 メイン実行関数"""
    try:
        print("🏆 [競馬AI] Ultimate Live Race Predictor")
        print("= 最強統合版予測システム =")
        print("🔥 リアルタイムデータ取得 + 🤖 Selenium統合 + 🛡️ 最大BAN対策 + 🚀 TFR統合")
        
        # 🏆 システム初期化
        predictor = UltimateLiveRacePredictor(
            use_selenium=True,           # 🤖 Selenium統合
            enable_live_scraping=True,   # 🔥 リアルタイムスクレイピング（最重要）
            enable_tfr=True,            # 🚀 TensorFlow Ranking統合
            max_ban_protection=True     # 🛡️ 最大BAN対策
        )
        
        # レースID入力
        try:
            race_id = input("\\n🏁 レースIDを入力してください (例: 202412080101): ").strip()
        except EOFError:
            print("非対話モードでテスト実行中...")
            race_id = "202406080101"  # テスト用レースID
        
        if not race_id:
            print("レースIDが入力されませんでした。デモを実行します。")
            race_id = "202406080101"
        
        print(f"\\n🏆 究極のレース予測を実行中: {race_id}")
        print("🔥 リアルタイムデータ取得中... (BAN対策のため時間がかかります)")
        
        # 🏆 究極の予測実行
        results, race_info = predictor.predict_race_ultimate(race_id)
        
        if not results.empty:
            predictor.display_ultimate_results(results, race_info)
        else:
            print("❌ [エラー] 予測に失敗しました")
            print("・レースIDが存在しない可能性があります")
            print("・ネットワーク接続を確認してください")
            print("・システムの設定を確認してください")
        
    except KeyboardInterrupt:
        print("\\n\\n⚠️ 処理が中断されました")
    except Exception as e:
        logger.error(f"メイン実行エラー: {e}")
        print(f"❌ [エラー] エラーが発生しました: {e}")
    finally:
        # 🧹 クリーンアップ
        if 'predictor' in locals():
            predictor.cleanup()


if __name__ == "__main__":
    main()