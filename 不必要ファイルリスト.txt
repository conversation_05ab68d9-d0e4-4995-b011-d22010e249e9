# 競馬AI予測システム - 不必要ファイルリスト
# 生成日時: 2025-01-06
# プロジェクト: keiba_ai_system

## 概要
プロジェクト全体を調査し、不必要なファイルを削除優先度別に整理しました。
推定容量削減: 約30-50%（2-10GB程度）

==================================================================================
## 【優先度：高】即座に削除推奨のファイル（リスク最小）
==================================================================================

### 重複・コピーファイル
CLAUDE - コピー.md                                    # 重複ドキュメント
activate_env - コピー.bat                            # 重複環境設定ファイル
core/features/calculators_backup.py                  # バックアップファイル
core/processors/comprehensive_integrator_old.py      # 古いバージョン
core/processors/race_processor_old.py                # 古いバージョン

### 履歴・キャッシュディレクトリ（ディレクトリ全削除）
.history/                                            # VSCode編集履歴（113ファイル）
.pytest_cache/                                       # pytest キャッシュ
venv/                                                # 仮想環境（再生成可能）

### 一時・ログファイル
keiba_ai_system.log                                  # 古いログファイル
model_training.log                                   # 古いログファイル
model_training_fixed.log                             # 古いログファイル

推定削除容量: 約2.1GB

==================================================================================
## 【優先度：中】機能統合検討ファイル（削除前に機能確認推奨）
==================================================================================

### 類似機能を持つトレーニングスクリプト（最新版のみ保持推奨）
simple_train.py                                      # 基本版
enhanced_train.py                                    # 改良版
fixed_enhanced_train.py                              # 修正版
age_enhanced_train.py                                # 年齢特徴量版
quick_enhanced_train.py                              # 高速版
train_model.py                                       # 別実装
train_model_fixed.py                                 # 修正版

### 類似機能を持つライブ予測スクリプト（最適版のみ保持推奨）
enhanced_live_predictor.py                          # 改良版
enhanced_live_predictor_no_ssl.py                   # SSL無し版
safe_live_predictor.py                              # セーフ版
run_predictor_safe.py                               # セーフ実行版
race_predictor_live.py                              # ライブ版

### 実験用・デモ・分析スクリプト
calculate_days_old_demo.py                          # デモファイル
simple_days_old_calc.py                             # 簡易計算
race_day_calculator.py                              # レース日計算
simple_age_analysis.py                              # 簡易年齢分析
race_age_analysis.py                                # レース年齢分析
demo_race_predictor.py                              # デモ予測器

### テストファイル（有効性確認後削除検討）
test_enhanced_predictor.py                          # 拡張予測器テスト
test_prediction.py                                  # 予測テスト
core/processors/test_comprehensive_integrator.py    # 統合テスト

推定削除容量: 約5MB

==================================================================================
## 【優先度：低】整理対象ファイル（慎重検討推奨）
==================================================================================

### 実験結果・一時出力ディレクトリ（バックアップ後削除検討）
demo_analysis_output/                               # デモ分析結果
integrated_analysis_output/                         # 統合分析結果
model_interpretation_results/                       # モデル解釈結果
test_explanation_output/                            # テスト説明出力
optuna_results/                                     # Optuna最適化結果
quick_optuna_results/                               # 高速Optuna結果
tfr_optuna_quick_results/                           # TensorFlowランキング結果
tfr_optuna_fixed_results/                           # TensorFlowランキング修正結果
simple_optuna_demo/                                 # 簡易Optunaデモ

### 古いモデルファイル（最新版のみ保持推奨）
models/features_20250607_205540.pkl                 # 古い特徴量ファイル
models/lgb_model_20250607_205540.pkl                # 古いLightGBMモデル
models/scaler_20250607_205540.pkl                   # 古いスケーラー
models/simple_features_20250607_213413.pkl          # 簡易特徴量ファイル
models/simple_lgb_model_20250607_213413.pkl         # 簡易LightGBMモデル
models/simple_scaler_20250607_213413.pkl            # 簡易スケーラー

推定削除容量: 約150MB

==================================================================================
## 【注意】削除前に確認が必要なファイル・ディレクトリ
==================================================================================

### 大容量データファイル（要慎重検討）
data/                                               # 生データ（約17GB、22万binファイル）
                                                    # 確認事項：バックアップ存在、再生成可能性

output/                                             # 処理済みデータ（約2GB）
                                                    # 確認事項：再処理コスト

### 重複設定ファイル（統合検討）
training_config.yaml                                # メイン設定
feature_config.yaml                                # 特徴量設定
core/features/config.yaml                          # コア特徴量設定

==================================================================================
## 推奨削除手順
==================================================================================

【第一段階】即座削除（リスク最小）
1. コピー・バックアップファイルの削除
2. キャッシュディレクトリの削除
3. 古いログファイルの削除

【第二段階】機能統合（機能確認後）
1. 重複スクリプトの機能確認
2. 最新・最適版の特定
3. 古いバージョンの段階的削除

【第三段階】実験結果整理（バックアップ後）
1. 重要な実験結果の特定・保存
2. 不要な出力ディレクトリの削除
3. 古いモデルファイルの整理

【第四段階】データファイル最適化（十分検討後）
1. データ再生成可能性の確認
2. バックアップ戦略の策定
3. 大容量データの段階的削除

==================================================================================
## 削除実行例（Windows バッチファイル形式）
==================================================================================

REM 即座削除推奨ファイル
del "CLAUDE - コピー.md"
del "activate_env - コピー.bat"
del "core\features\calculators_backup.py"
del "core\processors\comprehensive_integrator_old.py"
del "core\processors\race_processor_old.py"
del "keiba_ai_system.log"
del "model_training.log"
del "model_training_fixed.log"

REM ディレクトリ削除
rmdir /s /q ".history"
rmdir /s /q ".pytest_cache"
REM venv削除は要注意（再構築必要）
REM rmdir /s /q "venv"

==================================================================================
## 注意事項
==================================================================================

1. **バックアップ作成**: 削除前に重要ファイルのバックアップを作成
2. **段階的削除**: 一度にすべて削除せず、段階的に実行
3. **機能確認**: スクリプト削除前に依存関係を確認
4. **再生成確認**: データファイル削除前に再生成手順を確認
5. **版数管理**: Git管理下のファイルは慎重に削除

この整理により、プロジェクトの保守性向上と容量削減を実現できます。