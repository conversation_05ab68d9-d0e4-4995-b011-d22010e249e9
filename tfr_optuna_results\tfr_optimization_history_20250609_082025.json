[{"trial_number": 0, "params": {"n_hidden_layers": 2, "activation": "relu", "learning_rate": 4.207988669606632e-05, "l2_reg": 8.62913219007185e-08, "use_dropout": false, "dropout_rate": 0.2, "use_batch_norm": false, "optimizer": "adam", "ranking_loss": "pairwise_soft_zero_one_loss", "batch_size": 32, "hidden_size_0": 256, "hidden_size_1": 32}, "cv_score": 0.0, "cv_std": 0.0}, {"trial_number": 1, "params": {"n_hidden_layers": 1, "activation": "relu", "learning_rate": 0.06245139574743072, "l2_reg": 0.006220025976819169, "use_dropout": true, "dropout_rate": 0.2, "use_batch_norm": true, "optimizer": "rmsprop", "ranking_loss": "pairwise_logistic_loss", "batch_size": 16, "hidden_size_0": 32}, "cv_score": 0.0, "cv_std": 0.0}, {"trial_number": 2, "params": {"n_hidden_layers": 3, "activation": "relu", "learning_rate": 1.5167330688076188e-05, "l2_reg": 8.9532762476427e-07, "use_dropout": true, "dropout_rate": 0.2123738038749523, "use_batch_norm": true, "optimizer": "rmsprop", "ranking_loss": "pairwise_hinge_loss", "batch_size": 8, "hidden_size_0": 32, "hidden_size_1": 32, "hidden_size_2": 128}, "cv_score": 0.0, "cv_std": 0.0}, {"trial_number": 3, "params": {"n_hidden_layers": 4, "activation": "elu", "learning_rate": 0.011044350847124691, "l2_reg": 2.3316490638960624e-05, "use_dropout": true, "dropout_rate": 0.11016765069763808, "use_batch_norm": true, "optimizer": "adam", "ranking_loss": "pairwise_hinge_loss", "batch_size": 8, "hidden_size_0": 128, "hidden_size_1": 64, "hidden_size_2": 256, "hidden_size_3": 256}, "cv_score": 0.0, "cv_std": 0.0}, {"trial_number": 4, "params": {"n_hidden_layers": 4, "activation": "relu", "learning_rate": 0.0004673518999562748, "l2_reg": 2.1510319582575732e-07, "use_dropout": false, "dropout_rate": 0.30751624869734645, "use_batch_norm": true, "optimizer": "adam", "ranking_loss": "pairwise_hinge_loss", "batch_size": 16, "hidden_size_0": 128, "hidden_size_1": 128, "hidden_size_2": 64, "hidden_size_3": 128}, "cv_score": 0.0, "cv_std": 0.0}, {"trial_number": 5, "params": {"n_hidden_layers": 1, "activation": "relu", "learning_rate": 1.4557961490251982e-05, "l2_reg": 3.510408513301911e-05, "use_dropout": true, "dropout_rate": 0.35806911616378, "use_batch_norm": false, "optimizer": "sgd", "ranking_loss": "pairwise_soft_zero_one_loss", "batch_size": 8, "hidden_size_0": 32}, "cv_score": 0.0, "cv_std": 0.0}, {"trial_number": 6, "params": {"n_hidden_layers": 1, "activation": "tanh", "learning_rate": 0.00022704877557909215, "l2_reg": 1.2452525727440503e-06, "use_dropout": false, "dropout_rate": 0.35681265846171517, "use_batch_norm": false, "optimizer": "adam", "ranking_loss": "pairwise_logistic_loss", "batch_size": 32, "hidden_size_0": 128}, "cv_score": 0.0, "cv_std": 0.0}, {"trial_number": 7, "params": {"n_hidden_layers": 2, "activation": "elu", "learning_rate": 0.004270232968405542, "l2_reg": 2.5695177376559533e-05, "use_dropout": false, "dropout_rate": 0.48920422190097823, "use_batch_norm": false, "optimizer": "sgd", "ranking_loss": "pairwise_hinge_loss", "batch_size": 8, "hidden_size_0": 256, "hidden_size_1": 256}, "cv_score": 0.0, "cv_std": 0.0}, {"trial_number": 8, "params": {"n_hidden_layers": 2, "activation": "relu", "learning_rate": 0.00015058980408366387, "l2_reg": 2.0444965009175185e-06, "use_dropout": true, "dropout_rate": 0.2, "use_batch_norm": true, "optimizer": "rmsprop", "ranking_loss": "pairwise_hinge_loss", "batch_size": 8, "hidden_size_0": 256, "hidden_size_1": 128}, "cv_score": 0.0, "cv_std": 0.0}, {"trial_number": 9, "params": {"n_hidden_layers": 3, "activation": "relu", "learning_rate": 0.015246518242848608, "l2_reg": 0.002187923086577435, "use_dropout": false, "dropout_rate": 0.2, "use_batch_norm": false, "optimizer": "rmsprop", "ranking_loss": "pairwise_soft_zero_one_loss", "batch_size": 32, "hidden_size_0": 32, "hidden_size_1": 256, "hidden_size_2": 64}, "cv_score": 0.0, "cv_std": 0.0}]