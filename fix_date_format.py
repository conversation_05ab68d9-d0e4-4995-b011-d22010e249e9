#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日付フォーマット修正スクリプト
enhanced_comprehensive_data_2020.pickleの日付カラムを修正
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_japanese_date(date_str):
    """日本語の日付文字列をdatetimeに変換"""
    try:
        if pd.isna(date_str):
            return pd.NaT
        
        date_str = str(date_str)
        # '2020年7月25日' -> '2020-07-25'
        date_str = date_str.replace('年', '-').replace('月', '-').replace('日', '')
        return pd.to_datetime(date_str, format='%Y-%m-%d')
    except Exception as e:
        logger.warning(f"日付変換エラー: {date_str} -> {e}")
        return pd.NaT

def fix_date_format():
    """enhanced_comprehensive_data_2020.pickleの日付フォーマットを修正"""
    logger.info("🔧 日付フォーマット修正開始...")
    
    try:
        # データ読み込み
        logger.info("📂 データ読み込み中...")
        data = pd.read_pickle('enhanced_comprehensive_data_2020.pickle')
        logger.info(f"データ読み込み完了: {len(data):,}行, {len(data.columns)}カラム")
        
        # 現在の日付カラムの状態確認
        if 'date' in data.columns:
            logger.info(f"修正前の日付カラム: {data['date'].dtype}")
            logger.info(f"例: {data['date'].head(3).tolist()}")
            
            # 日付変換
            logger.info("📅 日付変換中...")
            data['date'] = data['date'].apply(parse_japanese_date)
            
            # 変換結果の確認
            logger.info(f"修正後の日付カラム: {data['date'].dtype}")
            logger.info(f"日付範囲: {data['date'].min()} ～ {data['date'].max()}")
            logger.info(f"有効な日付数: {data['date'].notna().sum():,}件")
            logger.info(f"NaT数: {data['date'].isna().sum():,}件")
            
            # その他の日付カラムも確認・修正
            other_date_cols = ['birthday', 'last_race_date']
            for col in other_date_cols:
                if col in data.columns:
                    if data[col].dtype == 'object':
                        logger.info(f"📅 {col}カラムも修正中...")
                        data[col] = data[col].apply(parse_japanese_date)
                        logger.info(f"{col}修正完了: {data[col].notna().sum():,}件有効")
            
            # 修正済みデータを保存
            output_file = 'enhanced_comprehensive_data_2020_fixed.pickle'
            data.to_pickle(output_file)
            logger.info(f"💾 修正済みデータ保存完了: {output_file}")
            
            # 元ファイルも更新
            data.to_pickle('enhanced_comprehensive_data_2020.pickle')
            logger.info(f"💾 元ファイルも更新完了")
            
            # 統計サマリー
            logger.info("📊 修正結果サマリー:")
            logger.info(f"  ・総レコード数: {len(data):,}件")
            logger.info(f"  ・データ期間: {data['date'].min().strftime('%Y年%m月%d日')} ～ {data['date'].max().strftime('%Y年%m月%d日')}")
            logger.info(f"  ・期間: {(data['date'].max() - data['date'].min()).days}日間")
            
            return True
            
        else:
            logger.error("❌ dateカラムが見つかりません")
            return False
            
    except Exception as e:
        logger.error(f"❌ エラー: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_date_format()
    if success:
        logger.info("🎉 日付フォーマット修正完了！")
    else:
        logger.error("❌ 日付フォーマット修正に失敗しました")