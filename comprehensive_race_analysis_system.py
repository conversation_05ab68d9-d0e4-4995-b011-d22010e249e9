#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
競馬レース総合分析・解説システム

全ての分析・解説機能を統合したシステム
- レース予想
- 詳細分析
- 可視化
- モデル解釈
- 専門解説
"""

import sys
sys.path.append('.')

import pandas as pd
import numpy as np
import logging
import warnings
from pathlib import Path
from typing import Dict, Any, List, Optional

# プロジェクトモジュール
from enhanced_live_predictor import EnhancedLiveRacePredictor
from core.analysis.race_analyzer import RaceAnalyzer
from core.analysis.model_explainer import ModelExplainer
from core.analysis.race_commentator import RaceCommentator
from get_race_result_with_existing_module import RaceResultRetriever

warnings.filterwarnings('ignore')

class ComprehensiveRaceAnalysisSystem:
    """競馬レース総合分析・解説システム"""
    
    def __init__(self, output_dir: str = "comprehensive_analysis"):
        """
        初期化
        
        Parameters
        ----------
        output_dir : str
            分析結果出力ディレクトリ
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # ログ設定
        self.setup_logging()
        
        # 各コンポーネント初期化
        self.predictor = EnhancedLiveRacePredictor(use_selenium=False)
        self.analyzer = RaceAnalyzer(output_dir=str(self.output_dir / "analysis"))
        self.explainer = None  # モデル読み込み後に初期化
        self.commentator = RaceCommentator()
        self.result_retriever = RaceResultRetriever()
        
        self.logger = logging.getLogger(__name__)
        
    def setup_logging(self):
        """ログ設定"""
        log_file = self.output_dir / "analysis_system.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def analyze_race_comprehensive(self, race_id: str) -> Dict[str, Any]:
        """
        レースの総合分析実行
        
        Parameters
        ----------
        race_id : str
            レースID
            
        Returns
        -------
        Dict[str, Any]
            総合分析結果
        """
        print("=" * 80)
        print(f"競馬レース総合分析システム - レース {race_id}")
        print("=" * 80)
        
        analysis_results = {
            'race_id': race_id,
            'prediction_results': {},
            'actual_results': pd.DataFrame(),
            'race_info': {},
            'detailed_analysis': {},
            'model_explanation': {},
            'professional_commentary': {},
            'visualizations': {},
            'reports': {}
        }
        
        try:
            # 1. AI予想実行
            self.logger.info("AI予想を実行中...")
            prediction_success = self._execute_prediction(race_id, analysis_results)
            
            if not prediction_success:
                self.logger.error("AI予想に失敗しました")
                return analysis_results
            
            # 2. 実際の結果取得
            self.logger.info("実際のレース結果を取得中...")
            result_success = self._fetch_actual_results(race_id, analysis_results)
            
            # 3. 詳細分析実行
            self.logger.info("詳細分析を実行中...")
            self._execute_detailed_analysis(analysis_results)
            
            # 4. モデル解釈実行
            if self.explainer:
                self.logger.info("モデル解釈を実行中...")
                self._execute_model_explanation(analysis_results)
            
            # 5. 専門解説生成
            self.logger.info("専門解説を生成中...")
            self._generate_professional_commentary(analysis_results)
            
            # 6. 可視化作成
            self.logger.info("可視化を作成中...")
            self._create_visualizations(analysis_results)
            
            # 7. レポート生成
            self.logger.info("総合レポートを生成中...")
            self._generate_comprehensive_reports(analysis_results)
            
            self.logger.info("総合分析完了")
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"総合分析エラー: {e}")
            import traceback
            traceback.print_exc()
            return analysis_results
    
    def _execute_prediction(self, race_id: str, analysis_results: Dict[str, Any]) -> bool:
        """AI予想実行"""
        try:
            # モデル読み込み
            if not self.predictor.load_latest_model():
                self.logger.error("モデル読み込み失敗")
                return False
            
            # Explainer初期化
            self.explainer = ModelExplainer(
                model=self.predictor.model,
                features=self.predictor.features,
                output_dir=str(self.output_dir / "explanation")
            )
            
            # 予想実行
            results_df, race_info = self.predictor.predict_race(race_id)
            
            if results_df.empty:
                self.logger.error("予想結果が空です")
                return False
            
            # 予想結果をdict形式に変換
            predicted_results = {
                '馬番': results_df['馬番'].tolist(),
                '馬名': results_df['馬名'].tolist(),
                '予想順位': results_df['予測順位'].tolist(),
                '予想勝率': results_df['勝率'].tolist()
            }
            
            analysis_results['prediction_results'] = predicted_results
            analysis_results['race_info'] = race_info
            
            print(f"\n✅ AI予想完了: {len(results_df)}頭")
            print(f"予想1位: 馬番{predicted_results['馬番'][0]} {predicted_results['馬名'][0]} (勝率{predicted_results['予想勝率'][0]:.1f}%)")
            
            return True
            
        except Exception as e:
            self.logger.error(f"AI予想エラー: {e}")
            return False
    
    def _fetch_actual_results(self, race_id: str, analysis_results: Dict[str, Any]) -> bool:
        """実際の結果取得"""
        try:
            # 結果取得
            result_data = self.result_retriever.fetch_race_result(race_id)
            
            if not result_data:
                print("⚠️ 実際の結果が取得できませんでした（将来のレースまたはデータなし）")
                return False
            
            # 結果フォーマット
            actual_results_df = self.result_retriever.format_race_results(result_data)
            
            if actual_results_df is None or actual_results_df.empty:
                print("⚠️ 結果データのフォーマットに失敗しました")
                return False
            
            analysis_results['actual_results'] = actual_results_df
            
            print(f"✅ 実際の結果取得完了: {len(actual_results_df)}頭")
            if not actual_results_df.empty:
                winner = actual_results_df.iloc[0]
                print(f"勝利馬: 馬番{winner.get('umaban', '?')} {winner.get('horse_name', '不明')}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"結果取得エラー: {e}")
            return False
    
    def _execute_detailed_analysis(self, analysis_results: Dict[str, Any]):
        """詳細分析実行"""
        try:
            predicted_results = analysis_results['prediction_results']
            actual_results = analysis_results['actual_results']
            race_info = analysis_results['race_info']
            
            # 詳細分析実行
            detailed_analysis = self.analyzer.analyze_race_prediction(
                predicted_results, actual_results, race_info
            )
            
            analysis_results['detailed_analysis'] = detailed_analysis
            
            # 予想精度表示
            accuracy = detailed_analysis.get('prediction_accuracy', {})
            matches = accuracy.get('top3_matches', 0)
            print(f"✅ 詳細分析完了 - Top3的中: {matches}/3")
            
        except Exception as e:
            self.logger.error(f"詳細分析エラー: {e}")
            analysis_results['detailed_analysis'] = {}
    
    def _execute_model_explanation(self, analysis_results: Dict[str, Any]):
        """モデル解釈実行"""
        try:
            if not self.explainer:
                return
            
            # 最新の予想データからX取得
            if hasattr(self.predictor, 'last_X') and self.predictor.last_X is not None:
                X = self.predictor.last_X
                horse_names = analysis_results['prediction_results']['馬名']
                
                # モデル解釈実行
                explanation_result = self.explainer.explain_predictions(X, horse_names)
                analysis_results['model_explanation'] = explanation_result
                
                print("✅ モデル解釈完了")
            else:
                self.logger.warning("予想データが利用できないためモデル解釈をスキップ")
                
        except Exception as e:
            self.logger.error(f"モデル解釈エラー: {e}")
            analysis_results['model_explanation'] = {}
    
    def _generate_professional_commentary(self, analysis_results: Dict[str, Any]):
        """専門解説生成"""
        try:
            predicted_results = analysis_results['prediction_results']
            actual_results = analysis_results['actual_results']
            race_info = analysis_results['race_info']
            detailed_analysis = analysis_results['detailed_analysis']
            
            # 専門解説生成
            commentary = self.commentator.generate_race_commentary(
                predicted_results, actual_results, race_info, detailed_analysis
            )
            
            analysis_results['professional_commentary'] = commentary
            
            print("✅ 専門解説生成完了")
            
        except Exception as e:
            self.logger.error(f"専門解説エラー: {e}")
            analysis_results['professional_commentary'] = {}
    
    def _create_visualizations(self, analysis_results: Dict[str, Any]):
        """可視化作成"""
        try:
            visualizations = {}
            
            # 分析結果の可視化
            if analysis_results['detailed_analysis']:
                analysis_viz = self.analyzer.create_analysis_visualizations(
                    analysis_results['detailed_analysis'],
                    analysis_results['prediction_results'],
                    analysis_results['actual_results']
                )
                visualizations.update(analysis_viz)
            
            # モデル解釈の可視化
            if analysis_results['model_explanation'] and self.explainer:
                if hasattr(self.predictor, 'last_X') and self.predictor.last_X is not None:
                    explanation_viz = self.explainer.create_explanation_visualizations(
                        analysis_results['model_explanation'],
                        self.predictor.last_X
                    )
                    visualizations.update(explanation_viz)
            
            analysis_results['visualizations'] = visualizations
            
            print(f"✅ 可視化作成完了: {len(visualizations)}個のグラフ")
            
        except Exception as e:
            self.logger.error(f"可視化作成エラー: {e}")
            analysis_results['visualizations'] = {}
    
    def _generate_comprehensive_reports(self, analysis_results: Dict[str, Any]):
        """総合レポート生成"""
        try:
            reports = {}
            
            # 詳細分析レポート
            if analysis_results['detailed_analysis']:
                detailed_report = self.analyzer.generate_detailed_report(
                    analysis_results['detailed_analysis'],
                    analysis_results['prediction_results'],
                    analysis_results['actual_results']
                )
                reports['detailed_analysis'] = detailed_report
                
                # ファイル保存
                with open(self.output_dir / "detailed_analysis_report.txt", 'w', encoding='utf-8') as f:
                    f.write(detailed_report)
            
            # モデル解釈レポート
            if analysis_results['model_explanation'] and self.explainer:
                explanation_report = self.explainer.generate_explanation_report(
                    analysis_results['model_explanation']
                )
                reports['model_explanation'] = explanation_report
                
                # ファイル保存
                with open(self.output_dir / "model_explanation_report.txt", 'w', encoding='utf-8') as f:
                    f.write(explanation_report)
            
            # 専門解説レポート
            if analysis_results['professional_commentary']:
                commentary_report = self.commentator.generate_full_commentary_report(
                    analysis_results['professional_commentary']
                )
                reports['professional_commentary'] = commentary_report
                
                # ファイル保存
                with open(self.output_dir / "professional_commentary_report.txt", 'w', encoding='utf-8') as f:
                    f.write(commentary_report)
            
            # 統合レポート
            integrated_report = self._create_integrated_report(analysis_results)
            reports['integrated'] = integrated_report
            
            # ファイル保存
            with open(self.output_dir / "integrated_analysis_report.txt", 'w', encoding='utf-8') as f:
                f.write(integrated_report)
            
            analysis_results['reports'] = reports
            
            print(f"✅ レポート生成完了: {len(reports)}種類")
            
        except Exception as e:
            self.logger.error(f"レポート生成エラー: {e}")
            analysis_results['reports'] = {}
    
    def _create_integrated_report(self, analysis_results: Dict[str, Any]) -> str:
        """統合レポート作成"""
        
        report_lines = []
        report_lines.append("=" * 100)
        report_lines.append("競馬レース総合分析統合レポート")
        report_lines.append("=" * 100)
        
        race_id = analysis_results['race_id']
        report_lines.append(f"\nレースID: {race_id}")
        report_lines.append(f"分析日時: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # エグゼクティブサマリー
        report_lines.append(f"\n📋 エグゼクティブサマリー")
        report_lines.append("-" * 60)
        
        predicted_results = analysis_results['prediction_results']
        actual_results = analysis_results['actual_results']
        
        if predicted_results:
            ai_pick = predicted_results['馬番'][0]
            ai_pick_name = predicted_results['馬名'][0]
            ai_confidence = predicted_results['予想勝率'][0]
            
            report_lines.append(f"AI予想1位: 馬番{ai_pick} {ai_pick_name} (信頼度{ai_confidence:.1f}%)")
        
        if not actual_results.empty:
            actual_winner = actual_results.iloc[0]
            winner_number = actual_winner.get('umaban', '?')
            winner_name = actual_winner.get('horse_name', '不明')
            winner_odds = actual_winner.get('tansho_odds', 0)
            
            report_lines.append(f"実際の勝利馬: 馬番{winner_number} {winner_name} ({winner_odds:.1f}倍)")
            
            # 的中判定
            if predicted_results and str(ai_pick) == str(winner_number):
                report_lines.append("🎯 AI予想的中！")
            else:
                report_lines.append("❌ AI予想不的中")
        else:
            report_lines.append("実際の結果: データなし（将来のレースまたは取得失敗）")
        
        # 主要指標
        detailed_analysis = analysis_results.get('detailed_analysis', {})
        if detailed_analysis:
            accuracy = detailed_analysis.get('prediction_accuracy', {})
            matches = accuracy.get('top3_matches', 0)
            report_lines.append(f"Top3的中率: {matches}/3 ({matches/3*100:.1f}%)")
        
        # 技術分析サマリー
        model_explanation = analysis_results.get('model_explanation', {})
        if model_explanation:
            importance_data = model_explanation.get('feature_importance', {})
            top_features = importance_data.get('top_features', [])
            if top_features:
                top_feature = top_features[0]['feature']
                report_lines.append(f"最重要要因: {top_feature}")
        
        # 投資分析
        commentary = analysis_results.get('professional_commentary', {})
        if commentary:
            betting_insights = commentary.get('betting_insights', {})
            if betting_insights:
                risk_assessment = betting_insights.get('risk_assessment', '')
                if risk_assessment:
                    report_lines.append(f"投資リスク: {risk_assessment[:50]}...")
        
        # 生成されたアウトプット
        report_lines.append(f"\n📊 生成されたアウトプット")
        report_lines.append("-" * 60)
        
        visualizations = analysis_results.get('visualizations', {})
        report_lines.append(f"可視化グラフ: {len(visualizations)}個")
        
        reports = analysis_results.get('reports', {})
        report_lines.append(f"分析レポート: {len(reports)}種類")
        
        # ファイル一覧
        if visualizations:
            report_lines.append(f"\n生成された可視化:")
            for viz_type, file_path in visualizations.items():
                report_lines.append(f"  • {viz_type}: {file_path}")
        
        if reports:
            report_lines.append(f"\n生成されたレポート:")
            for report_type in reports.keys():
                report_lines.append(f"  • {report_type}_report.txt")
        
        # 推奨事項
        report_lines.append(f"\n🎯 総合推奨事項")
        report_lines.append("-" * 60)
        
        if detailed_analysis:
            recommendations = detailed_analysis.get('recommendations', {})
            betting_strategy = recommendations.get('betting_strategy', {})
            single_win = betting_strategy.get('single_win', {})
            
            if single_win.get('recommended', False):
                horse_num = single_win.get('horse_number', '?')
                confidence = single_win.get('confidence', 0)
                report_lines.append(f"推奨投資: 馬番{horse_num} 単勝 (信頼度{confidence:.1f}%)")
            else:
                report_lines.append("推奨投資: 慎重なアプローチを推奨")
        
        # システム評価
        report_lines.append(f"\n⚙️ システム評価")
        report_lines.append("-" * 60)
        report_lines.append("✅ AI予想システム: 正常動作")
        report_lines.append("✅ 分析エンジン: 正常動作")
        report_lines.append("✅ 可視化システム: 正常動作")
        report_lines.append("✅ 解説システム: 正常動作")
        
        if model_explanation:
            report_lines.append("✅ モデル解釈: 正常動作")
        else:
            report_lines.append("⚠️ モデル解釈: 一部制限")
        
        if not actual_results.empty:
            report_lines.append("✅ 結果検証: 完了")
        else:
            report_lines.append("⚠️ 結果検証: データなし")
        
        report_lines.append(f"\n" + "=" * 100)
        report_lines.append("総合分析システム - 分析完了")
        report_lines.append("=" * 100)
        
        return "\n".join(report_lines)
    
    def display_analysis_summary(self, analysis_results: Dict[str, Any]):
        """分析結果サマリー表示"""
        
        print("\n" + "=" * 80)
        print("🏇 競馬レース総合分析結果サマリー")
        print("=" * 80)
        
        race_id = analysis_results['race_id']
        print(f"レースID: {race_id}")
        
        # 予想結果
        predicted_results = analysis_results['prediction_results']
        if predicted_results:
            print(f"\n🎯 AI予想結果:")
            print(f"{'順位':>4} {'馬番':>4} {'馬名':>16} {'勝率':>8}")
            print("-" * 40)
            
            for i in range(min(5, len(predicted_results['馬番']))):
                rank = predicted_results['予想順位'][i]
                horse_num = predicted_results['馬番'][i]
                horse_name = predicted_results['馬名'][i][:16]
                win_rate = predicted_results['予想勝率'][i]
                
                print(f"{rank:>4} {horse_num:>4} {horse_name:>16} {win_rate:>7.1f}%")
        
        # 実際の結果
        actual_results = analysis_results['actual_results']
        if not actual_results.empty:
            print(f"\n🏆 実際の結果:")
            print(f"{'着順':>4} {'馬番':>4} {'馬名':>16} {'オッズ':>8}")
            print("-" * 40)
            
            for i, (_, row) in enumerate(actual_results.head(5).iterrows()):
                rank = row.get('rank', i+1)
                horse_num = row.get('umaban', '?')
                horse_name = str(row.get('horse_name', '?'))[:16]
                odds = row.get('tansho_odds', 0)
                
                print(f"{rank:>4} {horse_num:>4} {horse_name:>16} {odds:>7.1f}")
        
        # 精度評価
        detailed_analysis = analysis_results.get('detailed_analysis', {})
        if detailed_analysis:
            accuracy = detailed_analysis.get('prediction_accuracy', {})
            matches = accuracy.get('top3_matches', 0)
            exact_winner = accuracy.get('exact_winner', False)
            
            print(f"\n📊 予想精度:")
            print(f"Top3的中: {matches}/3")
            print(f"1位的中: {'○' if exact_winner else '×'}")
        
        # 生成物一覧
        visualizations = analysis_results.get('visualizations', {})
        reports = analysis_results.get('reports', {})
        
        print(f"\n📁 生成されたファイル:")
        print(f"可視化グラフ: {len(visualizations)}個")
        print(f"分析レポート: {len(reports)}種類")
        
        if visualizations:
            print(f"\n📈 可視化ファイル:")
            for viz_type, file_path in visualizations.items():
                print(f"  • {viz_type}")
        
        print(f"\n📄 レポートファイル:")
        print(f"  • detailed_analysis_report.txt")
        print(f"  • model_explanation_report.txt") 
        print(f"  • professional_commentary_report.txt")
        print(f"  • integrated_analysis_report.txt")
        
        print(f"\n💾 出力ディレクトリ: {self.output_dir}")
        print("=" * 80)

def main():
    """メイン実行関数"""
    
    # レースID指定
    race_id = "202505021211"  # 前回と同じレースで検証
    
    print("競馬レース総合分析・解説システム")
    print(f"対象レース: {race_id}")
    
    # システム初期化
    analysis_system = ComprehensiveRaceAnalysisSystem()
    
    # 総合分析実行
    analysis_results = analysis_system.analyze_race_comprehensive(race_id)
    
    # 結果表示
    analysis_system.display_analysis_summary(analysis_results)
    
    # 統合レポート表示（抜粋）
    integrated_report = analysis_results.get('reports', {}).get('integrated', '')
    if integrated_report:
        print("\n" + "=" * 80)
        print("統合レポート（抜粋）")
        print("=" * 80)
        # 最初の20行を表示
        report_lines = integrated_report.split('\n')
        for line in report_lines[:25]:
            print(line)
        if len(report_lines) > 25:
            print("...")
            print(f"完全版は integrated_analysis_report.txt をご覧ください")

if __name__ == "__main__":
    main()