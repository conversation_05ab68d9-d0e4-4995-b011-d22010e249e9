# データリーケージ検査レポート

## 検査対象
- ディレクトリ: `H:\AI\keiba_ai_system\core\features`
- 検査日時: 2025-06-08
- 主要ファイル: `manager.py`, `calculators.py`, `definitions.py`, `config.yaml`

## 発見された重大な問題

### 1. 時系列データのフィルタリング欠如 🚨
**問題箇所**: `calculators.py`の以下の関数
- `calculate_race_count()`
- `calculate_avg_rank()`
- `calculate_win_rate()`
- `calculate_place_rate()`
- `calculate_show_rate()`
- `calculate_avg_prize()`
- `calculate_max_prize()`
- `calculate_last_rank()`
- `calculate_jockey_win_rate()`
- `calculate_trainer_win_rate()`

**問題内容**:
```python
# 現在の実装（問題あり）
def calculate_win_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame, ...):
    # 全期間のデータを使用してしまっている
    horse_stats = horse_results_df.groupby('horse_id').agg({
        '着順': ['count', lambda x: (pd.to_numeric(x, errors='coerce') == 1).sum()]
    })
```

**影響**: 
- 予測時点で知り得ない**未来のレース結果**が特徴量に含まれる
- モデルの評価指標が不当に高くなる
- 実運用時の性能が大幅に低下する可能性

### 2. 設定の未実装 ⚠️
**問題箇所**: `config.yaml`
```yaml
performance_features:
  exclude_current_race: true # 設定は存在するが実装されていない
```

**影響**:
- 設定ファイルの意図が反映されていない
- データリーケージ防止機構が機能していない

### 3. 前走情報の取得方法 🚨
**問題箇所**: `calculate_last_rank()`関数
```python
# 日付チェックなしで最後のレースを取得
last_ranks = horse_results_df.groupby('horse_id')['着順'].last()
```

**影響**:
- データの並び順に依存した不安定な動作
- 未来のレース結果を前走として使用する可能性

## 修正方法

### 1. 日付フィルタリングの実装
修正版コード（`fixed/calculators_fixed.py`）の主要改善点：

```python
def filter_past_data(self, horse_results_df: pd.DataFrame, 
                    current_race_date: Union[str, pd.Timestamp],
                    horse_id: Optional[str] = None,
                    exclude_current_date: bool = True) -> pd.DataFrame:
    """現在のレース日より前のデータのみをフィルタリング"""
    # ... 実装 ...
```

### 2. 各関数での日付考慮
```python
def calculate_win_rate(self, data: pd.DataFrame, horse_results_df: pd.DataFrame,
                      column: str = 'horse_id', race_date_column: str = 'date', **kwargs):
    # 各レースごとに過去データのみを使用
    for idx, row in data.iterrows():
        past_data = self.filter_past_data(
            horse_results_df, 
            row[race_date_column], 
            horse_id=row[column],
            exclude_current_date=True  # 当日のデータは除外
        )
        # ... 計算処理 ...
```

### 3. 統計期間の考慮
騎手・調教師の成績では、直近期間のみを使用：
```python
# 統計期間の開始日を計算
start_date = race_date - timedelta(days=statistics_period_days)
```

## 推奨対応

### 即時対応が必要 🔴
1. `calculators.py`を`calculators_fixed.py`に置き換える
2. 全ての特徴量計算でレース日を必須パラメータとする
3. 既存の学習済みモデルの再学習

### 追加の改善提案 🟡
1. ユニットテストの追加
   - 未来データが含まれていないことを確認するテスト
   - 境界条件（同日レースなど）のテスト

2. データ検証の強化
   ```python
   def validate_no_future_data(train_data, test_data):
       """訓練データに未来の情報が含まれていないことを確認"""
       pass
   ```

3. ログ機能の強化
   - フィルタリングされたデータ件数の記録
   - 警告メッセージの充実

## 検証方法

修正後の検証手順：
1. 特定の日付でデータを分割
2. 過去成績特徴量を計算
3. その日付以降のデータが含まれていないことを確認

```python
# 検証コード例
test_date = '2024-01-01'
features = calculator.calculate_win_rate(data, horse_results_df, race_date_column='date')
# test_date以降のデータが使われていないことを確認
```

## まとめ

- **重大度**: 🔴 高（データリーケージは機械学習の致命的な問題）
- **影響範囲**: 全ての過去成績特徴量
- **推定修正時間**: 2-4時間（テスト含む）
- **再学習の必要性**: あり

このデータリーケージを修正することで、モデルの実運用時の性能と評価時の性能の乖離を防ぐことができます。
