#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
シンプルOptuna最適化デモ
独立したデータで高速最適化のデモンストレーション
"""

import pandas as pd
import numpy as np
import optuna
import lightgbm as lgb
from sklearn.model_selection import train_test_split, KFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, accuracy_score
from sklearn.datasets import make_classification
import pickle
import logging
import warnings
from datetime import datetime
from pathlib import Path
import json

warnings.filterwarnings('ignore')

class SimpleOptunaDemo:
    """シンプルなOptuna最適化デモクラス"""
    
    def __init__(self, n_trials: int = 25, random_seed: int = 42):
        self.n_trials = n_trials
        self.random_seed = random_seed
        self.output_dir = Path("simple_optuna_demo")
        self.output_dir.mkdir(exist_ok=True)
        
        # ログ設定
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # データとモデル用の変数
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        
        # 最適化結果
        self.best_params = None
        self.best_score = None
        self.optimization_history = []
    
    def generate_horse_racing_like_data(self) -> bool:
        """競馬風の合成データを生成"""
        
        try:
            self.logger.info("競馬風合成データを生成中...")
            
            # 基本的な分類データを生成
            X, y = make_classification(
                n_samples=8000,
                n_features=33,  # 既存モデルと同じ特徴量数
                n_informative=20,
                n_redundant=5,
                n_clusters_per_class=2,
                class_sep=0.8,
                random_state=self.random_seed
            )
            
            # 競馬風の特徴量名を作成
            feature_names = [
                '枠番', '馬番', '斤量', 'course_len',
                '着順_last_5R_mean', '人気_last_5R_mean', 'オッズ_last_5R_mean',
                '賞金_last_5R_mean', '斤量_last_5R_mean', '上り_last_5R_mean',
                '体重_last_5R_mean', '体重変化_last_5R_mean',
                '着順_last_10R_mean', '人気_last_10R_mean', 'オッズ_last_10R_mean',
                '賞金_last_10R_mean', '斤量_last_10R_mean', '上り_last_10R_mean',
                '体重_last_10R_mean', '体重変化_last_10R_mean',
                '着順_all_R_mean', '人気_all_R_mean', 'オッズ_all_R_mean',
                '賞金_all_R_mean', '斤量_all_R_mean', '上り_all_R_mean',
                '体重_all_R_mean', '体重変化_all_R_mean',
                'interval_days', 'race_class', 'ground_state', 'weather', 'track_direction'
            ]
            
            # DataFrameに変換
            df = pd.DataFrame(X, columns=feature_names)
            
            # 競馬らしい値の範囲に調整
            df['枠番'] = (df['枠番'] * 4 + 4).clip(1, 8).round().astype(int)
            df['馬番'] = (df['馬番'] * 9 + 9).clip(1, 18).round().astype(int)
            df['斤量'] = (df['斤量'] * 3 + 55).clip(52, 58)
            df['course_len'] = np.random.choice([1200, 1400, 1600, 1800, 2000, 2400], len(df))
            
            # 着順関連（1-18位）
            for col in df.columns:
                if '着順' in col:
                    df[col] = (df[col] * 5 + 9).clip(1, 18)
            
            # 人気関連（1-18番人気）
            for col in df.columns:
                if '人気' in col:
                    df[col] = (df[col] * 5 + 9).clip(1, 18)
            
            # オッズ関連（1.1-50倍）
            for col in df.columns:
                if 'オッズ' in col:
                    df[col] = np.exp(df[col] * 1.5 + 1).clip(1.1, 50)
            
            # 訓練・テストデータに分割
            self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
                df, y, test_size=0.2, random_state=self.random_seed, stratify=y
            )
            
            self.logger.info(f"データ生成完了:")
            self.logger.info(f"  訓練データ: {self.X_train.shape}")
            self.logger.info(f"  テストデータ: {self.X_test.shape}")
            self.logger.info(f"  正例率（訓練）: {self.y_train.mean():.3f}")
            self.logger.info(f"  正例率（テスト）: {self.y_test.mean():.3f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"データ生成エラー: {e}")
            return False
    
    def objective(self, trial) -> float:
        """Optuna最適化の目的関数"""
        
        # LightGBMハイパーパラメータの提案
        params = {
            'objective': 'binary',
            'metric': 'auc',
            'boosting_type': 'gbdt',
            'num_leaves': trial.suggest_int('num_leaves', 10, 100),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
            'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
            'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
            'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
            'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
            'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 10.0, log=True),
            'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 10.0, log=True),
            'random_state': self.random_seed,
            'verbosity': -1
        }
        
        # クロスバリデーション
        kfold = KFold(n_splits=3, shuffle=True, random_state=self.random_seed)
        scores = []
        
        for train_idx, val_idx in kfold.split(self.X_train):
            X_train_fold = self.X_train.iloc[train_idx]
            X_val_fold = self.X_train.iloc[val_idx]
            y_train_fold = self.y_train[train_idx]
            y_val_fold = self.y_train[val_idx]
            
            # LightGBMデータセット作成
            train_data = lgb.Dataset(X_train_fold, label=y_train_fold)
            val_data = lgb.Dataset(X_val_fold, label=y_val_fold, reference=train_data)
            
            # モデル訓練
            model = lgb.train(
                params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=100,
                callbacks=[
                    lgb.early_stopping(stopping_rounds=10),
                    lgb.log_evaluation(0)
                ]
            )
            
            # 予測とスコア計算
            y_pred = model.predict(X_val_fold, num_iteration=model.best_iteration)
            score = roc_auc_score(y_val_fold, y_pred)
            scores.append(score)
        
        cv_score = np.mean(scores)
        
        # 履歴記録
        self.optimization_history.append({
            'trial_number': trial.number,
            'params': params,
            'cv_score': cv_score,
            'cv_std': np.std(scores)
        })
        
        return cv_score
    
    def optimize_hyperparameters(self) -> bool:
        """ハイパーパラメータ最適化の実行"""
        
        try:
            self.logger.info(f"Optuna最適化開始: {self.n_trials}回試行")
            
            # Optunaスタディ作成
            study = optuna.create_study(
                direction='maximize',
                sampler=optuna.samplers.TPESampler(seed=self.random_seed),
                pruner=optuna.pruners.MedianPruner(n_startup_trials=5)
            )
            
            # 最適化実行
            study.optimize(
                self.objective,
                n_trials=self.n_trials,
                timeout=300,  # 5分でタイムアウト
                show_progress_bar=True
            )
            
            # 結果保存
            self.best_params = study.best_params
            self.best_score = study.best_value
            
            self.logger.info(f"最適化完了:")
            self.logger.info(f"  最高AUC: {self.best_score:.4f}")
            self.logger.info(f"  最適パラメータ: {self.best_params}")
            
            # 最適パラメータで最終モデル訓練
            self._train_final_model()
            
            return True
            
        except Exception as e:
            self.logger.error(f"最適化エラー: {e}")
            return False
    
    def _train_final_model(self):
        """最適パラメータで最終モデル訓練"""
        
        # 最適パラメータの設定
        final_params = self.best_params.copy()
        final_params.update({
            'objective': 'binary',
            'metric': 'auc',
            'boosting_type': 'gbdt',
            'random_state': self.random_seed,
            'verbosity': -1
        })
        
        # 全訓練データでモデル訓練
        train_data = lgb.Dataset(self.X_train, label=self.y_train)
        
        final_model = lgb.train(
            final_params,
            train_data,
            num_boost_round=200,
            callbacks=[lgb.log_evaluation(50)]
        )
        
        # テストデータで評価
        y_test_pred = final_model.predict(self.X_test)
        test_auc = roc_auc_score(self.y_test, y_test_pred)
        test_acc = accuracy_score(self.y_test, (y_test_pred > 0.5).astype(int))
        
        self.logger.info(f"最終モデル性能:")
        self.logger.info(f"  テストAUC: {test_auc:.4f}")
        self.logger.info(f"  テスト精度: {test_acc:.4f}")
        
        # モデル保存
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_file = self.output_dir / f"demo_optimized_model_{timestamp}.pkl"
        
        with open(model_file, 'wb') as f:
            pickle.dump(final_model, f)
        
        self.logger.info(f"最終モデル保存: {model_file}")
        
        return test_auc, test_acc
    
    def generate_report(self) -> str:
        """最適化レポート生成"""
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("Optuna ハイパーパラメータ最適化デモ レポート")
        report_lines.append("=" * 80)
        
        # 基本情報
        report_lines.append(f"\n📊 最適化概要:")
        report_lines.append(f"  試行回数: {len(self.optimization_history)}")
        report_lines.append(f"  特徴量数: {self.X_train.shape[1] if self.X_train is not None else 'N/A'}")
        report_lines.append(f"  訓練サンプル数: {len(self.X_train) if self.X_train is not None else 'N/A'}")
        report_lines.append(f"  テストサンプル数: {len(self.X_test) if self.X_test is not None else 'N/A'}")
        
        # 最高スコア
        if self.best_score is not None:
            report_lines.append(f"\n🎯 最適化結果:")
            report_lines.append(f"  最高CV-AUC: {self.best_score:.4f}")
            
            if self.best_params:
                report_lines.append(f"  最適パラメータ:")
                for param, value in self.best_params.items():
                    if isinstance(value, float):
                        report_lines.append(f"    {param}: {value:.4f}")
                    else:
                        report_lines.append(f"    {param}: {value}")
        
        # 最適化推移
        scores = [h['cv_score'] for h in self.optimization_history]
        if scores:
            report_lines.append(f"\n📈 最適化推移:")
            report_lines.append(f"  初期スコア: {scores[0]:.4f}")
            report_lines.append(f"  最終スコア: {scores[-1]:.4f}")
            report_lines.append(f"  改善幅: {max(scores) - scores[0]:+.4f}")
            report_lines.append(f"  平均スコア: {np.mean(scores):.4f}")
            report_lines.append(f"  標準偏差: {np.std(scores):.4f}")
        
        # 上位試行
        sorted_history = sorted(self.optimization_history, 
                              key=lambda x: x['cv_score'], reverse=True)
        
        report_lines.append(f"\n🏆 上位5試行:")
        report_lines.append(f"{'順位':>4} {'試行':>6} {'AUC':>8} {'学習率':>10} {'葉数':>6} {'特徴選択率':>10}")
        report_lines.append("-" * 60)
        
        for i, trial in enumerate(sorted_history[:5]):
            rank = i + 1
            trial_num = trial['trial_number']
            score = trial['cv_score']
            lr = trial['params'].get('learning_rate', 0)
            leaves = trial['params'].get('num_leaves', 0)
            feat_frac = trial['params'].get('feature_fraction', 0)
            
            report_lines.append(f"{rank:>4} {trial_num:>6} {score:>8.4f} {lr:>10.4f} {leaves:>6} {feat_frac:>10.3f}")
        
        # パラメータ重要度
        param_values = {}
        for trial in self.optimization_history:
            for param, value in trial['params'].items():
                if param not in param_values:
                    param_values[param] = []
                param_values[param].append(value)
        
        report_lines.append(f"\n📊 パラメータ統計:")
        for param, values in param_values.items():
            if isinstance(values[0], (int, float)):
                mean_val = np.mean(values)
                std_val = np.std(values)
                report_lines.append(f"  {param}: 平均={mean_val:.4f}, 標準偏差={std_val:.4f}")
        
        report_lines.append(f"\n💡 最適化のポイント:")
        report_lines.append(f"  • 学習率: 低めの値（0.01-0.1）が良い傾向")
        report_lines.append(f"  • 葉数: 中程度（30-70）が安定")
        report_lines.append(f"  • 正則化: 両方のパラメータが重要")
        report_lines.append(f"  • 特徴選択: 0.7-0.9の範囲が効果的")
        
        report_lines.append(f"\n" + "=" * 80)
        report_lines.append("Optuna最適化デモ完了")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)

def main():
    """メイン実行関数"""
    
    print("=" * 80)
    print("Optuna ハイパーパラメータ最適化デモ")
    print("=" * 80)
    
    # デモ設定
    demo = SimpleOptunaDemo(
        n_trials=25,  # 25回の試行
        random_seed=42
    )
    
    try:
        # 1. 合成データ生成
        print("\n📊 競馬風合成データ生成中...")
        if not demo.generate_horse_racing_like_data():
            print("❌ データ生成に失敗しました。")
            return
        
        print("✅ データ生成完了")
        print(f"  訓練データ: {demo.X_train.shape}")
        print(f"  テストデータ: {demo.X_test.shape}")
        print(f"  正例率: {demo.y_train.mean():.3f}")
        
        # 2. ハイパーパラメータ最適化
        print("\n🔍 ハイパーパラメータ最適化中...")
        if not demo.optimize_hyperparameters():
            print("❌ 最適化に失敗しました。")
            return
        
        print("✅ 最適化完了")
        print(f"  最高AUC: {demo.best_score:.4f}")
        
        # 3. レポート生成
        print("\n📄 最適化レポート生成中...")
        report = demo.generate_report()
        
        # レポート表示
        print("\n" + report)
        
        # レポート保存
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = demo.output_dir / f"demo_optimization_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # 最適パラメータ保存
        params_file = demo.output_dir / f"demo_best_params_{timestamp}.json"
        with open(params_file, 'w', encoding='utf-8') as f:
            json.dump({
                'best_params': demo.best_params,
                'best_score': demo.best_score,
                'n_trials': len(demo.optimization_history)
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 結果保存:")
        print(f"  レポート: {report_file}")
        print(f"  最適パラメータ: {params_file}")
        print("\n🎉 Optunaデモ完了！")
        
        print(f"\n💡 実際の運用では:")
        print(f"  • 試行回数を50-200回に増やす")
        print(f"  • より多くのパラメータを最適化")
        print(f"  • 複数の評価指標で最適化")
        print(f"  • アンサンブル手法との組み合わせ")
        
    except KeyboardInterrupt:
        print("\n⚠️ ユーザーによって中断されました。")
    except Exception as e:
        print(f"\n❌ エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()