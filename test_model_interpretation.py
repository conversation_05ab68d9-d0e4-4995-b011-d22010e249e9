#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
モデル解釈システムの動作テスト
"""

import sys
sys.path.append('.')

import pandas as pd
import numpy as np
import pickle
import logging
from pathlib import Path
from core.analysis.model_explainer import ModelExplainer

# ログ設定
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_model_interpretation():
    """モデル解釈システムのテスト"""
    
    print("=" * 60)
    print("モデル解釈システム動作テスト")
    print("=" * 60)
    
    try:
        # 1. 最新モデルの読み込み
        model_files = list(Path("models").glob("*leakage_model*.pkl"))
        if not model_files:
            print("❌ データリーケージ修正済みモデルが見つかりません")
            return False
        
        latest_model_file = max(model_files, key=lambda p: p.stat().st_mtime)
        print(f"📁 モデルファイル: {latest_model_file}")
        
        with open(latest_model_file, 'rb') as f:
            model = pickle.load(f)
        print(f"✅ モデル読み込み成功: {type(model)}")
        
        # 2. 特徴量リストの読み込み
        feature_files = list(Path("models").glob("*leakage_features*.pkl"))
        if not feature_files:
            print("❌ 特徴量ファイルが見つかりません")
            return False
        
        latest_feature_file = max(feature_files, key=lambda p: p.stat().st_mtime)
        print(f"📁 特徴量ファイル: {latest_feature_file}")
        
        with open(latest_feature_file, 'rb') as f:
            features = pickle.load(f)
        print(f"✅ 特徴量読み込み成功: {len(features)}個")
        
        # 3. ModelExplainerの初期化テスト
        print(f"\n🔍 ModelExplainer初期化テスト...")
        explainer = ModelExplainer(model=model, features=features, output_dir="test_explanation_output")
        
        if explainer.explainer is None:
            print("❌ SHAP Explainer初期化に失敗")
            return False
        else:
            print("✅ SHAP Explainer初期化成功")
        
        # 4. ダミーデータでの説明テスト
        print(f"\n🧪 ダミーデータでの説明テスト...")
        
        # ダミーの特徴量データ作成（18頭分）
        np.random.seed(42)
        dummy_data = pd.DataFrame(
            np.random.randn(18, len(features)), 
            columns=features
        )
        
        # 一部の特徴量を現実的な値に調整
        if '枠番' in dummy_data.columns:
            dummy_data['枠番'] = np.random.randint(1, 9, 18)
        if '馬番' in dummy_data.columns:
            dummy_data['馬番'] = range(1, 19)
        if '斤量' in dummy_data.columns:
            dummy_data['斤量'] = np.random.uniform(52, 58, 18)
        if 'course_len' in dummy_data.columns:
            dummy_data['course_len'] = 2400  # 統一
            
        print(f"🎲 ダミーデータ作成: {dummy_data.shape}")
        
        # 5. 説明実行
        horse_names = [f"テスト馬{i+1}" for i in range(18)]
        
        print("🔄 予想説明実行中...")
        explanation_result = explainer.explain_predictions(dummy_data, horse_names)
        
        if not explanation_result:
            print("❌ 説明生成に失敗")
            return False
        
        print("✅ 説明生成成功")
        
        # 6. 結果の確認
        print(f"\n📊 説明結果の確認:")
        
        if 'feature_importance' in explanation_result:
            importance = explanation_result['feature_importance']
            top_features = importance.get('top_features', [])
            print(f"  • 特徴量重要度: {len(top_features)}個の特徴量")
            
            if top_features:
                print(f"  • 最重要特徴量: {top_features[0]['feature']}")
        
        if 'individual_explanations' in explanation_result:
            individual = explanation_result['individual_explanations']
            print(f"  • 個別説明: {len(individual)}頭分")
        
        if 'model_insights' in explanation_result:
            insights = explanation_result['model_insights']
            print(f"  • モデル洞察: 複雑度{insights.get('model_complexity', 0)}")
        
        # 7. 可視化テスト
        print(f"\n🎨 可視化テスト...")
        visualization_files = explainer.create_explanation_visualizations(explanation_result, dummy_data)
        
        if visualization_files:
            print(f"✅ 可視化成功: {len(visualization_files)}個のファイル")
            for viz_type, file_path in visualization_files.items():
                print(f"  • {viz_type}: {file_path}")
        else:
            print("⚠️ 可視化ファイル生成なし")
        
        # 8. レポート生成テスト
        print(f"\n📄 レポート生成テスト...")
        report = explainer.generate_explanation_report(explanation_result)
        
        if report and len(report) > 100:
            print("✅ レポート生成成功")
            print(f"  • レポート長: {len(report)}文字")
            
            # レポートをファイルに保存
            with open("test_explanation_output/model_explanation_test_report.txt", 'w', encoding='utf-8') as f:
                f.write(report)
            print("  • レポート保存完了")
            
        else:
            print("❌ レポート生成に失敗")
            return False
        
        # 9. 制限事項の確認
        print(f"\n⚠️ 制限事項の確認:")
        limitations = []
        
        # モデルタイプチェック
        is_lightgbm = hasattr(model, 'booster_') or str(type(model)).find('lightgbm') != -1
        if not is_lightgbm:
            limitations.append("TreeExplainer最適化なし（LightGBM以外のモデル）")
        else:
            print("  ✅ LightGBM TreeExplainer最適化が有効")
        
        # 特徴量数チェック
        if len(features) > 50:
            limitations.append(f"特徴量数が多い（{len(features)}個）- 解釈が複雑")
        
        # SHAP値の形状チェック
        try:
            test_shap = explainer.explainer.shap_values(dummy_data.head(1))
            if len(test_shap.shape) != 2:
                limitations.append("SHAP値の形状が予期しない形式")
        except Exception as e:
            limitations.append(f"SHAP値計算エラー: {str(e)}")
        
        if limitations:
            print("  制限事項が検出されました:")
            for limitation in limitations:
                print(f"    - {limitation}")
        else:
            print("  主要な制限事項は検出されませんでした")
        
        print(f"\n✅ モデル解釈システムテスト完了")
        return True
        
    except Exception as e:
        print(f"❌ テスト中にエラーが発生: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_interpretation_issues():
    """解釈システムの問題診断"""
    
    print(f"\n🔧 解釈システム問題診断:")
    print("-" * 40)
    
    issues = []
    solutions = []
    
    # 1. 必要なライブラリのチェック
    try:
        import shap
        print(f"✅ SHAP: {shap.__version__}")
    except ImportError:
        issues.append("SHAPライブラリが不足")
        solutions.append("pip install shap")
    
    try:
        import matplotlib
        print(f"✅ Matplotlib: {matplotlib.__version__}")
    except ImportError:
        issues.append("Matplotlibライブラリが不足")
        solutions.append("pip install matplotlib")
    
    try:
        import japanize_matplotlib
        print(f"✅ japanize-matplotlib インストール済み")
    except ImportError:
        issues.append("日本語フォントサポートが不足")
        solutions.append("pip install japanize-matplotlib")
    
    # 2. モデルファイルの存在確認
    model_files = list(Path("models").glob("*.pkl"))
    if not model_files:
        issues.append("モデルファイルが見つからない")
        solutions.append("モデルを再トレーニングしてください")
    else:
        print(f"✅ モデルファイル: {len(model_files)}個")
    
    # 3. 出力ディレクトリの権限チェック
    try:
        test_dir = Path("test_explanation_output")
        test_dir.mkdir(exist_ok=True)
        test_file = test_dir / "permission_test.txt"
        test_file.write_text("test")
        test_file.unlink()
        print(f"✅ 出力ディレクトリ権限: OK")
    except Exception as e:
        issues.append(f"出力ディレクトリ権限エラー: {e}")
        solutions.append("出力ディレクトリの権限を確認してください")
    
    # 問題と解決策の表示
    if issues:
        print(f"\n⚠️ 検出された問題:")
        for i, issue in enumerate(issues):
            print(f"  {i+1}. {issue}")
        
        print(f"\n💡 推奨解決策:")
        for i, solution in enumerate(solutions):
            print(f"  {i+1}. {solution}")
    else:
        print(f"\n✅ 主要な問題は検出されませんでした")
    
    return len(issues) == 0

def main():
    """メイン実行"""
    
    print("モデル解釈システムの詳細テストを開始します...")
    
    # 1. 問題診断
    diagnosis_ok = diagnose_interpretation_issues()
    
    # 2. 実際のテスト
    if diagnosis_ok:
        test_ok = test_model_interpretation()
        
        if test_ok:
            print(f"\n🎉 モデル解釈システムは正常に動作しています！")
            print("⚠️ 制限事項がある場合は上記を参照してください。")
        else:
            print(f"\n❌ モデル解釈システムに問題があります。")
    else:
        print(f"\n❌ 事前診断で問題が検出されました。解決後に再テストしてください。")

if __name__ == "__main__":
    main()