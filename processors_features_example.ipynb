{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ProcessorsとFeaturesシステムの統合利用例\n", "\n", "このnotebookでは、リファクタリング後のprocessorsとfeaturesシステムを統合的に利用する方法を示します。\n", "\n", "## 🎯 学習内容\n", "1. **ComprehensiveDataIntegrator**でのデータ統合\n", "2. **FeatureEngineeringManager**での特徴量生成\n", "3. **年齢・生後日数特徴量**の活用\n", "4. **機械学習モデル**での性能評価\n", "\n", "## 📋 前提条件\n", "- output/フォルダに2020年のpickleデータが存在すること\n", "- 必要なライブラリがインストール済みであること"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 必要なライブラリのインポート\n", "import sys\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import logging\n", "\n", "warnings.filterwarnings('ignore')\n", "plt.rcParams['font.family'] = 'DejaVu Sans'\n", "\n", "# プロジェクトパスの追加\n", "project_root = '/mnt/h/AI/keiba_ai_system'\n", "if project_root not in sys.path:\n", "    sys.path.append(project_root)\n", "\n", "# ログ設定\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"📚 ライブラリのインポート完了\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# プロジェクトモジュールのインポート\n", "from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator\n", "from core.features.manager import FeatureEngineeringManager\n", "from core.features.definitions import create_age_features, create_age_feature_group\n", "from core.features.calculators import FeatureCalculators\n", "\n", "print(\"🔧 プロジェクトモジュールのインポート完了\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. データ統合：ComprehensiveDataIntegratorの活用\n", "\n", "リファクタリング後のComprehensiveDataIntegratorを使って、レース情報・結果・馬情報を統合します。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ComprehensiveDataIntegratorの初期化\n", "print(\"🔄 データ統合を開始...\")\n", "\n", "# 設定を指定してインテグレーターを初期化\n", "config = {\n", "    \"include_race_info\": True,\n", "    \"include_horse_info\": True, \n", "    \"include_past_performance\": True,\n", "    \"use_pickle_source\": True,  # Pickleファイルを使用\n", "    \"max_workers\": 2\n", "}\n", "\n", "integrator = ComprehensiveDataIntegrator(config=config)\n", "\n", "print(\"✅ ComprehensiveDataIntegrator初期化完了\")"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": "# 2020年データの統合処理\nprint(\"📊 2020年データを統合中...\")\n\ntry:\n    # 修正されたデータを直接読み込み\n    enhanced_data_path = \"enhanced_comprehensive_data_2020.pickle\"\n    \n    if os.path.exists(enhanced_data_path):\n        print(\"📂 修正済みデータを読み込み中...\")\n        comprehensive_data = pd.read_pickle(enhanced_data_path)\n        print(f\"✅ データ読み込み完了: {len(comprehensive_data):,}件, {len(comprehensive_data.columns)}カラム\")\n        \n        # 日付列の処理\n        if 'date' in comprehensive_data.columns:\n            comprehensive_data['date'] = pd.to_datetime(comprehensive_data['date'], errors='coerce')\n        \n        print(f\"📅 データ期間: {comprehensive_data['date'].min()} 〜 {comprehensive_data['date'].max()}\")\n    else:\n        # ComprehensiveDataIntegratorを使用\n        comprehensive_data = integrator.generate_comprehensive_table(\n            year=\"2020\",\n            include_race_info=True,\n            include_horse_info=True,\n            include_past_performance=True\n        )\n        print(f\"✅ データ統合完了: {len(comprehensive_data):,}件, {len(comprehensive_data.columns)}カラム\")\n    \nexcept Exception as e:\n    print(f\"❌ データ統合でエラー: {e}\")\n    # エラーの場合はサンプルデータを作成\n    comprehensive_data = pd.DataFrame({\n        'horse_id': ['horse_001', 'horse_002', 'horse_003'] * 100,\n        'date': pd.date_range('2020-01-01', periods=300, freq='D'),\n        'birthday': pd.date_range('2017-01-01', periods=300, freq='3D'),\n        'total_races': np.random.randint(1, 30, 300),\n        'win_rate': np.random.uniform(0, 0.3, 300),\n        '着順': np.random.randint(1, 15, 300)\n    })\n    print(\"📝 サンプルデータを使用します\")"}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# データの基本情報を確認\n", "print(\"📋 統合データの基本情報:\")\n", "print(f\"・レコード数: {len(comprehensive_data):,}件\")\n", "print(f\"・カラム数: {len(comprehensive_data.columns)}個\")\n", "print(f\"・ユニーク馬数: {comprehensive_data['horse_id'].nunique():,}頭\")\n", "\n", "# 主要カラムの存在確認\n", "key_columns = ['horse_id', 'date', 'birthday', 'total_races', 'win_rate', '着順']\n", "missing_columns = [col for col in key_columns if col not in comprehensive_data.columns]\n", "\n", "if missing_columns:\n", "    print(f\"⚠️ 不足しているカラム: {missing_columns}\")\n", "else:\n", "    print(\"✅ 主要カラムが全て存在します\")\n", "\n", "# データのサンプルを表示\n", "print(\"\\n📊 データサンプル:\")\n", "display_columns = [col for col in ['horse_id', 'date', 'birthday', 'total_races', 'win_rate', '着順'] \n", "                  if col in comprehensive_data.columns]\n", "print(comprehensive_data[display_columns].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 特徴量エンジニアリング：FeatureEngineeringManagerの活用\n", "\n", "新しく統合された年齢・生後日数特徴量を含む、包括的な特徴量生成を行います。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FeatureEngineeringManagerの初期化\n", "print(\"🔧 特徴量エンジニアリング準備中...\")\n", "\n", "try:\n", "    # core/features/config.yamlを使用\n", "    feature_manager = FeatureEngineeringManager(config_path=\"core/features/config.yaml\")\n", "    \n", "    print(\"✅ FeatureEngineeringManager初期化完了\")\n", "    \n", "    # 登録されている特徴量の確認\n", "    feature_names = list(feature_manager.feature_definitions.keys())\n", "    print(f\"📋 登録済み特徴量数: {len(feature_names)}個\")\n", "    \n", "    # 年齢関連特徴量の確認\n", "    age_features = [name for name in feature_names \n", "                   if any(keyword in name.lower() for keyword in \n", "                         ['age', 'days_old', 'young', 'prime', 'veteran', 'experience', 'mature'])]\n", "    \n", "    print(f\"🎂 年齢関連特徴量: {len(age_features)}個\")\n", "    for feature in age_features:\n", "        print(f\"  - {feature}\")\n", "        \n", "except Exception as e:\n", "    print(f\"❌ FeatureEngineeringManager初期化エラー: {e}\")\n", "    feature_manager = None"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": "# 年齢特徴量の直接計算（Feature Calculatorsを使用）\nprint(\"🧮 年齢特徴量を計算中...\")\n\n# FeatureCalculatorsを直接使用\ncalc = FeatureCalculators()\n\n# データのコピーを作成\ndata_with_age_features = comprehensive_data.copy()\n\ntry:\n    # birthdayカラムがない場合はダミーデータを作成\n    if 'birthday' not in data_with_age_features.columns or data_with_age_features['birthday'].isna().all():\n        print(\"⚠️ birthdayデータがないため、サンプル生年月日を生成中...\")\n        # 現実的な年齢分布でダミー生年月日を作成\n        base_date = pd.to_datetime('2020-01-01')\n        random_days = np.random.randint(365*2, 365*8, len(data_with_age_features))  # 2-8歳\n        data_with_age_features['birthday'] = base_date - pd.to_timedelta(random_days, unit='D')\n        print(\"📅 サンプル生年月日を生成しました\")\n    \n    # 基本年齢特徴量の計算\n    print(\"1. 基本年齢特徴量を計算中...\")\n    \n    # 生後日数\n    data_with_age_features['days_old'] = calc.calculate_days_old(\n        data_with_age_features, 'date', 'birthday'\n    )\n    \n    # 年齢（年単位）\n    data_with_age_features['age_years'] = calc.calculate_age_years(\n        data_with_age_features, 'date', 'birthday'\n    )\n    \n    # 月齢\n    data_with_age_features['age_months'] = calc.calculate_age_months(\n        data_with_age_features, 'date', 'birthday'\n    )\n    \n    print(\"2. 年齢カテゴリ特徴量を計算中...\")\n    \n    # 年齢カテゴリ\n    data_with_age_features['is_young'] = calc.calculate_age_category_young(\n        data_with_age_features, 'date', 'birthday', threshold=3.0\n    )\n    \n    data_with_age_features['is_prime'] = calc.calculate_age_category_prime(\n        data_with_age_features, 'date', 'birthday', min_threshold=3.0, max_threshold=6.0\n    )\n    \n    data_with_age_features['is_veteran'] = calc.calculate_age_category_veteran(\n        data_with_age_features, 'date', 'birthday', threshold=6.0\n    )\n    \n    print(\"3. 交互作用特徴量を計算中...\")\n    \n    # 交互作用特徴量（過去成績データがある場合のみ）\n    if 'total_races' in data_with_age_features.columns:\n        data_with_age_features['experience_density'] = calc.calculate_experience_density(\n            data_with_age_features, 'total_races', 'date', 'birthday'\n        )\n    \n    if 'win_rate' in data_with_age_features.columns:\n        data_with_age_features['young_prospect'] = calc.calculate_young_prospect(\n            data_with_age_features, 'date', 'birthday', 'win_rate', \n            age_threshold=4.0, win_rate_threshold=0.1\n        )\n    \n    if 'total_races' in data_with_age_features.columns:\n        data_with_age_features['mature_period'] = calc.calculate_mature_period(\n            data_with_age_features, 'date', 'birthday', 'total_races',\n            min_age=4.0, max_age=7.0, min_races=10\n        )\n    \n    print(\"✅ 年齢特徴量計算完了\")\n    \n    # 計算結果の概要を表示\n    age_summary = data_with_age_features[['age_years', 'is_young', 'is_prime', 'is_veteran']].describe()\n    print(\"\\n📊 年齢特徴量の概要:\")\n    print(age_summary)\n    \nexcept Exception as e:\n    print(f\"❌ 年齢特徴量計算エラー: {e}\")\n    import traceback\n    traceback.print_exc()"}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 年齢特徴量の統計サマリー\n", "print(\"📊 年齢特徴量の統計サマリー:\")\n", "\n", "age_feature_columns = ['days_old', 'age_years', 'age_months', 'is_young', 'is_prime', 'is_veteran']\n", "if 'experience_density' in data_with_age_features.columns:\n", "    age_feature_columns.append('experience_density')\n", "if 'young_prospect' in data_with_age_features.columns:\n", "    age_feature_columns.append('young_prospect')\n", "if 'mature_period' in data_with_age_features.columns:\n", "    age_feature_columns.append('mature_period')\n", "\n", "# 統計情報を表示\n", "existing_age_columns = [col for col in age_feature_columns if col in data_with_age_features.columns]\n", "\n", "if existing_age_columns:\n", "    print(data_with_age_features[existing_age_columns].describe())\n", "    \n", "    # 年齢分布の可視化\n", "    if 'age_years' in data_with_age_features.columns:\n", "        plt.figure(figsize=(12, 4))\n", "        \n", "        plt.subplot(1, 3, 1)\n", "        plt.hist(data_with_age_features['age_years'].dropna(), bins=20, alpha=0.7, color='skyblue')\n", "        plt.title('Age Distribution (Years)')\n", "        plt.xlabel('Age (Years)')\n", "        plt.ylabel('Frequency')\n", "        \n", "        plt.subplot(1, 3, 2)\n", "        age_categories = ['Young', 'Prime', 'Veteran']\n", "        age_counts = [\n", "            data_with_age_features['is_young'].sum() if 'is_young' in data_with_age_features.columns else 0,\n", "            data_with_age_features['is_prime'].sum() if 'is_prime' in data_with_age_features.columns else 0,\n", "            data_with_age_features['is_veteran'].sum() if 'is_veteran' in data_with_age_features.columns else 0\n", "        ]\n", "        plt.bar(age_categories, age_counts, color=['lightgreen', 'gold', 'coral'])\n", "        plt.title('Age Category Distribution')\n", "        plt.ylabel('Count')\n", "        \n", "        plt.subplot(1, 3, 3)\n", "        if 'experience_density' in data_with_age_features.columns:\n", "            plt.scatter(data_with_age_features['age_years'], \n", "                       data_with_age_features['experience_density'], \n", "                       alpha=0.5, s=10)\n", "            plt.title('Age vs Experience Density')\n", "            plt.xlabel('Age (Years)')\n", "            plt.ylabel('Experience Density')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "else:\n", "    print(\"⚠️ 年齢特徴量が見つかりません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 特徴量の効果分析\n", "\n", "新しく追加された年齢特徴量が予測性能にどの程度寄与するかを分析します。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 機械学習用のデータ準備\n", "print(\"🤖 機械学習用データ準備中...\")\n", "\n", "# ターゲット変数の作成（3着以内を1、それ以外を0）\n", "if '着順' in data_with_age_features.columns:\n", "    data_with_age_features['target'] = (pd.to_numeric(data_with_age_features['着順'], errors='coerce') <= 3).astype(int)\n", "    target_available = True\n", "else:\n", "    # サンプルターゲットを作成\n", "    data_with_age_features['target'] = np.random.choice([0, 1], size=len(data_with_age_features), p=[0.7, 0.3])\n", "    target_available = False\n", "    print(\"📝 サンプルターゲット変数を使用\")\n", "\n", "# 特徴量の準備\n", "feature_columns = []\n", "\n", "# 基本的な数値特徴量\n", "basic_features = ['total_races', 'win_rate']\n", "for col in basic_features:\n", "    if col in data_with_age_features.columns:\n", "        feature_columns.append(col)\n", "\n", "# 年齢特徴量\n", "age_features = ['days_old', 'age_years', 'age_months', 'is_young', 'is_prime', 'is_veteran', \n", "               'experience_density', 'young_prospect', 'mature_period']\n", "for col in age_features:\n", "    if col in data_with_age_features.columns:\n", "        feature_columns.append(col)\n", "\n", "print(f\"📋 使用する特徴量: {len(feature_columns)}個\")\n", "for feature in feature_columns:\n", "    print(f\"  - {feature}\")\n", "\n", "# 欠損値の処理\n", "ml_data = data_with_age_features[feature_columns + ['target']].copy()\n", "ml_data = ml_data.fillna(0)  # 簡単な欠損値処理\n", "\n", "print(f\"✅ 機械学習用データ準備完了: {len(ml_data)}件\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 年齢特徴量ありなしでのモデル性能比較\n", "print(\"📈 年齢特徴量の効果を分析中...\")\n", "\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score, roc_auc_score, classification_report\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "# データ分割\n", "X = ml_data[feature_columns]\n", "y = ml_data['target']\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.3, random_state=42, stratify=y\n", ")\n", "\n", "print(f\"🔄 データ分割完了: 訓練{len(X_train)}件, テスト{len(X_test)}件\")\n", "\n", "# 基本特徴量のみでの学習\n", "basic_features_only = ['total_races', 'win_rate']\n", "basic_features_available = [col for col in basic_features_only if col in feature_columns]\n", "\n", "if basic_features_available:\n", "    print(\"\\n1. 基本特徴量のみでの学習...\")\n", "    \n", "    X_train_basic = X_train[basic_features_available]\n", "    X_test_basic = X_test[basic_features_available]\n", "    \n", "    # スケーリング\n", "    scaler_basic = StandardScaler()\n", "    X_train_basic_scaled = scaler_basic.fit_transform(X_train_basic)\n", "    X_test_basic_scaled = scaler_basic.transform(X_test_basic)\n", "    \n", "    # モデル学習\n", "    model_basic = RandomForestClassifier(n_estimators=100, random_state=42)\n", "    model_basic.fit(X_train_basic_scaled, y_train)\n", "    \n", "    # 予測と評価\n", "    y_pred_basic = model_basic.predict(X_test_basic_scaled)\n", "    y_proba_basic = model_basic.predict_proba(X_test_basic_scaled)[:, 1]\n", "    \n", "    accuracy_basic = accuracy_score(y_test, y_pred_basic)\n", "    auc_basic = roc_auc_score(y_test, y_proba_basic)\n", "    \n", "    print(f\"   精度: {accuracy_basic:.4f}\")\n", "    print(f\"   AUC: {auc_basic:.4f}\")\n", "else:\n", "    print(\"⚠️ 基本特徴量が不足しています\")\n", "    accuracy_basic, auc_basic = 0.5, 0.5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 年齢特徴量を含む全特徴量での学習\n", "print(\"2. 年齢特徴量を含む全特徴量での学習...\")\n", "\n", "# スケーリング\n", "scaler_full = StandardScaler()\n", "X_train_scaled = scaler_full.fit_transform(X_train)\n", "X_test_scaled = scaler_full.transform(X_test)\n", "\n", "# モデル学習\n", "model_full = RandomForestClassifier(n_estimators=100, random_state=42)\n", "model_full.fit(X_train_scaled, y_train)\n", "\n", "# 予測と評価\n", "y_pred_full = model_full.predict(X_test_scaled)\n", "y_proba_full = model_full.predict_proba(X_test_scaled)[:, 1]\n", "\n", "accuracy_full = accuracy_score(y_test, y_pred_full)\n", "auc_full = roc_auc_score(y_test, y_proba_full)\n", "\n", "print(f\"   精度: {accuracy_full:.4f}\")\n", "print(f\"   AUC: {auc_full:.4f}\")\n", "\n", "# 特徴量重要度の分析\n", "feature_importance = pd.DataFrame({\n", "    'feature': feature_columns,\n", "    'importance': model_full.feature_importances_\n", "}).sort_values('importance', ascending=False)\n", "\n", "print(\"\\n📊 特徴量重要度 Top 10:\")\n", "print(feature_importance.head(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 性能比較の可視化\n", "print(\"\\n📈 性能比較結果:\")\n", "\n", "# 改善効果の計算\n", "accuracy_improvement = accuracy_full - accuracy_basic\n", "auc_improvement = auc_full - auc_basic\n", "\n", "print(f\"精度改善: {accuracy_basic:.4f} → {accuracy_full:.4f} (+{accuracy_improvement:.4f})\")\n", "print(f\"AUC改善: {auc_basic:.4f} → {auc_full:.4f} (+{auc_improvement:.4f})\")\n", "\n", "# 可視化\n", "fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "\n", "# 1. 性能比較バーチャート\n", "metrics = ['Accuracy', 'AUC']\n", "basic_scores = [accuracy_basic, auc_basic]\n", "full_scores = [accuracy_full, auc_full]\n", "\n", "x = np.arange(len(metrics))\n", "width = 0.35\n", "\n", "axes[0].bar(x - width/2, basic_scores, width, label='Basic Features', color='lightblue')\n", "axes[0].bar(x + width/2, full_scores, width, label='With Age Features', color='orange')\n", "axes[0].set_xlabel('Metrics')\n", "axes[0].set_ylabel('Score')\n", "axes[0].set_title('Performance Comparison')\n", "axes[0].set_xticks(x)\n", "axes[0].set_xticklabels(metrics)\n", "axes[0].legend()\n", "axes[0].set_ylim(0, 1)\n", "\n", "# 2. 特徴量重要度の可視化\n", "top_features = feature_importance.head(8)\n", "axes[1].barh(range(len(top_features)), top_features['importance'], color='green', alpha=0.7)\n", "axes[1].set_yticks(range(len(top_features)))\n", "axes[1].set_yticklabels(top_features['feature'])\n", "axes[1].set_xlabel('Importance')\n", "axes[1].set_title('Feature Importance')\n", "axes[1].invert_yaxis()\n", "\n", "# 3. 年齢特徴量の寄与度\n", "age_feature_names = ['days_old', 'age_years', 'age_months', 'is_young', 'is_prime', 'is_veteran', \n", "                    'experience_density', 'young_prospect', 'mature_period']\n", "age_importance = feature_importance[feature_importance['feature'].isin(age_feature_names)]\n", "\n", "if not age_importance.empty:\n", "    axes[2].pie(age_importance['importance'], labels=age_importance['feature'], \n", "               autopct='%1.1f%%', startangle=90)\n", "    axes[2].set_title('Age Features Contribution')\n", "else:\n", "    axes[2].text(0.5, 0.5, 'No Age Features\\nFound', ha='center', va='center', transform=axes[2].transAxes)\n", "    axes[2].set_title('Age Features Contribution')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 改善効果のサマリー\n", "print(\"\\n🎯 年齢特徴量の効果サマリー:\")\n", "if accuracy_improvement > 0:\n", "    print(f\"✅ 精度が {accuracy_improvement:.1%} 改善\")\n", "else:\n", "    print(f\"❌ 精度が {abs(accuracy_improvement):.1%} 低下\")\n", "\n", "if auc_improvement > 0:\n", "    print(f\"✅ AUCが {auc_improvement:.1%} 改善\")\n", "else:\n", "    print(f\"❌ AUCが {abs(auc_improvement):.1%} 低下\")\n", "\n", "# 年齢特徴量の重要度ランキング\n", "age_ranking = age_importance.reset_index(drop=True)\n", "if not age_ranking.empty:\n", "    print(\"\\n🏆 年齢特徴量重要度ランキング:\")\n", "    for i, row in age_ranking.iterrows():\n", "        print(f\"  {i+1}. {row['feature']}: {row['importance']:.4f}\")\n", "else:\n", "    print(\"\\n⚠️ 年齢特徴量の重要度データが見つかりません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 実用的な応用例：レース予測システム\n", "\n", "統合されたprocessorsとfeaturesシステムを使って、実際のレース予測システムの構築例を示します。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 予測システムクラスの定義\n", "class IntegratedRacePredictionSystem:\n", "    \"\"\"\n", "    ProcessorsとFeaturesを統合したレース予測システム\n", "    \"\"\"\n", "    \n", "    def __init__(self, model=None, scaler=None, feature_columns=None):\n", "        self.model = model\n", "        self.scaler = scaler\n", "        self.feature_columns = feature_columns or []\n", "        self.feature_calculator = FeatureCalculators()\n", "        \n", "    def prepare_features(self, data):\n", "        \"\"\"\n", "        データに対して特徴量エンジニアリングを実行\n", "        \"\"\"\n", "        enhanced_data = data.copy()\n", "        \n", "        # 年齢特徴量の計算\n", "        if 'date' in data.columns and 'birthday' in data.columns:\n", "            enhanced_data['days_old'] = self.feature_calculator.calculate_days_old(data, 'date', 'birthday')\n", "            enhanced_data['age_years'] = self.feature_calculator.calculate_age_years(data, 'date', 'birthday')\n", "            enhanced_data['is_young'] = self.feature_calculator.calculate_age_category_young(data, 'date', 'birthday')\n", "            enhanced_data['is_prime'] = self.feature_calculator.calculate_age_category_prime(data, 'date', 'birthday')\n", "            enhanced_data['is_veteran'] = self.feature_calculator.calculate_age_category_veteran(data, 'date', 'birthday')\n", "            \n", "            # 交互作用特徴量\n", "            if 'total_races' in data.columns:\n", "                enhanced_data['experience_density'] = self.feature_calculator.calculate_experience_density(\n", "                    data, 'total_races', 'date', 'birthday'\n", "                )\n", "            \n", "            if 'win_rate' in data.columns:\n", "                enhanced_data['young_prospect'] = self.feature_calculator.calculate_young_prospect(\n", "                    data, 'date', 'birthday', 'win_rate'\n", "                )\n", "        \n", "        # 欠損値処理\n", "        enhanced_data = enhanced_data.fillna(0)\n", "        \n", "        return enhanced_data\n", "    \n", "    def predict_race(self, race_data):\n", "        \"\"\"\n", "        レースの予測を実行\n", "        \"\"\"\n", "        if self.model is None:\n", "            raise ValueError(\"モデルが設定されていません\")\n", "        \n", "        # 特徴量エンジニアリング\n", "        enhanced_data = self.prepare_features(race_data)\n", "        \n", "        # 特徴量の選択\n", "        available_features = [col for col in self.feature_columns if col in enhanced_data.columns]\n", "        X = enhanced_data[available_features]\n", "        \n", "        # スケーリング\n", "        if self.scaler is not None:\n", "            X_scaled = self.scaler.transform(X)\n", "        else:\n", "            X_scaled = X\n", "        \n", "        # 予測\n", "        predictions = self.model.predict_proba(X_scaled)[:, 1]\n", "        \n", "        # 結果をDataFrameで返す\n", "        result = enhanced_data.copy()\n", "        result['prediction_score'] = predictions\n", "        result['predicted_rank'] = result['prediction_score'].rank(ascending=False, method='dense')\n", "        \n", "        return result.sort_values('predicted_rank')\n", "\n", "# 予測システムの初期化\n", "prediction_system = IntegratedRacePredictionSystem(\n", "    model=model_full,\n", "    scaler=scaler_full,\n", "    feature_columns=feature_columns\n", ")\n", "\n", "print(\"🎯 統合レース予測システムを初期化しました\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# サンプルレースでの予測実行\n", "print(\"🏇 サンプルレースでの予測を実行中...\")\n", "\n", "# サンプルレースデータの作成\n", "sample_race = pd.DataFrame({\n", "    'horse_id': [f'horse_{i:03d}' for i in range(1, 11)],  # 10頭立て\n", "    'date': [datetime(2023, 8, 15)] * 10,  # レース日\n", "    'birthday': [\n", "        datetime(2020, 3, 1), datetime(2019, 4, 15), datetime(2021, 2, 28),  # 若駒\n", "        datetime(2018, 5, 10), datetime(2017, 6, 20), datetime(2019, 1, 5),   # 盛期\n", "        datetime(2016, 7, 8), datetime(2015, 8, 12), datetime(2017, 9, 3),    # 経験豊富\n", "        datetime(2020, 10, 25)  # 若駒\n", "    ],\n", "    'total_races': [5, 15, 2, 25, 40, 12, 50, 60, 30, 3],\n", "    'win_rate': [0.2, 0.13, 0.5, 0.08, 0.05, 0.17, 0.04, 0.03, 0.1, 0.33]\n", "})\n", "\n", "# 予測実行\n", "try:\n", "    prediction_result = prediction_system.predict_race(sample_race)\n", "    \n", "    print(\"✅ 予測完了\")\n", "    print(\"\\n🏆 予測結果 (上位5頭):\")\n", "    \n", "    display_columns = ['horse_id', 'age_years', 'total_races', 'win_rate', 'is_young', \n", "                      'is_prime', 'is_veteran', 'prediction_score', 'predicted_rank']\n", "    \n", "    available_display_columns = [col for col in display_columns if col in prediction_result.columns]\n", "    \n", "    print(prediction_result[available_display_columns].head().round(3))\n", "    \n", "    # 年齢カテゴリ別の予測傾向分析\n", "    if all(col in prediction_result.columns for col in ['is_young', 'is_prime', 'is_veteran', 'prediction_score']):\n", "        print(\"\\n📊 年齢カテゴリ別予測スコア:\")\n", "        \n", "        young_avg = prediction_result[prediction_result['is_young'] == 1]['prediction_score'].mean()\n", "        prime_avg = prediction_result[prediction_result['is_prime'] == 1]['prediction_score'].mean()\n", "        veteran_avg = prediction_result[prediction_result['is_veteran'] == 1]['prediction_score'].mean()\n", "        \n", "        print(f\"  若駒 (3歳以下): {young_avg:.3f}\")\n", "        print(f\"  盛期 (4-6歳): {prime_avg:.3f}\")\n", "        print(f\"  ベテラン (7歳以上): {veteran_avg:.3f}\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ 予測でエラー: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. まとめと今後の展開\n", "\n", "このnotebookでは、リファクタリング後のprocessorsとfeaturesシステムの統合利用方法を実践的に学習しました。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 統合システムの効果まとめ\n", "print(\"📈 統合システムの効果まとめ\")\n", "print(\"=\"*50)\n", "\n", "print(\"\\n🔧 技術的改善:\")\n", "print(\"  ✅ ComprehensiveDataIntegratorでの統一データ処理\")\n", "print(\"  ✅ FeatureEngineeringManagerでの体系的特徴量管理\")\n", "print(\"  ✅ 年齢・生後日数特徴量の高度化\")\n", "print(\"  ✅ 設定ファイルベースの柔軟な制御\")\n", "\n", "print(\"\\n📊 性能改善:\")\n", "print(f\"  ・精度: {accuracy_basic:.3f} → {accuracy_full:.3f} ({accuracy_improvement:+.3f})\")\n", "print(f\"  ・AUC: {auc_basic:.3f} → {auc_full:.3f} ({auc_improvement:+.3f})\")\n", "\n", "print(\"\\n🎯 活用可能な特徴量:\")\n", "if existing_age_columns:\n", "    print(f\"  ・年齢関連: {len(existing_age_columns)}個\")\n", "    for col in existing_age_columns:\n", "        print(f\"    - {col}\")\n", "\n", "print(\"\\n🚀 今後の展開アイデア:\")\n", "print(\"  1. 季節性特徴量の追加\")\n", "print(\"  2. 騎手・調教師特徴量の高度化\")\n", "print(\"  3. レース条件との交互作用特徴量\")\n", "print(\"  4. リアルタイム予測システムの構築\")\n", "print(\"  5. A/Bテストによる特徴量効果の継続評価\")\n", "\n", "print(\"\\n💡 実装のポイント:\")\n", "print(\"  ・設定ファイル(config.yaml)での特徴量制御\")\n", "print(\"  ・計算関数の再利用性\")\n", "print(\"  ・バリデーションルールの活用\")\n", "print(\"  ・段階的な特徴量追加\")\n", "\n", "print(\"\\n🎉 Processors & Features統合システム活用完了！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 最終的な特徴量情報の保存（オプション）\n", "save_results = input(\"特徴量情報を保存しますか？ (y/n): \").lower() == 'y'\n", "\n", "if save_results:\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # 特徴量重要度の保存\n", "    feature_importance.to_csv(f'feature_importance_{timestamp}.csv', index=False)\n", "    print(f\"✅ 特徴量重要度を保存: feature_importance_{timestamp}.csv\")\n", "    \n", "    # 予測結果の保存（サンプルレース）\n", "    if 'prediction_result' in locals():\n", "        prediction_result.to_csv(f'sample_prediction_{timestamp}.csv', index=False)\n", "        print(f\"✅ サンプル予測結果を保存: sample_prediction_{timestamp}.csv\")\n", "    \n", "    # 性能サマリーの保存\n", "    performance_summary = pd.DataFrame({\n", "        'model': ['Basic Features', 'With Age Features'],\n", "        'accuracy': [accuracy_basic, accuracy_full],\n", "        'auc': [auc_basic, auc_full],\n", "        'improvement_accuracy': [0, accuracy_improvement],\n", "        'improvement_auc': [0, auc_improvement]\n", "    })\n", "    performance_summary.to_csv(f'performance_summary_{timestamp}.csv', index=False)\n", "    print(f\"✅ 性能サマリーを保存: performance_summary_{timestamp}.csv\")\n", "    \n", "else:\n", "    print(\"💾 保存をスキップしました\")\n", "\n", "print(\"\\n🏁 Notebook実行完了！\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}