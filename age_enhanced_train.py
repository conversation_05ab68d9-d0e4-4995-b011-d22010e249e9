#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
年齢強化版：馬の生年月日から生後日数を特徴量に追加
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, roc_auc_score
import joblib
import logging
import re
from datetime import datetime
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_japanese_date(date_str):
    """日本語日付を標準形式に変換"""
    if pd.isna(date_str):
        return None
    
    date_str = str(date_str)
    
    # 既に標準形式の場合
    if '/' in date_str or '-' in date_str:
        try:
            return pd.to_datetime(date_str)
        except:
            return None
    
    # 日本語形式の変換（例: 2020年7月25日 → 2020-07-25）
    match = re.search(r'(\d{4})年(\d{1,2})月(\d{1,2})日', date_str)
    if match:
        year, month, day = match.groups()
        try:
            return pd.to_datetime(f"{year}-{month.zfill(2)}-{day.zfill(2)}")
        except:
            return None
    
    return None

def load_and_merge_data():
    """データの読み込みと馬戦績統計・年齢情報の計算"""
    try:
        # データ読み込み
        race_info = pd.read_pickle('output/race_info_2020.pickle')
        race_results = pd.read_pickle('output/race_results_2020.pickle')
        horse_results = pd.read_pickle('output/horse_results_2020.pickle')
        horse_info = pd.read_pickle('output/horse_info_2020.pickle')
        
        # レースデータを結合
        data = pd.merge(race_results, race_info, on='race_id', how='left')
        logger.info(f"レースデータ: {len(data)}件")
        logger.info(f"馬戦績データ: {len(horse_results)}件")
        logger.info(f"馬情報データ: {len(horse_info)}件")
        
        # 日付変換（日本語形式対応）
        logger.info("日付変換中...")
        data['date'] = data['date'].apply(convert_japanese_date)
        horse_results['日付'] = horse_results['日付'].apply(convert_japanese_date)
        
        # horse_infoのインデックスがhorse_idの場合、カラムに変換
        if horse_info.index.name == 'horse_id':
            horse_info = horse_info.reset_index()
        
        # 馬情報の生年月日を変換
        if '生年月日' in horse_info.columns:
            horse_info['birthday'] = horse_info['生年月日'].apply(convert_japanese_date)
            logger.info(f"生年月日変換完了: {horse_info['birthday'].notna().sum()}/{len(horse_info)}件")
        else:
            logger.warning("馬情報に生年月日カラムが見つかりません")
            horse_info['birthday'] = None
        
        # 日付変換できなかった行を除外
        data = data.dropna(subset=['date'])
        horse_results = horse_results.dropna(subset=['日付'])
        
        logger.info(f"日付変換後 - レースデータ: {len(data)}件, 馬戦績: {len(horse_results)}件")
        
        # horse_resultsのインデックスがhorse_idの場合、カラムに変換
        if horse_results.index.name == 'horse_id':
            horse_results = horse_results.reset_index()
        
        # 馬戦績から簡易統計を計算
        logger.info("馬戦績統計を計算中...")
        
        # 着順を数値化
        horse_results['着順_num'] = pd.to_numeric(horse_results['着順'], errors='coerce')
        
        # 馬ごとの集計統計
        horse_stats = horse_results.groupby('horse_id').agg({
            '着順_num': ['count', 'mean', 'std'],  # レース数、平均着順、標準偏差
            '日付': 'max'  # 最終レース日
        }).reset_index()
        
        # カラム名を平坦化
        horse_stats.columns = ['horse_id', 'total_races', 'avg_rank', 'rank_std', 'last_race_date']
        
        # 勝率・3着以内率を計算
        win_stats = horse_results.groupby('horse_id')['着順_num'].agg([
            lambda x: (x == 1).mean(),  # 勝率
            lambda x: (x <= 3).mean(),  # 3着以内率
        ]).reset_index()
        win_stats.columns = ['horse_id', 'win_rate', 'top3_rate']
        
        # 統計を結合
        horse_stats = pd.merge(horse_stats, win_stats, on='horse_id', how='left')
        
        # 馬情報も結合（生年月日含む）
        enhanced_data = pd.merge(data, horse_stats, on='horse_id', how='left')
        enhanced_data = pd.merge(enhanced_data, horse_info[['horse_id', 'birthday']], on='horse_id', how='left')
        
        # 前走からの日数を計算
        enhanced_data['days_since_last_race'] = (
            enhanced_data['date'] - enhanced_data['last_race_date']
        ).dt.days
        
        # 生後日数を計算
        logger.info("生後日数を計算中...")
        enhanced_data['days_old'] = (
            enhanced_data['date'] - enhanced_data['birthday']
        ).dt.days
        
        # 生年月日から年齢関連特徴量を計算
        enhanced_data['age_years'] = enhanced_data['days_old'] / 365.25  # より正確な年齢
        enhanced_data['age_months'] = enhanced_data['days_old'] / 30.44  # 月齢
        
        # 年齢カテゴリ
        enhanced_data['is_young'] = (enhanced_data['age_years'] <= 3).astype(int)  # 若駒
        enhanced_data['is_prime'] = ((enhanced_data['age_years'] > 3) & (enhanced_data['age_years'] <= 6)).astype(int)  # 盛期
        enhanced_data['is_veteran'] = (enhanced_data['age_years'] > 6).astype(int)  # ベテラン
        
        # 統計値の欠損値を埋める
        enhanced_data['total_races'] = enhanced_data['total_races'].fillna(0)
        enhanced_data['avg_rank'] = enhanced_data['avg_rank'].fillna(10)
        enhanced_data['win_rate'] = enhanced_data['win_rate'].fillna(0)
        enhanced_data['top3_rate'] = enhanced_data['top3_rate'].fillna(0)
        enhanced_data['rank_std'] = enhanced_data['rank_std'].fillna(3)
        enhanced_data['days_since_last_race'] = enhanced_data['days_since_last_race'].fillna(365)
        
        # 年齢関連の欠損値処理
        enhanced_data['days_old'] = enhanced_data['days_old'].fillna(enhanced_data['days_old'].median())
        enhanced_data['age_years'] = enhanced_data['age_years'].fillna(enhanced_data['age_years'].median())
        enhanced_data['age_months'] = enhanced_data['age_months'].fillna(enhanced_data['age_months'].median())
        enhanced_data['is_young'] = enhanced_data['is_young'].fillna(0)
        enhanced_data['is_prime'] = enhanced_data['is_prime'].fillna(1)  # デフォルトは盛期
        enhanced_data['is_veteran'] = enhanced_data['is_veteran'].fillna(0)
        
        logger.info("馬戦績・年齢統計計算完了")
        logger.info(f"平均レース数: {enhanced_data['total_races'].mean():.1f}")
        logger.info(f"平均勝率: {enhanced_data['win_rate'].mean():.3f}")
        logger.info(f"平均3着以内率: {enhanced_data['top3_rate'].mean():.3f}")
        logger.info(f"平均生後日数: {enhanced_data['days_old'].mean():.0f}日")
        logger.info(f"平均年齢: {enhanced_data['age_years'].mean():.1f}歳")
        
        return enhanced_data
        
    except Exception as e:
        logger.error(f"データ処理エラー: {e}")
        raise

def prepare_features(data):
    """特徴量準備（過去戦績・詳細年齢情報込み）"""
    logger.info("特徴量準備開始（詳細年齢情報込み）")
    
    data = data.copy()
    
    # ターゲット変数
    data['着順'] = pd.to_numeric(data['着順'], errors='coerce')
    data = data.dropna(subset=['着順'])
    data['target'] = (data['着順'] <= 3).astype(int)
    
    # 基本特徴量
    feature_cols = []
    
    # 数値特徴量
    numeric_cols = ['course_len', '枠番', '馬番', '斤量']
    for col in numeric_cols:
        if col in data.columns:
            data[col] = pd.to_numeric(data[col], errors='coerce')
            feature_cols.append(col)
    
    # 過去戦績特徴量
    past_cols = ['total_races', 'avg_rank', 'win_rate', 'top3_rate', 'rank_std', 'days_since_last_race']
    for col in past_cols:
        if col in data.columns:
            feature_cols.append(col)
    
    # 年齢関連特徴量（生後日数ベース）
    age_cols = ['days_old', 'age_years', 'age_months', 'is_young', 'is_prime', 'is_veteran']
    for col in age_cols:
        if col in data.columns:
            feature_cols.append(col)
    
    # カテゴリカル特徴量
    categorical_cols = ['race_type', 'ground_state', 'weather', 'track_direction']
    for col in categorical_cols:
        if col in data.columns:
            le = LabelEncoder()
            data[f'{col}_encoded'] = le.fit_transform(data[col].astype(str))
            feature_cols.append(f'{col}_encoded')
    
    # 性別（性齢から抽出 - ただし年齢は生後日数を使用）
    if '性齢' in data.columns:
        data['性別_牡'] = data['性齢'].str.contains('牡', na=False).astype(int)
        data['性別_牝'] = data['性齢'].str.contains('牝', na=False).astype(int)
        data['性別_セ'] = data['性齢'].str.contains('セ', na=False).astype(int)
        feature_cols.extend(['性別_牡', '性別_牝', '性別_セ'])
    
    # 距離カテゴリ
    if 'course_len' in data.columns:
        data['距離_短距離'] = (data['course_len'] <= 1400).astype(int)
        data['距離_マイル'] = ((data['course_len'] > 1400) & (data['course_len'] <= 1800)).astype(int)
        data['距離_中距離'] = ((data['course_len'] > 1800) & (data['course_len'] <= 2200)).astype(int)
        data['距離_長距離'] = (data['course_len'] > 2200).astype(int)
        feature_cols.extend(['距離_短距離', '距離_マイル', '距離_中距離', '距離_長距離'])
    
    # 過去戦績からの派生特徴量
    if 'total_races' in data.columns and 'win_rate' in data.columns:
        data['経験豊富'] = (data['total_races'] >= 5).astype(int)
        data['好調'] = (data['win_rate'] >= 0.1).astype(int)
        data['連続出走'] = (data['days_since_last_race'] <= 60).astype(int)
        data['ベテラン_戦績'] = (data['total_races'] >= 20).astype(int)
        data['安定'] = (data['rank_std'] <= 2.0).astype(int)
        feature_cols.extend(['経験豊富', '好調', '連続出走', 'ベテラン_戦績', '安定'])
    
    # 年齢と戦績の相互作用特徴量
    if 'age_years' in data.columns and 'total_races' in data.columns:
        data['経験密度'] = data['total_races'] / (data['age_years'] + 1)  # 年齢に対するレース経験の密度
        data['若手有望'] = ((data['age_years'] <= 4) & (data['win_rate'] >= 0.1)).astype(int)
        data['円熟期'] = ((data['age_years'] >= 4) & (data['age_years'] <= 7) & (data['total_races'] >= 10)).astype(int)
        feature_cols.extend(['経験密度', '若手有望', '円熟期'])
    
    # 欠損値処理
    for col in feature_cols:
        if col in data.columns:
            data[col] = data[col].fillna(0)
    
    available_features = [col for col in feature_cols if col in data.columns]
    
    X = data[available_features]
    y = data['target']
    
    logger.info(f"総特徴量数: {len(available_features)}")
    logger.info(f"過去戦績特徴量: {[col for col in available_features if col in past_cols or col in ['経験豊富', '好調', '連続出走', 'ベテラン_戦績', '安定']]}")
    logger.info(f"年齢関連特徴量: {[col for col in available_features if col in age_cols or col in ['若手有望', '円熟期', '経験密度']]}")
    logger.info(f"データ形状: X={X.shape}, y={y.shape}")
    logger.info(f"正例率: {y.mean():.3f}")
    
    return X, y, available_features

def train_and_evaluate(X, y, features):
    """モデル学習と評価"""
    logger.info("モデル学習開始")
    
    # データ分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 正規化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # LightGBM学習
    lgb_train = lgb.Dataset(X_train_scaled, label=y_train)
    lgb_valid = lgb.Dataset(X_test_scaled, label=y_test, reference=lgb_train)
    
    params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': 0
    }
    
    model = lgb.train(
        params, lgb_train, valid_sets=[lgb_valid],
        num_boost_round=200,
        callbacks=[lgb.early_stopping(stopping_rounds=20), lgb.log_evaluation(0)]
    )
    
    # 予測・評価
    y_proba = model.predict(X_test_scaled)
    y_pred = (y_proba > 0.5).astype(int)
    
    accuracy = accuracy_score(y_test, y_pred)
    auc = roc_auc_score(y_test, y_proba)
    
    # 上位予測適中率
    def precision_at_k(y_true, y_proba_arr, k):
        threshold_idx = int(len(y_proba_arr) * k)
        if threshold_idx == 0:
            return 0.0
        top_k_indices = np.argsort(y_proba_arr)[-threshold_idx:]
        return y_true.iloc[top_k_indices].mean()
    
    precision_10 = precision_at_k(y_test, y_proba, 0.1)
    precision_20 = precision_at_k(y_test, y_proba, 0.2)
    
    # 特徴量重要度
    importance = model.feature_importance(importance_type='gain')
    feature_importance = list(zip(features, importance))
    feature_importance.sort(key=lambda x: x[1], reverse=True)
    
    results = {
        'model': model, 'scaler': scaler, 'features': features,
        'accuracy': accuracy, 'auc': auc,
        'precision_at_10': precision_10, 'precision_at_20': precision_20,
        'train_size': len(X_train), 'test_size': len(X_test),
        'feature_importance': feature_importance
    }
    
    return results

def save_model(results):
    """モデル保存"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    models_dir = Path('models')
    models_dir.mkdir(exist_ok=True)
    
    model_path = models_dir / f"age_enhanced_model_{timestamp}.pkl"
    scaler_path = models_dir / f"age_enhanced_scaler_{timestamp}.pkl"
    features_path = models_dir / f"age_enhanced_features_{timestamp}.pkl"
    
    joblib.dump(results['model'], model_path)
    joblib.dump(results['scaler'], scaler_path)
    joblib.dump(results['features'], features_path)
    
    return {
        'model': str(model_path),
        'scaler': str(scaler_path), 
        'features': str(features_path)
    }

def main():
    """メイン実行"""
    try:
        # データ読み込み・統計計算
        enhanced_data = load_and_merge_data()
        
        # 特徴量準備
        X, y, features = prepare_features(enhanced_data)
        
        # 学習・評価
        results = train_and_evaluate(X, y, features)
        
        # 保存
        saved_paths = save_model(results)
        
        # 結果表示
        print("\n" + "="*70)
        print("🐎 競馬AI学習完了（年齢詳細化版）!")
        print("="*70)
        print(f"精度: {results['accuracy']:.4f}")
        print(f"AUC: {results['auc']:.4f}")
        print(f"適中率@10%: {results['precision_at_10']:.4f}")
        print(f"適中率@20%: {results['precision_at_20']:.4f}")
        print(f"学習データ: {results['train_size']:,}件")
        print(f"テストデータ: {results['test_size']:,}件")
        print(f"総特徴量数: {len(features)}個")
        
        print("\n🔝 特徴量重要度 Top 10:")
        for i, (feat, imp) in enumerate(results['feature_importance'][:10]):
            if feat in ['total_races', 'avg_rank', 'win_rate', 'top3_rate', 'rank_std', 'days_since_last_race', '経験豊富', '好調', '連続出走', 'ベテラン_戦績', '安定']:
                category = "🐎過去戦績"
            elif feat in ['days_old', 'age_years', 'age_months', 'is_young', 'is_prime', 'is_veteran', '若手有望', '円熟期', '経験密度']:
                category = "📅年齢詳細"
            else:
                category = "📊基本情報"
            print(f"{i+1:2d}. {feat}: {imp:.1f} {category}")
        
        print("\n📁 保存ファイル:")
        for key, path in saved_paths.items():
            print(f"  {key}: {path}")
        
        # 従来モデルとの比較
        print("\n📊 モデル性能比較:")
        print("  基本モデル（過去戦績なし）:")
        print("    - 精度: 78.3%, AUC: 57.2%, 適中率@10%: 30.9%")
        print("  強化モデル（過去戦績あり）:")
        print("    - 精度: 79.4%, AUC: 75.7%, 適中率@10%: 55.0%")
        print(f"  年齢詳細版（生後日数ベース）:")
        print(f"    - 精度: {results['accuracy']:.1%}, AUC: {results['auc']:.1%}, 適中率@10%: {results['precision_at_10']:.1%}")
        
        # 改善効果
        improvement_auc = (results['auc'] - 0.757) / 0.757 * 100 if results['auc'] > 0.757 else (results['auc'] - 0.757) / 0.757 * 100
        improvement_precision = (results['precision_at_10'] - 0.55) / 0.55 * 100 if results['precision_at_10'] > 0.55 else (results['precision_at_10'] - 0.55) / 0.55 * 100
        
        print(f"  対強化版改善: AUC {improvement_auc:+.1f}%, 適中率@10% {improvement_precision:+.1f}%")
        
        logger.info("学習完了")
        return results
        
    except Exception as e:
        logger.error(f"エラー: {e}")
        raise

if __name__ == "__main__":
    main()