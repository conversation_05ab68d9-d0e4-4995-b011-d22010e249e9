#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
競馬AI予測システム - モデル学習スクリプト

既存のデータを使用してLightGBMモデルを学習し、
予測システムで使用可能な形式で保存します。
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, precision_score, recall_score
import joblib
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Tuple, Dict, Any
import warnings
import yaml
from dataclasses import dataclass, field
warnings.filterwarnings('ignore')

# プロジェクトモジュールのインポート
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
from core.features.manager import FeatureEngineeringManager
from core.utils.constants import LocalPaths

@dataclass
class TrainingConfig:
    """学習設定クラス"""
    training_years: list = field(default_factory=lambda: ["2020"])
    test_size: float = 0.2
    random_state: int = 42
    output_dir: str = "models"
    
    # LightGBMパラメータ
    lgb_params: dict = field(default_factory=lambda: {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': 0
    })
    
    # 特徴量設定
    leakage_risk_columns: list = field(default_factory=lambda: [
        '人気', '単勝', 'オッズ', '着差', 'タイム', 'ﾀｲﾑ指数', '通過', '上り'
    ])
    safe_categorical_cols: list = field(default_factory=lambda: [
        '場名', '距離', 'コース', '馬場状態', '天気', '性', '調教師', '騎手'
    ])
    
    @classmethod
    def from_yaml(cls, config_path: str) -> 'TrainingConfig':
        """YAMLファイルから設定を読み込み"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            return cls(**config_dict)
        except Exception as e:
            logger.warning(f"設定ファイル読み込み失敗: {e}, デフォルト設定を使用")
            return cls()

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('model_training.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class HorseRaceModelTrainer:
    """競馬予測モデルの学習クラス"""
    
    def __init__(self, config: TrainingConfig = None):
        """
        初期化
        
        Parameters
        ----------
        config : TrainingConfig
            学習設定
        """
        self.config = config or TrainingConfig()
        self.output_dir = Path(self.config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.model = None
        self.scaler = None
        self.label_encoder = None
        self.feature_columns = None
        
        # データ統合システム
        try:
            self.integrator = ComprehensiveDataIntegrator(
                config={'pickle_base_dir': 'output'}  # outputディレクトリを使用
            )
        except Exception as e:
            logger.warning(f"ComprehensiveDataIntegrator初期化失敗: {e}, フォールバックモードを使用")
            self.integrator = None
        
        try:
            self.feature_manager = FeatureEngineeringManager()
        except Exception as e:
            logger.warning(f"FeatureEngineeringManager初期化失敗: {e}")
            self.feature_manager = None
        
        logger.info(f"モデル訓練システムを初期化しました - 出力ディレクトリ: {self.output_dir}")
    
    def load_training_data(self, years: list = ["2020"]) -> pd.DataFrame:
        """
        学習用データの読み込み
        包括的データ統合システムを使用して、馬の基本情報・過去戦績・コーナー順位を含む完全なデータを取得
        
        Parameters
        ----------
        years : list
            学習に使用する年のリスト
            
        Returns
        -------
        pd.DataFrame
            統合された学習用データ
        """
        logger.info(f"包括的学習データを読み込み中: {years}")
        
        # 包括的データ統合システムを使用
        comprehensive_data = pd.DataFrame()
        if self.integrator:
            try:
                comprehensive_data = self.integrator.generate_comprehensive_table(
                    years=years,
                    include_race_info=True,
                    include_horse_info=True,
                    include_past_performance=True,
                    performance_window_races=[3, 5, 10],  # 直近3、5、10戦の統計
                    use_pickle_source=True,  # pickleファイルから効率的に読み込み
                    parallel=True,
                    max_workers=4
                )
            except Exception as e:
                logger.warning(f"包括的データ統合でエラー: {e}")
                comprehensive_data = pd.DataFrame()
        
        if comprehensive_data.empty:
            # フォールバック: 従来の方法でデータ読み込み
            logger.warning("包括的データが空のため、従来の方法でデータを読み込みます")
            return self._load_fallback_data(years)
        
        logger.info(f"包括的統合データ: {len(comprehensive_data)}件, {len(comprehensive_data.columns)}カラム")
        
        # 学習に必要な基本カラムを確認
        required_columns = ['着順', 'race_id', 'horse_id']
        missing_columns = [col for col in required_columns if col not in comprehensive_data.columns]
        
        if missing_columns:
            logger.warning(f"必要なカラムが不足: {missing_columns}")
            return self._load_fallback_data(years)
        
        return comprehensive_data
    
    def _load_fallback_data(self, years: list) -> pd.DataFrame:
        """
        フォールバック用のデータ読み込み（従来の方法）
        """
        logger.info("フォールバック方式でデータを読み込み中...")
        
        all_data = []
        
        for year in years:
            try:
                logger.info(f"{year}年のデータを処理中...")
                
                # pickleファイルから直接読み込み
                race_info_path = f"output/race_info_{year}.pickle"
                race_results_path = f"output/race_results_{year}.pickle"
                
                if os.path.exists(race_info_path) and os.path.exists(race_results_path):
                    logger.info(f"pickleファイルから読み込み: {race_info_path}, {race_results_path}")
                    
                    race_info = pd.read_pickle(race_info_path)
                    race_results = pd.read_pickle(race_results_path)
                    
                    # データを結合
                    if not race_info.empty and not race_results.empty:
                        # race_idで結合
                        data = pd.merge(race_results, race_info, on='race_id', how='left')
                        data['year'] = year
                        all_data.append(data)
                        logger.info(f"{year}年のデータ: {len(data)}件")
                    else:
                        logger.warning(f"{year}年のデータが空です")
                else:
                    logger.warning(f"{year}年のpickleファイルが見つかりません")
                    
            except Exception as e:
                logger.error(f"{year}年のデータ読み込みでエラー: {e}")
                continue
        
        if not all_data:
            raise ValueError("学習用データが見つかりません")
        
        # データを結合
        combined_data = pd.concat(all_data, ignore_index=True)
        logger.info(f"フォールバック統合データ: {len(combined_data)}件")
        
        return combined_data
    
    def prepare_features(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
        """
        特徴量の準備と前処理
        包括的データから豊富な特徴量を抽出・エンジニアリング
        
        Parameters
        ----------
        data : pd.DataFrame
            包括的統合データ
            
        Returns
        -------
        Tuple[pd.DataFrame, pd.Series]
            特徴量データ, ターゲットデータ
        """
        logger.info("包括的データから特徴量を準備中...")
        
        # 必要なカラムの確認
        required_columns = ['着順', 'race_id', 'horse_id']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            logger.error(f"必要なカラムが不足: {missing_columns}")
            logger.info(f"利用可能なカラム: {list(data.columns)}")
            raise ValueError(f"必要なカラムが不足: {missing_columns}")
        
        # 欠損値の処理
        data_clean = data.dropna(subset=['着順'])
        
        # 着順を数値に変換
        try:
            data_clean['着順'] = pd.to_numeric(data_clean['着順'], errors='coerce')
            data_clean = data_clean.dropna(subset=['着順'])
        except Exception as e:
            logger.error(f"着順の数値変換でエラー: {e}")
            raise
        
        # 分類問題用のターゲット作成（1-3着かそれ以外か）
        data_clean['target'] = (data_clean['着順'] <= 3).astype(int)
        
        # 包括的特徴量の抽出
        feature_data = self._extract_comprehensive_features(data_clean)
        
        # 数値カラムを特徴量として使用
        numeric_columns = feature_data.select_dtypes(include=[np.number]).columns
        numeric_columns = [col for col in numeric_columns if col not in ['着順', 'target', 'year']]
        
        # データ品質チェック
        if not self._validate_data_quality(feature_data):
            logger.warning("データ品質チェックで問題が検出されました")
        
        # リーケージリスクのある特徴量を除外
        leakage_risk_columns = self.config.leakage_risk_columns
        for col in leakage_risk_columns:
            if col in feature_data.columns:
                logger.warning(f"データリーケージリスクのため {col} を除外します")
                feature_data = feature_data.drop(columns=[col])
        
        # カテゴリカル変数のエンコーディング（リーケージリスクのない特徴量のみ）
        categorical_columns = []
        safe_categorical_cols = self.config.safe_categorical_cols
        for col in safe_categorical_cols:
            if col in feature_data.columns:
                categorical_columns.append(col)
                le = LabelEncoder()
                try:
                    feature_data[f'{col}_encoded'] = le.fit_transform(feature_data[col].astype(str))
                    numeric_columns = list(numeric_columns) + [f'{col}_encoded']
                except Exception as e:
                    logger.warning(f"{col}のエンコーディングでエラー: {e}")
        
        # 特徴量選択
        self.feature_columns = [col for col in numeric_columns if col in feature_data.columns]
        
        if not self.feature_columns:
            logger.error("使用可能な特徴量が見つかりません")
            raise ValueError("使用可能な特徴量が見つかりません")
        
        logger.info(f"使用する特徴量: {len(self.feature_columns)}個")
        logger.info(f"特徴量タイプ別内訳:")
        self._log_feature_breakdown(self.feature_columns)
        
        X = feature_data[self.feature_columns].fillna(0)
        y = feature_data['target']
        
        logger.info(f"特徴量データ形状: {X.shape}")
        logger.info(f"ターゲットデータ形状: {y.shape}")
        logger.info(f"正例率: {y.mean():.3f}")
        
        return X, y
    
    def _extract_comprehensive_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        包括的特徴量の抽出
        """
        feature_data = data.copy()
        
        # 手動で追加の特徴量を作成
        try:
            # 年齢特徴量の抽出（性齢カラムから）
            if '性齢' in feature_data.columns:
                feature_data['年齢'] = feature_data['性齢'].str.extract(r'(\d+)').astype(float)
                feature_data['性別_牡'] = (feature_data['性齢'].str.contains('牡', na=False)).astype(int)
                feature_data['性別_牝'] = (feature_data['性齢'].str.contains('牝', na=False)).astype(int)
                feature_data['性別_セ'] = (feature_data['性齢'].str.contains('セ', na=False)).astype(int)
            
            # 距離カテゴリ特徴量
            if '距離' in feature_data.columns:
                feature_data['距離_短距離'] = (feature_data['距離'] <= 1400).astype(int)
                feature_data['距離_マイル'] = ((feature_data['距離'] > 1400) & (feature_data['距離'] <= 1800)).astype(int)
                feature_data['距離_中距離'] = ((feature_data['距離'] > 1800) & (feature_data['距離'] <= 2200)).astype(int)
                feature_data['距離_長距離'] = (feature_data['距離'] > 2200).astype(int)
            
            # 人気度・オッズ特徴量は削除（データリーケージリスクのため）
            # これらの情報はレース直前に決まるため、事前予測では使用不可
            logger.info("人気・オッズ特徴量はデータリーケージ防止のため除外されました")
            
            # 重量特徴量
            if '斤量' in feature_data.columns:
                feature_data['斤量_重い'] = (feature_data['斤量'] >= 58.0).astype(int)
                feature_data['斤量_軽い'] = (feature_data['斤量'] <= 54.0).astype(int)
            
            logger.info(f"手動特徴量エンジニアリング適用後: {len(feature_data.columns)}カラム")
            return feature_data
        except Exception as e:
            logger.warning(f"特徴量エンジニアリング中にエラー: {e}")
            return feature_data
    
    def _validate_data_quality(self, data: pd.DataFrame) -> bool:
        """
        データ品質チェック
        
        Parameters
        ----------
        data : pd.DataFrame
            チェック対象データ
            
        Returns
        -------
        bool
            品質チェック結果
        """
        try:
            # 欠損値チェック
            null_ratio = data.isnull().sum() / len(data)
            high_null_cols = null_ratio[null_ratio > 0.5].index.tolist()
            if high_null_cols:
                logger.warning(f"欠損値50%超過カラム: {high_null_cols}")
            
            # 重複チェック
            duplicate_ratio = data.duplicated().sum() / len(data)
            if duplicate_ratio > 0.1:
                logger.warning(f"重複データ率: {duplicate_ratio:.2%}")
            
            # ターゲット分布チェック
            if '着順' in data.columns:
                target_dist = data['着順'].value_counts().head()
                logger.info(f"着順分布: \n{target_dist}")
            
            # 数値カラムの異常値チェック
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            for col in numeric_cols[:5]:  # 最初の5カラムのみチェック
                q99 = data[col].quantile(0.99)
                q1 = data[col].quantile(0.01)
                outlier_ratio = ((data[col] > q99) | (data[col] < q1)).sum() / len(data)
                if outlier_ratio > 0.05:
                    logger.info(f"{col}: 異常値率 {outlier_ratio:.2%}")
            
            return True
            
        except Exception as e:
            logger.error(f"データ品質チェック中にエラー: {e}")
            return False
    
    def _log_feature_breakdown(self, feature_columns: list):
        """
        特徴量の内訳をログ出力
        """
        if not feature_columns:
            return
            
        breakdown = {
            '基本レース情報': [col for col in feature_columns if any(keyword in col for keyword in ['距離', 'コース', '場名', '天気', '馬場'])],
            '馬基本情報': [col for col in feature_columns if any(keyword in col for keyword in ['年齢', '性', '体重', '斤量'])],
            '過去成績統計': [col for col in feature_columns if any(keyword in col for keyword in ['last_', 'mean_', 'std_', 'min_', 'max_'])],
            '人気・オッズ': [col for col in feature_columns if any(keyword in col for keyword in ['人気', 'オッズ'])],
            'その他': []
        }
        
        # 分類されていない特徴量をその他に振り分け
        classified = sum(breakdown.values(), [])
        breakdown['その他'] = [col for col in feature_columns if col not in classified]
        
        for category, cols in breakdown.items():
            if cols:
                logger.info(f"  {category}: {len(cols)}個")
                logger.debug(f"    {cols[:5]}{'...' if len(cols) > 5 else ''}")  # 最初の5個のみ表示
    
    def train_model(self, X: pd.DataFrame, y: pd.Series, data: pd.DataFrame) -> Dict[str, Any]:
        """
        モデルの学習（時系列を考慮したデータ分割）
        
        Parameters
        ----------
        X : pd.DataFrame
            特徴量データ
        y : pd.Series
            ターゲットデータ
        data : pd.DataFrame
            元データ（日付情報含む）
            
        Returns
        -------
        Dict[str, Any]
            学習結果の情報
        """
        logger.info("モデル学習を開始...")
        
        # 時系列を考慮したデータ分割
        if 'date' in data.columns:
            # 日付でソート
            sorted_data = data.copy()
            sorted_data['date'] = pd.to_datetime(sorted_data['date'])
            sorted_data = sorted_data.sort_values('date')
            
            # 時系列分割（最後の20%をテストデータとする）
            split_idx = int(len(sorted_data) * 0.8)
            train_idx = sorted_data.index[:split_idx]
            test_idx = sorted_data.index[split_idx:]
            
            X_train, X_test = X.loc[train_idx], X.loc[test_idx]
            y_train, y_test = y.loc[train_idx], y.loc[test_idx]
            
            logger.info(f"時系列分割 - 学習期間: {sorted_data.loc[train_idx, 'date'].min()} ~ {sorted_data.loc[train_idx, 'date'].max()}")
            logger.info(f"時系列分割 - テスト期間: {sorted_data.loc[test_idx, 'date'].min()} ~ {sorted_data.loc[test_idx, 'date'].max()}")
        else:
            logger.warning("日付情報がないため、ランダム分割を使用します（データリーケージの可能性があります）")
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
        
        logger.info(f"学習データ: {X_train.shape}, テストデータ: {X_test.shape}")
        
        # データの正規化
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # LightGBMモデルの学習
        lgb_train = lgb.Dataset(X_train_scaled, label=y_train)
        lgb_valid = lgb.Dataset(X_test_scaled, label=y_test, reference=lgb_train)
        
        params = self.config.lgb_params
        
        self.model = lgb.train(
            params,
            lgb_train,
            valid_sets=[lgb_valid],
            num_boost_round=100,
            callbacks=[lgb.early_stopping(stopping_rounds=10), lgb.log_evaluation(0)]
        )
        
        # 予測と評価
        y_proba = self.model.predict(X_test_scaled)
        y_pred = (y_proba > 0.5).astype(int)
        
        # 競馬特化の評価指標
        results = self._evaluate_horse_racing_model(y_test.values, y_pred, y_proba)
        results.update({
            'train_size': len(X_train),
            'test_size': len(X_test),
            'feature_count': len(self.feature_columns) if self.feature_columns else 0
        })
        
        # ログ出力
        logger.info(f"=== モデル評価結果 ===")
        logger.info(f"精度: {results['accuracy']:.4f}")
        logger.info(f"AUC: {results['auc']:.4f}")
        logger.info(f"適中率@10%: {results['precision_at_10']:.4f}")
        logger.info(f"適中率@20%: {results['precision_at_20']:.4f}")
        logger.info(f"分類レポート:\n{results['classification_report']}")
        
        return results
    
    def _evaluate_horse_racing_model(self, y_true: np.ndarray, y_pred: np.ndarray, y_proba: np.ndarray) -> Dict[str, Any]:
        """
        競馬予測に特化した評価指標の計算
        
        Parameters
        ----------
        y_true : np.ndarray
            実際のラベル
        y_pred : np.ndarray
            予測ラベル
        y_proba : np.ndarray
            予測確率
            
        Returns
        -------
        Dict[str, Any]
            評価指標辞書
        """
        # 基本的な分類指標
        accuracy = accuracy_score(y_true, y_pred)
        auc = roc_auc_score(y_true, y_proba)
        precision = precision_score(y_true, y_pred)
        recall = recall_score(y_true, y_pred)
        
        # 競馬特化指標：上位N%の適中率
        def precision_at_k(y_true_arr, y_proba_arr, k):
            """上位k%の予測での適中率"""
            if len(y_true_arr) == 0:
                return 0.0
            threshold_idx = int(len(y_proba_arr) * k)
            if threshold_idx == 0:
                return 0.0
            top_k_indices = np.argsort(y_proba_arr)[-threshold_idx:]
            return y_true_arr[top_k_indices].mean()
        
        precision_at_10 = precision_at_k(y_true, y_proba, 0.1)
        precision_at_20 = precision_at_k(y_true, y_proba, 0.2)
        
        # 投資収益率シミュレーション（簡易版）
        def simulate_betting_return(y_true_arr, y_proba_arr, threshold=0.6):
            """単勝投資の収益率シミュレーション"""
            # 閾値以上の確率の馬に投資
            bet_indices = y_proba_arr >= threshold
            if not bet_indices.any():
                return 0.0
            
            # 的中数と投資数
            hits = y_true_arr[bet_indices].sum()
            bets = bet_indices.sum()
            
            # 簡易的な収益計算（平均配当を3倍と仮定）
            # 実際は各レースのオッズデータが必要
            avg_payout = 3.0  # 3着以内平均配当倍率
            return (hits * avg_payout - bets) / bets if bets > 0 else 0.0
        
        betting_return = simulate_betting_return(y_true, y_proba)
        
        return {
            'accuracy': accuracy,
            'auc': auc,
            'precision': precision,
            'recall': recall,
            'precision_at_10': precision_at_10,
            'precision_at_20': precision_at_20,
            'betting_return': betting_return,
            'classification_report': classification_report(y_true, y_pred),
            'confusion_matrix': confusion_matrix(y_true, y_pred)
        }
    
    def save_model(self) -> Dict[str, str]:
        """
        学習済みモデルの保存
        
        Returns
        -------
        Dict[str, str]
            保存されたファイルのパス
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # モデル保存
        model_path = self.output_dir / f"lgb_model_{timestamp}.pkl"
        joblib.dump(self.model, model_path)
        
        # スケーラー保存
        scaler_path = self.output_dir / f"scaler_{timestamp}.pkl"
        joblib.dump(self.scaler, scaler_path)
        
        # 特徴量リスト保存
        features_path = self.output_dir / f"features_{timestamp}.pkl"
        if self.feature_columns is not None:
            joblib.dump(self.feature_columns, features_path)
        else:
            logger.warning("feature_columnsがNoneのため、特徴量リストの保存をスキップします")
        
        paths = {
            'model': str(model_path),
            'scaler': str(scaler_path),
            'features': str(features_path)
        }
        
        logger.info(f"モデルを保存しました:")
        for key, path in paths.items():
            logger.info(f"  {key}: {path}")
        
        return paths

def main(config_path: str | None = None):
    """メイン関数"""
    try:
        # 設定読み込み
        config = TrainingConfig.from_yaml(config_path) if config_path else TrainingConfig()
        logger.info(f"使用設定: 学習年度={config.training_years}, 出力先={config.output_dir}")
        
        # 学習システムの初期化
        trainer = HorseRaceModelTrainer(config)
        
        # データの読み込み
        data = trainer.load_training_data(years=config.training_years)
        
        # 特徴量の準備
        X, y = trainer.prepare_features(data)
        
        # モデル学習（元データも渡す）
        results = trainer.train_model(X, y, data)
        
        # モデル保存
        saved_paths = trainer.save_model()
        
        # 結果の表示
        logger.info("=== 学習完了 ===")
        logger.info(f"精度: {results['accuracy']:.4f}")
        logger.info(f"学習データ数: {results['train_size']}")
        logger.info(f"テストデータ数: {results['test_size']}")
        logger.info(f"特徴量数: {results['feature_count']}")
        
        print("\n" + "="*50)
        print("競馬AI予測モデル学習完了！")
        print("="*50)
        print(f"モデル精度: {results['accuracy']:.4f}")
        print(f"保存されたファイル:")
        for key, path in saved_paths.items():
            print(f"  {key}: {path}")
        
        return saved_paths
        
    except Exception as e:
        logger.error(f"学習中にエラーが発生: {e}")
        raise

if __name__ == "__main__":
    main()