# データ統合システム ガイド

**競馬AI予測システム - 包括的データ統合詳細ガイド**

本ガイドでは、競馬予測システムにおけるデータ統合機能の詳細を説明します。レース情報、馬基本情報、過去成績統計、血統情報を統合した機械学習対応データセットを効率的に生成するシステムです。

## 🎯 統合システム概要

### システム特徴

| 特徴 | 説明 | 技術詳細 |
|------|------|----------|
| **包括的統合** | レース・馬・血統・成績の統合 | HTML解析 → DataFrame変換 → 統合処理 |
| **血統情報強化** | 父・母・母父の3世代情報 | netkeiba血統ページ自動解析 |
| **成績統計** | 過去N戦の統計指標 | 平均・標準偏差・勝率・連対率・複勝率 |
| **高速処理** | 並列処理対応 | multiprocessing + tqdm進捗表示 |
| **柔軟な設定** | モジュラー構成 | 含める情報を個別に選択可能 |
| **複数形式** | CSV/Pickle出力 | 用途に応じたファイル形式選択 |

### データ統合フロー
```
1. HTMLデータ読み込み → 2. 構造化データ変換 → 3. データ統合
   ├─ レース情報            ├─ DataFrame化         ├─ レース+結果
   ├─ レース結果            ├─ 型変換・クリーニング   ├─ 馬基本情報追加
   ├─ 馬基本情報            └─ バリデーション       ├─ 血統情報追加
   └─ 馬過去成績                                  └─ 成績統計追加
              ↓                      ↓                     ↓
4. 特徴量エンジニアリング ← 5. 機械学習対応データ ← 6. ファイル出力
   268項目の特徴量            scikit-learn互換       CSV/Pickle形式
```

### 生成される統合データの構造

```
ベースデータ（レース結果）
├── レース情報
│   ├── 天気、馬場状態
│   ├── 距離、コース
│   ├── レースクラス、グレード
│   └── 開催場所、日付
├── 馬基本情報  
│   ├── 血統情報（父馬、母馬、母父）
│   ├── 調教師、馬主
│   ├── 生年月日、年齢
│   └── 産地、生産者
└── 馬過去成績統計
    ├── 直近N戦の成績
    ├── 距離別成績
    ├── 馬場状態別成績
    └── コース別成績
```

## 📦 ファイル構成

- `comprehensive_data_integrator.py`: メインの統合クラス
- `comprehensive_integration_example.py`: 使用例
- `horse_processor.py`: 馬基本情報処理（母父情報追加）
- `data_merger.py`: データ統合処理（母父情報対応）

## 🔧 使用方法

### 1. 基本的な使用方法

```python
from comprehensive_data_integrator import ComprehensiveDataIntegrator

# インテグレーターを作成
integrator = ComprehensiveDataIntegrator()

# 包括的データを生成
comprehensive_df = integrator.generate_comprehensive_table(
    year="2024",
    include_race_info=True,      # レース情報を含める
    include_horse_info=True,     # 馬基本情報（血統含む）を含める
    include_past_performance=True,  # 馬過去成績統計を含める
    performance_window_races=[5, 10],  # 直近5戦と10戦の統計
    parallel=True,
    max_workers=4
)

# ファイルに保存
pickle_path, csv_path = integrator.save_comprehensive_table(
    filename_prefix="comprehensive_2024",
    year="2024"
)
```

### 2. コマンドライン実行

```bash
# 基本的な実行
python comprehensive_data_integrator.py --year 2024 --save

# カスタム設定での実行
python comprehensive_data_integrator.py \
    --year 2024 \
    --performance-windows 3 5 10 20 \
    --workers 8 \
    --save \
    --filename-prefix "detailed_race_data"

# 軽量版（レース情報のみ）
python comprehensive_data_integrator.py \
    --year 2024 \
    --no-horse-info \
    --no-past-performance \
    --save
```

### 3. 使用例の実行

```bash
# 包括的な使用例を実行
python comprehensive_integration_example.py
```

## 📊 生成されるデータの例

### 基本カラム
- `race_id`: レースID
- `horse_id`: 馬ID
- `馬名`: 馬名
- `着順`: 着順
- `騎手`: 騎手名
- `斤量`: 斤量
- `オッズ`: 単勝オッズ
- `人気`: 人気

### レース情報カラム
- `天気`: 天気
- `馬場状態`: 馬場状態
- `距離`: 距離
- `コース`: コース
- `レースクラス`: レースクラス
- `開催場所`: 開催場所

### 馬基本情報カラム（血統含む）
- `father_name`: 父馬名
- `mother_name`: 母馬名
- `mother_father_name`: **母父名**
- `father_id`: 父馬ID
- `mother_id`: 母馬ID
- `mother_father_id`: **母父ID**
- `調教師`: 調教師名
- `馬主`: 馬主名
- `生年月日`: 生年月日

### 過去成績統計カラム
- `着順_mean_last_5`: 直近5戦平均着順
- `着順_std_last_5`: 直近5戦着順標準偏差
- `人気_mean_last_10`: 直近10戦平均人気
- `オッズ_mean_last_5`: 直近5戦平均オッズ
- など

## ⚙️ 設定オプション

### ComprehensiveDataIntegrator.generate_comprehensive_table()

| パラメータ | 型 | デフォルト | 説明 |
|-----------|----|-----------|----|
| `year` | str | None | 処理する年度 |
| `race_id` | str | None | 特定のレースID |
| `include_race_info` | bool | True | レース情報を含めるか |
| `include_horse_info` | bool | True | 馬基本情報を含めるか |
| `include_past_performance` | bool | True | 過去成績統計を含めるか |
| `performance_window_races` | List[int] | [5, 10] | 過去成績の集計対象レース数 |
| `parallel` | bool | True | 並列処理を使用するか |
| `max_workers` | int | None | 並列処理の最大ワーカー数 |

### コマンドライン引数

| 引数 | 説明 |
|------|------|
| `--year` | 処理する年度 |
| `--race-id` | 特定のレースID |
| `--no-race-info` | レース情報を含めない |
| `--no-horse-info` | 馬基本情報を含めない |
| `--no-past-performance` | 過去成績統計を含めない |
| `--performance-windows` | 過去成績の集計対象レース数 |
| `--no-parallel` | 並列処理を使用しない |
| `--workers` | 並列処理の最大ワーカー数 |
| `--save` | 結果をファイルに保存 |
| `--filename-prefix` | 保存ファイル名のプレフィックス |

## 🔍 データ品質と統計

生成されたデータの品質を確認するための機能も提供されています：

```python
# データ概要の取得
summary = integrator.get_data_summary()
print(f"総レコード数: {summary['total_records']}")
print(f"血統情報カバー率: {summary['missing_data_ratio']}")
```

## 📈 パフォーマンス最適化

- **並列処理**: 複数CPUコアを活用した高速処理
- **メモリ効率**: 大量データの分割処理
- **プログレスバー**: tqdmによる進捗表示
- **ファイル制限**: 処理ファイル数の制限でテスト実行可能

## 🚨 注意事項

1. **データサイズ**: 全年度のデータは非常に大きくなる可能性があります
2. **処理時間**: 包括的な統合は時間がかかる場合があります
3. **メモリ使用量**: 大量データ処理時はメモリ使用量にご注意ください
4. **血統情報**: 母父情報は利用可能なデータに依存します

## 🎯 活用例

- **機械学習**: 予測モデルの特徴量として活用
- **統計分析**: 血統と成績の相関分析
- **データ可視化**: 包括的なダッシュボード作成
- **研究**: 競馬データサイエンス研究

## 🔄 今後の拡張予定

- 騎手情報の統合
- 調教情報の追加
- リアルタイムデータ対応
- より詳細な血統分析機能
