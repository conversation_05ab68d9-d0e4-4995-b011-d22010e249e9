#netkeibaから競馬データをスクレイピングするツール

import sys
import logging
from datetime import datetime
import argparse

sys.path.append('.')  # refactored_scrap.pyが同じディレクトリにある場合


# デフォルト設定値
DEFAULT_RACE_FROM_DATE = '2024-01-01'
DEFAULT_RACE_TO_DATE = '2024-01-07' # 例として短期間に
DEFAULT_HORSE_YEAR = 2023
DEFAULT_PED_YEAR = 2023
DEFAULT_RACE_IDS_TO_SCRAPE = None # HTML取得対象のレースID数（Noneは全件取得）
DEFAULT_HORSE_IDS_TO_SCRAPE = None # HTML取得対象の馬ID数（例のため少なく設定）
DEFAULT_PED_IDS_TO_SCRAPE = None # HTML取得対象の血統ID数（例のため少なく設定）

MIN_YEAR = 1900
MAX_YEAR = 2100

# スクリプト固有のロガーは main 関数内で取得する
logger = logging.getLogger(__name__)

class UserInputHandler:
    """日付フォーマットを検証"""
    @staticmethod
    def validate_date_format(date_str: str) -> bool:
        """日付文字列が 'YYYY-MM-DD' 形式か検証する。"""
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False

    @staticmethod
    def get_date_range(prompt_prefix: str, default_from_date: str, default_to_date: str) -> dict:
        """開始日と終了日をユーザーから取得する。"""
        while True:
            from_date_input = input(f"{prompt_prefix} - 開始日 (YYYY-MM-DD, デフォルト: {default_from_date}): ").strip()
            from_date = from_date_input or default_from_date
            if UserInputHandler.validate_date_format(from_date):
                break
            logger.warning("無効な日付フォーマットです。YYYY-MM-DD形式で入力してください。")

        while True:
            to_date_input = input(f"{prompt_prefix} - 終了日 (YYYY-MM-DD, デフォルト: {default_to_date}): ").strip()
            to_date = to_date_input or default_to_date
            if UserInputHandler.validate_date_format(to_date):
                if datetime.strptime(to_date, '%Y-%m-%d') >= datetime.strptime(from_date, '%Y-%m-%d'):
                    break
                logger.warning("終了日は開始日以降である必要があります。")
            else:
                logger.warning("無効な日付フォーマットです。YYYY-MM-DD形式で入力してください。")
        return {'from_date': from_date, 'to_date': to_date}

    @staticmethod
    def get_target_year(prompt_message: str, default_year: int) -> int:
        """対象年をユーザーから取得する。"""
        while True:
            try:
                year_input = input(f"{prompt_message} (YYYY, デフォルト: {default_year}): ").strip()
                year = int(year_input) if year_input else default_year
                if MIN_YEAR <= year <= MAX_YEAR:
                    return year
                logger.warning(f"無効な年です。{MIN_YEAR}から{MAX_YEAR}の間で入力してください。")
            except ValueError:
                logger.warning("無効な入力です。数字で年を入力してください。")

def main():
    # コマンドライン引数の設定
    parser = argparse.ArgumentParser(description="競馬データスクレイピングツール (netkeiba版)")
    parser.add_argument(
        "-d", "--debug",
        action="store_true",
        help="デバッグログを有効にする"
    )
    args = parser.parse_args()

    # --- ロギング設定 ---
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(name)s - [%(funcName)s] %(message)s', # funcName を追加
        handlers=[
            logging.StreamHandler(sys.stdout), # STDERRではなくSTDOUTへ
            logging.FileHandler('netkeiba_scraping_tool.log', encoding='utf-8') # ログファイル名を変更
        ],
        force=True # Python 3.8+
    )

    # 外部ライブラリのログレベル調整
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("selenium.webdriver.remote.remote_connection").setLevel(logging.WARNING)
    logging.getLogger("selenium.webdriver.common.service").setLevel(logging.WARNING)
    logging.getLogger("webdriver_manager").setLevel(logging.WARNING)
    # abslライブラリのロガーも制御下に置く試み (効果がない場合もある)
    # logging.getLogger("absl").setLevel(logging.WARNING)

    # --- カスタムモジュールのインポート ---
    # ロギング設定が完了した後にインポートする
    from core.scrapers.scraper import (
        scrape_netkeiba_race_dates,
        scrape_netkeiba_race_ids,
        scrape_html_race,
        scrape_html_horse,
        scrape_html_ped,
        # scrape_html_horse_with_master # 必要に応じてコメント解除
    )
    from core.processors.race_processor import RaceProcessor

    # このスクリプト用のロガーを再度取得 (basicConfigの影響を受けるため)
    logger = logging.getLogger(__name__)
    logger.info("=== 競馬データスクレイピングツール（netkeiba版） ===")
    if args.debug:
        logger.debug("デバッグモードが有効です。")

    # --- 処理関数を main の内部関数として定義 ---
    def _get_horse_ids_for_year(year, context_name=""):
        """指定年の馬IDを取得する共通関数"""
        # RaceProcessor は main 内でインポートされているのでアクセス可能
        race_processor = RaceProcessor()
        horse_ids = race_processor.extract_horse_ids_from_race_data(year=year)
        if not horse_ids:
            logger.warning(f'{year}年の馬IDが取得できませんでした({context_name})。先にレース情報を処理する必要があるかもしれません。')
            return []
        logger.info(f'取得した馬ID数 ({year}年, {context_name}): {len(horse_ids)}')
        return horse_ids

    def scrape_horse_data(year):
        """馬情報のスクレイピング"""
        logger.info(f"\n--- 馬情報のスクレイピング開始 ({year}年) ---")
        try:
            # 馬IDを取得
            horse_ids = _get_horse_ids_for_year(year, "馬情報") # _get_horse_ids_for_year も main 内関数
            if not horse_ids:
                return

            # 最初の数件の馬IDでHTMLをスクレイピング (例のため)
            if DEFAULT_HORSE_IDS_TO_SCRAPE is None:
                target_horse_ids = horse_ids
            else:
                target_horse_ids = horse_ids[:DEFAULT_HORSE_IDS_TO_SCRAPE]
            if not target_horse_ids:
                logger.warning("スクレイピング対象の馬IDがありません。")
                return

            logger.info(f'HTMLスクレイピング対象の馬ID: {target_horse_ids}')
            # scrape_html_horse などは main 内でインポートされているのでアクセス可能
            successful_ids = scrape_html_horse(target_horse_ids, skip=True)
            logger.info(f'スクレイピング成功した馬HTML数: {len(successful_ids)}')
            if successful_ids:
                logger.info(f"保存された馬ID (一部): {successful_ids[:5]}")

        except Exception as e:
            logger.error(f'馬情報スクレイピング中にエラーが発生しました: {e}', exc_info=True)

    def scrape_pedigree_data(year):
        """血統情報のスクレイピング"""
        logger.info(f"\n--- 血統情報のスクレイピング開始 ({year}年) ---")
        try:
            horse_ids = _get_horse_ids_for_year(year, "血統情報") # _get_horse_ids_for_year も main 内関数
            if not horse_ids:
                return

            if DEFAULT_PED_IDS_TO_SCRAPE is None:
                target_ped_ids = horse_ids
            else:
                target_ped_ids = horse_ids[:DEFAULT_PED_IDS_TO_SCRAPE]
            if not target_ped_ids:
                logger.warning("スクレイピング対象の馬IDがありません。")
                return

            logger.info(f'HTMLスクレイピング対象の馬ID (血統用): {target_ped_ids}')
            successful_ids = scrape_html_ped(target_ped_ids, skip=True)
            logger.info(f'スクレイピング成功した馬血統HTML数: {len(successful_ids)}')
            if successful_ids:
                logger.info(f"保存された馬ID (血統用・一部): {successful_ids[:5]}")

        except Exception as e:
            logger.error(f'血統情報スクレイピング中にエラーが発生しました: {e}', exc_info=True)

    def scrape_race_data(settings):
        """レース情報のスクレイピング"""
        logger.info(f"\n--- レース情報のスクレイピング開始 ({settings['from_date']} ～ {settings['to_date']}) ---")
        try:
            # scrape_netkeiba_race_dates などは main 内でインポートされているのでアクセス可能
            kaisai_dates = scrape_netkeiba_race_dates(settings['from_date'], settings['to_date'])
            logger.info(f'取得した開催日数: {len(kaisai_dates)}')

            if not kaisai_dates:
                logger.warning('開催日が取得できませんでした。')
                return

            logger.info(f"最初の5件の開催日: {kaisai_dates[:5]}")

            all_race_ids = scrape_netkeiba_race_ids(kaisai_dates, debug=args.debug) # args.debug を使用

            if not all_race_ids:
                logger.warning(f"指定期間 ({settings['from_date']} ～ {settings['to_date']}) のレースIDが取得できませんでした。")
                return

            logger.info(f"取得した総レースID数 ({settings['from_date']} ～ {settings['to_date']}): {len(all_race_ids)}")
            if all_race_ids:
                logger.info(f"取得したレースID (最初の5件): {all_race_ids[:5]}")

            if DEFAULT_RACE_IDS_TO_SCRAPE is None:
                target_race_ids = all_race_ids
            else:
                target_race_ids = all_race_ids[:DEFAULT_RACE_IDS_TO_SCRAPE]

            if not target_race_ids:
                logger.warning("スクレイピング対象のレースIDがありません。")
                return

            logger.info(f'HTMLスクレイピング対象のレースID数: {len(target_race_ids)}')
            if target_race_ids:
                logger.info(f'HTMLスクレイピング対象のレースID (最初の5件): {target_race_ids[:5]}')
            successful_ids = scrape_html_race(target_race_ids, skip=True)
            logger.info(f'スクレイピング成功したレースHTML数: {len(successful_ids)}')
            if successful_ids:
                logger.info(f"保存されたレースID (一部): {successful_ids[:5]}")

        except Exception as e:
            logger.error(f'レース情報スクレイピング中にエラーが発生しました: {e}', exc_info=True)

    # --- ここまで内部関数の定義 ---

    settings = {
        'race_from_date': DEFAULT_RACE_FROM_DATE,
        'race_to_date': DEFAULT_RACE_TO_DATE,
        'horse_year': DEFAULT_HORSE_YEAR,
        'ped_year': DEFAULT_PED_YEAR,
    }

    input_handler = UserInputHandler()

    while True:
        print("\n実行したい処理を選択してください：")
        print(f"1. レース情報のスクレイピング (現在設定: {settings['race_from_date']} ～ {settings['race_to_date']})")
        print(f"2. 馬情報のスクレイピング (現在設定年: {settings['horse_year']})")
        print(f"3. 血統情報のスクレイピング (現在設定年: {settings['ped_year']})")
        print("4. 全て実行 (各処理前に設定確認)")
        print("5. 終了")

        choice = input("選択肢を入力してください (1-5): ").strip()

        if choice == "1":
            if input(f"レース情報期間を更新しますか？ (現在の設定: {settings['race_from_date']} ～ {settings['race_to_date']}) (y/N): ").strip().lower() == 'y':
                date_range = input_handler.get_date_range(
                    "レース情報", settings['race_from_date'], settings['race_to_date']
                )
                settings['race_from_date'] = date_range['from_date']
                settings['race_to_date'] = date_range['to_date']
            scrape_race_data({'from_date': settings['race_from_date'], 'to_date': settings['race_to_date']})
        elif choice == "2":
            year_to_scrape = input_handler.get_target_year(
                "馬情報をスクレイピングする対象年を入力してください", settings['horse_year']
            )
            settings['horse_year'] = year_to_scrape
            scrape_horse_data(year_to_scrape)
        elif choice == "3":
            year_to_scrape = input_handler.get_target_year(
                "血統情報をスクレイピングする対象年を入力してください", settings['ped_year']
            )
            settings['ped_year'] = year_to_scrape
            scrape_pedigree_data(year_to_scrape)
        elif choice == "4":
            logger.info("--- 全スクレイピング処理開始 ---")

            logger.info("レース情報の設定:")
            if input(f"レース情報期間を更新しますか？ (現在の設定: {settings['race_from_date']} ～ {settings['race_to_date']}) (y/N): ").strip().lower() == 'y':
                date_range = input_handler.get_date_range("レース情報", settings['race_from_date'], settings['race_to_date'])
                settings['race_from_date'] = date_range['from_date']
                settings['race_to_date'] = date_range['to_date']

            logger.info("馬情報の設定:")
            settings['horse_year'] = input_handler.get_target_year("馬情報をスクレイピングする対象年を入力してください", settings['horse_year'])

            logger.info("血統情報の設定:")
            settings['ped_year'] = input_handler.get_target_year("血統情報をスクレイピングする対象年を入力してください", settings['ped_year'])

            scrape_race_data({'from_date': settings['race_from_date'], 'to_date': settings['race_to_date']})
            scrape_horse_data(settings['horse_year'])
            scrape_pedigree_data(settings['ped_year'])
            logger.info("--- 全スクレイピング処理完了 ---")
        elif choice == "5":
            logger.info("処理を終了します。")
            break
        else:
            logger.warning("無効な選択です。1-5の数字を入力してください。")

if __name__ == "__main__":
    main()