# 基本ライブラリのインポート
import os
import sys
from pathlib import Path
from datetime import datetime
import pandas as pd
import numpy as np
from tqdm.notebook import tqdm
import logging
import json
from typing import List, Dict, Tuple, Optional
import gc
import warnings
warnings.filterwarnings('ignore')

# プロジェクトルートの設定
PROJECT_ROOT = Path.cwd()
sys.path.insert(0, str(PROJECT_ROOT))

print(f"プロジェクトルート: {PROJECT_ROOT}")

# ログ設定
def setup_logging(log_dir: Path = Path('logs')):
    """ログ設定のセットアップ"""
    log_dir.mkdir(exist_ok=True)
    log_file = log_dir / f'batch_processing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()
logger.info("ログ設定完了")

# 必要なモジュールのインポート
try:
    from core.processors.race_processor import RaceProcessor
    from core.processors.race_file_handler import RaceFileHandler
    from core.processors.race_html_parser import RaceHtmlParser
    from core.processors.corner_analyzer import CornerAnalyzer
    logger.info("モジュールのインポートに成功しました")
except ImportError as e:
    logger.error(f"モジュールのインポートに失敗しました: {e}")
    raise

class BatchProcessor:
    """バッチ処理を管理するクラス"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.setup_directories()
        self.setup_processors()
        self.processing_stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'start_time': None,
            'end_time': None
        }
    
    def setup_directories(self):
        """ディレクトリの設定"""
        self.data_dir = Path(self.config['data_dir'])
        self.output_dir = Path(self.config['output_dir'])
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # サブディレクトリの作成
        self.pickle_dir = self.output_dir / 'pickle'
        self.stats_dir = self.output_dir / 'stats'
        self.checkpoint_dir = self.output_dir / 'checkpoints'
        
        for dir_path in [self.pickle_dir, self.stats_dir, self.checkpoint_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def setup_processors(self):
        """プロセッサーの初期化"""
        self.html_parser = RaceHtmlParser()
        self.corner_analyzer = CornerAnalyzer()
        self.file_handler = RaceFileHandler(
            html_parser=self.html_parser,
            corner_analyzer=self.corner_analyzer
        )
    
    def process_years(self, years: List[str]):
        """複数年のデータを処理"""
        self.processing_stats['start_time'] = datetime.now()
        logger.info(f"処理開始: {len(years)}年分のデータを処理します")
        
        all_results = {
            'race_info': [],
            'race_results': [],
            'corner_features': []
        }
        
        for year in tqdm(years, desc="年次処理", unit="年"):
            try:
                year_results = self.process_single_year(year)
                
                # 結果を統合
                for key in all_results:
                    if year_results[key] is not None:
                        all_results[key].append(year_results[key])
                
                # メモリ解放
                gc.collect()
                
            except Exception as e:
                logger.error(f"{year}年の処理でエラーが発生: {e}")
                self.save_checkpoint(year, status='failed')
                continue
        
        # 全体の結果を結合
        final_results = self.merge_results(all_results)
        
        self.processing_stats['end_time'] = datetime.now()
        self.save_processing_stats()
        
        return final_results
    
    def process_single_year(self, year: str) -> Dict:
        """単一年のデータを処理"""
        logger.info(f"{year}年のデータ処理を開始")
        
        # チェックポイントの確認
        checkpoint = self.load_checkpoint(year)
        if checkpoint and checkpoint.get('status') == 'completed':
            logger.info(f"{year}年は既に処理済みです")
            return self.load_year_results(year)
        
        try:
            # データ処理
            race_info, race_results, corner_features = self.file_handler.process_race_bin_to_yearly_pickles(
                years=[year],
                bin_base_dir=self.data_dir,
                output_dir=self.pickle_dir,
                parallel=self.config.get('parallel', True),
                max_files_per_year=self.config.get('max_files_per_year'),
                include_corner_features=self.config.get('include_corner_features', True),
                max_workers=self.config.get('max_workers', 4)
            )
            
            # 統計情報の保存
            self.save_year_stats(year, race_info, race_results, corner_features)
            
            # チェックポイントの保存
            self.save_checkpoint(year, status='completed')
            
            return {
                'race_info': race_info,
                'race_results': race_results,
                'corner_features': corner_features
            }
            
        except Exception as e:
            logger.error(f"{year}年の処理中にエラーが発生: {e}")
            self.save_checkpoint(year, status='failed', error=str(e))
            raise
    
    def merge_results(self, all_results: Dict) -> Dict:
        """複数年の結果を結合"""
        logger.info("結果の結合を開始")
        
        merged = {}
        for key in ['race_info', 'race_results', 'corner_features']:
            if all_results[key]:
                # Noneでないデータフレームのみ結合
                valid_dfs = [df for df in all_results[key] if df is not None]
                if valid_dfs:
                    merged[key] = pd.concat(valid_dfs, ignore_index=True)
                    logger.info(f"{key}: {len(merged[key])}行のデータを結合")
                else:
                    merged[key] = None
            else:
                merged[key] = None
        
        return merged
    
    def save_year_stats(self, year: str, race_info: pd.DataFrame, 
                       race_results: pd.DataFrame, corner_features: pd.DataFrame):
        """年次統計の保存"""
        stats = {
            'year': year,
            'race_info_rows': len(race_info) if race_info is not None else 0,
            'race_results_rows': len(race_results) if race_results is not None else 0,
            'corner_features_rows': len(corner_features) if corner_features is not None else 0,
            'processing_time': datetime.now().isoformat()
        }
        
        stats_file = self.stats_dir / f'stats_{year}.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
    
    def save_checkpoint(self, year: str, status: str, error: Optional[str] = None):
        """チェックポイントの保存"""
        checkpoint = {
            'year': year,
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'error': error
        }
        
        checkpoint_file = self.checkpoint_dir / f'checkpoint_{year}.json'
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint, f, ensure_ascii=False, indent=2)
    
    def load_checkpoint(self, year: str) -> Optional[Dict]:
        """チェックポイントの読み込み"""
        checkpoint_file = self.checkpoint_dir / f'checkpoint_{year}.json'
        if checkpoint_file.exists():
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def load_year_results(self, year: str) -> Dict:
        """年次結果の読み込み"""
        results = {}
        for data_type in ['race_info', 'race_results', 'corner_features']:
            pickle_file = self.pickle_dir / f'{data_type}_{year}.pkl'
            if pickle_file.exists():
                results[data_type] = pd.read_pickle(pickle_file)
            else:
                results[data_type] = None
        return results
    
    def save_processing_stats(self):
        """処理統計の保存"""
        if self.processing_stats['start_time'] and self.processing_stats['end_time']:
            duration = self.processing_stats['end_time'] - self.processing_stats['start_time']
            self.processing_stats['duration_seconds'] = duration.total_seconds()
        
        stats_file = self.stats_dir / 'overall_stats.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.processing_stats, f, default=str, ensure_ascii=False, indent=2)

def validate_results(results: Dict) -> Dict:
    """結果の検証"""
    validation = {}
    
    for key, df in results.items():
        if df is not None:
            validation[key] = {
                'rows': len(df),
                'columns': len(df.columns),
                'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,
                'null_counts': df.isnull().sum().to_dict() if len(df) < 1000000 else 'Too large to display',
                'dtypes': df.dtypes.to_dict()
            }
        else:
            validation[key] = None
    
    return validation

def display_summary(results: Dict, validation: Dict):
    """処理結果のサマリー表示"""
    print("\n" + "="*50)
    print("処理結果サマリー")
    print("="*50)
    
    for key, df in results.items():
        print(f"\n【{key}】")
        if df is not None:
            print(f"  - 行数: {len(df):,}")
            print(f"  - 列数: {len(df.columns)}")
            print(f"  - メモリ使用量: {validation[key]['memory_usage_mb']:.2f} MB")
            if key == 'race_results':
                print(f"  - ユニークなレース数: {df['race_id'].nunique():,}")
                print(f"  - ユニークな馬数: {df['horse_id'].nunique():,}")
        else:
            print("  - データなし")
    
    print("\n" + "="*50)

# 設定
config = {
    # データディレクトリ
    'data_dir': 'H:/AI/keiba_ai_system/data/html/race/race_by_year',
    'output_dir': 'output',
    
    # 処理対象年
    # 個別指定の場合
    # 'years': ['2020', '2021', '2022'],
    
    # 範囲指定の場合
    'year_range': (2020, 2024),
    
    # 処理設定
    'parallel': True,
    'max_workers': 4,
    'max_files_per_year': None,  # Noneで全ファイル処理、テスト時は100など指定
    'include_corner_features': True,
    
    # 保存設定
    'save_final_results': True
}

# 年次リストの生成
if config.get('year_range'):
    start_year, end_year = config['year_range']
    years = [str(year) for year in range(start_year, end_year + 1)]
else:
    years = config.get('years', [])

print(f"処理対象年: {years}")
print(f"データディレクトリ: {config['data_dir']}")
print(f"出力ディレクトリ: {config['output_dir']}")

# BatchProcessorの初期化
processor = BatchProcessor(config)
logger.info(f"処理対象年: {years}")

# データ処理の実行
results = processor.process_years(years)

# 結果の検証
validation = validate_results(results)

# サマリー表示
display_summary(results, validation)

# 最終結果の保存（オプション）
if config.get('save_final_results', True):
    for key, df in results.items():
        if df is not None:
            output_file = processor.output_dir / f'{key}_all_years.pkl'
            df.to_pickle(output_file)
            logger.info(f"{key}を保存: {output_file}")
            print(f"{key}を保存しました: {output_file}")

# race_infoの確認
if results['race_info'] is not None:
    print("=== Race Info Sample ===")
    display(results['race_info'].head())
    print(f"\n列名: {list(results['race_info'].columns)}")

# race_resultsの確認
if results['race_results'] is not None:
    print("=== Race Results Sample ===")
    display(results['race_results'].head())
    print(f"\n列名: {list(results['race_results'].columns)}")

# corner_featuresの確認
if results['corner_features'] is not None:
    print("=== Corner Features Sample ===")
    display(results['corner_features'].head())
    print(f"\n列名: {list(results['corner_features'].columns)}")

# 処理統計の読み込みと表示
stats_file = processor.stats_dir / 'overall_stats.json'
if stats_file.exists():
    with open(stats_file, 'r', encoding='utf-8') as f:
        overall_stats = json.load(f)
    
    print("=== 処理統計 ===")
    for key, value in overall_stats.items():
        print(f"{key}: {value}")
    
    if 'duration_seconds' in overall_stats:
        duration_min = overall_stats['duration_seconds'] / 60
        print(f"\n処理時間: {duration_min:.2f} 分")

# 年次統計の確認
print("=== 年次統計 ===")
for year in years:
    stats_file = processor.stats_dir / f'stats_{year}.json'
    if stats_file.exists():
        with open(stats_file, 'r', encoding='utf-8') as f:
            year_stats = json.load(f)
        print(f"\n{year}年:")
        for key, value in year_stats.items():
            print(f"  {key}: {value}")