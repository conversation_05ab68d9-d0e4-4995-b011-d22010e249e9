#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日付処理プロセッサー

日本語の日付文字列やオブジェクト型の日付データを適切なdatetime型に変換し、
データ統合処理で使用できる形式に標準化します。
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Optional, Union, List, Dict, Any
import re

# from core.utils.constants import ProcessorType  # 不要なため削除


class DateProcessor:
    """
    日付データの標準化と変換を行うプロセッサー
    
    主な機能:
    - 日本語日付文字列 ('2020年7月25日') をdatetime型に変換
    - 各種日付フォーマットの自動検出と変換
    - 欠損値やエラー値の適切な処理
    - データフレーム全体の日付カラム一括処理
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初期化
        
        Args:
            config: 設定辞書（オプション）
                - strict_mode: 厳密モード（デフォルト: False）
                - error_handling: エラー処理方法 ('coerce'|'raise'|'ignore')
                - timezone: タイムゾーン設定
        """
        self.config = config or {}
        self.strict_mode = self.config.get('strict_mode', False)
        self.error_handling = self.config.get('error_handling', 'coerce')
        self.timezone = self.config.get('timezone', None)
        
        # ログ設定
        self.logger = logging.getLogger(__name__)
        
        # 日本語日付パターンの定義
        self.japanese_date_patterns = [
            r'(\d{4})年(\d{1,2})月(\d{1,2})日',  # 2020年7月25日
            r'(\d{4})/(\d{1,2})/(\d{1,2})',     # 2020/7/25
            r'(\d{4})-(\d{1,2})-(\d{1,2})',     # 2020-7-25
            r'(\d{4})\.(\d{1,2})\.(\d{1,2})'    # 2020.7.25
        ]
        
        # サポートする日付フォーマット
        self.supported_formats = [
            '%Y年%m月%d日',
            '%Y/%m/%d',
            '%Y-%m-%d',
            '%Y.%m.%d',
            '%Y%m%d'
        ]
    
    def parse_japanese_date(self, date_value: Any) -> pd.Timestamp:
        """
        日本語日付文字列またはその他の日付値をdatetime型に変換
        
        Args:
            date_value: 変換対象の日付値
            
        Returns:
            pd.Timestamp: 変換された日付（変換できない場合はNaT）
        """
        if pd.isna(date_value):
            return pd.NaT
        
        # 既にdatetime型の場合はそのまま返す
        if isinstance(date_value, (pd.Timestamp, datetime)):
            return pd.Timestamp(date_value)
        
        # 文字列に変換
        date_str = str(date_value).strip()
        
        if not date_str or date_str.lower() in ['nan', 'nat', 'none', '']:
            return pd.NaT
        
        # 日本語日付パターンの変換
        for pattern in self.japanese_date_patterns:
            match = re.match(pattern, date_str)
            if match:
                try:
                    year, month, day = match.groups()
                    return pd.Timestamp(int(year), int(month), int(day))
                except (ValueError, TypeError) as e:
                    self.logger.debug(f"日付パターンマッチング失敗: {date_str} -> {e}")
                    continue
        
        # 標準的なpandas日付変換を試行
        try:
            return pd.to_datetime(date_str, errors='raise')
        except Exception as e:
            if self.error_handling == 'raise':
                raise ValueError(f"日付変換失敗: {date_str} -> {e}")
            elif self.error_handling == 'coerce':
                self.logger.debug(f"日付変換失敗（NaTに変換）: {date_str} -> {e}")
                return pd.NaT
            else:  # ignore
                return date_value
    
    def process_date_column(self, series: pd.Series, column_name: str = None) -> pd.Series:
        """
        データフレームの日付カラムを処理
        
        Args:
            series: 処理対象のシリーズ
            column_name: カラム名（ログ用）
            
        Returns:
            pd.Series: 処理済みのシリーズ
        """
        column_name = column_name or "unknown"
        
        self.logger.info(f"日付カラム '{column_name}' の処理を開始")
        self.logger.info(f"  元のデータ型: {series.dtype}")
        self.logger.info(f"  元の例: {series.head(3).tolist()}")
        
        # 変換処理
        processed_series = series.apply(self.parse_japanese_date)
        
        # 結果の統計
        total_count = len(processed_series)
        valid_count = processed_series.notna().sum()
        nat_count = processed_series.isna().sum()
        
        self.logger.info(f"  変換後のデータ型: {processed_series.dtype}")
        self.logger.info(f"  有効な日付数: {valid_count:,} / {total_count:,}")
        self.logger.info(f"  NaT数: {nat_count:,}")
        
        if valid_count > 0:
            min_date = processed_series.min()
            max_date = processed_series.max()
            self.logger.info(f"  日付範囲: {min_date} ～ {max_date}")
            
            if min_date and max_date:
                days_span = (max_date - min_date).days
                self.logger.info(f"  期間: {days_span}日間")
        
        # 厳密モードでの検証
        if self.strict_mode and nat_count > 0:
            self.logger.warning(f"厳密モード: {nat_count}件の変換失敗を検出")
            if nat_count / total_count > 0.1:  # 10%以上失敗の場合
                raise ValueError(f"日付変換失敗率が高すぎます: {nat_count/total_count:.1%}")
        
        return processed_series
    
    def process_dataframe(self, df: pd.DataFrame, 
                         date_columns: Optional[List[str]] = None,
                         auto_detect: bool = True) -> pd.DataFrame:
        """
        データフレーム内の日付カラムを一括処理
        
        Args:
            df: 処理対象のデータフレーム
            date_columns: 処理対象の日付カラム名リスト（指定しない場合は自動検出）
            auto_detect: 日付カラムの自動検出を行うかどうか
            
        Returns:
            pd.DataFrame: 処理済みのデータフレーム
        """
        result_df = df.copy()
        
        # 日付カラムの特定
        if date_columns is None:
            if auto_detect:
                date_columns = self._detect_date_columns(df)
            else:
                date_columns = []
        
        if not date_columns:
            self.logger.info("処理対象の日付カラムが見つかりません")
            return result_df
        
        self.logger.info(f"日付カラム一括処理開始: {date_columns}")
        
        # 各日付カラムを処理
        for col in date_columns:
            if col in result_df.columns:
                try:
                    result_df[col] = self.process_date_column(result_df[col], col)
                except Exception as e:
                    self.logger.error(f"カラム '{col}' の処理でエラー: {e}")
                    if self.strict_mode:
                        raise
            else:
                self.logger.warning(f"カラム '{col}' が見つかりません")
        
        self.logger.info("日付カラム一括処理完了")
        return result_df
    
    def _detect_date_columns(self, df: pd.DataFrame) -> List[str]:
        """
        データフレームから日付カラムを自動検出
        
        Args:
            df: 検出対象のデータフレーム
            
        Returns:
            List[str]: 日付カラム名のリスト
        """
        date_columns = []
        
        # カラム名による検出
        date_keywords = ['date', 'time', '日', '時', '年月日', 'birthday', '生年月日', 
                        '開催日', 'race_date', 'last_race_date']
        
        for col in df.columns:
            col_lower = str(col).lower()
            
            # キーワードマッチング
            if any(keyword in col_lower for keyword in date_keywords):
                date_columns.append(col)
                continue
            
            # データ型による検出
            if df[col].dtype == 'object':
                # サンプルデータをチェック
                sample_values = df[col].dropna().head(10).astype(str)
                
                if len(sample_values) > 0:
                    # 日本語日付パターンの検出
                    for pattern in self.japanese_date_patterns:
                        if any(re.match(pattern, str(val)) for val in sample_values):
                            date_columns.append(col)
                            break
        
        self.logger.info(f"自動検出された日付カラム: {date_columns}")
        return date_columns
    
    def validate_date_range(self, df: pd.DataFrame, date_column: str,
                           min_year: int = 1990, max_year: int = 2030) -> Dict[str, Any]:
        """
        日付カラムの妥当性を検証
        
        Args:
            df: 検証対象のデータフレーム
            date_column: 検証対象の日付カラム
            min_year: 最小年
            max_year: 最大年
            
        Returns:
            Dict[str, Any]: 検証結果の辞書
        """
        if date_column not in df.columns:
            return {"valid": False, "error": f"カラム '{date_column}' が見つかりません"}
        
        date_series = df[date_column]
        valid_dates = date_series.dropna()
        
        if len(valid_dates) == 0:
            return {"valid": False, "error": "有効な日付が見つかりません"}
        
        min_date = valid_dates.min()
        max_date = valid_dates.max()
        
        # 年の範囲チェック
        if min_date.year < min_year or max_date.year > max_year:
            return {
                "valid": False,
                "error": f"日付が範囲外です: {min_date} ～ {max_date} (許可範囲: {min_year}-{max_year})"
            }
        
        # 未来日付のチェック
        today = pd.Timestamp.now().normalize()
        future_dates = valid_dates[valid_dates > today]
        
        return {
            "valid": True,
            "min_date": min_date,
            "max_date": max_date,
            "total_dates": len(date_series),
            "valid_dates": len(valid_dates),
            "nat_count": date_series.isna().sum(),
            "future_dates": len(future_dates),
            "date_range_days": (max_date - min_date).days
        }
    
    def create_date_features(self, df: pd.DataFrame, date_column: str) -> pd.DataFrame:
        """
        日付カラムから派生特徴量を生成
        
        Args:
            df: 対象のデータフレーム
            date_column: 日付カラム名
            
        Returns:
            pd.DataFrame: 派生特徴量が追加されたデータフレーム
        """
        result_df = df.copy()
        
        if date_column not in df.columns:
            self.logger.warning(f"カラム '{date_column}' が見つかりません")
            return result_df
        
        date_series = result_df[date_column]
        base_name = date_column
        
        # 年、月、日の抽出
        result_df[f'{base_name}_year'] = date_series.dt.year
        result_df[f'{base_name}_month'] = date_series.dt.month
        result_df[f'{base_name}_day'] = date_series.dt.day
        result_df[f'{base_name}_weekday'] = date_series.dt.weekday
        result_df[f'{base_name}_quarter'] = date_series.dt.quarter
        
        # 季節特徴量
        result_df[f'{base_name}_season'] = (date_series.dt.month % 12 + 3) // 3
        
        self.logger.info(f"日付派生特徴量を6個生成: {base_name}_year, _month, _day, _weekday, _quarter, _season")
        
        return result_df


def process_date_data(data: pd.DataFrame, 
                     date_columns: Optional[List[str]] = None,
                     config: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
    """
    便利関数：データフレームの日付処理を簡単に実行
    
    Args:
        data: 処理対象のデータフレーム
        date_columns: 処理対象の日付カラム（None の場合は自動検出）
        config: DateProcessor の設定
        
    Returns:
        pd.DataFrame: 日付処理済みのデータフレーム
    """
    processor = DateProcessor(config)
    return processor.process_dataframe(data, date_columns)


if __name__ == "__main__":
    # テスト用コード
    import sys
    import os
    
    # ログ設定
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # サンプルデータでテスト
    sample_data = pd.DataFrame({
        'date': ['2020年7月25日', '2020年8月15日', '2020年9月5日'],
        'birthday': ['2017年3月10日', '2018年5月20日', '2016年12月1日'],
        'race_id': ['202007250101', '202008150101', '202009050101'],
        'value': [1, 2, 3]
    })
    
    print("🧪 DateProcessor テスト開始")
    print(f"元データ:\n{sample_data}")
    print(f"データ型:\n{sample_data.dtypes}")
    
    # 処理実行
    processor = DateProcessor()
    processed_data = processor.process_dataframe(sample_data)
    
    print(f"\n処理後データ:\n{processed_data}")
    print(f"処理後データ型:\n{processed_data.dtypes}")
    
    # 検証
    for col in ['date', 'birthday']:
        if col in processed_data.columns:
            validation = processor.validate_date_range(processed_data, col)
            print(f"\n{col} 検証結果: {validation}")