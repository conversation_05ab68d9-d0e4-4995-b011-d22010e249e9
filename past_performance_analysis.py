#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
過去成績取得システムの詳細分析とデモ
実際のレースの過去成績取得方法を解説
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any
import json

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PastPerformanceAnalyzer:
    """過去成績取得システム分析クラス"""
    
    def __init__(self):
        self.output_dir = Path("output")
        logger.info("PastPerformanceAnalyzer初期化完了")
    
    def analyze_current_system(self):
        """現在のシステムでの過去成績取得方法を分析"""
        print("🔍 現在の過去成績取得システム分析")
        print("=" * 60)
        
        # 1. データソースの確認
        print("\n1️⃣ データソースの確認")
        self._check_data_sources()
        
        # 2. 過去成績取得方法の分析
        print("\n2️⃣ 過去成績取得方法の分析")
        self._analyze_performance_extraction()
        
        # 3. データ品質の評価
        print("\n3️⃣ データ品質の評価")
        self._evaluate_data_quality()
        
        # 4. 改善提案
        print("\n4️⃣ 改善提案")
        self._suggest_improvements()
    
    def _check_data_sources(self):
        """データソースの確認"""
        print("📊 利用可能なデータソース:")
        
        # pickleファイルの確認
        pickle_files = list(self.output_dir.glob("*.pickle"))
        pickle_files.sort()
        
        print(f"\n📁 {self.output_dir} ディレクトリ内のpickleファイル:")
        for file in pickle_files:
            size_mb = file.stat().st_size / (1024 * 1024)
            print(f"   - {file.name}: {size_mb:.1f}MB")
        
        # レース結果ファイルの詳細確認
        race_result_files = [f for f in pickle_files if 'race_results' in f.name]
        print(f"\n🏇 レース結果ファイル: {len(race_result_files)}個")
        
        for file in race_result_files:
            try:
                df = pd.read_pickle(file)
                year = file.name.split('_')[-1].split('.')[0]
                print(f"   - {year}年: {len(df)}件のレース結果")
                
                # カラム情報
                if len(df) > 0:
                    key_columns = ['horse_id', '馬名', '着順', '人気', '賞金(万円)', 'race_id']
                    available_columns = [col for col in key_columns if col in df.columns]
                    print(f"     利用可能カラム: {available_columns}")
                    
            except Exception as e:
                print(f"   - {file.name}: 読み込みエラー ({e})")
        
        # データ統合ファイルの確認
        integration_files = [f for f in pickle_files if 'comprehensive' in f.name]
        print(f"\n🔗 統合データファイル: {len(integration_files)}個")
        
        for file in integration_files:
            try:
                df = pd.read_pickle(file)
                print(f"   - {file.name}: {len(df)}件, {len(df.columns)}カラム")
            except Exception as e:
                print(f"   - {file.name}: 読み込みエラー ({e})")
    
    def _analyze_performance_extraction(self):
        """過去成績取得方法の分析"""
        print("🎯 現在の過去成績取得フロー:")
        
        print("\n【方法1: 既存pickleファイルからの取得】")
        print("1. output/race_results_{年}.pickle ファイルを確認")
        print("2. 対象馬のhorse_idで絞り込み")
        print("3. ターゲット日付以前のデータのみ抽出")
        print("4. 統計計算（勝率、平均着順、人気など）")
        
        print("\n【方法2: リアルタイム取得（現在はスタブ）】")
        print("1. netkeibaから馬の詳細ページにアクセス")
        print("2. 過去戦績テーブルをスクレイピング")
        print("3. データをパースして統計計算")
        print("（注: 現在は_get_realtime_horse_stats()がスタブ実装）")
        
        print("\n【方法3: フォールバック（デフォルト値）】")
        print("1. データが取得できない場合の予備手段")
        print("2. 一般的な統計値を設定")
        print("3. 人気ベースの推定値計算")
        
        # 実際の実装例を表示
        print("\n📝 実装例（improved_live_predictor.py）:")
        print("""
def get_horse_past_performance_enhanced(self, horse_ids, target_date):
    # 既存データから取得
    past_results_data = self._load_existing_horse_data(horse_ids, target_datetime)
    
    for horse_id in horse_ids:
        if horse_id in past_results_data:
            # 既存データから統計計算
            horse_data = past_results_data[horse_id]
            stats[horse_id] = self._calculate_enhanced_stats(horse_data, target_datetime)
        else:
            # リアルタイム取得（現在はスタブ）
            stats[horse_id] = self._get_realtime_horse_stats(horse_id, target_datetime)
        """)
    
    def _evaluate_data_quality(self):
        """データ品質の評価"""
        print("📈 データ品質評価:")
        
        # 最新のレース結果ファイルを確認
        try:
            latest_year = datetime.now().year
            for check_year in range(latest_year, latest_year - 5, -1):
                results_path = self.output_dir / f"race_results_{check_year}.pickle"
                
                if results_path.exists():
                    df = pd.read_pickle(results_path)
                    print(f"\n📊 {check_year}年データ分析:")
                    print(f"   総レース数: {len(df)}件")
                    
                    # ユニーク馬数
                    if 'horse_id' in df.columns:
                        unique_horses = df['horse_id'].nunique()
                        print(f"   ユニーク馬数: {unique_horses}頭")
                    
                    # データ完整性チェック
                    key_columns = ['着順', '人気', '馬名']
                    for col in key_columns:
                        if col in df.columns:
                            null_rate = df[col].isnull().mean() * 100
                            print(f"   {col}欠損率: {null_rate:.1f}%")
                    
                    # 統計情報
                    if '着順' in df.columns:
                        rank_stats = df['着順'].describe()
                        print(f"   着順統計: 平均{rank_stats['mean']:.1f}, 中央値{rank_stats['50%']:.1f}")
                    
                    break
            else:
                print("❌ レース結果データが見つかりません")
                
        except Exception as e:
            print(f"❌ データ品質評価エラー: {e}")
    
    def _suggest_improvements(self):
        """改善提案"""
        print("💡 過去成績取得システムの改善提案:")
        
        print("\n🚀 短期改善案:")
        print("1. リアルタイム取得機能の実装")
        print("   - core/scrapers/scraper.pyを活用")
        print("   - 馬の詳細ページからの戦績取得")
        print("   - キャッシュ機能の追加")
        
        print("\n2. データ品質向上")
        print("   - 欠損値の補完ロジック改善")
        print("   - 異常値検出と除去")
        print("   - データ整合性チェック")
        
        print("\n3. 統計計算の高度化")
        print("   - 馬場状態別成績")
        print("   - 距離別成績")
        print("   - 騎手・厩舎との組み合わせ成績")
        
        print("\n🎯 中長期改善案:")
        print("1. データベース化")
        print("   - SQLiteまたはPostgreSQLの導入")
        print("   - インデックス最適化")
        print("   - 高速検索の実現")
        
        print("\n2. API化")
        print("   - 過去成績取得API作成")
        print("   - 外部システムとの連携")
        print("   - リアルタイム更新機能")
        
        print("\n3. 機械学習強化")
        print("   - 過去成績からの特徴量自動生成")
        print("   - 時系列分析の導入")
        print("   - アンサンブル学習への活用")
    
    def demonstrate_current_flow(self):
        """現在のフローをデモ"""
        print("\n🎪 過去成績取得フローのデモ")
        print("=" * 50)
        
        # サンプル馬IDでデモ
        sample_horse_ids = ["2018104423", "2019105123", "2020106789"]  # 仮のID
        target_date = datetime.now() - timedelta(days=30)
        
        print(f"📋 テスト条件:")
        print(f"   対象馬ID: {sample_horse_ids}")
        print(f"   基準日: {target_date.strftime('%Y-%m-%d')}")
        
        # 実際のフロー実行
        try:
            from improved_live_predictor import ImprovedLiveRacePredictor
            
            predictor = ImprovedLiveRacePredictor()
            
            print(f"\n🔍 過去成績取得実行中...")
            stats = predictor.get_horse_past_performance_enhanced(
                sample_horse_ids, 
                target_date.strftime('%Y-%m-%d')
            )
            
            print(f"✅ 取得完了: {len(stats)}頭分の統計")
            
            # 結果表示
            for horse_id, horse_stats in stats.items():
                print(f"\n📊 馬ID {horse_id} の統計:")
                print(f"   勝率: {horse_stats.get('win_rate', 0):.3f}")
                print(f"   平均着順: {horse_stats.get('avg_rank', 0):.1f}")
                print(f"   平均人気: {horse_stats.get('avg_popularity', 0):.1f}")
                print(f"   出走回数: {horse_stats.get('total_races', 0)}回")
                
        except Exception as e:
            print(f"❌ デモ実行エラー: {e}")
    
    def create_improvement_plan(self):
        """具体的な改善計画を作成"""
        print("\n📋 具体的な改善実装計画")
        print("=" * 50)
        
        plan = {
            "phase1_immediate": {
                "title": "即座実装可能な改善",
                "tasks": [
                    "リアルタイム馬データ取得関数の実装",
                    "キャッシュ機能の追加",
                    "エラーハンドリングの強化"
                ],
                "estimated_time": "2-3日"
            },
            "phase2_short_term": {
                "title": "短期改善（1週間以内）",
                "tasks": [
                    "統計計算アルゴリズムの改善",
                    "データ品質チェック機能",
                    "馬場・距離別成績の追加"
                ],
                "estimated_time": "1週間"
            },
            "phase3_medium_term": {
                "title": "中期改善（1ヶ月以内）",
                "tasks": [
                    "データベース化の検討",
                    "API設計と実装",
                    "パフォーマンス最適化"
                ],
                "estimated_time": "1ヶ月"
            }
        }
        
        for phase, details in plan.items():
            print(f"\n🎯 {details['title']} ({details['estimated_time']})")
            print("-" * 40)
            for i, task in enumerate(details['tasks'], 1):
                print(f"{i}. {task}")
        
        # 計画をJSONファイルに保存
        plan_file = "past_performance_improvement_plan.json"
        with open(plan_file, 'w', encoding='utf-8') as f:
            json.dump(plan, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 改善計画を保存: {plan_file}")

def main():
    """メイン実行"""
    try:
        print("🏇 過去成績取得システム詳細分析")
        print("=" * 60)
        
        analyzer = PastPerformanceAnalyzer()
        
        # 現在のシステム分析
        analyzer.analyze_current_system()
        
        # デモ実行
        analyzer.demonstrate_current_flow()
        
        # 改善計画作成
        analyzer.create_improvement_plan()
        
        print(f"\n{'='*60}")
        print("📊 分析完了！")
        print("\n📝 要約:")
        print("現在の過去成績取得は主にpickleファイルベース")
        print("リアルタイム取得機能は実装の余地あり")
        print("改善により予測精度向上が期待できます")
        
    except Exception as e:
        logger.error(f"分析エラー: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()