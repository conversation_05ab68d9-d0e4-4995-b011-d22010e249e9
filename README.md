# 競馬AI予測システム (keiba_ai_system)

**包括的な日本競馬予測システム - データ収集から機械学習予測まで**

このプロジェクトは、netkeiba.comからのデータ収集、特徴量エンジニアリング、機械学習モデルの訓練、リアルタイム予測までを一貫して行う包括的な競馬予測システムです。

## 🎯 主要機能

- **データスクレイピング**: netkeiba.comから自動データ収集
- **データ処理**: HTML解析と構造化データ変換
- **特徴量エンジニアリング**: 体系的な特徴量管理システム
- **機械学習**: LightGBM/TensorFlow Rankingモデル
- **リアルタイム予測**: ライブレース予測システム
- **可視化・分析**: Jupyter Notebookによる分析環境

## 📁 プロジェクト構造

```
keiba_ai_system/
├── 📄 README.md                      # プロジェクト概要
├── 📄 CLAUDE.md                      # 開発者向け指示書
├── 📄 特徴量まとめ.md                # 特徴量仕様書
├── 📄 requirements.txt               # Python依存関係
├── 📄 training_config.yaml           # モデル訓練設定
│
├── 🔧 core/                          # コアモジュール
│   ├── features/                     # 特徴量エンジニアリング
│   │   ├── manager.py               # 特徴量管理システム
│   │   ├── definitions.py           # 特徴量定義
│   │   ├── calculators.py           # 特徴量計算関数
│   │   └── config.yaml              # 特徴量設定
│   ├── processors/                   # データ処理
│   │   ├── comprehensive_integrator.py  # 包括的データ統合
│   │   ├── race_processor.py        # レースデータ処理
│   │   ├── horse_processor.py       # 馬データ処理
│   │   └── race_feature_engineer.py # レース特徴量
│   ├── scrapers/                     # Webスクレイピング
│   │   └── scraper.py               # netkeiba.com スクレイパー
│   └── utils/                        # ユーティリティ
│       └── constants.py             # 共通定数・設定
│
├── 🎯 prediction/                    # 予測システム
│   ├── live_predictor.py            # ライブ予測システム
│   └── race_predictor.py            # TensorFlow Ranking予測
│
├── 📊 models/                        # 学習済みモデル
│   ├── lgb_model_*.pkl              # LightGBMモデル
│   ├── features_*.pkl               # 特徴量定義
│   └── scaler_*.pkl                 # 前処理パイプライン
│
├── 💡 examples/                      # 使用例・ノートブック
│   ├── notebooks/                   # Jupyter分析例
│   └── configs/                     # 設定ファイル例
│
├── 📖 docs/                          # ドキュメント
│   ├── FEATURE_ENGINEERING_GUIDE.md # 特徴量エンジニアリング
│   ├── INTEGRATION_GUIDE.md         # データ統合ガイド
│   └── PREDICTION_GUIDE.md          # 予測システムガイド
│
├── 🧪 *.py                          # 訓練・実験スクリプト
│   ├── enhanced_train.py            # 拡張特徴量での訓練
│   ├── simple_train.py              # 基本特徴量での訓練
│   └── netkeiba_scraping_tool.py    # データ収集ツール
│
└── 📂 data/                          # データディレクトリ
    └── output/                       # 処理済みデータ
```

## 🏗️ アーキテクチャ概要

### データフロー
```
1. データ収集      →  2. データ処理     →  3. 特徴量エンジニアリング
   netkeiba.com       HTML → DataFrame       血統・成績統計
         ↓                    ↓                     ↓
4. モデル訓練      ←  5. 予測システム   ←  6. リアルタイム分析
   LightGBM           ライブ予測            可視化・評価
```

### 主要コンポーネント

#### 🔧 コアモジュール (`core/`)
- **`features/`**: 特徴量エンジニアリング管理システム
  - 7つのカテゴリ（基本・成績・血統・騎手・調教師・レース条件・高度）
  - YAML設定による柔軟な特徴量管理
- **`processors/`**: データ処理パイプライン
  - HTML解析、データ統合、前処理
- **`scrapers/`**: netkeiba.comスクレイピング
- **`utils/`**: 共通定数・ユーティリティ

#### 🎯 予測システム (`prediction/`)
- **`live_predictor.py`**: リアルタイム予測
  - ライブデータ取得 → 特徴量計算 → 予測実行
- **`race_predictor.py`**: TensorFlow Ranking
  - ランキング学習による順位予測

#### 📊 学習済みモデル (`models/`)
- **LightGBMモデル**: 現在99.21%の精度
- **特徴量パイプライン**: 前処理・スケーリング
- **設定ファイル**: 再現可能な実験環境

## 🚀 クイックスタート

### 🎯 超簡単起動（推奨）

#### 1つのファイルで全機能アクセス
```bash
# 統合メニューシステム起動
python keiba_ai_main.py

# または、バッチファイルで起動
start_keiba_ai.bat          # Windows batch
start_keiba_ai.ps1          # PowerShell
```

#### クイック操作
```bash
# 1コマンドで予測
python quick_predict.py 202406010101

# 1コマンドで訓練
python quick_train.py --enhanced

# 自動実行モード
python keiba_ai_main.py --auto train_simple
python keiba_ai_main.py --auto scraping
```

### ⚙️ 従来の個別実行方法

#### 1. 環境セットアップ
```bash
# 仮想環境の作成・アクティベート (Windows)
.\venv\Scripts\activate
# または
.\activate_env.bat

# 依存関係のインストール
pip install -r requirements.txt
```

#### 2. リアルタイム予測の実行
```bash
# コマンドライン実行
python -m prediction.live_predictor 202406010101

# カスタムモデルを使用
python -m prediction.live_predictor 202406010101 \
    --model models/lgb_model_20250607_205540.pkl \
    --scaler models/scaler_20250607_205540.pkl
```

#### 3. Pythonスクリプトでの使用
```python
from prediction.live_predictor import LiveRacePredictor

# 予測システムの初期化
predictor = LiveRacePredictor()

# レース予測の実行
results = predictor.predict_race_complete("202406010101")
print(results[['馬名', '予測順位', '勝率予測']].head())
```

#### 4. モデル訓練
```bash
# 基本特徴量での訓練
python simple_train.py

# 拡張特徴量での訓練
python enhanced_train.py

# 高速訓練（実験用）
python quick_enhanced_train.py
```

#### 5. データ収集
```bash
# netkeiba.comからデータ収集
python netkeiba_scraping_tool.py
```

## 📋 主要機能詳細

### 🕷️ データ収集・スクレイピング
- **自動データ収集**: netkeiba.comから競馬データを自動収集
- **アンチ検出機能**: ユーザーエージェント回転、リクエスト間隔調整
- **包括的データ**: レース情報、馬基本情報、過去成績、血統情報
- **エラーハンドリング**: 再試行機能、エラーログ記録

### ⚙️ データ処理・統合
- **HTML解析**: BeautifulSoupによる構造化データ抽出
- **包括的統合**: レース情報、馬情報、成績統計の統合
- **並列処理**: マルチコア活用による高速処理
- **データ品質管理**: 欠損値処理、型チェック、バリデーション

### 🔧 特徴量エンジニアリング
- **体系的管理**: 7カテゴリ268項目の設定可能特徴量
- **動的計算**: 過去N戦統計、勝率、連対率、複勝率
- **血統特徴量**: 父、母、母父の産駒成績統計
- **YAML設定**: 特徴量の有効化/無効化、パラメータ調整

### 🤖 機械学習・予測
- **LightGBMモデル**: 高精度勝率予測（現在99.21%）
- **TensorFlow Ranking**: ランキング学習による順位予測
- **リアルタイム予測**: ライブレースの即座予測
- **モデル管理**: 訓練済みモデルとパラメータの体系的管理

### 📊 分析・可視化
- **Jupyter Notebook**: インタラクティブな分析環境
- **予測結果保存**: CSV形式での結果出力
- **ログ機能**: 詳細な処理ログとエラー追跡
- **統計情報**: データ品質、特徴量重要度の可視化

## 💻 開発・運用ガイド

### 設定管理
- **`training_config.yaml`**: モデル訓練パラメータ、データリーク防止設定
- **`core/features/config.yaml`**: 特徴量カテゴリ、計算パラメータ
- **`examples/configs/`**: 本番・開発環境設定例

### コード品質ツール
```bash
# コードフォーマット
black .

# リンティング
flake8 .

# 型チェック
mypy .

# インポートソート
isort .
```

### テスト実行
```bash
# 特定テストファイル実行
python -m pytest core/processors/test_comprehensive_integrator.py -v

# 全テスト実行
python -m pytest -v
```

## ⚠️ 重要な注意事項

### データサイズ・パフォーマンス
- **大容量データ**: プロジェクト全体で約19.5GB（22万ファイル）
- **メモリ使用量**: 大量データ処理時は16GB以上推奨
- **処理時間**: 全年度データ統合は数時間要する場合あり

### スクレイピング制限
- **適度なアクセス**: 過度なリクエストを避ける
- **再取得防止**: `skip_existing=True`で既存ファイル保護
- **エラー対応**: ログファイルでの問題特定

### データリーク防止
- **時系列考慮**: 未来情報の混入を自動防止
- **設定管理**: `leakage_risk_columns`での危険特徴量管理
- **検証機能**: 訓練データの時系列整合性チェック

## 🔗 関連ドキュメント

| ドキュメント | 説明 |
|------------|------|
| [`特徴量まとめ.md`](特徴量まとめ.md) | 実装済み特徴量の詳細仕様 |
| [`docs/FEATURE_ENGINEERING_GUIDE.md`](docs/FEATURE_ENGINEERING_GUIDE.md) | 特徴量エンジニアリング詳細 |
| [`docs/INTEGRATION_GUIDE.md`](docs/INTEGRATION_GUIDE.md) | データ統合システム詳細 |
| [`docs/PREDICTION_GUIDE.md`](docs/PREDICTION_GUIDE.md) | 予測システム詳細 |
| [`CLAUDE.md`](CLAUDE.md) | 開発者向け技術仕様 |

## 📞 サポート・免責事項

- **技術サポート**: ログファイル確認、設定検証
- **予測精度**: 参考用途、投資判断には使用禁止
- **データ使用**: スクレイピングは利用規約準拠
