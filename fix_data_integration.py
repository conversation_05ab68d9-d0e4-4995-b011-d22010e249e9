#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
データ統合の問題を修正するスクリプト
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# プロジェクトパスの追加
sys.path.append('.')

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_pickle_files():
    """pickleファイルの存在と内容を確認"""
    logger.info("📂 pickleファイルの確認中...")
    
    pickle_files = [
        'output/race_info_2020.pickle',
        'output/race_results_2020.pickle', 
        'output/horse_results_2020.pickle',
        'output/horse_info_2020.pickle'
    ]
    
    file_info = {}
    
    for file_path in pickle_files:
        if os.path.exists(file_path):
            try:
                df = pd.read_pickle(file_path)
                file_info[file_path] = {
                    'exists': True,
                    'records': len(df),
                    'columns': list(df.columns),
                    'sample': df.head(2)
                }
                logger.info(f"✅ {file_path}: {len(df):,}件, {len(df.columns)}カラム")
            except Exception as e:
                file_info[file_path] = {
                    'exists': True,
                    'error': str(e)
                }
                logger.error(f"❌ {file_path}: 読み込みエラー - {e}")
        else:
            file_info[file_path] = {'exists': False}
            logger.warning(f"⚠️ {file_path}: ファイルが存在しません")
    
    return file_info

def create_enhanced_comprehensive_data():
    """拡張版のデータ統合を実行"""
    logger.info("🔄 拡張版データ統合を開始...")
    
    try:
        # 個別にファイルを読み込み
        race_results = pd.read_pickle('output/race_results_2020.pickle')
        logger.info(f"レース結果: {len(race_results):,}件")
        
        race_info = pd.read_pickle('output/race_info_2020.pickle') 
        logger.info(f"レース情報: {len(race_info):,}件")
        
        # まずレース情報と結果を統合
        logger.info("1. レース情報と結果を統合中...")
        base_data = pd.merge(race_results, race_info, on='race_id', how='left')
        logger.info(f"基本統合後: {len(base_data):,}件")
        
        # 馬情報の統合
        if os.path.exists('output/horse_info_2020.pickle'):
            logger.info("2. 馬情報を統合中...")
            horse_info = pd.read_pickle('output/horse_info_2020.pickle')
            logger.info(f"馬情報: {len(horse_info):,}件")
            
            # horse_infoのインデックスを確認
            if horse_info.index.name == 'horse_id':
                horse_info = horse_info.reset_index()
            
            # 生年月日カラムの確認と処理
            birthday_col = None
            for col in ['生年月日', 'birthday', '生日']:
                if col in horse_info.columns:
                    birthday_col = col
                    break
            
            if birthday_col:
                horse_info['birthday'] = pd.to_datetime(horse_info[birthday_col], errors='coerce')
                logger.info(f"生年月日を変換: {horse_info['birthday'].notna().sum()}件")
                
                # 必要なカラムのみ選択
                horse_cols_to_merge = ['horse_id', 'birthday']
                if '馬名' in horse_info.columns:
                    horse_cols_to_merge.append('馬名')
                
                horse_info_filtered = horse_info[horse_cols_to_merge].dropna(subset=['horse_id'])
                
                # 馬情報をマージ
                base_data = pd.merge(base_data, horse_info_filtered, on='horse_id', how='left')
                logger.info(f"馬情報統合後: {len(base_data):,}件, birthday有り: {base_data['birthday'].notna().sum()}件")
            else:
                logger.warning("⚠️ 馬情報に生年月日カラムが見つかりません")
        
        # 馬過去成績の統合
        if os.path.exists('output/horse_results_2020.pickle'):
            logger.info("3. 馬過去成績を統合中...")
            horse_results = pd.read_pickle('output/horse_results_2020.pickle')
            logger.info(f"馬過去成績: {len(horse_results):,}件")
            
            # horse_resultsのインデックスを確認
            if horse_results.index.name == 'horse_id':
                horse_results = horse_results.reset_index()
            
            # 日付を変換
            date_cols = ['日付', 'date', '開催日']
            for col in date_cols:
                if col in horse_results.columns:
                    horse_results['race_date'] = pd.to_datetime(horse_results[col], errors='coerce')
                    break
            
            # 着順を数値化
            if '着順' in horse_results.columns:
                horse_results['着順_num'] = pd.to_numeric(horse_results['着順'], errors='coerce')
            
            # 馬ごとの統計を計算
            logger.info("馬ごとの過去成績統計を計算中...")
            
            stats_list = []
            for horse_id in base_data['horse_id'].unique():
                if pd.isna(horse_id):
                    continue
                    
                horse_races = horse_results[horse_results['horse_id'] == horse_id]
                
                if len(horse_races) > 0:
                    # 基本統計
                    total_races = len(horse_races)
                    
                    if '着順_num' in horse_races.columns:
                        valid_ranks = horse_races['着順_num'].dropna()
                        if len(valid_ranks) > 0:
                            avg_rank = valid_ranks.mean()
                            win_rate = (valid_ranks == 1).mean()
                            top3_rate = (valid_ranks <= 3).mean()
                        else:
                            avg_rank = 10.0
                            win_rate = 0.0
                            top3_rate = 0.0
                    else:
                        avg_rank = 10.0
                        win_rate = 0.0
                        top3_rate = 0.0
                    
                    # 最終レース日
                    if 'race_date' in horse_races.columns:
                        last_race_date = horse_races['race_date'].max()
                    else:
                        last_race_date = None
                    
                    stats_list.append({
                        'horse_id': horse_id,
                        'total_races': total_races,
                        'avg_rank': avg_rank,
                        'win_rate': win_rate,
                        'top3_rate': top3_rate,
                        'last_race_date': last_race_date
                    })
            
            # 統計をDataFrameに変換
            horse_stats = pd.DataFrame(stats_list)
            logger.info(f"馬統計データ: {len(horse_stats):,}件")
            
            # 統計をマージ
            base_data = pd.merge(base_data, horse_stats, on='horse_id', how='left')
            logger.info(f"過去成績統合後: {len(base_data):,}件")
            
            # 前走からの日数を計算
            if 'date' in base_data.columns and 'last_race_date' in base_data.columns:
                base_data['date'] = pd.to_datetime(base_data['date'], errors='coerce')
                base_data['days_since_last_race'] = (base_data['date'] - base_data['last_race_date']).dt.days
        
        # 欠損値の基本的な処理
        logger.info("4. 欠損値処理中...")
        
        if 'total_races' in base_data.columns:
            base_data['total_races'] = base_data['total_races'].fillna(0)
        if 'win_rate' in base_data.columns:
            base_data['win_rate'] = base_data['win_rate'].fillna(0)
        if 'avg_rank' in base_data.columns:
            base_data['avg_rank'] = base_data['avg_rank'].fillna(10)
        if 'top3_rate' in base_data.columns:
            base_data['top3_rate'] = base_data['top3_rate'].fillna(0)
        if 'days_since_last_race' in base_data.columns:
            base_data['days_since_last_race'] = base_data['days_since_last_race'].fillna(365)
        
        logger.info("✅ 拡張版データ統合完了")
        return base_data
        
    except Exception as e:
        logger.error(f"❌ データ統合エラー: {e}")
        import traceback
        traceback.print_exc()
        return None

def verify_integration_result(data):
    """統合結果の検証"""
    logger.info("🔍 統合結果を検証中...")
    
    required_columns = ['horse_id', 'date', 'birthday', 'total_races', 'win_rate', '着順']
    missing_columns = [col for col in required_columns if col not in data.columns]
    
    if missing_columns:
        logger.warning(f"⚠️ 不足しているカラム: {missing_columns}")
    else:
        logger.info("✅ 必要なカラムが全て存在します")
    
    # データ品質の確認
    logger.info(f"📊 データ品質:")
    logger.info(f"  ・総レコード数: {len(data):,}件")
    logger.info(f"  ・ユニーク馬数: {data['horse_id'].nunique():,}頭")
    
    if 'birthday' in data.columns:
        birthday_available = data['birthday'].notna().sum()
        logger.info(f"  ・生年月日有り: {birthday_available:,}件 ({birthday_available/len(data)*100:.1f}%)")
    
    if 'total_races' in data.columns:
        total_races_available = data['total_races'].notna().sum()
        logger.info(f"  ・過去成績有り: {total_races_available:,}件 ({total_races_available/len(data)*100:.1f}%)")
    
    # サンプルデータの表示
    logger.info(f"📋 データサンプル:")
    display_columns = [col for col in ['horse_id', 'date', 'birthday', 'total_races', 'win_rate', '着順'] 
                      if col in data.columns]
    print(data[display_columns].head())
    
    return len(missing_columns) == 0

def save_enhanced_data(data, filename="enhanced_comprehensive_data_2020.pickle"):
    """拡張データを保存"""
    try:
        data.to_pickle(filename)
        logger.info(f"✅ 拡張データを保存: {filename}")
        logger.info(f"   ・{len(data):,}件, {len(data.columns)}カラム")
        return True
    except Exception as e:
        logger.error(f"❌ データ保存エラー: {e}")
        return False

def main():
    """メイン実行"""
    logger.info("🔧 データ統合修正スクリプト開始")
    
    # 1. pickleファイルの確認
    file_info = check_pickle_files()
    
    # 2. 拡張版データ統合
    enhanced_data = create_enhanced_comprehensive_data()
    
    if enhanced_data is not None:
        # 3. 結果の検証
        is_complete = verify_integration_result(enhanced_data)
        
        # 4. データの保存
        if is_complete:
            save_enhanced_data(enhanced_data)
            logger.info("🎉 データ統合修正完了！")
        else:
            logger.warning("⚠️ 一部データが不足していますが、利用可能な形で保存します")
            save_enhanced_data(enhanced_data)
    else:
        logger.error("❌ データ統合に失敗しました")
    
    return enhanced_data

if __name__ == "__main__":
    result = main()