#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
レース当日の日付と馬の生年月日から生後日数を計算するデモ
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# プロジェクトパスの追加
sys.path.append('.')

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from core.features.calculators import FeatureCalculators

def demo_days_old_calculation():
    """生後日数計算のデモンストレーション"""
    logger.info("🐎 レース当日と生年月日による生後日数計算デモ")
    
    # サンプルデータの作成
    sample_data = pd.DataFrame({
        'horse_id': ['ディープインパクト', 'オルフェーヴル', 'ダイワスカーレット', 'ウオッカ', 'ジェンティルドンナ'],
        'race_date': [  # レース当日の日付
            '2023年10月29日',  # 天皇賞（秋）
            '2023年11月26日',  # ジャパンカップ
            '2023年12月24日',  # 有馬記念
            '2024年01月21日',  # 京都記念
            '2024年02月18日'   # フェブラリーS
        ],
        'birthday': [  # 馬の生年月日
            '2020年3月15日',   # 3歳馬
            '2019年4月22日',   # 4歳馬
            '2018年5月10日',   # 5歳馬
            '2017年2月28日',   # 6歳馬
            '2016年6月5日'     # 7歳馬
        ]
    })
    
    logger.info(f"📊 サンプルデータ: {len(sample_data)}頭")
    print("\n対象馬とレース情報:")
    print(sample_data[['horse_id', 'race_date', 'birthday']].to_string(index=False))
    
    return sample_data

def convert_japanese_date_to_datetime(date_str):
    """日本語日付文字列をdatetimeに変換"""
    if pd.isna(date_str):
        return pd.NaT
    
    try:
        # '2023年10月29日' → '2023-10-29'
        date_str = str(date_str)
        date_str = date_str.replace('年', '-').replace('月', '-').replace('日', '')
        return pd.to_datetime(date_str)
    except:
        return pd.NaT

def calculate_days_old_manual(data):
    """手動での生後日数計算"""
    logger.info("🧮 手動での生後日数計算...")
    
    result_data = data.copy()
    
    # 日付文字列をdatetimeに変換
    result_data['race_date_dt'] = result_data['race_date'].apply(convert_japanese_date_to_datetime)
    result_data['birthday_dt'] = result_data['birthday'].apply(convert_japanese_date_to_datetime)
    
    # 生後日数を計算
    result_data['days_old_manual'] = (result_data['race_date_dt'] - result_data['birthday_dt']).dt.days
    
    # 年齢も計算
    result_data['age_years_manual'] = result_data['days_old_manual'] / 365.25
    
    print("\n📈 手動計算結果:")
    display_cols = ['horse_id', 'race_date', 'birthday', 'days_old_manual', 'age_years_manual']
    print(result_data[display_cols].round(2).to_string(index=False))
    
    return result_data

def calculate_days_old_with_feature_calculator(data):
    """FeatureCalculatorを使った生後日数計算"""
    logger.info("🔧 FeatureCalculatorを使った生後日数計算...")
    
    calc = FeatureCalculators()
    result_data = data.copy()
    
    # 日付をdatetimeに変換
    result_data['race_date_dt'] = result_data['race_date'].apply(convert_japanese_date_to_datetime)
    result_data['birthday_dt'] = result_data['birthday'].apply(convert_japanese_date_to_datetime)
    
    # FeatureCalculatorで生後日数を計算
    result_data['days_old_calc'] = calc.calculate_days_old(
        result_data, 
        date_column='race_date_dt', 
        birthday_column='birthday_dt'
    )
    
    # FeatureCalculatorで年齢を計算
    result_data['age_years_calc'] = calc.calculate_age_years(
        result_data,
        date_column='race_date_dt',
        birthday_column='birthday_dt'
    )
    
    # FeatureCalculatorで月齢を計算
    result_data['age_months_calc'] = calc.calculate_age_months(
        result_data,
        date_column='race_date_dt',
        birthday_column='birthday_dt'
    )
    
    print("\n🔧 FeatureCalculator計算結果:")
    display_cols = ['horse_id', 'race_date', 'birthday', 'days_old_calc', 'age_years_calc', 'age_months_calc']
    print(result_data[display_cols].round(2).to_string(index=False))
    
    return result_data

def calculate_age_categories(data):
    """年齢カテゴリの計算"""
    logger.info("📊 年齢カテゴリの計算...")
    
    calc = FeatureCalculators()
    result_data = data.copy()
    
    # 年齢カテゴリを計算
    result_data['is_young'] = calc.calculate_age_category_young(
        result_data, 'race_date_dt', 'birthday_dt', threshold=3.0
    )
    
    result_data['is_prime'] = calc.calculate_age_category_prime(
        result_data, 'race_date_dt', 'birthday_dt', min_threshold=3.0, max_threshold=6.0
    )
    
    result_data['is_veteran'] = calc.calculate_age_category_veteran(
        result_data, 'race_date_dt', 'birthday_dt', threshold=6.0
    )
    
    print("\n🎯 年齢カテゴリ分類:")
    category_cols = ['horse_id', 'age_years_calc', 'is_young', 'is_prime', 'is_veteran']
    category_data = result_data[category_cols].copy()
    
    # カテゴリ名を追加
    def get_age_category(row):
        if row['is_young']:
            return '若駒(3歳以下)'
        elif row['is_prime']:
            return '盛期(4-6歳)'
        elif row['is_veteran']:
            return 'ベテラン(7歳以上)'
        else:
            return 'その他'
    
    category_data['age_category'] = category_data.apply(get_age_category, axis=1)
    
    print(category_data[['horse_id', 'age_years_calc', 'age_category']].round(2).to_string(index=False))
    
    return result_data

def real_data_example():
    """実際のデータでの生後日数計算例"""
    logger.info("📂 実際のデータでの生後日数計算例...")
    
    try:
        # 修正済みデータを読み込み
        comprehensive_data = pd.read_pickle('enhanced_comprehensive_data_2020.pickle')
        logger.info(f"✅ データ読み込み完了: {len(comprehensive_data):,}件")
        
        # サンプルとして最初の10件を使用
        sample_real_data = comprehensive_data.head(10).copy()
        
        # 日付列の確認
        if 'date' in sample_real_data.columns:
            sample_real_data['race_date_formatted'] = pd.to_datetime(sample_real_data['date']).dt.strftime('%Y年%m月%d日')
        
        if 'birthday' not in sample_real_data.columns or sample_real_data['birthday'].isna().all():
            # サンプル生年月日を生成
            logger.info("⚠️ 生年月日データがないため、サンプルデータを生成...")
            base_date = pd.to_datetime('2020-01-01')
            random_days = np.random.randint(365*2, 365*8, len(sample_real_data))
            sample_real_data['birthday'] = base_date - pd.to_timedelta(random_days, unit='D')
            sample_real_data['birthday_formatted'] = sample_real_data['birthday'].dt.strftime('%Y年%m月%d日')
        
        # FeatureCalculatorで計算
        calc = FeatureCalculators()
        
        sample_real_data['days_old_real'] = calc.calculate_days_old(
            sample_real_data, 'date', 'birthday'
        )
        
        sample_real_data['age_years_real'] = calc.calculate_age_years(
            sample_real_data, 'date', 'birthday'
        )
        
        print("\n🏁 実際のデータでの計算結果:")
        real_display_cols = ['horse_id', 'race_date_formatted', 'birthday_formatted', 'days_old_real', 'age_years_real']
        available_cols = [col for col in real_display_cols if col in sample_real_data.columns]
        
        if available_cols:
            print(sample_real_data[available_cols].round(2).to_string(index=False))
        else:
            print("表示可能なカラムが見つかりません")
            
        # 統計サマリー
        if 'days_old_real' in sample_real_data.columns:
            print(f"\n📊 生後日数統計:")
            print(f"  平均: {sample_real_data['days_old_real'].mean():.1f}日")
            print(f"  最小: {sample_real_data['days_old_real'].min():.0f}日")
            print(f"  最大: {sample_real_data['days_old_real'].max():.0f}日")
            print(f"  標準偏差: {sample_real_data['days_old_real'].std():.1f}日")
        
    except Exception as e:
        logger.error(f"❌ 実際のデータでの計算でエラー: {e}")
        import traceback
        traceback.print_exc()

def main():
    """メイン実行関数"""
    logger.info("🚀 生後日数計算デモ開始")
    
    # 1. サンプルデータの作成
    sample_data = demo_days_old_calculation()
    
    # 2. 手動計算
    manual_result = calculate_days_old_manual(sample_data)
    
    # 3. FeatureCalculatorによる計算
    calc_result = calculate_days_old_with_feature_calculator(sample_data)
    
    # 4. 年齢カテゴリの計算
    category_result = calculate_age_categories(calc_result)
    
    # 5. 実際のデータでの例
    real_data_example()
    
    # 6. 計算方法の比較
    logger.info("\n🔍 計算方法の検証:")
    if 'days_old_manual' in manual_result.columns and 'days_old_calc' in calc_result.columns:
        manual_days = manual_result['days_old_manual'].values
        calc_days = calc_result['days_old_calc'].values
        
        differences = np.abs(manual_days - calc_days)
        max_diff = np.max(differences)
        
        if max_diff == 0:
            print("✅ 手動計算とFeatureCalculatorの結果が完全に一致")
        else:
            print(f"⚠️ 最大差異: {max_diff}日")
    
    logger.info("✨ 生後日数計算デモ完了")

if __name__ == "__main__":
    main()