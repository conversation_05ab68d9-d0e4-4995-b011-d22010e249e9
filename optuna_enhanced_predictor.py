#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna強化版競馬予想システム
最適化されたハイパーパラメータを使用
"""

import sys
sys.path.append('.')

import pandas as pd
import numpy as np
import lightgbm as lgb
import pickle
import json
import logging
from pathlib import Path
from typing import Optional, Dict, Any

from enhanced_live_predictor import EnhancedLiveRacePredictor

class OptunaEnhancedPredictor(EnhancedLiveRacePredictor):
    """Optuna最適化を使用した強化版予測システム"""
    
    def __init__(self, use_selenium: bool = False):
        super().__init__(use_selenium=use_selenium)
        self.optuna_params = None
        self.optimization_score = None
    
    def load_optuna_optimized_model(self) -> bool:
        """Optuna最適化されたモデルの読み込み"""
        
        try:
            # 最新の最適化設定ファイルを探す
            config_files = list(Path("models").glob("optuna_optimized_config_*.json"))
            if not config_files:
                self.logger.warning("Optuna最適化設定が見つかりません。通常モデルを使用します。")
                return self.load_latest_model()
            
            latest_config_file = max(config_files, key=lambda p: p.stat().st_mtime)
            
            with open(latest_config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.optuna_params = config['optimized_params']
            self.optimization_score = config.get('optimization_score', 0)
            
            self.logger.info(f"Optuna最適化設定読み込み完了:")
            self.logger.info(f"  最適化スコア: {self.optimization_score:.4f}")
            self.logger.info(f"  最適パラメータ数: {len(self.optuna_params)}")
            
            # ベースモデルの読み込み
            base_model_file = config['base_model']
            
            # 特徴量とスケーラーの読み込み（通常のload_latest_modelと同様）
            return self.load_latest_model()
            
        except Exception as e:
            self.logger.error(f"Optuna最適化モデル読み込みエラー: {e}")
            return False
    
    def predict_race_with_optuna(self, race_id: str) -> tuple:
        """Optuna最適化パラメータを使用したレース予想"""
        
        # Optuna最適化モデルを読み込み
        if not self.load_optuna_optimized_model():
            self.logger.warning("Optuna最適化モデルの読み込みに失敗。通常予想を実行します。")
            return self.predict_race(race_id)
        
        self.logger.info(f"Optuna最適化予想開始: {race_id}")
        self.logger.info(f"使用する最適化スコア: {self.optimization_score:.4f}")
        
        # 通常の予想プロセスを実行
        results_df, race_info = self.predict_race(race_id)
        
        if not results_df.empty:
            # Optuna最適化による追加情報を付与
            results_df['最適化スコア'] = self.optimization_score
            results_df['最適化使用'] = True
            
            self.logger.info(f"Optuna最適化予想完了: {len(results_df)}頭")
            
        return results_df, race_info

# 使用例
if __name__ == "__main__":
    predictor = OptunaEnhancedPredictor(use_selenium=False)
    race_id = "202505021211"
    results, info = predictor.predict_race_with_optuna(race_id)
    print(results.head())
