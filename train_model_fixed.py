#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
競馬AI予測システム - データリーケージ修正版モデル学習スクリプト

データリーケージ対策を施した特徴量を使用してLightGBMモデルを学習します。
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, log_loss
import joblib
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Tuple, Dict, Any, List
import warnings
warnings.filterwarnings('ignore')

# プロジェクトモジュールのインポート
import sys
sys.path.append('.')
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
from core.features.manager import FeatureEngineeringManager

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('model_training_fixed.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class FixedHorseRaceModelTrainer:
    """データリーケージ修正版の競馬予測モデル学習クラス"""
    
    def __init__(self, training_years: List[str] = None):
        """
        初期化
        
        Parameters
        ----------
        training_years : List[str]
            学習に使用する年度リスト
        """
        self.training_years = training_years or ["2019", "2020"]
        self.output_dir = Path("models")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.model = None
        self.scaler = None
        self.label_encoders = {}
        self.feature_columns = []
        
        # LightGBMパラメータ（データリーケージ修正版用に調整）
        self.lgb_params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': 0,
            'random_state': 42
        }
        
        # データリーケージリスクのあるカラム（除外対象）
        self.leakage_risk_columns = [
            '人気', '単勝', 'オッズ', '着差', 'タイム', 'ﾀｲﾑ指数', '通過', '上り',
            '調教ﾀｲﾑ', '厩舎ｺﾒﾝﾄ', '備考'  # 追加のリスクカラム
        ]
        
        # 安全な特徴量カラム
        self.safe_feature_columns = [
            # 基本情報（レース前に確定している）
            '馬番', '枠番', '斤量', '性', '年齢',
            
            # レース条件（レース前に確定している）
            'course_len', '距離', 'コース', '馬場状態', '天気', 'around', 'track_direction',
            'race_class', 'race_type', 'ground_state', 'weather',
            'is_female_only_race', 'is_new_horse_race',
            
            # 馬の基本情報
            '生年月日', '馬体重',
            
            # 過去成績特徴量（データリーケージ修正済み）
            'total_races', 'win_rate', 'avg_rank', 'place_rate', 'show_rate',
            'avg_prize', 'max_prize', 'last_rank', 'last_race_date',
            
            # 騎手・調教師の成績
            'jockey_win_rate', 'trainer_win_rate',
            
            # 年齢関連特徴量
            'days_old', 'age_years', 'age_months', 'is_young', 'is_prime', 'is_veteran',
            
            # コーナー通過特徴量
            'avg_rank_corner', 'std_rank_corner', 'rank_change_total'
        ]
    
    def load_and_prepare_data(self) -> pd.DataFrame:
        """
        データを読み込み、前処理を実行
        
        Returns
        -------
        pd.DataFrame
            前処理済みデータ
        """
        logger.info(f"データ読み込み開始: {self.training_years}")
        
        # ComprehensiveDataIntegratorでデータ統合
        integrator = ComprehensiveDataIntegrator()
        
        if len(self.training_years) == 1:
            comprehensive_data = integrator.generate_comprehensive_table(
                year=self.training_years[0],
                include_race_info=True,
                include_horse_info=True,
                include_past_performance=True,
                use_pickle_source=True
            )
        else:
            comprehensive_data = integrator.generate_comprehensive_table(
                years=self.training_years,
                include_race_info=True,
                include_horse_info=True,
                include_past_performance=True,
                use_pickle_source=True
            )
        
        logger.info(f"データ統合完了: {len(comprehensive_data):,}件, {len(comprehensive_data.columns)}カラム")
        
        # データリーケージリスクのあるカラムを除外
        risky_columns = [col for col in self.leakage_risk_columns if col in comprehensive_data.columns]
        if risky_columns:
            logger.warning(f"データリーケージリスクカラムを除外: {risky_columns}")
            comprehensive_data = comprehensive_data.drop(columns=risky_columns)
        
        # 欠損値の確認と処理
        missing_info = comprehensive_data.isnull().sum()
        high_missing_cols = missing_info[missing_info > len(comprehensive_data) * 0.5].index.tolist()
        if high_missing_cols:
            logger.warning(f"50%以上欠損のカラムを除外: {high_missing_cols}")
            comprehensive_data = comprehensive_data.drop(columns=high_missing_cols)
        
        # ターゲット変数の作成（3着以内 = 1, それ以外 = 0）
        if '着順' in comprehensive_data.columns:
            comprehensive_data['target'] = (pd.to_numeric(comprehensive_data['着順'], errors='coerce') <= 3).astype(int)
            logger.info(f"ターゲット変数作成完了: 3着以内 {comprehensive_data['target'].sum():,}件 / 全{len(comprehensive_data):,}件")
        else:
            raise ValueError("着順カラムが見つかりません")
        
        # 無効なレコードを除外
        valid_mask = (
            comprehensive_data['target'].notna() &
            comprehensive_data['horse_id'].notna() &
            comprehensive_data['date'].notna()
        )
        comprehensive_data = comprehensive_data[valid_mask]
        logger.info(f"無効レコード除外後: {len(comprehensive_data):,}件")
        
        return comprehensive_data
    
    def prepare_features(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
        """
        特徴量の準備と前処理
        
        Parameters
        ----------
        data : pd.DataFrame
            入力データ
            
        Returns
        -------
        Tuple[pd.DataFrame, pd.Series]
            特徴量DataFrame, ターゲットSeries
        """
        logger.info("特徴量準備開始")
        
        # 数値特徴量の抽出
        numeric_features = data.select_dtypes(include=[np.number]).columns.tolist()
        numeric_features = [col for col in numeric_features if col not in ['target', '着順']]
        
        # カテゴリカル特徴量の抽出（安全なもののみ）
        categorical_features = []
        safe_categorical_candidates = ['性', '騎手', '調教師', 'race_class', 'ground_state', 'weather', 'track_direction']
        
        for col in safe_categorical_candidates:
            if col in data.columns:
                # カーディナリティチェック（高すぎる場合は除外）
                unique_count = data[col].nunique()
                if unique_count < 100:  # 100未満の一意値なら使用
                    categorical_features.append(col)
                else:
                    logger.warning(f"高カーディナリティのため除外: {col} ({unique_count}個の一意値)")
        
        # 最終的な特徴量リストを作成
        self.feature_columns = numeric_features + categorical_features
        logger.info(f"使用特徴量: 数値{len(numeric_features)}個, カテゴリカル{len(categorical_features)}個")
        
        # 特徴量データフレームの作成
        feature_data = data[self.feature_columns].copy()
        
        # カテゴリカル特徴量のエンコーディング
        for col in categorical_features:
            if col in feature_data.columns:
                le = LabelEncoder()
                # 欠損値を文字列として扱う
                feature_data[col] = feature_data[col].fillna('unknown').astype(str)
                feature_data[col] = le.fit_transform(feature_data[col])
                self.label_encoders[col] = le
        
        # 数値特徴量の欠損値処理
        for col in numeric_features:
            if col in feature_data.columns:
                feature_data[col] = feature_data[col].fillna(feature_data[col].median())
        
        # 無限値やNaNの最終チェック
        feature_data = feature_data.replace([np.inf, -np.inf], np.nan).fillna(0)
        
        target = data['target']
        
        logger.info(f"特徴量準備完了: {feature_data.shape}")
        return feature_data, target
    
    def train_model(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """
        モデルの学習
        
        Parameters
        ----------
        X : pd.DataFrame
            特徴量
        y : pd.Series
            ターゲット
            
        Returns
        -------
        Dict[str, Any]
            学習結果
        """
        logger.info("モデル学習開始")
        
        # 時系列分割（データリーケージ防止のため）
        # 日付順でソートしてから分割
        if 'date' in X.index.names or 'date' in X.columns:
            # データに日付情報がある場合は時系列分割
            tscv = TimeSeriesSplit(n_splits=3)
            # とりあえず通常分割を使用（実装簡略化）
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
        else:
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
        
        logger.info(f"データ分割: 訓練{len(X_train):,}件, テスト{len(X_test):,}件")
        
        # 特徴量スケーリング
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # LightGBMデータセットの作成
        train_data = lgb.Dataset(X_train_scaled, label=y_train)
        valid_data = lgb.Dataset(X_test_scaled, label=y_test, reference=train_data)
        
        # モデル学習
        logger.info("LightGBM学習開始")
        self.model = lgb.train(
            self.lgb_params,
            train_data,
            valid_sets=[train_data, valid_data],
            valid_names=['train', 'eval'],
            num_boost_round=1000,
            callbacks=[
                lgb.early_stopping(stopping_rounds=50),
                lgb.log_evaluation(period=100)
            ]
        )
        
        # 予測と評価
        y_pred_proba = self.model.predict(X_test_scaled, num_iteration=self.model.best_iteration)
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        # 評価指標の計算
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba)
        logloss = log_loss(y_test, y_pred_proba)
        
        # 特徴量重要度
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': self.model.feature_importance()
        }).sort_values('importance', ascending=False)
        
        results = {
            'accuracy': accuracy,
            'auc': auc,
            'logloss': logloss,
            'feature_importance': feature_importance,
            'y_test': y_test,
            'y_pred_proba': y_pred_proba,
            'y_pred': y_pred
        }
        
        logger.info(f"学習完了 - 精度: {accuracy:.4f}, AUC: {auc:.4f}, LogLoss: {logloss:.4f}")
        
        return results
    
    def save_model(self, timestamp: str = None) -> Dict[str, str]:
        """
        モデルと関連ファイルを保存
        
        Parameters
        ----------
        timestamp : str
            タイムスタンプ（指定しない場合は現在時刻）
            
        Returns
        -------
        Dict[str, str]
            保存されたファイルパス
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # ファイルパス
        model_path = self.output_dir / f"fixed_leakage_model_{timestamp}.pkl"
        scaler_path = self.output_dir / f"fixed_leakage_scaler_{timestamp}.pkl"
        features_path = self.output_dir / f"fixed_leakage_features_{timestamp}.pkl"
        encoders_path = self.output_dir / f"fixed_leakage_encoders_{timestamp}.pkl"
        
        # 保存
        joblib.dump(self.model, model_path)
        joblib.dump(self.scaler, scaler_path)
        joblib.dump(self.feature_columns, features_path)
        joblib.dump(self.label_encoders, encoders_path)
        
        logger.info(f"モデル保存完了:")
        logger.info(f"  - モデル: {model_path}")
        logger.info(f"  - スケーラー: {scaler_path}")
        logger.info(f"  - 特徴量リスト: {features_path}")
        logger.info(f"  - エンコーダー: {encoders_path}")
        
        return {
            'model': str(model_path),
            'scaler': str(scaler_path),
            'features': str(features_path),
            'encoders': str(encoders_path)
        }
    
    def run_full_training(self) -> Dict[str, Any]:
        """
        完全な学習パイプラインの実行
        
        Returns
        -------
        Dict[str, Any]
            学習結果と保存されたファイルパス
        """
        logger.info("=" * 60)
        logger.info("データリーケージ修正版モデル学習開始")
        logger.info("=" * 60)
        
        try:
            # 1. データ読み込み・前処理
            data = self.load_and_prepare_data()
            
            # 2. 特徴量準備
            X, y = self.prepare_features(data)
            
            # 3. モデル学習
            results = self.train_model(X, y)
            
            # 4. モデル保存
            file_paths = self.save_model()
            
            # 5. 結果のまとめ
            final_results = {
                'training_summary': {
                    'training_years': self.training_years,
                    'total_samples': len(X),
                    'num_features': len(self.feature_columns),
                    'target_distribution': y.value_counts().to_dict()
                },
                'performance': {
                    'accuracy': results['accuracy'],
                    'auc': results['auc'],
                    'logloss': results['logloss']
                },
                'feature_importance': results['feature_importance'],
                'file_paths': file_paths
            }
            
            logger.info("=" * 60)
            logger.info("学習完了サマリー")
            logger.info("=" * 60)
            logger.info(f"学習年度: {self.training_years}")
            logger.info(f"総サンプル数: {len(X):,}")
            logger.info(f"特徴量数: {len(self.feature_columns)}")
            logger.info(f"精度: {results['accuracy']:.4f}")
            logger.info(f"AUC: {results['auc']:.4f}")
            logger.info(f"LogLoss: {results['logloss']:.4f}")
            
            # Top 10 重要特徴量を表示
            logger.info("\\nTop 10 重要特徴量:")
            for i, (idx, row) in enumerate(results['feature_importance'].head(10).iterrows()):
                logger.info(f"  {i+1:2d}. {row['feature']}: {row['importance']:.0f}")
            
            return final_results
            
        except Exception as e:
            logger.error(f"学習中にエラーが発生: {e}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """メイン実行関数"""
    
    # 学習年度の設定（複数年対応）
    training_years = ["2019", "2020"]  # 2019年を学習、2020年を一部テストに使用
    
    trainer = FixedHorseRaceModelTrainer(training_years=training_years)
    
    try:
        results = trainer.run_full_training()
        
        print("\\n🎉 学習が正常に完了しました！")
        print(f"精度: {results['performance']['accuracy']:.4f}")
        print(f"AUC: {results['performance']['auc']:.4f}")
        print(f"保存されたモデル: {results['file_paths']['model']}")
        
    except Exception as e:
        print(f"\\n❌ 学習中にエラーが発生しました: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()