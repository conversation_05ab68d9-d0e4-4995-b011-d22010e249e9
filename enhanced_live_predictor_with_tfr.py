#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TensorFlow Ranking統合版 最新馬戦績スクレイピング機能付き強化版実際のレース予測システム
- LightGBM + TensorFlow Ranking ハイブリッド予測
- リアルタイム出馬表取得
- 最新馬戦績のリアルタイムスクレイピング機能
- 適応的モデル選択
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any
import warnings
warnings.filterwarnings('ignore')

# パス追加
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# ハイブリッド予測システム
try:
    from core.prediction.hybrid_predictor import HybridRacePredictionSystem
    HYBRID_AVAILABLE = True
except ImportError:
    HYBRID_AVAILABLE = False

# 既存システムのフォールバック
from enhanced_live_predictor_with_scraping import EnhancedLiveRacePredictorWithScraping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedLiveRacePredictorWithTFR:
    """TensorFlow Ranking統合版強化ライブ予測システム"""
    
    def __init__(self, model_dir="models", use_selenium=False, enable_live_scraping=True, 
                 enable_tfr=True, prediction_mode="auto"):
        """
        初期化
        
        Parameters
        ----------
        model_dir : str
            学習済みモデルのディレクトリ
        use_selenium : bool
            Seleniumを使用するかどうか
        enable_live_scraping : bool
            最新馬戦績のリアルタイムスクレイピングを有効にするか
        enable_tfr : bool
            TensorFlow Rankingを有効にするか
        prediction_mode : str
            予測モード ("auto", "lightgbm", "tensorflow_ranking", "ensemble")
        """
        self.model_dir = Path(model_dir)
        self.use_selenium = use_selenium
        self.enable_live_scraping = enable_live_scraping
        self.enable_tfr = enable_tfr and HYBRID_AVAILABLE
        self.prediction_mode = prediction_mode
        
        # ハイブリッドシステム
        self.hybrid_system = None
        
        # フォールバックシステム
        self.fallback_system = None
        
        # 初期化
        self._initialize_systems()
        
        logger.info(f"TFR統合予測システム初期化完了")
        logger.info(f"ハイブリッドシステム利用可能: {HYBRID_AVAILABLE}")
        logger.info(f"TensorFlow Ranking有効: {self.enable_tfr}")
        logger.info(f"予測モード: {self.prediction_mode}")
    
    def _initialize_systems(self):
        """予測システムを初期化"""
        try:
            # ハイブリッドシステム
            if self.enable_tfr and HYBRID_AVAILABLE:
                config = self._get_hybrid_config()
                self.hybrid_system = HybridRacePredictionSystem(
                    model_dir=str(self.model_dir),
                    config=config
                )
                logger.info("ハイブリッド予測システム初期化完了")
            
            # フォールバックシステム（必須）
            self.fallback_system = EnhancedLiveRacePredictorWithScraping(
                model_dir=str(self.model_dir),
                use_selenium=self.use_selenium,
                enable_live_scraping=self.enable_live_scraping
            )
            logger.info("フォールバック予測システム初期化完了")
            
        except Exception as e:
            logger.error(f"システム初期化エラー: {e}")
            # 最低限フォールバックシステムは確保
            if not self.fallback_system:
                self.fallback_system = EnhancedLiveRacePredictorWithScraping(
                    model_dir=str(self.model_dir),
                    use_selenium=False,
                    enable_live_scraping=False
                )
    
    def _get_hybrid_config(self) -> Dict[str, Any]:
        """ハイブリッドシステム用設定を生成"""
        return {
            'ensemble': {
                'lgb_weight': 0.65,  # LightGBMを少し重視（安定性）
                'tfr_weight': 0.35,  # TFRは補助的役割
                'enable_dynamic_weights': True
            },
            'selection': {
                'data_completeness_threshold': 0.7,  # より厳格な閾値
                'min_field_size_for_tfr': 10,  # 10頭以上でTFR使用
                'force_tfr_for_grades': ['G1', 'G2', 'G3']  # 重賞では必ずTFR
            },
            'missing_values': {
                'lgb_strategy': 'native',
                'tfr_strategy': 'median_impute'
            }
        }
    
    def predict_race(self, race_id: str) -> tuple:
        """
        レース予測を実行（TFR統合版）
        
        Parameters
        ----------
        race_id : str
            レースID
            
        Returns
        -------
        tuple
            (予測結果DataFrame, レース情報辞書)
        """
        try:
            logger.info(f"TFR統合版レース予測開始: {race_id}")
            
            # 予測モード判定
            actual_mode = self._determine_prediction_mode(race_id)
            logger.info(f"使用予測モード: {actual_mode}")
            
            if actual_mode in ["auto", "ensemble"] and self.hybrid_system:
                # ハイブリッド予測
                return self._predict_with_hybrid(race_id)
            elif actual_mode == "tensorflow_ranking" and self.hybrid_system:
                # TFR単体予測
                return self._predict_with_tfr_only(race_id)
            else:
                # LightGBM予測（フォールバック含む）
                return self._predict_with_lightgbm_only(race_id)
                
        except Exception as e:
            logger.error(f"TFR統合版予測エラー: {e}")
            # 最終フォールバック
            return self._predict_with_fallback(race_id)
    
    def _determine_prediction_mode(self, race_id: str) -> str:
        """予測モードを決定"""
        if self.prediction_mode != "auto":
            return self.prediction_mode
        
        # レースIDから情報を推定
        try:
            # 競馬場コードで判定
            venue_code = race_id[4:6] if len(race_id) >= 6 else "01"
            race_num = int(race_id[10:12]) if len(race_id) >= 12 else 1
            
            # 中央競馬の重要レース推定
            if venue_code in ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10"]:
                # メインレース（後半のレース）でハイブリッド使用
                if race_num >= 9:  # 9R以降
                    return "ensemble"
                else:
                    return "lightgbm"
            else:
                # 地方競馬はLightGBMのみ
                return "lightgbm"
                
        except Exception:
            return "lightgbm"
    
    def _predict_with_hybrid(self, race_id: str) -> tuple:
        """ハイブリッド予測"""
        try:
            results, analysis = self.hybrid_system.predict_hybrid(race_id)
            
            if not results.empty:
                # レース情報を構築
                race_info = {
                    'race_id': race_id,
                    'prediction_method': 'hybrid',
                    'models_used': analysis.get('model', 'ensemble'),
                    'analysis': analysis
                }
                
                # 重みを表示用に追加
                if 'weights' in analysis:
                    race_info.update(analysis['weights'])
                
                logger.info(f"ハイブリッド予測完了: {len(results)}頭")
                return results, race_info
            else:
                raise RuntimeError("ハイブリッド予測結果が空です")
                
        except Exception as e:
            logger.warning(f"ハイブリッド予測失敗: {e}、フォールバックを使用")
            return self._predict_with_fallback(race_id)
    
    def _predict_with_tfr_only(self, race_id: str) -> tuple:
        """TensorFlow Ranking単体予測"""
        try:
            if not self.hybrid_system:
                raise RuntimeError("ハイブリッドシステムが利用できません")
            
            # 出馬表データ取得
            race_data = self.hybrid_system.lgb_predictor.scrape_race_card_requests(race_id)
            if race_data.empty:
                raise ValueError("出馬表データが取得できませんでした")
            
            # レース情報構築
            race_info = {
                'race_id': race_id,
                'course_len': self.hybrid_system.lgb_predictor._get_smart_default_distance(race_id),
                **self.hybrid_system.lgb_predictor._get_smart_default_conditions(race_id)
            }
            
            # TFR予測
            scores, analysis = self.hybrid_system.predict_with_tfr(race_data, race_info)
            
            # 結果フォーマット
            results = self.hybrid_system._format_prediction_results(race_data, scores, analysis)
            
            race_info.update({
                'prediction_method': 'tensorflow_ranking',
                'analysis': analysis
            })
            
            logger.info(f"TFR単体予測完了: {len(results)}頭")
            return results, race_info
            
        except Exception as e:
            logger.warning(f"TFR単体予測失敗: {e}、フォールバックを使用")
            return self._predict_with_fallback(race_id)
    
    def _predict_with_lightgbm_only(self, race_id: str) -> tuple:
        """LightGBM単体予測"""
        try:
            if self.hybrid_system and self.hybrid_system.lgb_predictor:
                # ハイブリッドシステム内のLightGBMを使用
                results, race_info = self.hybrid_system.lgb_predictor.predict_race(race_id)
                race_info['prediction_method'] = 'lightgbm'
                logger.info(f"LightGBM単体予測完了: {len(results)}頭")
                return results, race_info
            else:
                # フォールバックシステムを使用
                return self._predict_with_fallback(race_id)
                
        except Exception as e:
            logger.warning(f"LightGBM単体予測失敗: {e}、フォールバックを使用")
            return self._predict_with_fallback(race_id)
    
    def _predict_with_fallback(self, race_id: str) -> tuple:
        """フォールバック予測"""
        try:
            if not self.fallback_system:
                raise RuntimeError("フォールバックシステムが利用できません")
            
            results, race_info = self.fallback_system.predict_race(race_id)
            race_info['prediction_method'] = 'fallback'
            
            logger.info(f"フォールバック予測完了: {len(results)}頭")
            return results, race_info
            
        except Exception as e:
            logger.error(f"フォールバック予測も失敗: {e}")
            return pd.DataFrame(), {'error': str(e), 'prediction_method': 'failed'}
    
    def display_prediction_results(self, results: pd.DataFrame, race_info: Dict[str, Any]):
        """予測結果を表示（TFR統合版）"""
        if results.empty:
            print("[エラー] 予測結果がありません")
            return
        
        print("\n" + "="*95)
        print("[競馬AI] TensorFlow Ranking統合版予測結果")
        print("="*95)
        
        # 基本情報
        print(f"レースID: {race_info.get('race_id', 'N/A')}")
        print(f"距離: {race_info.get('course_len', 'N/A')}m")
        print(f"コース: {race_info.get('race_type', 'N/A')} {race_info.get('track_direction', 'N/A')}回り")
        print(f"馬場: {race_info.get('ground_state', 'N/A')} / 天気: {race_info.get('weather', 'N/A')}")
        print(f"予測時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 予測方法
        method = race_info.get('prediction_method', 'unknown')
        print(f"予測方法: {method}")
        
        if method == 'hybrid':
            models = race_info.get('models_used', 'unknown')
            print(f"使用モデル: {models}")
            if 'lightgbm' in race_info and 'tensorflow_ranking' in race_info:
                lgb_weight = race_info.get('lightgbm', 0)
                tfr_weight = race_info.get('tensorflow_ranking', 0)
                print(f"重み: LightGBM={lgb_weight:.2f}, TensorFlow Ranking={tfr_weight:.2f}")
        
        # 勝率合計確認
        total_win_rate = results['勝率'].sum() if '勝率' in results.columns else 0
        
        print(f"\n[予測結果] (予測順位順) - 出馬頭数: {len(results)}頭")
        print(f"勝率合計: {total_win_rate:.1f}% (正規化済み)")
        print("-" * 95)
        
        # 結果表示
        for i, (_, row) in enumerate(results.iterrows()):
            # データ検証
            枠番 = row.get('枠番', 0)
            馬番 = row.get('馬番', 0)
            horse_name = row.get('馬名', 'N/A')
            
            if (枠番 == 0 or 馬番 == 0 or 
                horse_name in ['N/A', '', '0', '--'] or 
                pd.isna(horse_name)):
                continue
            
            性齢 = row.get('性齢', 'N/A')
            if pd.isna(性齢):
                性齢 = 'N/A'
            斤量 = row.get('斤量', 0)
            予測順位 = row.get('予測順位', 0)
            勝率 = row.get('勝率', 0)
            確率 = row.get('3着以内確率', 0)
            
            # モデル情報があれば表示
            model_info = ""
            if '使用モデル' in row:
                model_info = f" [{row['使用モデル']}]"
            
            try:
                print(f"{予測順位:2.0f}位 {枠番:2.0f}-{馬番:2.0f} {str(horse_name):12s} "
                      f"{str(性齢):4s} {斤量:4.1f}kg "
                      f"勝率:{勝率:5.1f}% 3着内:{確率:5.1f}%{model_info}")
            except:
                print(f"{int(予測順位)}位 {int(枠番)}-{int(馬番)} {str(horse_name)} "
                      f"{str(性齢)} {float(斤量):.1f}kg "
                      f"勝率:{float(勝率):.1f}% 3着内:{float(確率):.1f}%{model_info}")
        
        # 買い目候補
        self._display_betting_suggestions(results)
        
        # 注意事項
        print("\n[システム情報]")
        print("-" * 50)
        print("・TensorFlow Ranking統合版予測システム")
        print("・LightGBM + TensorFlow Ranking のハイブリッド予測")
        print("・リアルタイム出馬表取得 + 最新馬戦績スクレイピング")
        print("・適応的モデル選択（レース条件に応じて最適化）")
        print("・投資は自己責任で行ってください")
        
        print("\n" + "="*95)
    
    def _display_betting_suggestions(self, results: pd.DataFrame):
        """買い目候補を表示"""
        print("\n[買い目候補]")
        print("-" * 50)
        
        if len(results) >= 3:
            top3 = results.head(3)
            print("[3連複候補]")
            try:
                print(f"   {top3.iloc[0].get('枠番', 0):.0f}-{top3.iloc[0].get('馬番', 0):.0f}-"
                      f"{top3.iloc[1].get('枠番', 0):.0f}-{top3.iloc[1].get('馬番', 0):.0f}-"
                      f"{top3.iloc[2].get('枠番', 0):.0f}-{top3.iloc[2].get('馬番', 0):.0f}")
            except:
                print("   データが不完全です")
        
        if len(results) >= 1:
            winner_candidate = results.iloc[0]
            try:
                horse_name = winner_candidate.get('馬名', 'N/A')
                if pd.isna(horse_name):
                    horse_name = 'N/A'
                print(f"\n[単勝候補] {winner_candidate.get('枠番', 0):.0f}-{winner_candidate.get('馬番', 0):.0f} {horse_name}")
                print(f"   勝率: {winner_candidate.get('勝率', 0):.1f}%")
                print(f"   3着以内確率: {winner_candidate.get('3着以内確率', 0):.1f}%")
            except:
                print("\n[単勝候補] データが不完全です")

def main():
    """メイン実行関数"""
    try:
        print("[競馬AI] TensorFlow Ranking統合版予測システム")
        print("LightGBM + TensorFlow Ranking ハイブリッド予測")
        
        # システム初期化
        predictor = EnhancedLiveRacePredictorWithTFR(
            use_selenium=False,
            enable_live_scraping=True,
            enable_tfr=True,
            prediction_mode="auto"  # 自動選択
        )
        
        # レースID入力
        try:
            race_id = input("\nレースIDを入力してください (例: 202412080101): ").strip()
        except EOFError:
            print("非対話モードでテスト実行中...")
            race_id = "202406080101"  # テスト用レースID
        
        if not race_id:
            print("レースIDが入力されませんでした。デモを実行します。")
            race_id = "202406080101"
        
        print(f"\nTFR統合版レース予測を実行中: {race_id}")
        
        # 予測実行
        results, race_info = predictor.predict_race(race_id)
        
        if not results.empty:
            predictor.display_prediction_results(results, race_info)
        else:
            print("[エラー] 予測に失敗しました")
            print("・レースIDが存在しない可能性があります")
            print("・ネットワーク接続を確認してください")
            print("・システムの設定を確認してください")
        
    except KeyboardInterrupt:
        print("\n\n処理が中断されました")
    except Exception as e:
        logger.error(f"メイン実行エラー: {e}")
        print(f"[エラー] エラーが発生しました: {e}")

if __name__ == "__main__":
    main()