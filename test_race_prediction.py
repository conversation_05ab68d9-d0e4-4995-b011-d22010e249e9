#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
レース予想システムのテストスクリプト

実際のレースIDまたはテストデータで予想システムの動作を確認します。
"""

import pandas as pd
import numpy as np
import sys
import logging
from pathlib import Path

# プロジェクトモジュールのインポート
sys.path.append('.')
from enhanced_live_predictor import EnhancedLiveRacePredictor

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_race_data():
    """テスト用のレースデータを作成"""
    test_data = pd.DataFrame({
        '枠番': [1, 2, 3, 4, 5, 6, 7, 8],
        '馬番': [1, 2, 3, 4, 5, 6, 7, 8],
        '馬名': ['テストホース1', 'テストホース2', 'テストホース3', 'テストホース4', 
                'テストホース5', 'テストホース6', 'テストホース7', 'テストホース8'],
        '性齢': ['牡4', '牝3', '牡5', '牝4', 'セ6', '牡3', '牝5', '牡4'],
        '斤量': [57.0, 54.0, 57.0, 54.0, 57.0, 57.0, 54.0, 57.0],
        'course_len': [1600] * 8,
        'race_type': ['芝'] * 8,
        'ground_state': ['良'] * 8,
        'weather': ['晴'] * 8,
        'track_direction': ['右'] * 8,
        'race_class': ['3勝クラス'] * 8
    })
    
    # レース情報
    race_info = {
        'race_id': 'TEST_RACE_001',
        'course_len': 1600,
        'race_type': '芝',
        'ground_state': '良',
        'weather': '晴',
        'track_direction': '右'
    }
    
    return test_data, race_info

def test_prediction_system():
    """予想システムの動作テスト"""
    try:
        print("=" * 60)
        print("レース予想システム 動作テスト")
        print("=" * 60)
        
        # 予想システム初期化
        predictor = EnhancedLiveRacePredictor(use_selenium=False)  # テスト用にSeleniumを無効化
        
        # モデル読み込みテスト
        print("\n1. モデル読み込みテスト...")
        if predictor.load_latest_model():
            print("✓ モデル読み込み成功")
            print(f"  - 特徴量数: {len(predictor.features)}")
            print(f"  - エンコーダー数: {len(predictor.label_encoders) if hasattr(predictor, 'label_encoders') else 0}")
        else:
            print("✗ モデル読み込み失敗")
            return False
        
        # テストデータ作成
        print("\n2. テストデータ作成...")
        test_race_data, race_info = create_test_race_data()
        print(f"✓ テストレース作成: {len(test_race_data)}頭")
        
        # 特徴量準備テスト
        print("\n3. 特徴量準備テスト...")
        X, processed_data = predictor.prepare_prediction_features(test_race_data, race_info)
        if not X.empty:
            print(f"✓ 特徴量準備成功: {X.shape}")
            print(f"  - 特徴量カラム数: {len(X.columns)}")
            print(f"  - データ行数: {len(X)}")
        else:
            print("✗ 特徴量準備失敗")
            return False
        
        # 予想実行テスト
        print("\n4. 予想実行テスト...")
        try:
            # スケーリング
            X_scaled = predictor.scaler.transform(X)
            print("✓ スケーリング成功")
            
            # 予想
            predictions = predictor.model.predict(X_scaled, num_iteration=predictor.model.best_iteration)
            print(f"✓ 予想実行成功: {len(predictions)}頭の予想完了")
            
            # 結果整理
            results = test_race_data[['枠番', '馬番', '馬名', '性齢', '斤量']].copy()
            results['予測スコア'] = predictions
            results['予測順位'] = results['予測スコア'].rank(ascending=False, method='first').astype(int)
            
            # 勝率を正規化
            total_score = predictions.sum()
            if total_score > 0:
                normalized_proba = (predictions / total_score) * 100
                results['勝率'] = normalized_proba.round(1)
            else:
                results['勝率'] = (np.ones(len(predictions)) / len(predictions) * 100).round(1)
            
            results['3着以内確率'] = (predictions * 100).round(1)
            results = results.sort_values('予測順位')
            
            print("✓ 結果整理成功")
            
            # 結果表示
            print("\n5. 予想結果表示...")
            print("-" * 60)
            print(f"{'順位':>4} {'枠':>2} {'馬':>2} {'馬名':>12} {'性齢':>4} {'斤量':>6} {'勝率':>8} {'3着内':>8}")
            print("-" * 60)
            
            for _, row in results.iterrows():
                print(f"{row['予測順位']:>4} {row['枠番']:>2.0f} {row['馬番']:>2.0f} "
                      f"{row['馬名'][:12]:>12} {row['性齢']:>4} {row['斤量']:>6.1f} "
                      f"{row['勝率']:>7.1f}% {row['3着以内確率']:>7.1f}%")
            
            print("-" * 60)
            print(f"勝率合計: {results['勝率'].sum():.1f}%")
            
            # 推奨
            winner = results.iloc[0]
            print(f"\n推奨:")
            print(f"  単勝候補: {winner['枠番']:.0f}-{winner['馬番']:.0f} {winner['馬名']} (勝率:{winner['勝率']:.1f}%)")
            
            if len(results) >= 3:
                top3 = results.head(3)
                print(f"  3連複候補: " + 
                      f"{top3.iloc[0]['馬番']:.0f}-{top3.iloc[1]['馬番']:.0f}-{top3.iloc[2]['馬番']:.0f}")
            
            return True
            
        except Exception as e:
            print(f"✗ 予想実行失敗: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"テスト実行エラー: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_real_race_id():
    """実際のレースIDでのテスト（2025年のレース）"""
    predictor = EnhancedLiveRacePredictor(use_selenium=False)
    
    # 2025年のレースIDを試す（より可能性の高いレースID）
    test_race_ids = [
        "202501050101",  # 2025年1月5日 1R
        "202501050201",  # 2025年1月5日 2R  
        "202501040101",  # 2025年1月4日 1R
        "202412290101",  # 2024年12月29日 1R（有馬記念週）
        "202412280101",  # 2024年12月28日 1R
    ]
    
    print("\n実際のレースIDでのテスト...")
    for race_id in test_race_ids:
        print(f"\nテスト中: {race_id}")
        try:
            results, race_info = predictor.predict_race(race_id)
            if not results.empty:
                print(f"✓ レース{race_id}で予想成功!")
                print(f"  出馬頭数: {len(results)}頭")
                predictor.display_prediction_results(results, race_info)
                return True
            else:
                print(f"  レース{race_id}は存在しないか、データが取得できませんでした")
        except Exception as e:
            print(f"  レース{race_id}でエラー: {e}")
    
    print("実際のレースIDでのテストはすべて失敗しました")
    return False

def main():
    """メイン実行関数"""
    print("レース予想システム 総合テスト")
    
    # 1. システム動作テスト（テストデータ）
    print("\n=== 1. システム動作テスト（テストデータ使用） ===")
    system_test_ok = test_prediction_system()
    
    # 2. 実際のレースIDテスト
    print("\n=== 2. 実際のレースIDテスト ===")
    real_race_test_ok = test_with_real_race_id()
    
    # 結果サマリー
    print("\n" + "=" * 60)
    print("テスト結果サマリー")
    print("=" * 60)
    print(f"システム動作テスト: {'✓ 成功' if system_test_ok else '✗ 失敗'}")
    print(f"実際のレースIDテスト: {'✓ 成功' if real_race_test_ok else '✗ 失敗'}")
    
    if system_test_ok:
        print("\n✓ 予想システムは正常に動作しています！")
        print("  - データリーケージ修正版モデルを正常に使用")
        print("  - 特徴量準備とスケーリングが正常動作")
        print("  - 予想結果の表示が正常動作")
        
        if not real_race_test_ok:
            print("\n注意: 実際のレースデータが取得できませんでした")
            print("  - ネットワーク接続を確認してください")
            print("  - より新しいレースIDを試してください")
            print("  - システム自体は正常に動作しています")
    else:
        print("\n✗ 予想システムに問題があります")
        print("  - ログを確認して問題を修正してください")

if __name__ == "__main__":
    main()