#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TensorFlow Ranking + Optuna 統合分析システム

このシステムは学習済みTensorFlow Rankingモデルと
Optuna最適化結果を統合して包括的な分析を提供します。
"""

import sys
sys.path.append('.')

import pandas as pd
import numpy as np
import tensorflow as tf
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import warnings

# 独自モジュール
from core.analysis.tensorflow_ranking_analyzer import TensorFlowRankingAnalyzer

warnings.filterwarnings('ignore')

class TensorFlowRankingAnalysisIntegration:
    """TensorFlow Ranking + Optuna 統合分析システム"""
    
    def __init__(self, 
                 model_path: str = None,
                 metadata_path: str = None,
                 optuna_results_path: str = None,
                 output_dir: str = "integrated_analysis_output"):
        """
        初期化
        
        Parameters
        ----------
        model_path : str
            学習済みモデルのパス
        metadata_path : str
            モデルメタデータのパス
        optuna_results_path : str
            Optuna結果ファイルのパス
        output_dir : str
            出力ディレクトリ
        """
        self.model_path = model_path
        self.metadata_path = metadata_path
        self.optuna_results_path = optuna_results_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # コンポーネント初期化
        self.logger = self._setup_logging()
        self.model = None
        self.metadata = {}
        self.optuna_results = {}
        self.analyzer = None
        
        # 自動ロード
        self._load_components()
    
    def _setup_logging(self) -> logging.Logger:
        """ログ設定"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _load_components(self):
        """各コンポーネントのロード"""
        
        try:
            # モデル・メタデータロード
            if self.model_path and Path(self.model_path).exists():
                self.model = tf.keras.models.load_model(self.model_path)
                self.logger.info(f"モデルロード完了: {self.model_path}")
            
            if self.metadata_path and Path(self.metadata_path).exists():
                with open(self.metadata_path, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
                self.logger.info(f"メタデータロード完了: {self.metadata_path}")
            
            if self.optuna_results_path and Path(self.optuna_results_path).exists():
                with open(self.optuna_results_path, 'r', encoding='utf-8') as f:
                    self.optuna_results = json.load(f)
                self.logger.info(f"Optuna結果ロード完了: {self.optuna_results_path}")
            
            # アナライザー初期化
            feature_names = self.metadata.get('feature_names', [])
            self.analyzer = TensorFlowRankingAnalyzer(
                model=self.model,
                feature_names=feature_names,
                optuna_results=self.optuna_results,
                output_dir=str(self.output_dir / "detailed_analysis")
            )
            
        except Exception as e:
            self.logger.error(f"コンポーネントロードエラー: {e}")
    
    def auto_detect_latest_results(self, results_dir: str = "tfr_optuna_quick_results") -> bool:
        """最新結果の自動検出とロード"""
        
        try:
            results_path = Path(results_dir)
            if not results_path.exists():
                # 他の結果ディレクトリもチェック
                for alt_dir in ["tfr_optuna_fixed_results", "tfr_optuna_results"]:
                    if Path(alt_dir).exists():
                        results_path = Path(alt_dir)
                        break
            
            if not results_path.exists():
                self.logger.warning(f"結果ディレクトリが見つかりません: {results_dir}")
                return False
            
            # 最新のメタデータファイルを検索
            metadata_files = list(results_path.glob("*metadata*.json"))
            if metadata_files:
                latest_metadata = max(metadata_files, key=lambda x: x.stat().st_mtime)
                self.metadata_path = str(latest_metadata)
                
                with open(latest_metadata, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
                
                # モデルパスを取得
                model_path = self.metadata.get('model_path')
                if model_path and Path(model_path).exists():
                    self.model_path = model_path
                    self.model = tf.keras.models.load_model(model_path)
                
                self.logger.info(f"最新結果を自動検出: {latest_metadata}")
            
            # Optuna結果ファイルを検索
            optuna_files = list(results_path.glob("*best_params*.json"))
            if optuna_files:
                latest_optuna = max(optuna_files, key=lambda x: x.stat().st_mtime)
                self.optuna_results_path = str(latest_optuna)
                
                with open(latest_optuna, 'r', encoding='utf-8') as f:
                    self.optuna_results = json.load(f)
                
                self.logger.info(f"Optuna結果を自動検出: {latest_optuna}")
            
            # アナライザー再初期化
            feature_names = self.metadata.get('feature_names', [])
            self.analyzer = TensorFlowRankingAnalyzer(
                model=self.model,
                feature_names=feature_names,
                optuna_results=self.optuna_results,
                output_dir=str(self.output_dir / "detailed_analysis")
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"自動検出エラー: {e}")
            return False
    
    def analyze_sample_prediction(self, 
                                horse_count: int = 10,
                                race_id: str = "sample_race") -> Dict[str, Any]:
        """サンプルデータでの予測分析"""
        
        if self.model is None or self.analyzer is None:
            self.logger.error("モデルまたはアナライザーが初期化されていません")
            return {}
        
        try:
            self.logger.info(f"サンプル予測分析開始: {horse_count}頭のレース")
            
            # サンプルデータ生成
            feature_names = self.metadata.get('feature_names', [])
            n_features = len(feature_names)
            
            if n_features == 0:
                self.logger.error("特徴量情報が見つかりません")
                return {}
            
            # ランダムな特徴量データ生成
            np.random.seed(42)
            X_sample = np.random.randn(horse_count, n_features)
            
            # 競馬らしい特徴量に調整
            feature_adjustments = {
                '枠番': lambda x: np.random.randint(1, 9, x.shape[0]),
                '馬番': lambda x: np.arange(1, x.shape[0] + 1),
                '斤量': lambda x: np.random.uniform(52, 58, x.shape[0]),
                'course_len': lambda x: np.random.choice([1200, 1600, 2000], x.shape[0]),
                '着順_last_5R_mean': lambda x: np.random.uniform(1, 15, x.shape[0]),
                '人気_last_5R_mean': lambda x: np.random.uniform(1, 15, x.shape[0]),
                'オッズ_last_5R_mean': lambda x: np.random.lognormal(1, 0.5, x.shape[0]),
                'interval_days': lambda x: np.random.randint(7, 90, x.shape[0])
            }
            
            for i, feature_name in enumerate(feature_names):
                if i < X_sample.shape[1] and feature_name in feature_adjustments:
                    X_sample[:, i] = feature_adjustments[feature_name](X_sample)
            
            # 馬名生成
            horse_names = [f"サンプル馬{i+1}号" for i in range(horse_count)]
            
            # サンプル実績データ（検証用）
            y_true_sample = np.random.uniform(0, 1, horse_count)
            y_true_sample = (y_true_sample - y_true_sample.min()) / (y_true_sample.max() - y_true_sample.min())
            
            # 分析実行
            ranking_analysis = self.analyzer.analyze_ranking_predictions(
                X=X_sample,
                y_true=y_true_sample,
                horse_names=horse_names,
                race_id=race_id
            )
            
            return ranking_analysis
            
        except Exception as e:
            self.logger.error(f"サンプル予測分析エラー: {e}")
            return {}
    
    def run_comprehensive_analysis(self, 
                                 X: np.ndarray = None,
                                 y_true: np.ndarray = None,
                                 horse_names: List[str] = None,
                                 race_id: str = None) -> Dict[str, Any]:
        """包括的分析の実行"""
        
        if self.analyzer is None:
            self.logger.error("アナライザーが初期化されていません")
            return {}
        
        try:
            self.logger.info("包括的分析開始")
            
            # データが提供されていない場合はサンプルデータを使用
            if X is None:
                self.logger.info("入力データが未提供のため、サンプルデータで分析実行")
                return self.analyze_sample_prediction()
            
            # 1. ランキング予測分析
            self.logger.info("ランキング予測分析実行中...")
            ranking_analysis = self.analyzer.analyze_ranking_predictions(
                X=X, y_true=y_true, horse_names=horse_names, race_id=race_id
            )
            
            # 2. Optuna分析
            self.logger.info("Optuna最適化分析実行中...")
            optuna_analysis = self.analyzer.analyze_optuna_optimization()
            
            # 3. 可視化作成
            self.logger.info("可視化作成中...")
            ranking_visualizations = self.analyzer.create_ranking_visualizations(ranking_analysis)
            optuna_visualizations = self.analyzer.create_optuna_visualizations(optuna_analysis)
            
            all_visualizations = {**ranking_visualizations, **optuna_visualizations}
            
            # 4. 結果保存
            self.logger.info("分析結果保存中...")
            report_path = self.analyzer.save_analysis_results(
                ranking_analysis=ranking_analysis,
                optuna_analysis=optuna_analysis,
                visualizations=all_visualizations
            )
            
            # 5. 統合レポート生成
            comprehensive_report = self.analyzer.generate_comprehensive_report(
                ranking_analysis, optuna_analysis
            )
            
            # 統合結果
            integrated_result = {
                'analysis_timestamp': datetime.now().isoformat(),
                'ranking_analysis': ranking_analysis,
                'optuna_analysis': optuna_analysis,
                'visualizations': all_visualizations,
                'comprehensive_report': comprehensive_report,
                'report_path': report_path,
                'model_info': {
                    'model_path': self.model_path,
                    'metadata_path': self.metadata_path,
                    'optuna_results_path': self.optuna_results_path
                }
            }
            
            self.logger.info("包括的分析完了")
            return integrated_result
            
        except Exception as e:
            self.logger.error(f"包括的分析エラー: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def create_analysis_summary(self, analysis_result: Dict[str, Any]) -> str:
        """分析サマリー作成"""
        
        summary_lines = []
        summary_lines.append("=" * 60)
        summary_lines.append("TensorFlow Ranking + Optuna 分析サマリー")
        summary_lines.append("=" * 60)
        
        # 基本情報
        timestamp = analysis_result.get('analysis_timestamp', 'N/A')
        summary_lines.append(f"\n📅 分析実行時刻: {timestamp}")
        
        # モデル情報
        model_info = analysis_result.get('model_info', {})
        summary_lines.append(f"\n🤖 モデル情報:")
        summary_lines.append(f"  モデルパス: {model_info.get('model_path', 'N/A')}")
        summary_lines.append(f"  メタデータ: {model_info.get('metadata_path', 'N/A')}")
        summary_lines.append(f"  Optuna結果: {model_info.get('optuna_results_path', 'N/A')}")
        
        # ランキング分析サマリー
        ranking_analysis = analysis_result.get('ranking_analysis', {})
        if ranking_analysis:
            basic_info = ranking_analysis.get('basic_info', {})
            summary_lines.append(f"\n🏇 予測対象:")
            summary_lines.append(f"  出走頭数: {basic_info.get('horse_count', 0)}頭")
            summary_lines.append(f"  特徴量数: {basic_info.get('feature_count', 0)}個")
            
            # 上位3頭
            predictions = ranking_analysis.get('predictions', [])
            if predictions:
                summary_lines.append(f"\n🥇 予測上位3頭:")
                for i, pred in enumerate(predictions[:3]):
                    rank = i + 1
                    name = pred['horse_name']
                    score = pred['predicted_score']
                    confidence = pred['confidence_level']
                    summary_lines.append(f"  {rank}位: {name} (スコア: {score:.3f}, 信頼度: {confidence})")
            
            # 精度情報
            ranking_quality = ranking_analysis.get('ranking_analysis', {})
            accuracy = ranking_quality.get('accuracy_metrics', {})
            if accuracy:
                summary_lines.append(f"\n📊 予測精度:")
                summary_lines.append(f"  NDCG@5: {accuracy.get('ndcg_5', 0):.3f}")
                summary_lines.append(f"  順位相関: {accuracy.get('rank_correlation', 0):.3f}")
        
        # Optuna分析サマリー
        optuna_analysis = analysis_result.get('optuna_analysis', {})
        if optuna_analysis:
            opt_summary = optuna_analysis.get('optimization_summary', {})
            summary_lines.append(f"\n⚙️ Optuna最適化:")
            summary_lines.append(f"  最高スコア: {opt_summary.get('best_score', 0):.4f}")
            summary_lines.append(f"  試行回数: {opt_summary.get('total_trials', 0)}回")
            
            best_params = opt_summary.get('best_params', {})
            if best_params:
                summary_lines.append(f"  主要最適パラメータ:")
                key_params = ['learning_rate', 'hidden_size', 'dropout_rate']
                for param in key_params:
                    if param in best_params:
                        value = best_params[param]
                        if isinstance(value, float):
                            summary_lines.append(f"    {param}: {value:.4f}")
                        else:
                            summary_lines.append(f"    {param}: {value}")
        
        # 可視化ファイル
        visualizations = analysis_result.get('visualizations', {})
        if visualizations:
            summary_lines.append(f"\n📈 生成された可視化:")
            for viz_name, viz_path in visualizations.items():
                summary_lines.append(f"  • {viz_name}: {Path(viz_path).name}")
        
        # レポートパス
        report_path = analysis_result.get('report_path')
        if report_path:
            summary_lines.append(f"\n📄 詳細レポート: {Path(report_path).name}")
        
        summary_lines.append(f"\n" + "=" * 60)
        summary_lines.append("分析完了")
        summary_lines.append("=" * 60)
        
        return "\n".join(summary_lines)
    
    def generate_status_report(self) -> str:
        """システム状況レポート"""
        
        status_lines = []
        status_lines.append("=" * 60)
        status_lines.append("TensorFlow Ranking Analysis Integration Status")
        status_lines.append("=" * 60)
        
        # コンポーネント状況
        status_lines.append(f"\n🔧 コンポーネント状況:")
        status_lines.append(f"  モデル: {'✅ ロード済み' if self.model else '❌ 未ロード'}")
        status_lines.append(f"  メタデータ: {'✅ ロード済み' if self.metadata else '❌ 未ロード'}")
        status_lines.append(f"  Optuna結果: {'✅ ロード済み' if self.optuna_results else '❌ 未ロード'}")
        status_lines.append(f"  アナライザー: {'✅ 初期化済み' if self.analyzer else '❌ 未初期化'}")
        
        # ファイルパス
        status_lines.append(f"\n📁 ファイルパス:")
        status_lines.append(f"  モデル: {self.model_path or 'N/A'}")
        status_lines.append(f"  メタデータ: {self.metadata_path or 'N/A'}")
        status_lines.append(f"  Optuna結果: {self.optuna_results_path or 'N/A'}")
        status_lines.append(f"  出力ディレクトリ: {self.output_dir}")
        
        # メタデータ詳細
        if self.metadata:
            status_lines.append(f"\n📋 モデル詳細:")
            status_lines.append(f"  特徴量数: {len(self.metadata.get('feature_names', []))}個")
            status_lines.append(f"  最高スコア: {self.metadata.get('best_score', 'N/A')}")
            status_lines.append(f"  テストLoss: {self.metadata.get('test_loss', 'N/A')}")
            
            feature_names = self.metadata.get('feature_names', [])
            if feature_names:
                status_lines.append(f"  特徴量: {', '.join(feature_names[:5])}{'...' if len(feature_names) > 5 else ''}")
        
        # Optuna詳細
        if self.optuna_results:
            status_lines.append(f"\n⚙️ Optuna詳細:")
            status_lines.append(f"  最高スコア: {self.optuna_results.get('best_score', 'N/A')}")
            status_lines.append(f"  試行回数: {self.optuna_results.get('n_trials', 'N/A')}回")
            
            best_params = self.optuna_results.get('best_params', {})
            if best_params:
                status_lines.append(f"  最適パラメータ数: {len(best_params)}個")
        
        # 実行可能機能
        status_lines.append(f"\n⚡ 実行可能機能:")
        if self.analyzer:
            status_lines.append(f"  ✅ サンプル予測分析")
            status_lines.append(f"  ✅ 包括的分析")
            status_lines.append(f"  ✅ 可視化生成")
            status_lines.append(f"  ✅ レポート生成")
        else:
            status_lines.append(f"  ❌ 分析機能（アナライザー未初期化）")
        
        status_lines.append(f"\n" + "=" * 60)
        status_lines.append("Status Report Complete")
        status_lines.append("=" * 60)
        
        return "\n".join(status_lines)

def main():
    """メイン実行関数"""
    
    print("=" * 60)
    print("TensorFlow Ranking + Optuna 統合分析システム")
    print("=" * 60)
    
    # システム初期化
    integration_system = TensorFlowRankingAnalysisIntegration()
    
    try:
        # 最新結果の自動検出
        print("\n🔍 最新結果の自動検出中...")
        if integration_system.auto_detect_latest_results():
            print("✅ 最新結果の検出・ロード完了")
        else:
            print("⚠️ 最新結果が見つかりません。サンプルデータで実行します。")
        
        # システム状況確認
        print("\n📊 システム状況:")
        status_report = integration_system.generate_status_report()
        print(status_report)
        
        # サンプル分析実行
        print("\n🔬 サンプル分析実行中...")
        analysis_result = integration_system.run_comprehensive_analysis()
        
        if analysis_result:
            print("✅ 包括的分析完了")
            
            # サマリー表示
            summary = integration_system.create_analysis_summary(analysis_result)
            print("\n" + summary)
            
            # 詳細レポート表示
            comprehensive_report = analysis_result.get('comprehensive_report', '')
            if comprehensive_report:
                print("\n" + comprehensive_report)
            
            # 結果保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            result_file = integration_system.output_dir / f"integration_result_{timestamp}.json"
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n📁 統合分析結果保存: {result_file}")
            print("🎉 TensorFlow Ranking + Optuna 統合分析完了！")
            
        else:
            print("❌ 分析に失敗しました。")
    
    except KeyboardInterrupt:
        print("\n⚠️ ユーザーによって中断されました。")
    except Exception as e:
        print(f"\n❌ エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()