================================================================================
TensorFlow Ranking + Optuna 分析デモレポート
================================================================================

🏇 レース情報:
  レース名: TensorFlow Ranking記念
  レースID: 202506090101
  距離: 2000m
  クラス: G3
  出走頭数: 14頭

🥇 予測結果（上位5頭）:
  予測順位              馬名      予測スコア     実際順位     誤差
-------------------------------------------------------
     1            シャップ      0.828       10     -9
     2          アルゴリズム      0.779       13    -11
     3       ハイパーパラメータ      0.706        6     -3
     4          オプチューナ      0.698        7     -3
     5         テンソルフロー      0.691        8     -3

📊 予測性能:
  NDCG@5: 0.890
  NDCG@10: 0.909
  順位相関: -0.174
  Top3精度: 0.0%
  Top5精度: 20.0%
  平均順位誤差: 5.43

🔍 特徴量分析:
  最重要特徴量: 人気_mean
  平均相関: 0.355

  主要特徴量（相関上位3位）:
    1. 人気_mean: -0.709
    2. 着順_mean: -0.648
    3. 枠番: -0.539

🔒 予測信頼度:
  高信頼度: 1頭 (7.1%)
  中信頼度: 4頭 (28.6%)
  普通信頼度: 5頭 (35.7%)
  低信頼度: 4頭 (28.6%)
  予測不確実性: 高 (0.996)

💡 分析結果に基づく推奨事項:
  ❌ 低い予測精度（相関-0.174）- 予測結果の使用に注意
  ⚠️ 上位3頭の予測も不確実（精度0.0%）
  🎯 人気_meanが最も重要な判断要因
  ⚠️ 不確実性が高い - 保守的戦略推奨

🔧 技術詳細:
  アルゴリズム: TensorFlow Ranking + Optuna最適化
  特徴量数: 8個
  評価指標: NDCG, 順位相関, Top-K精度
  可視化: 5種類のチャートを生成

================================================================================
TensorFlow Ranking + Optuna 分析デモ完了
================================================================================