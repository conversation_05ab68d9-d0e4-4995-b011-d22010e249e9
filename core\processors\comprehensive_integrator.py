#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
包括のデータ統合モジュール
レース情報、レース結果、馬基本情報、馬過去成績を統合した
包括的な表形式データを生成する
"""

import argparse # main関数で使用
import os
import pandas as pd
import datetime
import json # 設定ファイル読み込み用
import logging # ロギング設定はmain関数で行う
from typing import Optional, Dict, Any, List, Tuple

from core.processors.race_processor import RaceProcessor
from core.processors.horse_processor import HorseProcessor
from core.processors.date_processor import DateProcessor
from core.utils.constants import LocalPaths
from core.processors.race_feature_engineer import RaceFeatureEngineer # RaceFeatureEngineer をインポート
from tqdm.auto import tqdm # tqdm は _merge_horse_performance_stats 内の RaceProcessor で使用 # type: ignore
import dataclasses # dataclasses.fields を使用するため

from core.utils.constants import ComprehensiveIntegratorConfig, HorseInfoCols, HorseResultsCols # dataclassとしてインポート
from core.utils.constants import ResultsCols # ResultsCols.UMABAN を使用するため追加
class ComprehensiveDataIntegrator:
    """
    レース情報、レース結果、馬基本情報、馬過去成績を統合した
    包括的な表形式データを生成するクラス
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None,
                 horse_results_pickle_path_template: Optional[str] = None,
                 horse_info_pickle_path_template: Optional[str] = None,
                 custom_feature_pickle_path_template: Optional[str] = None): # 新しいpickleテンプレート用引数
        """
        初期化

        Parameters
        ----------
        config : Dict[str, Any], optional
            設定情報
            例: {"max_workers": 4, "default_year": "2023", ...}
        horse_results_pickle_path_template : str, optional
            馬の過去成績pickleファイル名のテンプレート (例: "horse_results_{year}.pickle")
        horse_info_pickle_path_template : str, optional
            馬の基本情報pickleファイル名のテンプレート (例: "horse_info_{year}.pickle")
        custom_feature_pickle_path_template : str, optional
            カスタム特徴量pickleファイル名のテンプレート (例: "custom_features_{year}.pickle")
        """
        self.logger = logging.getLogger(__name__)

        # dataclassを使用して設定を初期化し、バリデーション（__post_init__）
        try:
            if config is not None:
                # config 辞書に含まれるキーのみを ComprehensiveIntegratorConfig に渡す
                # ComprehensiveIntegratorConfig のフィールド名と一致するキーのみを抽出
                valid_keys = {f.name for f in dataclasses.fields(ComprehensiveIntegratorConfig)}
                filtered_config = {k: v for k, v in config.items() if k in valid_keys}
                self.config = ComprehensiveIntegratorConfig(**filtered_config)
            else:
                self.config = ComprehensiveIntegratorConfig() # デフォルト値で初期化
            self.logger.info("設定の初期化とバリデーションに成功しました。")
        except (ValueError, TypeError) as e: # __post_init__ からの ValueError や、引数不一致の TypeError
            self.logger.error(f"設定の初期化/バリデーションに失敗しました: {e}")
            # バリデーション失敗時は、デフォルト設定で続行するか、例外を発生させるか検討
            try:
                self.config = ComprehensiveIntegratorConfig() # デフォルト設定で再試行
                self.logger.warning("エラーのため、デフォルト設定でConfigを初期化しました。")
            except Exception as init_e:
                self.logger.critical(f"デフォルト設定でのConfig初期化も失敗しました: {init_e}")
                raise # クリティカルなエラーとして上位に伝播

        # HorseProcessorが使用するpickleファイル名のテンプレート
        # コンストラクタ引数が指定されていればそれを優先、なければconfigから取得
        self.horse_results_pickle_path_template = horse_results_pickle_path_template or self.config.horse_results_pickle_filename_template
        self.horse_info_pickle_path_template = horse_info_pickle_path_template or self.config.horse_info_pickle_filename_template

        # カスタム特徴量pickleのテンプレートをインスタンス変数に設定
        # コンストラクタ引数が指定されていればそれを優先、なければconfigから取得
        # configに設定がない場合のデフォルト値も考慮
        self.custom_feature_pickle_path_template = custom_feature_pickle_path_template or (self.config.custom_feature_pickle_filename_template if hasattr(self.config, 'custom_feature_pickle_filename_template') else "custom_features_{year}.pickle")

        # プロセッサの初期化 (RaceProcessorはここで初期化、HorseProcessorは実行時にuse_pickle_sourceに応じて再初期化)
        self.race_processor = RaceProcessor(config=dataclasses.asdict(self.config) if self.config else None)
        self.horse_processor = HorseProcessor(config=dataclasses.asdict(self.config) if self.config else None) # 一旦デフォルトで初期化
        self.date_processor = DateProcessor(config=dataclasses.asdict(self.config) if self.config else None)  # 日付処理プロセッサ
        # RaceFeatureEngineer のインスタンスを初期化 (RaceProcessorの内部コンポーネントを渡す)
        # RaceProcessorがRaceHtmlParserとRaceDataPreprocessorを内部で初期化・保持していることを確認してください。
        # RaceProcessorのコンストラクタでこれらが初期化されていない場合、ここでエラーになります。
        # RaceProcessorのコンストラクタで _html_parser と _preprocessor を初期化するように修正が必要です。
        # 現状のRaceProcessorの実装では、これらの属性はメソッド内で初期化される場合があるため、
        # ここで直接アクセスするとAttributeErrorが発生する可能性があります。
        self._feature_engineer = RaceFeatureEngineer(html_parser=self.race_processor._html_parser, data_preprocessor=self.race_processor._preprocessor, config=dataclasses.asdict(self.config) if self.config else None) # type: ignore
        # データ保持用
        self._race_info_df = pd.DataFrame()
        self._race_results_df = pd.DataFrame()
        self._horse_info_df = pd.DataFrame()
        self._horse_results_df = pd.DataFrame()
        self._comprehensive_df = pd.DataFrame()



    def generate_comprehensive_table(self,
                                   year: Optional[str] = None,
                                   years: Optional[List[str]] = None,
                                   race_id: Optional[str] = None,
                                   include_race_info: Optional[bool] = None,
                                   include_horse_info: Optional[bool] = None,
                                   include_past_performance: Optional[bool] = None,
                                   performance_window_races: Optional[List[int]] = None,
                                   parallel: Optional[bool] = None,
                                   max_workers: Optional[int] = None, # メソッド引数はユーザーが明示的に指定したい場合用
                                   use_pickle_source: Optional[bool] = None, # デフォルトをNoneに変更
                                   pickle_base_dir: Optional[str] = None) -> pd.DataFrame:
        """
        包括的な表形式データを生成

        Parameters
        ----------
        year : str, optional
            処理する年度（単一年度）
        years : List[str], optional
            処理する年度（複数年度）
        race_id : str, optional
            特定のレースID
        include_race_info : bool, default True
            レース情報（天気、距離、馬場状態等）を含めるか
        include_horse_info : bool, default True
            馬基本情報（血統、調教師、馬主等）を含めるか
        include_past_performance : bool, default True
            馬の過去成績統計を含めるか
        performance_window_races : List[int], default [5, 10]
            過去成績の集計対象レース数
        parallel : bool, default True
            並列処理を使用するか
        max_workers : int, optional
            並列処理の最大ワーカー数
        use_pickle_source : bool, default False
            Trueの場合、HTMLの代わりにpickleファイルからデータを読み込む
        pickle_base_dir : str, optional
            pickleファイルが格納されているベースディレクトリ (デフォルト: data/processed)

        Returns
        -------
        pd.DataFrame
            包括的な統合データ
        """
        self.logger.info("包括的な表形式データの生成を開始します")

        # 実行時の設定を構築 (メソッド引数で self.config を上書きした新しいConfigインスタンスを作成)
        effective_config_dict = dataclasses.asdict(self.config)
        if year is not None:
            effective_config_dict['default_year'] = year
            effective_config_dict['default_years'] = [year]  # 単一年度を配列として設定
        if years is not None: effective_config_dict['default_years'] = years
        if race_id is not None: effective_config_dict['race_id'] = race_id
        if include_race_info is not None: effective_config_dict['include_race_info'] = include_race_info
        if include_horse_info is not None: effective_config_dict['include_horse_info'] = include_horse_info
        if include_past_performance is not None: effective_config_dict['include_past_performance'] = include_past_performance
        if performance_window_races is not None: effective_config_dict['performance_window_races'] = performance_window_races
        if parallel is not None: effective_config_dict['parallel'] = parallel
        if max_workers is not None: effective_config_dict['max_workers'] = max_workers

        # use_pickle_source: メソッド引数がNoneでなければそれを使用し、Noneならインスタンスのconfig値を使用
        if use_pickle_source is not None:
            effective_config_dict['use_pickle_source'] = use_pickle_source

        # pickle_base_dir: メソッド引数 -> インスタンスconfig -> デフォルト の優先順位
        if pickle_base_dir is not None:
            effective_config_dict['pickle_base_dir'] = pickle_base_dir
        elif self.config.pickle_base_dir is not None: # インスタンスのconfigから取得
            effective_config_dict['pickle_base_dir'] = self.config.pickle_base_dir
        else: # 最終的なデフォルト値
            effective_config_dict['pickle_base_dir'] = os.path.join(LocalPaths.DATA_DIR, "processed")

        try:
            effective_config = ComprehensiveIntegratorConfig(**effective_config_dict)
        except (ValueError, TypeError) as e:
            self.logger.error(f"実行時設定の構築に失敗: {e}。インスタンスのデフォルト設定を使用します。")
            effective_config = self.config

        # HorseProcessor の初期化 (use_pickle_source に応じて)
        # このタイミングで HorseProcessor を初期化することで、pickleパスを渡せる
        if effective_config.use_pickle_source: # type: ignore
            current_target_year = None
            if effective_config.race_id:
                current_target_year = effective_config.race_id[:4]
            elif effective_config.default_years and isinstance(effective_config.default_years, list) and len(effective_config.default_years) > 0:
                # 複数年の場合は、各年でHorseProcessorを再初期化するか、
                # HorseProcessor側で複数年のpickleを扱えるようにする必要がある。
                # ここでは、単一年度処理(_generate_single_year_table)の中で年ごとに初期化する想定。
                # _generate_multi_year_table のループ内で年ごとにHorseProcessorを初期化する。
                pass # _generate_single_year_table / _generate_multi_year_table で処理
            elif effective_config.default_year:
                current_target_year = effective_config.default_year

            # current_target_year が決定している場合（主に単一年度処理時）
            if current_target_year and not (effective_config.default_years and len(effective_config.default_years) > 1) :
                hr_pickle_path = os.path.join(effective_config.pickle_base_dir, self.horse_results_pickle_path_template.format(year=current_target_year)) # type: ignore
                hi_pickle_path = os.path.join(effective_config.pickle_base_dir, self.horse_info_pickle_path_template.format(year=current_target_year)) # type: ignore
                self.horse_processor = HorseProcessor(
                    horse_results_filepath=hr_pickle_path if os.path.exists(hr_pickle_path) else None,
                    horse_info_filepath=hi_pickle_path if os.path.exists(hi_pickle_path) else None,
                    config=dataclasses.asdict(effective_config)
                )
                self.logger.info(f"HorseProcessorをpickleモードで初期化: results='{hr_pickle_path}', info='{hi_pickle_path}'")
        else: # HTMLモードの場合
            self.horse_processor = HorseProcessor(config=dataclasses.asdict(effective_config))
            self.logger.info("HorseProcessorをHTMLモードで初期化しました。")

        # 年度の処理（複数年度対応）
        # race_id が指定されていれば、それを最優先
        if effective_config.race_id:
            year_from_race_id = effective_config.race_id[:4]
            return self._generate_single_year_table(year_from_race_id, effective_config)

        target_years_list = effective_config.default_years if effective_config.default_years is not None else []
        if not isinstance(target_years_list, list):
            target_years_list = [str(target_years_list)] # 単一年の場合もリスト化

        # 空のリストの場合はエラー
        if not target_years_list:
            self.logger.error("処理対象の年度が指定されていません")
            return pd.DataFrame()

        if len(target_years_list) == 1:
            return self._generate_single_year_table(target_years_list[0], effective_config)
        else:
            return self._generate_multi_year_table(target_years_list, effective_config)

    def _generate_single_year_table(self, year: str, effective_config: ComprehensiveIntegratorConfig) -> pd.DataFrame:
        """
        単一年度の包括的データを生成
        """
        # 1. レース情報とレース結果の取得
        self.logger.info(f"1. レース情報とレース結果を取得中 (年度: {year}, pickle使用: {effective_config.use_pickle_source})") # type: ignore
        if effective_config.use_pickle_source: # type: ignore
            race_info_df, race_results_df = self.race_processor.process_race_pickle_files(
                year=year,
                race_id=effective_config.race_id,
                pickle_base_dir=effective_config.pickle_base_dir, # type: ignore
            )
        else:
            race_info_df, race_results_df = self.race_processor.process_race_bin_files(
                year=year,
                race_id=effective_config.race_id,
                parallel=effective_config.parallel,
                max_workers=effective_config.max_workers
            )

        # HorseProcessorの再初期化 (単一年度処理の場合、ここで年指定で初期化)
        if effective_config.use_pickle_source: # type: ignore
            hr_pickle_path = os.path.join(effective_config.pickle_base_dir, self.horse_results_pickle_path_template.format(year=year)) # type: ignore
            hi_pickle_path = os.path.join(effective_config.pickle_base_dir, self.horse_info_pickle_path_template.format(year=year)) # type: ignore
            self.horse_processor = HorseProcessor(
                horse_results_filepath=hr_pickle_path if os.path.exists(hr_pickle_path) else None,
                horse_info_filepath=hi_pickle_path if os.path.exists(hi_pickle_path) else None,
                config=dataclasses.asdict(effective_config))
        if race_results_df.empty:
            self.logger.warning("レース結果データが空です")
            return pd.DataFrame()

        self._race_info_df = race_info_df
        self._race_info_df = self._feature_engineer.standardize_data_types(self._race_info_df)
        self._race_results_df = race_results_df
        self._race_results_df = self._feature_engineer.standardize_data_types(self._race_results_df)

        self.logger.info(f"レース情報: {len(race_info_df)}件")
        self.logger.info(f"レース結果: {len(race_results_df)}件")

        # 2. ベースデータの作成（レース結果をベースとする）
        self.logger.info("2. ベースデータを作成中...")
        comprehensive_df = race_results_df.copy()
        comprehensive_df = self._feature_engineer.standardize_data_types(comprehensive_df) # 型標準化

        # 3. レース情報の統合 (設定値はPydanticモデルから取得)
        if effective_config.include_race_info and not race_info_df.empty:
            self.logger.info("3. レース情報を統合中...")
            comprehensive_df = self._merge_race_info(comprehensive_df, race_info_df)
            self.logger.info(f"レース情報統合後: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")

        # 4. 馬基本情報の統合 (設定値はPydanticモデルから取得)
        if effective_config.include_horse_info:
            self.logger.info("4. 馬基本情報を統合中...")
            comprehensive_df = self._merge_horse_info(
                comprehensive_df, year=year,
                target_horse_ids=self._extract_horse_ids_from_race_data(race_results_df),
                use_pickle_source=effective_config.use_pickle_source # type: ignore
            )
            self.logger.info(f"馬基本情報統合後: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")

        # 5. 馬過去成績統計の統合 (設定値はPydanticモデルから取得)
        if effective_config.include_past_performance:
            self.logger.info("5. 馬過去成績統計を統合中...")
            comprehensive_df = self._merge_horse_performance_stats(
                comprehensive_df,
                performance_window_races=effective_config.performance_window_races,
                max_workers=effective_config.max_workers,
                target_horse_ids=self._extract_horse_ids_from_race_data(race_results_df),
                use_pickle_source=effective_config.use_pickle_source # type: ignore
            )
            self.logger.info(f"過去成績統計統合後: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")

        # 6. カスタム特徴量データの統合 (設定で有効な場合)
        if effective_config.use_pickle_source and hasattr(effective_config, 'enable_custom_feature_pickle') and effective_config.enable_custom_feature_pickle: # type: ignore
            self.logger.info(f"6. カスタム特徴量pickleデータを読み込み・統合中 (年度: {year})...")
            custom_feature_pickle_filename = self.custom_feature_pickle_path_template.format(year=year)
            custom_feature_pickle_path = os.path.join(
                effective_config.pickle_base_dir, # type: ignore
                custom_feature_pickle_filename
            )
            if os.path.exists(custom_feature_pickle_path):
                try:
                    custom_feature_df = pd.read_pickle(custom_feature_pickle_path)
                    custom_feature_df = self._feature_engineer.standardize_data_types(custom_feature_df) # 型標準化
                    self.logger.info(f"カスタム特徴量pickleデータ読み込み完了: {len(custom_feature_df)}件 ({custom_feature_pickle_filename})")

                    comprehensive_df = self._merge_custom_features(comprehensive_df, custom_feature_df)
                    self.logger.info(f"カスタム特徴量統合後: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")
                except Exception as e:
                    self.logger.error(f"カスタム特徴量pickleファイルの読み込みまたはマージに失敗: {custom_feature_pickle_path}, エラー: {e}")
            else:
                self.logger.warning(f"カスタム特徴量pickleファイルが見つかりません: {custom_feature_pickle_path}")


        self._comprehensive_df = comprehensive_df
        self.logger.info(f"包括的データ生成完了: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")
        # === コーナー特徴量の統合を追加 ===
        comprehensive_df = self._merge_corner_features(
            base_df=comprehensive_df,
            year=year,
            effective_config=effective_config,
            n_races=2
        )
        
        # === 日付データの標準化処理 ===
        self.logger.info("日付データの標準化処理を開始")
        try:
            comprehensive_df = self.date_processor.process_dataframe(
                comprehensive_df, 
                auto_detect=True
            )
            self.logger.info("日付データの標準化処理が完了しました")
        except Exception as e:
            self.logger.warning(f"日付処理でエラーが発生しました（処理を継続）: {e}")
        
        self._comprehensive_df = comprehensive_df
        return comprehensive_df
    
    def _merge_corner_features(self, base_df: pd.DataFrame, year: str, 
                              effective_config: ComprehensiveIntegratorConfig, 
                              n_races: int = 2) -> pd.DataFrame:
        """
        コーナー特徴量データを統合する
        
        Parameters
        ----------
        base_df : pd.DataFrame
            ベースとなるデータフレーム
        year : str
            対象年度
        effective_config : ComprehensiveIntegratorConfig
            実行設定
        n_races : int, default 2
            結合する前走のレース数
            
        Returns
        -------
        pd.DataFrame
            コーナー特徴量が統合されたデータフレーム
        """
        try:
            # コーナー特徴量pickleファイルのパスを構築
            corner_pickle_path = os.path.join(
                effective_config.pickle_base_dir or "output", 
                f"corner_features_{year}.pickle"
            )
            
            if not os.path.exists(corner_pickle_path):
                self.logger.warning(f"コーナー特徴量ファイルが見つかりません: {corner_pickle_path}")
                return base_df
            
            # コーナー特徴量データの読み込み
            corner_features_df = pd.read_pickle(corner_pickle_path)
            corner_features_df = self._feature_engineer.standardize_data_types(corner_features_df)
            
            self.logger.info(f"コーナー特徴量データ読み込み完了: {len(corner_features_df)}件")
            
            if corner_features_df.empty:
                self.logger.warning("コーナー特徴量データが空です")
                return base_df
            
            # HorseProcessorのmerge_latest_corner_featuresメソッドを使用
            merged_df = self.horse_processor.merge_latest_corner_features(
                base_df=base_df,
                corner_features_df=corner_features_df,
                n_races=n_races
            )
            
            self.logger.info(f"コーナー特徴量統合完了: {len(merged_df)}件, {len(merged_df.columns)}カラム")
            return merged_df
            
        except Exception as e:
            self.logger.error(f"コーナー特徴量統合エラー: {e}")
            return base_df

    def _generate_multi_year_table(self, target_years: List[str], effective_config: ComprehensiveIntegratorConfig) -> pd.DataFrame:
        """
        複数年度の包括的データを生成
        """
        self.logger.info(f"複数年度データ統合開始: {target_years}")

        all_dfs = []

        for year in tqdm(target_years, desc="複数年度データ統合中", leave=False): # tqdmのインポートはクラス外で行うことを推奨
            self.logger.info(f"年度 {year} を処理中...")
            # 複数年度の場合、ここで年ごとにHorseProcessorを初期化
            if effective_config.use_pickle_source: # type: ignore
                hr_pickle_path = os.path.join(effective_config.pickle_base_dir, self.horse_results_pickle_path_template.format(year=year)) # type: ignore
                hi_pickle_path = os.path.join(effective_config.pickle_base_dir, self.horse_info_pickle_path_template.format(year=year)) # type: ignore
                self.horse_processor = HorseProcessor(
                    horse_results_filepath=hr_pickle_path if os.path.exists(hr_pickle_path) else None,
                    horse_info_filepath=hi_pickle_path if os.path.exists(hi_pickle_path) else None,
                    config=dataclasses.asdict(effective_config)
                )
                self.logger.info(f"HorseProcessorをpickleモードで再初期化 (年: {year})")

            year_df = self._generate_single_year_table(year, effective_config)

            if not year_df.empty:
                # 年度情報を追加
                year_df['year'] = year
                all_dfs.append(year_df)
                self.logger.info(f"年度 {year}: {len(year_df)}件のデータを取得")
            else:
                self.logger.warning(f"年度 {year}: データが空です")

        if all_dfs:
            # 全年度のデータを結合
            comprehensive_df = pd.concat(all_dfs, ignore_index=True)
            
            # === 複数年度データの日付標準化処理 ===
            self.logger.info("複数年度データの日付標準化処理を開始")
            try:
                comprehensive_df = self.date_processor.process_dataframe(
                    comprehensive_df, 
                    auto_detect=True
                )
                self.logger.info("複数年度データの日付標準化処理が完了しました")
            except Exception as e:
                self.logger.warning(f"複数年度データの日付処理でエラーが発生しました（処理を継続）: {e}")
            
            self._comprehensive_df = comprehensive_df
            self.logger.info(f"複数年度統合完了: {len(comprehensive_df)}件, {len(comprehensive_df.columns)}カラム")
            return comprehensive_df
        else:
            self.logger.warning("全年度でデータが空です")
            return pd.DataFrame()
    
    @staticmethod
    def _normalize_date(date_input: Any) -> Optional[str]:
        """
        日付入力（文字列、数値、datetimeオブジェクトなど）を統一形式（YYYY-MM-DD）に変換します。
        変換できない場合はNoneを返します。
        """
        if pd.isna(date_input) or date_input == '':
            return None
        if isinstance(date_input, datetime.datetime): # datetime.datetime を使用
            return date_input.strftime('%Y-%m-%d')
        date_str = str(int(date_input)) if isinstance(date_input, (int, float)) else str(date_input)
        try:
            if '/' in date_str: return datetime.datetime.strptime(date_str, '%Y/%m/%d').strftime('%Y-%m-%d')
            elif '-' in date_str:
                datetime.datetime.strptime(date_str, '%Y-%m-%d') # 形式チェック
                return date_str
            elif len(date_str) == 8 and date_str.isdigit(): return datetime.datetime.strptime(date_str, '%Y%m%d').strftime('%Y-%m-%d')
            return None
        except ValueError:
            return None

    def _merge_race_info(self, base_df: pd.DataFrame, race_info_df: pd.DataFrame) -> pd.DataFrame: # DataMergerから移管
        """
        レース情報をベースデータに統合
        """
        try:
            if 'race_id' in base_df.columns and 'race_id' in race_info_df.columns:
                merged_df = base_df.merge(
                    race_info_df,
                    on='race_id',
                    how='left',
                    suffixes=('', '_race_info')
                )
                return merged_df
            else:
                self.logger.warning("race_idカラムが見つからないため、レース情報の統合をスキップします")
                return base_df
        except Exception as e:
            self.logger.error(f"レース情報統合エラー: {e}")
            return base_df
    
    def _merge_horse_info(self, base_df: pd.DataFrame, year: Optional[str] = None, # DataMergerから移管
                          target_horse_ids: Optional[List[str]] = None,
                          use_pickle_source: bool = False) -> pd.DataFrame:
        """
        馬基本情報をベースデータに統合
        """
        try:
            if not target_horse_ids:
                self.logger.warning("対象の馬IDが指定されていないため、馬基本情報の統合をスキップします。")
                return base_df

            horse_info_df = pd.DataFrame()
            if use_pickle_source:
                self.logger.info(f"Pickleから馬基本情報を取得試行 (対象ID: {len(target_horse_ids)}件)")
                horse_info_df = self.horse_processor.get_horse_info(horse_id_list=target_horse_ids)
                if horse_info_df.empty:
                    self.logger.warning(f"Pickleから馬基本情報を取得できませんでした。HTMLからの取得を試みます。")
                    # フォールバックとしてHTMLから取得
                    horse_info_df = self.horse_processor.process_horse_info_for_ids(
                        horse_ids=target_horse_ids,
                        parallel=self.config.parallel,
                        max_workers=self.config.max_workers
                    )
            else: # HTMLから取得
                self.logger.info(f"HTMLから馬基本情報を取得 (対象ID: {len(target_horse_ids)}件)")
                horse_info_df = self.horse_processor.process_horse_info_for_ids(
                    horse_ids=target_horse_ids,
                    parallel=self.config.parallel,
                    max_workers=self.config.max_workers
                )

            if horse_info_df.empty:
                self.logger.warning("馬基本情報データが空のため、統合をスキップします")
                return base_df

            horse_info_df = self._feature_engineer.standardize_data_types(horse_info_df) # 型標準化
            # horse_idでマージ
            if 'horse_id' in base_df.columns:
                # horse_idの型を統一
                base_df['horse_id'] = base_df['horse_id'].astype(str)
                horse_info_df.index = horse_info_df.index.astype(str)

                # 重複する可能性のあるカラムをリネーム (例: 調教師、馬主など)
                # horse_info_df = horse_info_df.rename(columns=lambda c: c + '_info' if c in base_df.columns and c != 'horse_id' else c)

                merged_df = base_df.merge(
                    horse_info_df,
                    left_on='horse_id',
                    right_index=True,
                    how='left',
                    suffixes=('', '_horse_info')
                )
                return merged_df
            else:
                self.logger.warning("horse_idカラムが見つからないため、馬基本情報の統合をスキップします")
                return base_df

        except Exception as e:
            self.logger.error(f"馬基本情報統合エラー: {e}")
            return base_df

    def _extract_horse_ids_from_race_data(self, race_df: pd.DataFrame) -> List[str]:
        """
        レースデータから馬IDを抽出

        Parameters
        ----------
        race_df : pd.DataFrame
            レースデータ

        Returns
        -------
        List[str]
            馬IDのリスト
        """
        try:
            if 'horse_id' not in race_df.columns:
                self.logger.warning("horse_idカラムが見つかりません")
                return []

            # ユニークな馬IDを抽出
            horse_ids = race_df['horse_id'].dropna().astype(str).unique().tolist()
            self.logger.info(f"レースから抽出した馬ID数: {len(horse_ids)}頭")
            return horse_ids

        except Exception as e:
            self.logger.error(f"馬ID抽出エラー: {e}")
            return []

    def _merge_horse_performance_stats(self, base_df: pd.DataFrame,
                                     performance_window_races: List[int],
                                     max_workers: Optional[int] = None,
                                     target_horse_ids: Optional[List[str]] = None,
                                     use_pickle_source: bool = False) -> pd.DataFrame:
        """
        馬過去成績統計をベースデータに統合

        Parameters
        ----------
        base_df : pd.DataFrame
            ベースデータ
        performance_window_races : List[int]
            過去成績の集計対象レース数
        max_workers : int, optional (未使用、HorseProcessor側で利用)
            並列処理の最大ワーカー数 (このメソッド内では直接使用されませんが、将来的な拡張や呼び出し元での判断材料として残します)
        target_horse_ids : List[str], optional
            処理対象の馬IDリスト。指定されていれば、これらの馬の過去成績のみ取得・計算する。
        use_pickle_source : bool, default False
            Trueの場合、pickleから馬過去成績を取得試行

        Returns
        -------
        pd.DataFrame
            過去成績統計が統合されたデータ
        """
        self.logger.info("馬過去成績統計の統合を開始します。")
        if 'horse_id' not in base_df.columns:
            self.logger.error("ベースデータに 'horse_id' カラムが見つかりません。")
            return base_df
        if 'date' not in base_df.columns:
            self.logger.error("ベースデータに 'date' (レース開催日) カラムが見つかりません。")
            return base_df

        if not target_horse_ids:
            self.logger.warning("過去成績統計の対象となる馬がいません。")
            return base_df

        all_past_horse_results_df = pd.DataFrame()
        if use_pickle_source:
            self.logger.info("Pickleから馬過去成績を取得試行...")
            all_past_horse_results_df = self.horse_processor.preprocessed_data # 初期化時にpickleが読み込まれていれば利用
            if not all_past_horse_results_df.empty and target_horse_ids:
                # preprocessed_data は全馬データなので、target_horse_ids でフィルタリング
                # horse_id がインデックスの場合とカラムの場合を考慮
                ids_to_filter_str = [str(tid) for tid in target_horse_ids]
                if 'horse_id' in all_past_horse_results_df.columns:
                    all_past_horse_results_df = all_past_horse_results_df[all_past_horse_results_df['horse_id'].astype(str).isin(ids_to_filter_str)]
                elif all_past_horse_results_df.index.name == 'horse_id':
                    all_past_horse_results_df = all_past_horse_results_df[all_past_horse_results_df.index.astype(str).isin(ids_to_filter_str)]
                else:
                    self.logger.warning("HorseProcessor.preprocessed_data に horse_id が見つかりません。フィルタリングできません。")

            if all_past_horse_results_df.empty:
                self.logger.warning(f"Pickleから馬過去成績を取得できませんでした。HTMLからの取得を試みます。")
                # フォールバックとしてHTMLから取得
                if target_horse_ids:
                    all_past_horse_results_df = self.horse_processor.process_horse_results_for_ids(
                        horse_ids=target_horse_ids,
                        parallel=self.config.parallel,
                        max_workers=max_workers
                    )
                else: # target_horse_ids がない場合は全件取得 (重い可能性あり)
                    all_past_horse_results_df = self.horse_processor.get_all_horse_results(max_workers=max_workers)
        else: # HTMLから取得
            if target_horse_ids:
                self.logger.info(f"HTMLから指定された {len(target_horse_ids)} 頭の馬の過去成績を取得中...")
                all_past_horse_results_df = self.horse_processor.process_horse_results_for_ids(
                    horse_ids=target_horse_ids,
                    parallel=self.config.parallel,
                    max_workers=max_workers
                )
            else:
                self.logger.info("HTMLからHorseProcessorから全馬の過去成績データを取得中...")
                all_past_horse_results_df = self.horse_processor.get_all_horse_results(
                    max_workers=max_workers
                )

        if all_past_horse_results_df.empty:
            self.logger.warning("HorseProcessorから取得した全馬の過去成績データが空です。")
            # preprocessed_data をフォールバックとして試みる (ただし、これは限定的なデータである可能性が高い)
            self.logger.info("フォールバックとして HorseProcessor.preprocessed_data を試みます。")
            all_past_horse_results_df = self.horse_processor.preprocessed_data

        if not all_past_horse_results_df.empty:
            all_past_horse_results_df = self._feature_engineer.standardize_data_types(all_past_horse_results_df)
            self.logger.info(f"取得・標準化後の馬過去成績データ: {len(all_past_horse_results_df)}件")

        if all_past_horse_results_df.empty:
            self.logger.warning("馬過去成績データが空のため、過去成績統計の統合をスキップします")
            return base_df

        # 型変換とカラム名統一
        base_df['horse_id'] = base_df['horse_id'].astype(str)
        base_df['date'] = pd.to_datetime(base_df['date'], errors='coerce')

        # all_past_horse_results_df の 'horse_id' の曖昧さを解消
        # 優先順位：列に 'horse_id' があればそれを使用し、インデックス名が 'horse_id' なら None にする。
        # 列に 'horse_id' がなく、インデックス名が 'horse_id' なら、インデックスから列を作成する。
        if 'horse_id' in all_past_horse_results_df.columns: # horse_id が列として存在する場合
            all_past_horse_results_df['horse_id'] = all_past_horse_results_df['horse_id'].astype(str)
            if all_past_horse_results_df.index.name == 'horse_id': # かつ、インデックス名も horse_id の場合
                self.logger.debug(
                    "all_past_horse_results_df: 'horse_id' 列が存在し、かつインデックス名も 'horse_id' です。"
                    "groupby での曖昧さを避けるため、インデックス名を None にします。"
                )
                all_past_horse_results_df.index.name = None
        elif all_past_horse_results_df.index.name == 'horse_id': # horse_id が列にはなく、インデックス名が horse_id の場合
                self.logger.info("all_past_horse_results_df のインデックス 'horse_id' から 'horse_id' 列を作成します。")
                all_past_horse_results_df['horse_id'] = all_past_horse_results_df.index.astype(str)
        else: # horse_id が列にもインデックス名にもない場合
            self.logger.error(
                "馬過去成績データに 'horse_id' カラムまたはインデックスが見つかりません。"
            )
            return base_df

        if 'date' in all_past_horse_results_df.columns:
            all_past_horse_results_df['date'] = pd.to_datetime(all_past_horse_results_df['date'], errors='coerce')
        else:
            self.logger.error("馬過去成績データに 'date' カラムが見つかりません。")
            return base_df
        # target_horse_ids を使って all_past_horse_results_df をフィルタリング
        if target_horse_ids:
            # horse_id カラムの型を文字列に統一してからフィルタリング
            all_past_horse_results_df['horse_id'] = all_past_horse_results_df['horse_id'].astype(str)
            target_horse_ids_str = [str(tid) for tid in target_horse_ids]
            all_past_horse_results_df = all_past_horse_results_df[
                all_past_horse_results_df['horse_id'].isin(target_horse_ids_str)
            ].copy() # .copy() を追加して SettingWithCopyWarning を回避
            self.logger.info(f"対象馬ID ({len(target_horse_ids_str)}件) で過去成績データをフィルタリングしました。残りの過去成績データ: {len(all_past_horse_results_df)}件")

        if all_past_horse_results_df.empty:
            self.logger.warning("フィルタリング後の馬過去成績データが空のため、過去成績統計の統合をスキップします")
            return base_df

        # 処理対象の統計カラム
        # HorseResultsCols から参照するように変更を検討
        # HorseResultsCols はモジュールレベルでインポートされている想定
        potential_stat_cols = [
            HorseResultsCols.RANK, HorseResultsCols.POPULARITY,
            HorseResultsCols.TANSHO_ODDS, HorseResultsCols.PRIZE,
            HorseResultsCols.KINRYO, HorseResultsCols.TIME, # TIMEは秒に変換後が望ましい
            HorseResultsCols.NOBORI, # 上がりタイム
            "体重", "体重変化" # これらは _preprocess_horse_results で生成される想定
        ]
        stat_target_cols = [col for col in potential_stat_cols if col in all_past_horse_results_df.columns]

        for col_name in stat_target_cols:
            all_past_horse_results_df[col_name] = pd.to_numeric(all_past_horse_results_df[col_name], errors='coerce')

        if not stat_target_cols:
            self.logger.warning("過去成績データに統計計算可能な数値カラムが見つかりません。")
            return base_df

        self.logger.info(f"過去成績の統計計算対象カラム: {stat_target_cols}")

        # RaceFeatureEngineer の merge_past_horse_performance メソッドを呼び出して過去成績をマージ
        # target_cols は stat_target_cols を使用
        # n_races_list は self.config.performance_window_races を使用
        # group_cols は RaceProcessor のデフォルトまたは設定に従う (ここではNoneとしておく)
        final_merged_df = self._feature_engineer.merge_past_horse_performance(
            current_race_results_df=base_df,
            all_horse_past_results_df=all_past_horse_results_df,
            target_cols=stat_target_cols,
            n_races_list=performance_window_races, # type: ignore
            group_cols=None # 必要に応じて設定
        )


        self.logger.info(f"馬過去成績統計の統合が完了しました。処理後の行数: {len(final_merged_df)}")
        return final_merged_df

    def _merge_custom_features(self, base_df: pd.DataFrame, custom_feature_df: pd.DataFrame) -> pd.DataFrame:
        """
        カスタム特徴量データをベースデータに統合します。

        Parameters
        ----------
        base_df : pd.DataFrame
            ベースとなるDataFrame。
        custom_feature_df : pd.DataFrame
            統合するカスタム特徴量データ。

        Returns
        -------
        pd.DataFrame
            カスタム特徴量が統合されたDataFrame。
        """
        if custom_feature_df.empty:
            self.logger.warning("カスタム特徴量データが空のため、統合をスキップします。")
            return base_df

        self.logger.info("カスタム特徴量を統合中...")
        try:
            # マージキーの候補 (例: race_id, horse_id)
            # 実際のカスタム特徴量データの構造に合わせて調整してください。
            merge_keys = []
            if 'race_id' in base_df.columns and 'race_id' in custom_feature_df.columns:
                merge_keys.append('race_id')
            if 'horse_id' in base_df.columns and 'horse_id' in custom_feature_df.columns:
                merge_keys.append('horse_id')

            if not merge_keys:
                self.logger.warning("カスタム特徴量のマージに必要な共通キー (race_idまたはhorse_id) が見つかりません。")
                return base_df

            # マージキーの型を統一 (文字列型を推奨)
            for key in merge_keys:
                base_df[key] = base_df[key].astype(str)
                custom_feature_df[key] = custom_feature_df[key].astype(str)

            return base_df.merge(custom_feature_df, on=merge_keys, how='left', suffixes=('', '_custom'))
        except Exception as e:
            self.logger.error(f"カスタム特徴量の統合中にエラーが発生しました: {e}", exc_info=True)
            return base_df

    def save_comprehensive_table(self,
                                filename_prefix: str = "comprehensive_race_data",
                                year: Optional[str] = None,
                                save_pickle: bool = True,
                                save_csv: bool = True) -> Tuple[Optional[str], Optional[str]]:
        """
        包括的な統合データをファイルに保存

        Parameters
        ----------
        filename_prefix : str, default "comprehensive_race_data"
            ファイル名のプレフィックス
        year : str, optional
            年度（ファイル名に含める）
        save_pickle : bool, default True
            Pickleファイルとして保存するか
        save_csv : bool, default True
            CSVファイルとして保存するか

        Returns
        -------
        Tuple[Optional[str], Optional[str]]
            (pickle_path, csv_path) 保存したファイルのパス
        """
        if self._comprehensive_df.empty:
            self.logger.warning("保存するデータがありません")
            return None, None

        # 保存先ディレクトリを作成
        csv_dir = os.path.join(LocalPaths.DATA_DIR, "csv")
        os.makedirs(csv_dir, exist_ok=True)

        # ファイル名を生成
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        year_str = f"_{year}" if year else ""

        pickle_path = None
        csv_path = None

        # Pickleファイルとして保存
        if save_pickle:
            pickle_filename = f"{filename_prefix}{year_str}_{timestamp}.pickle"
            pickle_path = os.path.join(csv_dir, pickle_filename)
            self._comprehensive_df.to_pickle(pickle_path)
            self.logger.info(f"Pickleファイルを保存しました: {pickle_path}")

        # CSVファイルとして保存
        if save_csv:
            csv_filename = f"{filename_prefix}{year_str}_{timestamp}.csv"
            csv_path = os.path.join(csv_dir, csv_filename)
            self._comprehensive_df.to_csv(csv_path, index=False, encoding="utf-8-sig")
            self.logger.info(f"CSVファイルを保存しました: {csv_path}")

        return pickle_path, csv_path

    def get_data_summary(self) -> Dict[str, Any]:
        """
        統合データの概要情報を取得

        Returns
        -------
        Dict[str, Any]
            データ概要情報
        """
        if self._comprehensive_df.empty:
            return {"status": "empty", "message": "データが生成されていません"}

        df = self._comprehensive_df

        summary = {
            "status": "success",
            "total_records": len(df),
            "total_columns": len(df.columns),
            "unique_races": df['race_id'].nunique() if 'race_id' in df.columns else 0,
            "unique_horses": df['horse_id'].nunique() if 'horse_id' in df.columns else 0,
            "data_columns": {
                "race_info_columns": [col for col in df.columns if any(keyword in col.lower() for keyword in ['天気', '馬場', '距離', 'コース', 'レース'])],
                "horse_info_columns": [col for col in df.columns if any(keyword in col.lower() for keyword in ['father', 'mother', '調教師', '馬主', '生年月日'])],
                "performance_columns": [col for col in df.columns if any(keyword in col.lower() for keyword in ['last_', 'mean', 'std', 'min', 'max'])],
                "basic_columns": [col for col in df.columns if col in ['race_id', 'horse_id', '着順', '馬名', '騎手', '斤量', 'オッズ', '人気']]
            },
            "missing_data_ratio": (df.isnull().sum() / len(df)).round(3).to_dict()
        }

        return summary

    @property
    def comprehensive_data(self) -> pd.DataFrame:
        """
        生成された包括的データを取得

        Returns
        -------
        pd.DataFrame
            包括的な統合データ
        """
        return self._comprehensive_df.copy() if not self._comprehensive_df.empty else pd.DataFrame()

    # _standardize_data_types メソッドは削除
    # RaceFeatureEngineer.standardize_data_types() を使用してください
    
    # _normalize_date_static メソッドは削除
    # RaceFeatureEngineer._normalize_date() を使用してください

def load_config_from_file(config_path: Optional[str]) -> Dict[str, Any]:
    """設定ファイルを読み込むヘルパー関数"""
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            logging.error(f"設定ファイル {config_path} のJSON形式が不正です: {e}")
        except IOError as e:
            logging.error(f"設定ファイル {config_path} の読み込みに失敗しました: {e}")
    return {}

def merge_settings(file_config: Dict[str, Any], args: argparse.Namespace) -> Dict[str, Any]:
    """設定ファイルとコマンドライン引数をマージする"""
    settings = file_config.copy()

    # コマンドライン引数が指定されていれば、それで上書き
    if args.year: settings['year'] = args.year
    if args.years: settings['years'] = args.years
    if args.race_id: settings['race_id'] = args.race_id
    if args.no_race_info: settings['include_race_info'] = False
    if args.no_horse_info: settings['include_horse_info'] = False
    if args.no_past_performance: settings['include_past_performance'] = False
    if args.performance_windows: settings['performance_window_races'] = args.performance_windows
    if args.no_parallel: settings['parallel'] = False
    if args.workers is not None: settings['max_workers'] = args.workers # Noneでない場合のみ上書き
    if args.save is not None: settings['save_output'] = args.save # store_true/store_false のため
    if args.filename_prefix: settings['filename_prefix'] = args.filename_prefix
    # integrator_options は設定ファイルからのみ取得する例（必要なら引数追加も可）
    return settings

def main():
    """
    メイン実行関数
    """
    import argparse
    # ロギング設定
    logging.basicConfig(level=logging.INFO,
                       format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                       handlers=[logging.StreamHandler()])

    parser = argparse.ArgumentParser(description="包括的な競馬データ統合表を生成")
    parser.add_argument("--year", type=str, help="処理する年度（単一年度）")
    parser.add_argument("--years", nargs="*", type=str, help="処理する年度（複数年度）。指定しない場合は設定ファイルの値を使用。") # nargs="*" に変更
    parser.add_argument("--race-id", type=str, help="処理する特定のレースID")
    parser.add_argument("--no-race-info", action="store_true", help="レース情報を含めない")
    parser.add_argument("--no-horse-info", action="store_true", help="馬基本情報を含めない")
    parser.add_argument("--no-past-performance", action="store_true", help="馬過去成績統計を含めない")
    parser.add_argument("--performance-windows", nargs="*", type=int, # nargs="*" に変更
                       help="過去成績の集計対象レース数")
    parser.add_argument("--no-parallel", action="store_true", help="並列処理を使用しない (デフォルトは設定ファイルに従う)")
    parser.add_argument("--workers", type=int, help="並列処理の最大ワーカー数。指定しない場合は設定ファイルまたはデフォルト値。")
    parser.add_argument("--save", action=argparse.BooleanOptionalAction, default=None, help="結果をファイルに保存する/しない。指定なしは設定ファイルに従う。") # BooleanOptionalAction
    parser.add_argument("--filename-prefix", type=str,
                       help="保存ファイル名のプレフィックス")
    parser.add_argument("--config-file", type=str, default="config.json", help="設定ファイルのパス (default: config.json)")
    parser.add_argument("--use-pickle", action="store_true", help="HTMLの代わりにpickleファイルからデータを読み込む")
    parser.add_argument("--pickle-dir", type=str, help="pickleファイルが格納されているベースディレクトリ")
    # カスタム特徴量pickle用の引数を追加 (例)
    parser.add_argument("--enable-custom-pickle", action="store_true", help="カスタム特徴量pickleを読み込む")

    args = parser.parse_args()

    # 設定ファイルの読み込み
    file_config = load_config_from_file(args.config_file)
    if file_config:
        logging.info(f"設定ファイル {args.config_file} を読み込みました。")
    else:
        logging.info(f"設定ファイル {args.config_file} は読み込まれなかったか、空です。デフォルト設定またはコマンドライン引数を使用します。")

    # 設定のマージ
    settings = merge_settings(file_config, args) # type: ignore
    logging.info(f"最終的な実行設定: {settings}")

    # コマンドライン引数からpickle関連の設定をsettingsに反映
    if args.use_pickle: settings['use_pickle_source'] = True
    if args.pickle_dir: settings['pickle_base_dir'] = args.pickle_dir
    if args.enable_custom_pickle: settings['enable_custom_feature_pickle'] = True # 新しい設定


    # ComprehensiveDataIntegratorのインスタンスを作成
    integrator = ComprehensiveDataIntegrator(config=settings) # マージ済みの設定を渡す

    # 包括的データを生成
    comprehensive_df = integrator.generate_comprehensive_table(
        year=settings.get('year'), # type: ignore
        years=settings.get('years'), # type: ignore
        race_id=settings.get('race_id'), # type: ignore
        include_race_info=settings.get('include_race_info', integrator.config.include_race_info),
        include_horse_info=settings.get('include_horse_info', integrator.config.include_horse_info),
        include_past_performance=settings.get('include_past_performance', integrator.config.include_past_performance),
        performance_window_races=settings.get('performance_window_races', integrator.config.performance_window_races),
        parallel=settings.get('parallel', integrator.config.parallel),
        max_workers=settings.get('max_workers'),
        use_pickle_source=settings.get('use_pickle_source', False), # settingsから取得
        pickle_base_dir=settings.get('pickle_base_dir') # settingsから取得
    )

    # 結果を表示
    if not comprehensive_df.empty:
        integrator.logger.info(f"\n✅ 包括的データ生成完了:")
        integrator.logger.info(f"   データ件数: {len(comprehensive_df):,}件")
        integrator.logger.info(f"   カラム数: {len(comprehensive_df.columns)}個")
        if 'race_id' in comprehensive_df.columns:
            integrator.logger.info(f"   ユニークレース数: {comprehensive_df['race_id'].nunique()}")
        if 'horse_id' in comprehensive_df.columns:
            integrator.logger.info(f"   ユニーク馬数: {comprehensive_df['horse_id'].nunique()}")

        # データ概要を表示
        summary = integrator.get_data_summary()
        integrator.logger.info(f"\n📊 データ構成:")
        for category, columns in summary["data_columns"].items():
            if columns:
                integrator.logger.info(f"   {category}: {len(columns)}個")

        integrator.logger.info(f"\n📋 サンプルデータ:")
        integrator.logger.info(f"\n{comprehensive_df.head()}") # DataFrameの表示は改行を入れる

        # ファイルに保存
        if settings.get('save_output', False):
            # 複数年度の場合はファイル名に年度範囲を含める
            current_years = settings.get('years', [])
            if isinstance(current_years, list) and len(current_years) > 1:
                year_range_str = f"{min(current_years)}-{max(current_years)}"
                filename_prefix_for_save = f"{settings.get('filename_prefix', integrator.config.filename_prefix)}_multi_year_{year_range_str}"
                save_year = None
            else:
                filename_prefix_for_save = settings.get('filename_prefix', integrator.config.filename_prefix)
                save_year = current_years[0] if isinstance(current_years, list) and current_years else settings.get('year', integrator.config.default_year)
            pickle_path, csv_path = integrator.save_comprehensive_table(
                filename_prefix=filename_prefix_for_save,
                year=save_year
            )
            integrator.logger.info(f"\n💾 ファイル保存完了:")
            if pickle_path:
                integrator.logger.info(f"   Pickle: {pickle_path}")
            if csv_path:
                integrator.logger.info(f"   CSV: {csv_path}")
    else:
        integrator.logger.error("❌ データの生成に失敗しました")


if __name__ == "__main__":
    main()
