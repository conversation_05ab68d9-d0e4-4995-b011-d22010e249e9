{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 改善版：競馬データ一括処理Notebook\n", "\n", "このNotebookは、複数年分のbinファイルをDataFrameに変換し、レース情報と馬情報を統合処理します。\n", "\n", "## 主な改善点\n", "- **BatchProcessorクラス**: 処理全体を管理する統合クラス\n", "- **チェックポイント機能**: 中断した処理を再開可能\n", "- **詳細なログ記録**: 処理状況を完全に追跡\n", "- **メモリ効率化**: 大規模データでも安定動作\n", "- **統計情報の自動保存**: 各年の処理結果を記録\n", "- **エラーハンドリング**: 堅牢な例外処理\n", "- **進捗の可視化**: tqdmによる詳細な進捗表示"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 環境設定とインポート"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 基本ライブラリのインポート\n", "import os\n", "import sys\n", "from pathlib import Path\n", "from datetime import datetime\n", "import pandas as pd\n", "import numpy as np\n", "from tqdm.notebook import tqdm\n", "import logging\n", "import json\n", "from typing import List, Dict, Tuple, Optional\n", "import gc\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# プロジェクトルートの設定\n", "PROJECT_ROOT = Path.cwd()\n", "sys.path.insert(0, str(PROJECT_ROOT))\n", "\n", "print(f\"プロジェクトルート: {PROJECT_ROOT}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ログ設定\n", "def setup_logging(log_dir: Path = Path('logs')):\n", "    \"\"\"ログ設定のセットアップ\"\"\"\n", "    log_dir.mkdir(exist_ok=True)\n", "    log_file = log_dir / f'batch_processing_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.log'\n", "    \n", "    logging.basicConfig(\n", "        level=logging.INFO,\n", "        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n", "        handlers=[\n", "            logging.FileHandler(log_file),\n", "            logging.StreamHandler()\n", "        ]\n", "    )\n", "    return logging.getLogger(__name__)\n", "\n", "logger = setup_logging()\n", "logger.info(\"ログ設定完了\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 必要なモジュールのインポート\n", "try:\n", "    from core.processors.race_processor import RaceProcessor\n", "    from core.processors.race_file_handler import RaceFileHandler\n", "    from core.processors.race_html_parser import RaceHtmlParser\n", "    from core.processors.corner_analyzer import CornerAnalyzer\n", "    logger.info(\"モジュールのインポートに成功しました\")\n", "except ImportError as e:\n", "    logger.error(f\"モジュールのインポートに失敗しました: {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. BatchProcessorクラスの定義"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BatchProcessor:\n", "    \"\"\"バッチ処理を管理するクラス\"\"\"\n", "    \n", "    def __init__(self, config: Dict):\n", "        self.config = config\n", "        self.setup_directories()\n", "        self.setup_processors()\n", "        self.processing_stats = {\n", "            'total_files': 0,\n", "            'processed_files': 0,\n", "            'failed_files': 0,\n", "            'start_time': None,\n", "            'end_time': None\n", "        }\n", "    \n", "    def setup_directories(self):\n", "        \"\"\"ディレクトリの設定\"\"\"\n", "        self.data_dir = Path(self.config['data_dir'])\n", "        self.output_dir = Path(self.config['output_dir'])\n", "        self.output_dir.mkdir(parents=True, exist_ok=True)\n", "        \n", "        # サブディレクトリの作成\n", "        self.pickle_dir = self.output_dir / 'pickle'\n", "        self.stats_dir = self.output_dir / 'stats'\n", "        self.checkpoint_dir = self.output_dir / 'checkpoints'\n", "        \n", "        for dir_path in [self.pickle_dir, self.stats_dir, self.checkpoint_dir]:\n", "            dir_path.mkdir(exist_ok=True)\n", "    \n", "    def setup_processors(self):\n", "        \"\"\"プロセッサーの初期化\"\"\"\n", "        self.html_parser = RaceHtmlParser()\n", "        self.corner_analyzer = CornerAnalyzer()\n", "        self.file_handler = RaceFileHandler(\n", "            html_parser=self.html_parser,\n", "            corner_analyzer=self.corner_analyzer\n", "        )\n", "    \n", "    def process_years(self, years: List[str]):\n", "        \"\"\"複数年のデータを処理\"\"\"\n", "        self.processing_stats['start_time'] = datetime.now()\n", "        logger.info(f\"処理開始: {len(years)}年分のデータを処理します\")\n", "        \n", "        all_results = {\n", "            'race_info': [],\n", "            'race_results': [],\n", "            'corner_features': []\n", "        }\n", "        \n", "        for year in tqdm(years, desc=\"年次処理\", unit=\"年\"):\n", "            try:\n", "                year_results = self.process_single_year(year)\n", "                \n", "                # 結果を統合\n", "                for key in all_results:\n", "                    if year_results[key] is not None:\n", "                        all_results[key].append(year_results[key])\n", "                \n", "                # メモリ解放\n", "                gc.collect()\n", "                \n", "            except Exception as e:\n", "                logger.error(f\"{year}年の処理でエラーが発生: {e}\")\n", "                self.save_checkpoint(year, status='failed')\n", "                continue\n", "        \n", "        # 全体の結果を結合\n", "        final_results = self.merge_results(all_results)\n", "        \n", "        self.processing_stats['end_time'] = datetime.now()\n", "        self.save_processing_stats()\n", "        \n", "        return final_results\n", "    \n", "    def process_single_year(self, year: str) -> Dict:\n", "        \"\"\"単一年のデータを処理\"\"\"\n", "        logger.info(f\"{year}年のデータ処理を開始\")\n", "        \n", "        # チェックポイントの確認\n", "        checkpoint = self.load_checkpoint(year)\n", "        if checkpoint and checkpoint.get('status') == 'completed':\n", "            logger.info(f\"{year}年は既に処理済みです\")\n", "            return self.load_year_results(year)\n", "        \n", "        try:\n", "            # データ処理\n", "            race_info, race_results, corner_features = self.file_handler.process_race_bin_to_yearly_pickles(\n", "                years=[year],\n", "                bin_base_dir=self.data_dir,\n", "                output_dir=self.pickle_dir,\n", "                parallel=self.config.get('parallel', True),\n", "                max_files_per_year=self.config.get('max_files_per_year'),\n", "                include_corner_features=self.config.get('include_corner_features', True),\n", "                max_workers=self.config.get('max_workers', 4)\n", "            )\n", "            \n", "            # 統計情報の保存\n", "            self.save_year_stats(year, race_info, race_results, corner_features)\n", "            \n", "            # チェックポイントの保存\n", "            self.save_checkpoint(year, status='completed')\n", "            \n", "            return {\n", "                'race_info': race_info,\n", "                'race_results': race_results,\n", "                'corner_features': corner_features\n", "            }\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"{year}年の処理中にエラーが発生: {e}\")\n", "            self.save_checkpoint(year, status='failed', error=str(e))\n", "            raise\n", "    \n", "    def merge_results(self, all_results: Dict) -> Dict:\n", "        \"\"\"複数年の結果を結合\"\"\"\n", "        logger.info(\"結果の結合を開始\")\n", "        \n", "        merged = {}\n", "        for key in ['race_info', 'race_results', 'corner_features']:\n", "            if all_results[key]:\n", "                # Noneでないデータフレームのみ結合\n", "                valid_dfs = [df for df in all_results[key] if df is not None]\n", "                if valid_dfs:\n", "                    merged[key] = pd.concat(valid_dfs, ignore_index=True)\n", "                    logger.info(f\"{key}: {len(merged[key])}行のデータを結合\")\n", "                else:\n", "                    merged[key] = None\n", "            else:\n", "                merged[key] = None\n", "        \n", "        return merged\n", "    \n", "    def save_year_stats(self, year: str, race_info: pd.DataFrame, \n", "                       race_results: pd.DataFrame, corner_features: pd.DataFrame):\n", "        \"\"\"年次統計の保存\"\"\"\n", "        stats = {\n", "            'year': year,\n", "            'race_info_rows': len(race_info) if race_info is not None else 0,\n", "            'race_results_rows': len(race_results) if race_results is not None else 0,\n", "            'corner_features_rows': len(corner_features) if corner_features is not None else 0,\n", "            'processing_time': datetime.now().isoformat()\n", "        }\n", "        \n", "        stats_file = self.stats_dir / f'stats_{year}.json'\n", "        with open(stats_file, 'w', encoding='utf-8') as f:\n", "            json.dump(stats, f, ensure_ascii=False, indent=2)\n", "    \n", "    def save_checkpoint(self, year: str, status: str, error: Optional[str] = None):\n", "        \"\"\"チェックポイントの保存\"\"\"\n", "        checkpoint = {\n", "            'year': year,\n", "            'status': status,\n", "            'timestamp': datetime.now().isoformat(),\n", "            'error': error\n", "        }\n", "        \n", "        checkpoint_file = self.checkpoint_dir / f'checkpoint_{year}.json'\n", "        with open(checkpoint_file, 'w', encoding='utf-8') as f:\n", "            json.dump(checkpoint, f, ensure_ascii=False, indent=2)\n", "    \n", "    def load_checkpoint(self, year: str) -> Optional[Dict]:\n", "        \"\"\"チェックポイントの読み込み\"\"\"\n", "        checkpoint_file = self.checkpoint_dir / f'checkpoint_{year}.json'\n", "        if checkpoint_file.exists():\n", "            with open(checkpoint_file, 'r', encoding='utf-8') as f:\n", "                return json.load(f)\n", "        return None\n", "    \n", "    def load_year_results(self, year: str) -> Dict:\n", "        \"\"\"年次結果の読み込み\"\"\"\n", "        results = {}\n", "        for data_type in ['race_info', 'race_results', 'corner_features']:\n", "            pickle_file = self.pickle_dir / f'{data_type}_{year}.pkl'\n", "            if pickle_file.exists():\n", "                results[data_type] = pd.read_pickle(pickle_file)\n", "            else:\n", "                results[data_type] = None\n", "        return results\n", "    \n", "    def save_processing_stats(self):\n", "        \"\"\"処理統計の保存\"\"\"\n", "        if self.processing_stats['start_time'] and self.processing_stats['end_time']:\n", "            duration = self.processing_stats['end_time'] - self.processing_stats['start_time']\n", "            self.processing_stats['duration_seconds'] = duration.total_seconds()\n", "        \n", "        stats_file = self.stats_dir / 'overall_stats.json'\n", "        with open(stats_file, 'w', encoding='utf-8') as f:\n", "            json.dump(self.processing_stats, f, default=str, ensure_ascii=False, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. ユーティリティ関数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def validate_results(results: Dict) -> Dict:\n", "    \"\"\"結果の検証\"\"\"\n", "    validation = {}\n", "    \n", "    for key, df in results.items():\n", "        if df is not None:\n", "            validation[key] = {\n", "                'rows': len(df),\n", "                'columns': len(df.columns),\n", "                'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,\n", "                'null_counts': df.isnull().sum().to_dict() if len(df) < 1000000 else 'Too large to display',\n", "                'dtypes': df.dtypes.to_dict()\n", "            }\n", "        else:\n", "            validation[key] = None\n", "    \n", "    return validation\n", "\n", "def display_summary(results: Dict, validation: Dict):\n", "    \"\"\"処理結果のサマリー表示\"\"\"\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"処理結果サマリー\")\n", "    print(\"=\"*50)\n", "    \n", "    for key, df in results.items():\n", "        print(f\"\\n【{key}】\")\n", "        if df is not None:\n", "            print(f\"  - 行数: {len(df):,}\")\n", "            print(f\"  - 列数: {len(df.columns)}\")\n", "            print(f\"  - メモリ使用量: {validation[key]['memory_usage_mb']:.2f} MB\")\n", "            if key == 'race_results':\n", "                print(f\"  - ユニークなレース数: {df['race_id'].nunique():,}\")\n", "                print(f\"  - ユニークな馬数: {df['horse_id'].nunique():,}\")\n", "        else:\n", "            print(\"  - データなし\")\n", "    \n", "    print(\"\\n\" + \"=\"*50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 設定の定義"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 設定\n", "config = {\n", "    # データディレクトリ\n", "    'data_dir': 'H:/AI/keiba_ai_system/data/html/race/race_by_year',\n", "    'output_dir': 'output',\n", "    \n", "    # 処理対象年\n", "    # 個別指定の場合\n", "    # 'years': ['2020', '2021', '2022'],\n", "    \n", "    # 範囲指定の場合\n", "    'year_range': (2020, 2024),\n", "    \n", "    # 処理設定\n", "    'parallel': True,\n", "    'max_workers': 4,\n", "    'max_files_per_year': None,  # Noneで全ファイル処理、テスト時は100など指定\n", "    'include_corner_features': True,\n", "    \n", "    # 保存設定\n", "    'save_final_results': True\n", "}\n", "\n", "# 年次リストの生成\n", "if config.get('year_range'):\n", "    start_year, end_year = config['year_range']\n", "    years = [str(year) for year in range(start_year, end_year + 1)]\n", "else:\n", "    years = config.get('years', [])\n", "\n", "print(f\"処理対象年: {years}\")\n", "print(f\"データディレクトリ: {config['data_dir']}\")\n", "print(f\"出力ディレクトリ: {config['output_dir']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. メイン処理の実行"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# BatchProcessorの初期化\n", "processor = BatchProcessor(config)\n", "logger.info(f\"処理対象年: {years}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# データ処理の実行\n", "results = processor.process_years(years)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 結果の検証\n", "validation = validate_results(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# サマリー表示\n", "display_summary(results, validation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 最終結果の保存（オプション）\n", "if config.get('save_final_results', True):\n", "    for key, df in results.items():\n", "        if df is not None:\n", "            output_file = processor.output_dir / f'{key}_all_years.pkl'\n", "            df.to_pickle(output_file)\n", "            logger.info(f\"{key}を保存: {output_file}\")\n", "            print(f\"{key}を保存しました: {output_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. データの確認と分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# race_infoの確認\n", "if results['race_info'] is not None:\n", "    print(\"=== Race Info Sample ===\")\n", "    display(results['race_info'].head())\n", "    print(f\"\\n列名: {list(results['race_info'].columns)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# race_resultsの確認\n", "if results['race_results'] is not None:\n", "    print(\"=== Race Results Sample ===\")\n", "    display(results['race_results'].head())\n", "    print(f\"\\n列名: {list(results['race_results'].columns)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# corner_featuresの確認\n", "if results['corner_features'] is not None:\n", "    print(\"=== Corner Features Sample ===\")\n", "    display(results['corner_features'].head())\n", "    print(f\"\\n列名: {list(results['corner_features'].columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 処理統計の確認"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 処理統計の読み込みと表示\n", "stats_file = processor.stats_dir / 'overall_stats.json'\n", "if stats_file.exists():\n", "    with open(stats_file, 'r', encoding='utf-8') as f:\n", "        overall_stats = json.load(f)\n", "    \n", "    print(\"=== 処理統計 ===\")\n", "    for key, value in overall_stats.items():\n", "        print(f\"{key}: {value}\")\n", "    \n", "    if 'duration_seconds' in overall_stats:\n", "        duration_min = overall_stats['duration_seconds'] / 60\n", "        print(f\"\\n処理時間: {duration_min:.2f} 分\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 年次統計の確認\n", "print(\"=== 年次統計 ===\")\n", "for year in years:\n", "    stats_file = processor.stats_dir / f'stats_{year}.json'\n", "    if stats_file.exists():\n", "        with open(stats_file, 'r', encoding='utf-8') as f:\n", "            year_stats = json.load(f)\n", "        print(f\"\\n{year}年:\")\n", "        for key, value in year_stats.items():\n", "            print(f\"  {key}: {value}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}