#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
レース当日専用の生後日数・年齢計算ユーティリティ
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# プロジェクトパスの追加
sys.path.append('.')

from core.features.calculators import FeatureCalculators

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RaceDayCalculator:
    """
    レース当日の馬の年齢・生後日数計算専用クラス
    """
    
    def __init__(self):
        self.feature_calc = FeatureCalculators()
    
    def calculate_race_day_ages(self, horses_data, race_date):
        """
        レース当日の馬の年齢情報を一括計算
        
        Parameters
        ----------
        horses_data : pd.DataFrame
            馬の基本情報（horse_id, birthday列を含む）
        race_date : str or datetime
            レース開催日
            
        Returns
        -------
        pd.DataFrame
            年齢情報が追加されたデータ
        """
        result_data = horses_data.copy()
        
        # レース日を統一
        if isinstance(race_date, str):
            race_date = pd.to_datetime(race_date)
        
        result_data['race_date'] = race_date
        
        # 生年月日をdatetimeに変換
        if 'birthday' in result_data.columns:
            result_data['birthday'] = pd.to_datetime(result_data['birthday'], errors='coerce')
        else:
            raise ValueError("birthday列が見つかりません")
        
        # 生後日数計算
        result_data['days_old'] = self.feature_calc.calculate_days_old(
            result_data, 'race_date', 'birthday'
        )
        
        # 年齢計算
        result_data['age_years'] = self.feature_calc.calculate_age_years(
            result_data, 'race_date', 'birthday'
        )
        
        # 月齢計算
        result_data['age_months'] = self.feature_calc.calculate_age_months(
            result_data, 'race_date', 'birthday'
        )
        
        # 年齢カテゴリ
        result_data['is_young'] = self.feature_calc.calculate_age_category_young(
            result_data, 'race_date', 'birthday'
        )
        
        result_data['is_prime'] = self.feature_calc.calculate_age_category_prime(
            result_data, 'race_date', 'birthday'
        )
        
        result_data['is_veteran'] = self.feature_calc.calculate_age_category_veteran(
            result_data, 'race_date', 'birthday'
        )
        
        return result_data
    
    def get_age_summary(self, horses_data, race_date):
        """
        レース当日の年齢分布サマリーを取得
        """
        aged_data = self.calculate_race_day_ages(horses_data, race_date)
        
        summary = {
            'race_date': race_date,
            'total_horses': len(aged_data),
            'age_stats': {
                'mean_days': aged_data['days_old'].mean(),
                'mean_years': aged_data['age_years'].mean(),
                'min_age': aged_data['age_years'].min(),
                'max_age': aged_data['age_years'].max(),
                'age_range': aged_data['age_years'].max() - aged_data['age_years'].min()
            },
            'age_categories': {
                'young_count': aged_data['is_young'].sum(),
                'prime_count': aged_data['is_prime'].sum(),
                'veteran_count': aged_data['is_veteran'].sum()
            }
        }
        
        return summary

def demo_race_day_calculation():
    """レース当日計算のデモンストレーション"""
    logger.info("レース当日年齢計算デモ")
    
    # サンプル馬データ
    horses = pd.DataFrame({
        'horse_id': [f'馬{i:02d}' for i in range(1, 11)],
        'horse_name': [f'サンプルホース{i}' for i in range(1, 11)],
        'birthday': [
            '2021-03-15',  # 3歳
            '2021-04-20',  # 3歳
            '2020-02-28',  # 4歳
            '2020-05-10',  # 4歳
            '2019-06-15',  # 5歳
            '2019-01-25',  # 5歳
            '2018-04-08',  # 6歳
            '2017-07-12',  # 7歳
            '2016-03-30',  # 8歳
            '2015-09-18'   # 9歳
        ]
    })
    
    # レース日設定
    race_date = '2024-06-08'  # 今日の日付
    
    print(f"\nレース日: {race_date}")
    print(f"出走予定: {len(horses)}頭")
    print("\n出走馬一覧:")
    print(horses[['horse_id', 'horse_name', 'birthday']].to_string(index=False))
    
    # 計算実行
    calculator = RaceDayCalculator()
    aged_horses = calculator.calculate_race_day_ages(horses, race_date)
    
    print(f"\nレース当日の年齢情報:")
    display_cols = ['horse_id', 'birthday', 'days_old', 'age_years', 'is_young', 'is_prime', 'is_veteran']
    print(aged_horses[display_cols].round(2).to_string(index=False))
    
    # サマリー取得
    summary = calculator.get_age_summary(horses, race_date)
    
    print(f"\n年齢分布サマリー:")
    print(f"  総出走頭数: {summary['total_horses']}頭")
    print(f"  平均年齢: {summary['age_stats']['mean_years']:.2f}歳")
    print(f"  年齢幅: {summary['age_stats']['min_age']:.1f}歳 - {summary['age_stats']['max_age']:.1f}歳")
    print(f"  若駒(3歳以下): {summary['age_categories']['young_count']}頭")
    print(f"  盛期(4-6歳): {summary['age_categories']['prime_count']}頭")
    print(f"  ベテラン(7歳以上): {summary['age_categories']['veteran_count']}頭")
    
    return aged_horses

def calculate_single_horse_age(horse_id, birthday, race_date):
    """
    単一馬の年齢計算
    
    Parameters
    ----------
    horse_id : str
        馬ID
    birthday : str
        生年月日
    race_date : str
        レース日
        
    Returns
    -------
    dict
        年齢情報
    """
    single_horse = pd.DataFrame({
        'horse_id': [horse_id],
        'birthday': [birthday]
    })
    
    calculator = RaceDayCalculator()
    result = calculator.calculate_race_day_ages(single_horse, race_date)
    
    return {
        'horse_id': horse_id,
        'birthday': birthday,
        'race_date': race_date,
        'days_old': int(result['days_old'].iloc[0]),
        'age_years': round(result['age_years'].iloc[0], 2),
        'age_months': round(result['age_months'].iloc[0], 1),
        'is_young': bool(result['is_young'].iloc[0]),
        'is_prime': bool(result['is_prime'].iloc[0]),
        'is_veteran': bool(result['is_veteran'].iloc[0])
    }

def demo_single_horse():
    """単一馬計算のデモ"""
    logger.info("単一馬年齢計算デモ")
    
    # テストケース
    test_cases = [
        ('ディープインパクト', '2020-03-29', '2024-06-08'),
        ('オルフェーヴル', '2018-05-15', '2024-06-08'),
        ('アーモンドアイ', '2016-01-20', '2024-06-08')
    ]
    
    print("\n単一馬年齢計算結果:")
    print("-" * 80)
    
    for horse_id, birthday, race_date in test_cases:
        result = calculate_single_horse_age(horse_id, birthday, race_date)
        
        category = ""
        if result['is_young']:
            category = "若駒"
        elif result['is_prime']:
            category = "盛期"
        elif result['is_veteran']:
            category = "ベテラン"
        
        print(f"馬名: {result['horse_id']}")
        print(f"  生年月日: {result['birthday']}")
        print(f"  レース日: {result['race_date']}")
        print(f"  生後日数: {result['days_old']}日")
        print(f"  年齢: {result['age_years']}歳 ({result['age_months']}ヶ月)")
        print(f"  カテゴリ: {category}")
        print()

def main():
    """メイン実行"""
    logger.info("レース当日年齢計算ユーティリティ デモ開始")
    
    # 1. 複数馬の一括計算デモ
    print("=" * 60)
    print("1. 複数馬一括計算デモ")
    print("=" * 60)
    demo_race_day_calculation()
    
    # 2. 単一馬計算デモ
    print("\n" + "=" * 60)
    print("2. 単一馬計算デモ")
    print("=" * 60)
    demo_single_horse()
    
    logger.info("デモ完了")

if __name__ == "__main__":
    main()