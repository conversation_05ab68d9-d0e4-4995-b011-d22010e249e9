#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
週選択機能のテストスクリプト
"""

import sys
import logging
from datetime import datetime, timedelta

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_week_selection_functionality():
    """週選択機能テスト"""
    try:
        print("🗓️ 週選択機能テスト開始")
        print("=" * 50)
        
        # 1. WeeklyRacePredictorのインポートテスト
        print("\n1️⃣ WeeklyRacePredictor週選択機能テスト")
        try:
            from weekly_race_predictor import WeeklyRacePredictor
            
            predictor = WeeklyRacePredictor()
            print("✅ WeeklyRacePredictor初期化成功")
            
            # 週計算テスト
            print("\n📅 週計算テスト:")
            for offset in range(4):
                week_dates = predictor.get_week_dates(week_offset=offset)
                if week_dates:
                    saturday, sunday = week_dates
                    week_name = ["今週", "来週", "再来週", "3週間後"][offset]
                    print(f"   {week_name}: {saturday.strftime('%m/%d')}(土) - {sunday.strftime('%m/%d')}(日)")
                else:
                    print(f"   {offset}週後: 計算失敗")
            
        except ImportError as e:
            print(f"❌ インポートエラー: {e}")
            return False
        
        # 2. 統合システムの週選択機能テスト
        print("\n2️⃣ 統合システム週選択機能テスト")
        try:
            from keiba_ai_main import KeibaAISystem
            
            system = KeibaAISystem()
            print("✅ KeibaAISystem初期化成功")
            
            # 週選択関連メソッドの存在確認
            required_methods = [
                '_show_week_selection_menu',
                '_get_week_dates_from_base',
                '_get_week_dates_by_input',
                '_show_schedule_and_select',
                '_get_custom_date_range',
                '_check_actual_race_schedule'
            ]
            
            for method_name in required_methods:
                if hasattr(system, method_name):
                    print(f"✅ {method_name}: 存在")
                else:
                    print(f"❌ {method_name}: 不在")
            
        except Exception as e:
            print(f"❌ 統合システムテストエラー: {e}")
        
        # 3. 日付計算ロジックテスト
        print("\n3️⃣ 日付計算ロジックテスト")
        test_dates = [
            ("2024-12-09", "月曜日"),  # 月曜 → 同じ週の土日
            ("2024-12-10", "火曜日"),  # 火曜 → 同じ週の土日
            ("2024-12-13", "金曜日"),  # 金曜 → 同じ週の土日
            ("2024-12-14", "土曜日"),  # 土曜 → 同じ週の土日
            ("2024-12-15", "日曜日"),  # 日曜 → 前日土曜と当日
        ]
        
        for date_str, weekday in test_dates:
            week_dates = predictor.get_week_dates(date_str)
            if week_dates:
                saturday, sunday = week_dates
                print(f"   {date_str} ({weekday}) → {saturday.strftime('%m/%d')}(土)-{sunday.strftime('%m/%d')}(日)")
            else:
                print(f"   {date_str} ({weekday}) → 計算失敗")
        
        # 4. カスタム期間機能テスト
        print("\n4️⃣ カスタム期間機能テスト")
        
        # 予定されているテスト日付範囲
        test_ranges = [
            ("2024-12-14", "2024-12-15"),  # 土日
            ("2024-12-09", "2024-12-15"),  # 1週間
            ("2024-12-01", "2024-12-07"),  # 月初週
        ]
        
        for start_date, end_date in test_ranges:
            try:
                start = datetime.strptime(start_date, '%Y-%m-%d')
                end = datetime.strptime(end_date, '%Y-%m-%d')
                
                # 日付範囲内の全日を計算
                dates = []
                current_date = start
                while current_date <= end:
                    dates.append(current_date)
                    current_date += timedelta(days=1)
                
                print(f"   {start_date} - {end_date}: {len(dates)}日間")
                
                # 土日の数を確認
                weekend_count = sum(1 for d in dates if d.weekday() in [5, 6])
                print(f"     土日: {weekend_count}日")
                
            except Exception as e:
                print(f"   {start_date} - {end_date}: エラー ({e})")
        
        # 5. コマンドライン引数テスト
        print("\n5️⃣ コマンドライン引数テスト")
        
        sample_commands = [
            "python weekly_race_predictor.py",
            "python weekly_race_predictor.py --interactive",
            "python weekly_race_predictor.py --date 2024-12-14",
            "python weekly_race_predictor.py --date 2024-12-14 --max-races 10",
            "python weekly_race_predictor.py -i --no-save",
        ]
        
        print("   利用可能なコマンド例:")
        for cmd in sample_commands:
            print(f"     {cmd}")
        
        print("\n🎉 週選択機能テスト完了")
        print("=" * 50)
        
        # 使用方法表示
        print("\n💡 使用方法:")
        print("【統合メニューから】")
        print("   python keiba_ai_main.py → 3(予測) → 8(週間予測)")
        print("   → 週選択メニューから任意の週を選択")
        
        print("\n【スタンドアロンツールから】")
        print("   # 対話式モード")
        print("   python weekly_race_predictor.py --interactive")
        print("   python weekly_race_predictor.py  # 日付未指定時は自動で対話式")
        
        print("\n   # 直接指定モード") 
        print("   python weekly_race_predictor.py --date 2024-12-14")
        print("   python weekly_race_predictor.py --date 2024-12-21 --max-races 5")
        
        print("\n【新機能】")
        print("   ✅ 今週・来週・再来週の選択")
        print("   ✅ 日付指定による週選択")
        print("   ✅ 4週間先までのスケジュール確認")
        print("   ✅ カスタム日付範囲の設定")
        print("   ✅ 開催確認機能")
        
        return True
        
    except Exception as e:
        logger.error(f"テストエラー: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_week_options():
    """週選択オプションのデモ"""
    print("\n📅 週選択オプション一覧")
    print("=" * 40)
    
    from datetime import datetime, timedelta
    today = datetime.now()
    
    print(f"基準日: {today.strftime('%Y年%m月%d日 (%a)')}")
    print()
    
    # 今後4週間の選択肢を表示
    for i in range(4):
        week_start = today + timedelta(days=i * 7)
        
        # その週の土日を計算
        weekday = week_start.weekday()
        days_to_saturday = (5 - weekday) % 7
        if weekday == 6:
            days_to_saturday = -1
        
        saturday = week_start + timedelta(days=days_to_saturday)
        sunday = saturday + timedelta(days=1)
        
        week_name = ["今週", "来週", "再来週", "3週間後"][i]
        
        print(f"{i + 1}. {week_name}")
        print(f"   土曜日: {saturday.strftime('%Y年%m月%d日')}")
        print(f"   日曜日: {sunday.strftime('%Y年%m月%d日')}")
        print()

if __name__ == "__main__":
    success = test_week_selection_functionality()
    
    # デモ表示
    demonstrate_week_options()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 週選択機能テストが正常に完了しました")
        sys.exit(0)
    else:
        print("❌ 週選択機能テストに問題があります")
        sys.exit(1)