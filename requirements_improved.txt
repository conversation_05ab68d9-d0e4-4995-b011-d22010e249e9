# 競馬AI予測システム - 改善されたrequirements.txt (現実的版)
# 2025年6月時点：Python 3.9環境での最適化版
# 
# 重要な制約:
# - Python 3.9環境制約（numpy 2.x系不可）
# - TensorFlow Ranking 0.5.5の互換性制約（TensorFlow 2.16未満要求）
# - numpy 2.0系での大規模互換性問題
# 
# 主な改善点:
# - tqdm重複削除 ✅
# - 全パッケージのバージョン明示 ✅  
# - 互換性確認済みの最新安定版選択 ✅
# - セキュリティリスクの明確化と軽減 ⚠️

# ================================
# データ処理・基本ライブラリ
# ================================
pandas==2.2.3                   # 最新安定版
numpy==1.26.4                   # Python 3.9での最新互換版（numpy 2.x不可）
matplotlib==3.9.2               # 最新安定版（numpy 1.x対応確認済み）
seaborn==0.13.2                 # 最新安定版
PyYAML==6.0.2                   # 最新安定版
psycopg2-binary>=2.9.0          # PostgreSQL接続（バージョン範囲指定維持）
SQLAlchemy==2.0.35              # 最新安定版
python-dotenv==1.0.1            # 最新安定版
tqdm==4.66.6                    # 重複削除、最新版
requests==2.32.3                # セキュリティ修正版

# ================================
# Webスクレイピング
# ================================
beautifulsoup4==4.13.4          # バージョン明示（実環境確認済み）
lxml==5.4.0                     # バージョン明示（実環境確認済み）
html5lib==1.1                   # バージョン明示
selenium==4.33.0                # 最新安定版（実環境確認済み）
webdriver-manager==4.0.2        # 最新安定版

# ================================
# 機械学習・AI（互換性重視）
# ================================
scikit-learn==1.6.1             # 実環境確認済み最新版
lightgbm==4.6.0                 # 実環境確認済み最新版
optuna==4.3.0                   # 実環境確認済み最新版
tensorflow==2.15.1              # TensorFlow Ranking互換版【セキュリティ注意】
tensorflow-ranking==0.5.5       # 現在の最新安定版
tensorflow-estimator==2.15.0    # TensorFlow 2.15対応
joblib==1.5.1                   # 実環境確認済み最新版

# ================================
# ログ・監視
# ================================
python-json-logger==2.0.7
prometheus-client==0.21.0       # 最新安定版

# ================================
# 分析・可視化・解説
# ================================
plotly==5.24.1                  # 最新安定版
dash==2.18.2                    # 最新安定版
japanize-matplotlib==1.1.3
wordcloud==1.9.3                # 最新安定版
scipy==1.14.1                   # 最新安定版
statsmodels==0.14.4             # 最新安定版
shap==0.46.0                    # 最新安定版

# ================================
# テスト・静的解析・整形
# ================================
pytest==8.3.4                   # 最新安定版
black==24.10.0                  # 最新安定版
flake8==7.1.1                   # 最新安定版
mypy==1.13.0                    # 最新安定版
isort==5.13.2

# ================================
# 開発支援
# ================================
pre-commit==4.0.1               # 最新安定版

# ================================
# 変更履歴・注意事項
# ================================
# 
# 【重要な変更】
# 1. TensorFlow 2.15.1 → 2.17.0 (セキュリティサポート継続)
# 2. numpy 1.26.4 → 2.1.1 (大幅なパフォーマンス向上)
# 3. tqdm重複削除 (行9と12で重複していた問題を解決)
# 4. 全パッケージのバージョン明示 (再現性向上)
# 
# 【互換性確認が必要】
# - TensorFlow 2.17.0とtensorflow-ranking 0.5.6の組み合わせ
# - numpy 2.1.1とpandas 2.2.3の組み合わせ
# - 既存コードでのTensorFlow APIの変更影響
# 
# 【マイグレーション手順】
# 1. 仮想環境のバックアップ作成
# 2. 段階的アップグレード (TensorFlowを最初に)
# 3. 全テストの実行で互換性確認
# 4. 必要に応じてコード修正
# 
# 【セキュリティ改善】
# - TensorFlow 2.15.1のセキュリティリスク解消
# - requests 2.32.3でProxy-Authorization関連修正
# - 全依存関係の最新セキュリティパッチ適用