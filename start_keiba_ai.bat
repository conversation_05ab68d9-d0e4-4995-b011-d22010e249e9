@echo off
chcp 65001 >nul
echo 🏇 競馬AI予測システム起動中...
echo.

REM 仮想環境の確認・アクティベート
if exist "venv\Scripts\activate.bat" (
    echo ✅ 仮想環境を発見、アクティベート中...
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  仮想環境が見つかりません。activate_env.batを実行します...
    if exist "activate_env.bat" (
        call activate_env.bat
    ) else (
        echo ❌ 仮想環境設定ファイルが見つかりません
        echo 手動で仮想環境をアクティベートしてから再実行してください
        pause
        exit /b 1
    )
)

echo.
echo 🚀 メインシステムを起動します...
echo.

REM メインプログラム実行
python keiba_ai_main.py

echo.
echo 👋 システムを終了しました
pause