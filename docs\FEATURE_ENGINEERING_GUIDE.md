# 特徴量エンジニアリング管理システム

**競馬AI予測システム - 特徴量エンジニアリング詳細ガイド**

本ガイドでは、競馬予測システムにおける特徴量エンジニアリングの設計思想、実装方法、運用ガイドを説明します。システムは7つの特徴量カテゴリで268項目の設定可能な特徴量を管理し、YAML設定による柔軟なカスタマイズを提供します。

## 📁 ファイル構成

| ファイル | 役割 | 説明 |
|---------|------|------|
| **`core/features/manager.py`** | 特徴量管理 | `FeatureEngineeringManager`クラス - システム全体の特徴量管理 |
| **`core/features/definitions.py`** | 特徴量定義 | データクラス、Enum定義、特徴量メタデータ |
| **`core/features/calculators.py`** | 計算実装 | 個別特徴量の計算関数実装 |
| **`core/features/config.yaml`** | 設定管理 | 268行の包括的特徴量設定（7カテゴリ） |
| **`examples/notebooks/feature_engineering_example.ipynb`** | 使用例 | 実際の使用例とベストプラクティス |

## 🏗️ システムアーキテクチャ

### 設計原則
1. **モジュラー設計**: 各特徴量カテゴリを独立したモジュールとして管理
2. **設定駆動**: YAML設定による実行時の柔軟な制御
3. **型安全性**: データクラスと型ヒントによる堅牢性
4. **拡張性**: 新しい特徴量の簡単な追加

### 主要コンポーネント

#### 1. 🎯 特徴量管理 (`FeatureEngineeringManager`)
- **統一インターフェース**: 全特徴量カテゴリの統合管理
- **依存関係解決**: 特徴量間の依存関係自動解決
- **キャッシュ機能**: 計算済み特徴量のメモリ効率的管理
- **エラーハンドリング**: 堅牢な例外処理と復旧機能

#### 2. 📋 特徴量定義 (`FeatureDefinition`)
```python
@dataclass
class FeatureDefinition:
    name: str                    # 特徴量名
    category: FeatureCategory    # カテゴリ（7種類）
    feature_type: FeatureType    # データ型（6種類）
    description: str             # 説明
    importance: FeatureImportance # 重要度（5段階）
    required_columns: List[str]   # 必要カラム
    dependencies: List[str]       # 依存関係
    calculator: Callable         # 計算関数
```

#### 3. 🧮 特徴量計算 (`FeatureCalculators`)
- **統計関数群**: 平均、標準偏差、勝率、連対率など
- **時系列処理**: 過去N戦の統計、重み付き統計
- **エンコーディング**: カテゴリ変数の適切な数値化
- **正規化**: スケーリング、標準化処理

#### 4. ⚙️ 設定管理 (`config.yaml`)
```yaml
# 7つの特徴量カテゴリ管理
feature_groups:
  basic: {enabled: true, priority: 1}
  performance: {enabled: true, priority: 2}
  pedigree: {enabled: true, priority: 3}
  # ... 残り4カテゴリ

# 詳細パラメータ設定
performance_features:
  lookback_races: 10      # 過去10戦分の統計
  min_races: 3           # 最低3戦以上のデータ要求
  weight_decay: 0.9      # 古いレースの重み減衰
```

## 使用方法

### 1. 基本的な使用

```python
from keiba_ai_system.core.features.manager import FeatureEngineeringManager

# 初期化
manager = FeatureEngineeringManager("feature_config.yaml")

# 特徴量計算
enhanced_data = manager.calculate_features(
    data=race_data,
    dependencies={'horse_results_df': horse_results}
)
```

### 2. カスタム特徴量の追加

```python
from keiba_ai_system.core.features.definitions import FeatureDefinition, FeatureCategory, FeatureType

# カスタム計算関数
def my_custom_feature(data, **kwargs):
    return data['馬番'] * data['人気']

# 特徴量定義
custom_feature = FeatureDefinition(
    name='カスタム特徴量',
    category=FeatureCategory.CUSTOM,
    feature_type=FeatureType.NUMERICAL,
    description='馬番と人気の積',
    required_columns=['馬番', '人気'],
    calculator=my_custom_feature
)

# 追加
manager.add_custom_feature(custom_feature)
```

### 3. 特徴量の管理

```python
# 有効な特徴量のリスト
enabled_features = manager.list_features(enabled_only=True)

# カテゴリ別特徴量
basic_features = manager.list_features(category=FeatureCategory.BASIC)

# 特徴量の有効化/無効化
manager.enable_feature('特徴量名')
manager.disable_feature('特徴量名')

# 要約情報
summary = manager.get_feature_summary()
```

## 特徴量カテゴリ

### 基本特徴量 (BASIC)
- 馬番、枠番、斤量、人気
- 性別（数値化）、年齢

### 過去成績特徴量 (PERFORMANCE)
- 出走回数、平均着順、勝率、連対率、複勝率
- 平均賞金、最高賞金
- 前走着順、前走からの間隔

### 血統特徴量 (PEDIGREE)
- 父馬ID、母父馬ID
- 血統統計（産駒成績など）

### 騎手特徴量 (JOCKEY)
- 騎手ID、騎手勝率
- 騎手統計（連対率、複勝率など）

### 調教師特徴量 (TRAINER)
- 調教師ID、調教師勝率
- 調教師統計

### レース条件特徴量 (RACE_CONDITION)
- 距離、距離カテゴリ
- 芝・ダート、開催場

### 高度な特徴量 (ADVANCED)
- 交互作用特徴量
- 複合指標

## 設定ファイル

`feature_config.yaml`で以下を設定可能：

```yaml
feature_groups:
  basic:
    enabled: true
    priority: 1
  performance:
    enabled: true
    priority: 2

performance_features:
  lookback_races: 10
  min_races: 3
  weight_decay: 0.9

encoding:
  categorical_threshold: 50
  rare_category_threshold: 0.01
```

## LiveRacePredictorとの統合

```python
from keiba_ai_system.prediction.live_predictor import LiveRacePredictor
from keiba_ai_system.core.features.manager import FeatureEngineeringManager

# 予測システムと特徴量管理システムを初期化
predictor = LiveRacePredictor()
feature_manager = FeatureEngineeringManager()

# レースデータを取得
race_info, race_results, race_name = predictor.process_race_data(race_id)

# 特徴量を追加
enhanced_data = feature_manager.calculate_features(
    data=race_results,
    dependencies={'horse_results_df': horse_results}
)

# 予測実行
predictions = predictor.predict_race(enhanced_data)
```

## 利点

### 開発効率の向上
- **再利用性**: 一度定義した特徴量は再利用可能
- **保守性**: 特徴量の変更が容易
- **可読性**: 特徴量の意味と計算方法が明確

### 実験の効率化
- **A/Bテスト**: 特徴量の有効/無効を簡単に切り替え
- **パラメータ調整**: 設定ファイルでの簡単な調整
- **バージョン管理**: 特徴量セットのバージョン管理

### 品質管理
- **一貫性**: 特徴量計算の一貫性確保
- **バリデーション**: 自動的なデータ品質チェック
- **ドキュメント**: 特徴量の自動ドキュメント化

### チーム開発
- **標準化**: 特徴量定義の標準化
- **共有**: 特徴量の簡単な共有
- **協力**: 複数人での特徴量開発

## 拡張方法

### 新しい特徴量カテゴリの追加

1. `feature_definitions.py`の`FeatureCategory`に追加
2. `feature_engineering_manager.py`に対応メソッドを追加
3. `feature_calculators.py`に計算関数を追加

### 新しい計算関数の追加

1. `feature_calculators.py`に関数を追加
2. 関数は`(data, **kwargs) -> pd.Series`の形式
3. エラーハンドリングを適切に実装

### 設定項目の追加

1. `feature_config.yaml`に設定項目を追加
2. `_create_default_config()`にデフォルト値を追加
3. 対応する処理ロジックを実装

## 注意事項

1. **メモリ使用量**: 大量の特徴量計算時はメモリ使用量に注意
2. **計算時間**: 複雑な特徴量は計算時間が長くなる可能性
3. **依存関係**: 特徴量間の依存関係を適切に管理
4. **データ品質**: 入力データの品質が特徴量の品質に直結

## トラブルシューティング

### よくあるエラー

1. **必要カラムの不足**: `required_columns`の確認
2. **依存データの不足**: `dependencies`の確認
3. **計算関数のエラー**: 計算関数の実装確認
4. **設定ファイルエラー**: YAML形式の確認

### デバッグ方法

1. ログレベルをDEBUGに設定
2. 個別特徴量での計算テスト
3. サンプルデータでの動作確認

## ライセンス

このプロジェクトのライセンスに従います。
