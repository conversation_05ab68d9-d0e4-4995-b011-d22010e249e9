#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改善版モデルの既存データでのテスト
人気・オッズ重視の改善効果を検証
"""

import pandas as pd
import numpy as np
import joblib
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedModelTester:
    """改善版モデルテスター"""
    
    def __init__(self, model_dir="models"):
        self.model_dir = Path(model_dir)
        self.model = None
        self.scaler = None
        self.features = None
        self.label_encoders = {}
        
    def load_model(self):
        """モデル読み込み"""
        try:
            model_timestamp = "20250608_212220"
            model_path = self.model_dir / f"fixed_leakage_model_{model_timestamp}.pkl"
            scaler_path = self.model_dir / f"fixed_leakage_scaler_{model_timestamp}.pkl"
            features_path = self.model_dir / f"fixed_leakage_features_{model_timestamp}.pkl"
            encoders_path = self.model_dir / f"fixed_leakage_encoders_{model_timestamp}.pkl"
            
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            self.features = joblib.load(features_path)
            
            if encoders_path.exists():
                self.label_encoders = joblib.load(encoders_path)
            
            logger.info(f"モデル読み込み完了: {len(self.features)}特徴量")
            return True
            
        except Exception as e:
            logger.error(f"モデル読み込みエラー: {e}")
            return False
    
    def prepare_improved_features_offline(self, race_data: pd.DataFrame) -> pd.DataFrame:
        """オフライン用改善版特徴量準備"""
        try:
            data = race_data.copy()
            
            # 基本特徴量
            data['枠番'] = pd.to_numeric(data.get('枠番', 1), errors='coerce').fillna(1)
            data['馬番'] = pd.to_numeric(data.get('馬番', 1), errors='coerce').fillna(1)
            data['斤量'] = pd.to_numeric(data.get('斤量', 57.0), errors='coerce').fillna(57.0)
            data['course_len'] = 1600  # デフォルト値
            
            # 重要：人気とオッズ特徴量（改善の核心）
            data['人気'] = pd.to_numeric(data.get('人気', 8), errors='coerce').fillna(8)
            data['単勝オッズ'] = pd.to_numeric(data.get('単勝', 10.0), errors='coerce').fillna(10.0)
            
            # 人気・オッズ由来の特徴量（新規追加）
            data['人気逆数'] = 1.0 / data['人気']
            data['オッズ逆数'] = 1.0 / data['単勝オッズ']
            data['人気順位正規化'] = (len(data) + 1 - data['人気']) / len(data)
            data['log_オッズ'] = np.log(data['単勝オッズ'])
            
            # 人気ベースの過去戦績推定（改善点）
            popularity = data['人気']
            odds = data['単勝オッズ']
            
            # 人気から過去成績を推定（より現実的な関係性）
            estimated_win_rate = np.where(popularity <= 3, 0.3 / popularity, 0.05 / popularity)
            estimated_rank = popularity + np.random.normal(0, 1, len(popularity))  # 人気に若干のノイズ
            estimated_rank = np.clip(estimated_rank, 1, 18)
            
            # オッズから成績を推定
            odds_based_win_rate = np.minimum(0.5, 1.0 / odds)
            
            # 人気とオッズの平均で最終推定
            final_win_rate = (estimated_win_rate + odds_based_win_rate) / 2
            final_rank = (estimated_rank + odds) / 2
            
            # 過去戦績統計（改善版）
            # last_5R統計
            data['着順_last_5R_mean'] = final_rank
            data['人気_last_5R_mean'] = popularity
            data['オッズ_last_5R_mean'] = odds
            data['賞金_last_5R_mean'] = np.where(final_win_rate > 0.1, 1000000, 500000)
            data['斤量_last_5R_mean'] = 57.0
            data['上り_last_5R_mean'] = 35.0 - final_win_rate * 5  # 勝率が高いほど速い上り
            data['体重_last_5R_mean'] = 480
            data['体重変化_last_5R_mean'] = 0.0
            
            # last_10R統計
            data['着順_last_10R_mean'] = final_rank
            data['人気_last_10R_mean'] = popularity
            data['オッズ_last_10R_mean'] = odds
            data['賞金_last_10R_mean'] = np.where(final_win_rate > 0.1, 1000000, 500000)
            data['斤量_last_10R_mean'] = 57.0
            data['上り_last_10R_mean'] = 35.0 - final_win_rate * 5
            data['体重_last_10R_mean'] = 480
            data['体重変化_last_10R_mean'] = 0.0
            
            # all_R統計
            data['着順_all_R_mean'] = final_rank
            data['人気_all_R_mean'] = popularity
            data['オッズ_all_R_mean'] = odds
            data['賞金_all_R_mean'] = np.where(final_win_rate > 0.1, 1000000, 500000)
            data['斤量_all_R_mean'] = 57.0
            data['上り_all_R_mean'] = 35.0 - final_win_rate * 5
            data['体重_all_R_mean'] = 480
            data['体重変化_all_R_mean'] = 0.0
            
            # インターバル
            data['interval_days'] = 30
            
            # カテゴリカル変数処理
            if self.label_encoders:
                for col, encoder in self.label_encoders.items():
                    if col in data.columns:
                        known_classes = set(encoder.classes_)
                        data[col] = data[col].fillna('unknown').astype(str)
                        mask = ~data[col].isin(known_classes)
                        if mask.any():
                            data.loc[mask, col] = encoder.classes_[0]
                        data[col] = encoder.transform(data[col])
                    else:
                        data[col] = 0
            
            # 必要な特徴量を確保
            for col in self.features:
                if col not in data.columns:
                    if 'rank' in col.lower() or '着順' in col:
                        data[col] = final_rank
                    elif 'odds' in col.lower() or 'オッズ' in col:
                        data[col] = odds
                    elif 'popularity' in col.lower() or '人気' in col:
                        data[col] = popularity
                    else:
                        data[col] = 0
            
            # 最終特徴量
            X = data[self.features]
            X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
            
            return X
            
        except Exception as e:
            logger.error(f"特徴量準備エラー: {e}")
            return pd.DataFrame()
    
    def calculate_popularity_weight(self, popularity: pd.Series) -> np.ndarray:
        """人気重み計算（改善の核心）"""
        try:
            # 従来の問題：人気薄を上位予想
            # 改善：人気上位に適切な重み付け
            weights = np.where(popularity == 1, 2.0,      # 1番人気は2倍重み
                      np.where(popularity == 2, 1.5,      # 2番人気は1.5倍
                      np.where(popularity == 3, 1.2,      # 3番人気は1.2倍
                      np.where(popularity <= 5, 1.0,      # 4-5番人気は等倍
                      np.where(popularity <= 10, 0.8,     # 6-10番人気は0.8倍
                      0.5)))))                             # 11番人気以下は0.5倍
            
            return weights
            
        except Exception as e:
            logger.error(f"人気重み計算エラー: {e}")
            return np.ones(len(popularity))
    
    def test_on_existing_data(self):
        """既存データでのテスト"""
        try:
            print("=== 改善版モデルの既存データテスト ===")
            
            # モデル読み込み
            if not self.load_model():
                return False
            
            # 2024年データ読み込み
            race_results = pd.read_pickle("output/race_results_2024.pickle")
            print(f"2024年レース結果: {len(race_results)}件")
            
            # テスト対象レース選択
            race_counts = race_results['race_id'].value_counts()
            test_race_ids = race_counts[race_counts >= 12].head(5).index.tolist()
            
            print(f"テスト対象レース: {len(test_race_ids)}レース")
            
            test_results = []
            
            for race_id in test_race_ids:
                race_horses = race_results[race_results['race_id'] == race_id].copy()
                
                print(f"\n--- レース {race_id} ---")
                print(f"出馬頭数: {len(race_horses)}頭")
                
                # 実際の結果
                actual_results = race_horses[['馬名', '着順', '人気']].copy()
                actual_results = actual_results.sort_values('着順')
                
                print("実際の結果:")
                for _, horse in actual_results.head(3).iterrows():
                    print(f"  {horse['着順']}着: {horse['馬名']} (人気: {horse['人気']})")
                
                # 改善版特徴量準備
                X = self.prepare_improved_features_offline(race_horses)
                
                if X.empty:
                    print("特徴量準備失敗")
                    continue
                
                # 予測実行
                X_scaled = self.scaler.transform(X)
                raw_predictions = self.model.predict(X_scaled)
                
                # 人気重み適用（改善点）
                popularity_weights = self.calculate_popularity_weight(race_horses['人気'])
                adjusted_predictions = raw_predictions * popularity_weights
                
                # 結果比較
                results_df = race_horses[['馬名', '着順', '人気']].copy()
                results_df['生予測'] = raw_predictions
                results_df['調整予測'] = adjusted_predictions
                results_df['生予測順位'] = results_df['生予測'].rank(ascending=False, method='first')
                results_df['調整予測順位'] = results_df['調整予測'].rank(ascending=False, method='first')
                
                # ソート
                results_by_raw = results_df.sort_values('生予測順位')
                results_by_adj = results_df.sort_values('調整予測順位')
                
                print("\n生予測TOP3:")
                for _, horse in results_by_raw.head(3).iterrows():
                    print(f"  予測{horse['生予測順位']:.0f}位: {horse['馬名']} "
                          f"(実際{horse['着順']}着, 人気{horse['人気']})")
                
                print("\n調整予測TOP3:")
                for _, horse in results_by_adj.head(3).iterrows():
                    print(f"  予測{horse['調整予測順位']:.0f}位: {horse['馬名']} "
                          f"(実際{horse['着順']}着, 人気{horse['人気']})")
                
                # 評価指標計算
                actual_winner = actual_results.iloc[0]['馬名']
                
                # 生予測の精度
                raw_winner = results_by_raw.iloc[0]['馬名']
                raw_winner_hit = raw_winner == actual_winner
                raw_top3 = set(results_by_raw.head(3)['馬名'].tolist())
                actual_top3 = set(actual_results.head(3)['馬名'].tolist())
                raw_top3_hits = len(raw_top3 & actual_top3)
                
                # 調整予測の精度
                adj_winner = results_by_adj.iloc[0]['馬名']
                adj_winner_hit = adj_winner == actual_winner
                adj_top3 = set(results_by_adj.head(3)['馬名'].tolist())
                adj_top3_hits = len(adj_top3 & actual_top3)
                
                # 人気1位の順位
                favorite = race_horses[race_horses['人気'] == 1]
                if not favorite.empty:
                    favorite_name = favorite.iloc[0]['馬名']
                    favorite_actual_rank = favorite.iloc[0]['着順']
                    
                    favorite_raw_rank = results_by_raw[results_by_raw['馬名'] == favorite_name]['生予測順位'].iloc[0]
                    favorite_adj_rank = results_by_adj[results_by_adj['馬名'] == favorite_name]['調整予測順位'].iloc[0]
                    
                    print(f"\n1番人気 {favorite_name}:")
                    print(f"  実際: {favorite_actual_rank}着")
                    print(f"  生予測: {favorite_raw_rank:.0f}位")
                    print(f"  調整予測: {favorite_adj_rank:.0f}位")
                
                result_summary = {
                    'race_id': race_id,
                    'horses': len(race_horses),
                    'raw_winner_hit': raw_winner_hit,
                    'adj_winner_hit': adj_winner_hit,
                    'raw_top3_accuracy': raw_top3_hits / 3,
                    'adj_top3_accuracy': adj_top3_hits / 3,
                    'actual_winner': actual_winner,
                    'raw_predicted_winner': raw_winner,
                    'adj_predicted_winner': adj_winner
                }
                
                test_results.append(result_summary)
                
                print(f"\n=== 評価 ===")
                print(f"生予測 - 1着的中: {'○' if raw_winner_hit else '×'}, 3着内: {raw_top3_hits}/3")
                print(f"調整予測 - 1着的中: {'○' if adj_winner_hit else '×'}, 3着内: {adj_top3_hits}/3")
            
            # 総合評価
            if test_results:
                print(f"\n=== 総合評価 ({len(test_results)}レース) ===")
                
                raw_winner_rate = sum(r['raw_winner_hit'] for r in test_results) / len(test_results)
                adj_winner_rate = sum(r['adj_winner_hit'] for r in test_results) / len(test_results)
                raw_top3_rate = np.mean([r['raw_top3_accuracy'] for r in test_results])
                adj_top3_rate = np.mean([r['adj_top3_accuracy'] for r in test_results])
                
                print(f"【生予測】1着的中率: {raw_winner_rate:.1%}, 3着内的中率: {raw_top3_rate:.1%}")
                print(f"【調整予測】1着的中率: {adj_winner_rate:.1%}, 3着内的中率: {adj_top3_rate:.1%}")
                
                improvement_winner = (adj_winner_rate - raw_winner_rate) * 100
                improvement_top3 = (adj_top3_rate - raw_top3_rate) * 100
                
                print(f"\n=== 改善効果 ===")
                print(f"1着的中率改善: {improvement_winner:+.1f}ポイント")
                print(f"3着内的中率改善: {improvement_top3:+.1f}ポイント")
                
                if improvement_winner > 0 or improvement_top3 > 0:
                    print("✓ 人気・オッズ重み調整による改善効果を確認")
                else:
                    print("△ さらなる調整が必要")
                
                # 結果保存
                results_df = pd.DataFrame(test_results)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                results_path = f"improved_model_test_results_{timestamp}.csv"
                results_df.to_csv(results_path, index=False)
                print(f"\n詳細結果保存: {results_path}")
                
                return True
            else:
                print("テスト結果がありません")
                return False
                
        except Exception as e:
            logger.error(f"テストエラー: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    tester = ImprovedModelTester()
    tester.test_on_existing_data()