#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
強化版モデル学習スクリプト - 馬の過去戦績を特徴量に追加
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
import joblib
import logging
from datetime import datetime, timedelta
from pathlib import Path

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_data():
    """既存のpickleファイルからデータを読み込み"""
    try:
        # レース情報とレース結果を結合
        race_info = pd.read_pickle('output/race_info_2020.pickle')
        race_results = pd.read_pickle('output/race_results_2020.pickle')
        horse_results = pd.read_pickle('output/horse_results_2020.pickle')
        
        # データを結合
        data = pd.merge(race_results, race_info, on='race_id', how='left')
        logger.info(f"レースデータ読み込み完了: {len(data)}件")
        logger.info(f"馬戦績データ読み込み完了: {len(horse_results)}件")
        
        return data, horse_results
        
    except Exception as e:
        logger.error(f"データ読み込みエラー: {e}")
        raise

def calculate_past_performance(race_data, horse_results):
    """
    馬の過去戦績統計を計算（データリーケージを避ける）
    
    Parameters
    ----------
    race_data : pd.DataFrame
        レースデータ
    horse_results : pd.DataFrame
        馬の戦績データ
        
    Returns
    -------
    pd.DataFrame
        過去戦績統計付きのレースデータ
    """
    logger.info("馬の過去戦績統計を計算中...")
    
    # 日付を datetime 型に変換
    # 日付フォーマットを自動検出（日本語形式も含む）
    try:
        race_data['date'] = pd.to_datetime(race_data['date'], format='%Y年%m月%d日', errors='coerce')
    except:
        race_data['date'] = pd.to_datetime(race_data['date'], errors='coerce')
    
    try:
        horse_results['日付'] = pd.to_datetime(horse_results['日付'], format='%Y年%m月%d日', errors='coerce')
    except:
        horse_results['日付'] = pd.to_datetime(horse_results['日付'], errors='coerce')
    
    # 着順を数値に変換
    if '着順' in horse_results.columns:
        horse_results['着順_num'] = pd.to_numeric(horse_results['着順'], errors='coerce')
    
    # 各馬の各レースにおける過去戦績を計算
    enhanced_data = race_data.copy()
    
    # 過去戦績統計用のカラムを初期化
    past_stats_columns = [
        'past_races_count',  # 過去レース数
        'past_win_rate',     # 勝率
        'past_top3_rate',    # 3着以内率
        'past_avg_rank',     # 平均着順
        'past_std_rank',     # 着順標準偏差
        'days_since_last_race',  # 前走からの日数
        'past_distance_similar_races',  # 同距離での過去レース数
        'past_distance_win_rate',       # 同距離での勝率
        'past_surface_races',           # 同馬場での過去レース数
        'past_surface_win_rate'         # 同馬場での勝率
    ]
    
    for col in past_stats_columns:
        enhanced_data[col] = 0.0
    
    # 馬IDでグループ化して過去戦績を計算
    unique_horses = enhanced_data['horse_id'].unique()
    
    for i, horse_id in enumerate(unique_horses):
        if i % 1000 == 0:
            logger.info(f"過去戦績計算進捗: {i}/{len(unique_horses)} ({i/len(unique_horses)*100:.1f}%)")
        
        # その馬のレースデータ
        horse_race_data = enhanced_data[enhanced_data['horse_id'] == horse_id].copy()
        
        # horse_resultsにhorse_idカラムがない場合の対処
        if 'horse_id' in horse_results.columns:
            horse_past_results = horse_results[horse_results['horse_id'] == horse_id].copy()
        else:
            # horse_idがない場合は空のDataFrameを作成
            horse_past_results = pd.DataFrame()
        
        if horse_past_results.empty:
            continue
        
        # 日付でソート
        horse_race_data = horse_race_data.sort_values('date')
        horse_past_results = horse_past_results.sort_values('日付')
        
        for idx, race_row in horse_race_data.iterrows():
            race_date = race_row['date']
            race_distance = race_row.get('course_len', 0)
            race_surface = race_row.get('race_type', '')
            
            # その日より前の戦績のみを使用（データリーケージ回避）
            past_results = horse_past_results[horse_past_results['日付'] < race_date].copy()
            
            if len(past_results) == 0:
                continue
            
            # 基本統計
            past_races_count = len(past_results)
            
            # 着順統計（NaN除外）
            valid_ranks = past_results['着順_num'].dropna()
            if len(valid_ranks) > 0:
                past_win_rate = (valid_ranks == 1).mean()
                past_top3_rate = (valid_ranks <= 3).mean()
                past_avg_rank = valid_ranks.mean()
                past_std_rank = valid_ranks.std() if len(valid_ranks) > 1 else 0.0
            else:
                past_win_rate = past_top3_rate = past_avg_rank = past_std_rank = 0.0
            
            # 前走からの日数
            last_race_date = past_results['日付'].max()
            days_since_last_race = (race_date - last_race_date).days
            
            # 同距離での戦績（±200m以内）
            if race_distance > 0:
                similar_distance_results = past_results[
                    abs(past_results.get('距離', 0) - race_distance) <= 200
                ]
                past_distance_similar_races = len(similar_distance_results)
                if len(similar_distance_results) > 0:
                    distance_valid_ranks = similar_distance_results.get('着順_num', pd.Series()).dropna()
                    past_distance_win_rate = (distance_valid_ranks == 1).mean() if len(distance_valid_ranks) > 0 else 0.0
                else:
                    past_distance_win_rate = 0.0
            else:
                past_distance_similar_races = past_distance_win_rate = 0.0
            
            # 同馬場での戦績
            if race_surface:
                surface_results = past_results[
                    past_results.get('コース', '').str.contains(race_surface, na=False)
                ]
                past_surface_races = len(surface_results)
                if len(surface_results) > 0:
                    surface_valid_ranks = surface_results.get('着順_num', pd.Series()).dropna()
                    past_surface_win_rate = (surface_valid_ranks == 1).mean() if len(surface_valid_ranks) > 0 else 0.0
                else:
                    past_surface_win_rate = 0.0
            else:
                past_surface_races = past_surface_win_rate = 0.0
            
            # 統計値を更新
            enhanced_data.loc[idx, 'past_races_count'] = past_races_count
            enhanced_data.loc[idx, 'past_win_rate'] = past_win_rate
            enhanced_data.loc[idx, 'past_top3_rate'] = past_top3_rate
            enhanced_data.loc[idx, 'past_avg_rank'] = past_avg_rank
            enhanced_data.loc[idx, 'past_std_rank'] = past_std_rank
            enhanced_data.loc[idx, 'days_since_last_race'] = days_since_last_race
            enhanced_data.loc[idx, 'past_distance_similar_races'] = past_distance_similar_races
            enhanced_data.loc[idx, 'past_distance_win_rate'] = past_distance_win_rate
            enhanced_data.loc[idx, 'past_surface_races'] = past_surface_races
            enhanced_data.loc[idx, 'past_surface_win_rate'] = past_surface_win_rate
    
    # 過去戦績統計のサマリー
    logger.info("=== 過去戦績統計サマリー ===")
    logger.info(f"過去レース数 - 平均: {enhanced_data['past_races_count'].mean():.1f}, 最大: {enhanced_data['past_races_count'].max()}")
    logger.info(f"勝率 - 平均: {enhanced_data['past_win_rate'].mean():.3f}")
    logger.info(f"3着以内率 - 平均: {enhanced_data['past_top3_rate'].mean():.3f}")
    logger.info(f"平均着順 - 全体平均: {enhanced_data['past_avg_rank'].mean():.2f}")
    
    return enhanced_data

def prepare_features(data):
    """特徴量の準備（過去戦績を含む）"""
    logger.info("特徴量準備開始（過去戦績込み）")
    
    # 必要なカラムの確認
    if '着順' not in data.columns:
        raise ValueError("着順カラムが見つかりません")
    
    # 着順を数値に変換
    data = data.copy()  # コピーを作成してSettingWithCopyWarningを回避
    data['着順'] = pd.to_numeric(data['着順'], errors='coerce')
    data = data.dropna(subset=['着順'])
    
    # ターゲット変数：3着以内かどうか
    data['target'] = (data['着順'] <= 3).astype(int)
    
    # 基本的な特徴量を選択
    feature_cols = []
    
    # 数値特徴量
    numeric_cols = ['course_len', '枠番', '馬番', '斤量']
    for col in numeric_cols:
        if col in data.columns:
            data[col] = pd.to_numeric(data[col], errors='coerce')
            feature_cols.append(col)
    
    # 過去戦績特徴量を追加
    past_performance_cols = [
        'past_races_count', 'past_win_rate', 'past_top3_rate', 
        'past_avg_rank', 'past_std_rank', 'days_since_last_race',
        'past_distance_similar_races', 'past_distance_win_rate',
        'past_surface_races', 'past_surface_win_rate'
    ]
    
    for col in past_performance_cols:
        if col in data.columns:
            feature_cols.append(col)
    
    # カテゴリカル特徴量のエンコーディング
    categorical_cols = ['race_type', 'ground_state', 'weather', 'track_direction']
    le_dict = {}
    
    for col in categorical_cols:
        if col in data.columns:
            le = LabelEncoder()
            data[f'{col}_encoded'] = le.fit_transform(data[col].astype(str))
            feature_cols.append(f'{col}_encoded')
            le_dict[col] = le
    
    # 年齢特徴量（性齢から抽出）
    if '性齢' in data.columns:
        data['年齢'] = data['性齢'].str.extract(r'(\d+)').astype(float)
        feature_cols.append('年齢')
        
        # 性別
        data['性別_牡'] = data['性齢'].str.contains('牡', na=False).astype(int)
        data['性別_牝'] = data['性齢'].str.contains('牝', na=False).astype(int)
        feature_cols.extend(['性別_牡', '性別_牝'])
    
    # 距離カテゴリ
    if 'course_len' in data.columns:
        data['距離_短距離'] = (data['course_len'] <= 1400).astype(int)
        data['距離_マイル'] = ((data['course_len'] > 1400) & (data['course_len'] <= 1800)).astype(int)
        data['距離_中距離'] = ((data['course_len'] > 1800) & (data['course_len'] <= 2200)).astype(int)
        data['距離_長距離'] = (data['course_len'] > 2200).astype(int)
        feature_cols.extend(['距離_短距離', '距離_マイル', '距離_中距離', '距離_長距離'])
    
    # 過去戦績から派生した特徴量
    if 'past_races_count' in data.columns and 'past_win_rate' in data.columns:
        # 経験豊富な馬フラグ
        data['経験豊富'] = (data['past_races_count'] >= 5).astype(int)
        feature_cols.append('経験豊富')
        
        # 好調馬フラグ（勝率15%以上）
        data['好調'] = (data['past_win_rate'] >= 0.15).astype(int)
        feature_cols.append('好調')
        
        # 連続出走フラグ（前走から30日以内）
        data['連続出走'] = (data['days_since_last_race'] <= 30).astype(int)
        feature_cols.append('連続出走')
    
    # 欠損値を埋める
    for col in feature_cols:
        if col in data.columns:
            data[col] = data[col].fillna(0)
    
    # 使用可能な特徴量のみを選択
    available_features = [col for col in feature_cols if col in data.columns]
    
    X = data[available_features]
    y = data['target']
    
    logger.info(f"特徴量数: {len(available_features)}")
    logger.info(f"基本特徴量: {[col for col in available_features if not col.startswith('past_')]}")
    logger.info(f"過去戦績特徴量: {[col for col in available_features if col.startswith('past_')]}")
    logger.info(f"データ形状: X={X.shape}, y={y.shape}")
    logger.info(f"正例率: {y.mean():.3f}")
    
    return X, y, available_features, data

def train_model(X, y):
    """モデル学習"""
    logger.info("モデル学習開始")
    
    # 時系列分割（古い80%を学習、新しい20%をテスト）
    # ここでは簡単にランダム分割を使用
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # データ正規化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # LightGBM学習
    lgb_train = lgb.Dataset(X_train_scaled, label=y_train)
    lgb_valid = lgb.Dataset(X_test_scaled, label=y_test, reference=lgb_train)
    
    params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': 0
    }
    
    model = lgb.train(
        params,
        lgb_train,
        valid_sets=[lgb_valid],
        num_boost_round=200,
        callbacks=[lgb.early_stopping(stopping_rounds=20), lgb.log_evaluation(0)]
    )
    
    # 予測と評価
    y_proba = model.predict(X_test_scaled)
    y_pred = (y_proba > 0.5).astype(int)
    
    # 評価指標
    accuracy = accuracy_score(y_test, y_pred)
    auc = roc_auc_score(y_test, y_proba)
    
    # 上位予測の適中率
    def precision_at_k(y_true, y_proba, k):
        threshold_idx = int(len(y_proba) * k)
        if threshold_idx == 0:
            return 0.0
        top_k_indices = np.argsort(y_proba)[-threshold_idx:]
        return y_true.iloc[top_k_indices].mean()
    
    precision_at_10 = precision_at_k(y_test, y_proba, 0.1)
    precision_at_20 = precision_at_k(y_test, y_proba, 0.2)
    
    results = {
        'model': model,
        'scaler': scaler,
        'accuracy': accuracy,
        'auc': auc,
        'precision_at_10': precision_at_10,
        'precision_at_20': precision_at_20,
        'train_size': len(X_train),
        'test_size': len(X_test)
    }
    
    logger.info(f"=== 学習結果（過去戦績込み） ===")
    logger.info(f"精度: {accuracy:.4f}")
    logger.info(f"AUC: {auc:.4f}")
    logger.info(f"適中率@10%: {precision_at_10:.4f}")
    logger.info(f"適中率@20%: {precision_at_20:.4f}")
    logger.info(f"学習データ: {len(X_train)}件")
    logger.info(f"テストデータ: {len(X_test)}件")
    
    return results

def save_model(model, scaler, features):
    """モデル保存"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # ディレクトリ作成
    models_dir = Path('models')
    models_dir.mkdir(exist_ok=True)
    
    # ファイル保存
    model_path = models_dir / f"enhanced_lgb_model_{timestamp}.pkl"
    scaler_path = models_dir / f"enhanced_scaler_{timestamp}.pkl"
    features_path = models_dir / f"enhanced_features_{timestamp}.pkl"
    
    joblib.dump(model, model_path)
    joblib.dump(scaler, scaler_path)
    joblib.dump(features, features_path)
    
    logger.info(f"強化モデル保存完了:")
    logger.info(f"  モデル: {model_path}")
    logger.info(f"  スケーラー: {scaler_path}")
    logger.info(f"  特徴量: {features_path}")
    
    return {
        'model': str(model_path),
        'scaler': str(scaler_path),
        'features': str(features_path)
    }

def main():
    """メイン関数"""
    try:
        # データ読み込み
        race_data, horse_results = load_data()
        
        # 過去戦績統計を計算
        enhanced_data = calculate_past_performance(race_data, horse_results)
        
        # 特徴量準備
        X, y, features, processed_data = prepare_features(enhanced_data)
        
        # モデル学習
        results = train_model(X, y)
        
        # モデル保存
        saved_paths = save_model(results['model'], results['scaler'], features)
        
        print("\n" + "="*60)
        print("[競馬AI] 学習完了（過去戦績強化版）!")
        print("="*60)
        print(f"精度: {results['accuracy']:.4f}")
        print(f"AUC: {results['auc']:.4f}")
        print(f"適中率@10%: {results['precision_at_10']:.4f}")
        print(f"適中率@20%: {results['precision_at_20']:.4f}")
        print(f"学習データ: {results['train_size']:,}件")
        print(f"テストデータ: {results['test_size']:,}件")
        print(f"総特徴量数: {len(features)}個")
        
        # 特徴量重要度
        importance = results['model'].feature_importance(importance_type='gain')
        feature_importance = list(zip(features, importance))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        print("\n🔝 特徴量重要度 Top 10:")
        for i, (feat, imp) in enumerate(feature_importance[:10]):
            print(f"{i+1:2d}. {feat}: {imp:.1f}")
        
        print("\n📁 保存ファイル:")
        for key, path in saved_paths.items():
            print(f"  {key}: {path}")
        
        return results
        
    except Exception as e:
        logger.error(f"学習中にエラー: {e}")
        raise

if __name__ == "__main__":
    main()