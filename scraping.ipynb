{"cells": [{"cell_type": "markdown", "id": "10bb4622", "metadata": {}, "source": ["# netkeiba HTML スクレイピング例ノートブック（リファクタリング版）\n", "このノートブックは `refactored_scrap.py` の関数を使って netkeiba.com からHTMLをスクレイピングする例です。\n", "コードの重複を減らすためにリファクタリングされた関数を使用しています。"]}, {"cell_type": "code", "execution_count": 1, "id": "f81cc4bd", "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import sys\n", "import random\n", "\n", "import pandas as pd\n", "from tqdm.auto import tqdm\n", "\n", "# sys.path.append('.')  # scraper.pyが同じディレクトリにある場合\n", "from core.scrapers.scraper import (\n", "    scrape_netkeiba_race_dates,\n", "    scrape_netkeiba_race_ids,\n", "    scrape_html_race,\n", "    scrape_html_horse,\n", "    scrape_html_ped,\n", "    scrape_html_horse_with_master\n", ")\n", "from core.processors.race_processor import RaceProcessor\n", "from core.utils.constants import USER_AGENTS, BASE_HEADERS, UrlPaths, LocalPaths, REQUEST_TIMEOUT"]}, {"cell_type": "markdown", "id": "49217daf", "metadata": {}, "source": ["## ロガーの設定"]}, {"cell_type": "code", "execution_count": 6, "id": "5150e183", "metadata": {}, "outputs": [], "source": ["# # デバッグモードを有効にする場合はTrue（詳細なログが出力されます）\n", "# # 通常の実行では False に設定することで、重要なログのみ表示されます\n", "# setup_logger(debug_level=True)"]}, {"cell_type": "markdown", "id": "63c05d20", "metadata": {}, "source": ["## 開催日リストの取得例"]}, {"cell_type": "code", "execution_count": 2, "id": "fb5689d8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-10 16:12:21,962 - INFO - 2024-01-01 から 2024-03-01 までの開催日を取得します。\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "43bd2b23b70841ab8aa8b6e1fcc215bc", "version_major": 2, "version_minor": 0}, "text/plain": ["開催カレンダー取得中:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-10 16:12:21,976 - INFO - URLにアクセスします: https://race.netkeiba.com/top/calendar.html?year=2024&month=1\n", "2025-06-10 16:12:22,136 - INFO - 4.74秒待機します。\n", "2025-06-10 16:12:26,911 - INFO - URLにアクセスします: https://race.netkeiba.com/top/calendar.html?year=2024&month=2\n", "2025-06-10 16:12:27,030 - INFO - 5.25秒待機します。\n", "2025-06-10 16:12:32,306 - INFO - URLにアクセスします: https://race.netkeiba.com/top/calendar.html?year=2024&month=3\n", "2025-06-10 16:12:32,469 - INFO - 3.36秒待機します。\n", "2025-06-10 16:12:35,868 - INFO - 取得した開催日数: 27\n"]}, {"name": "stdout", "output_type": "stream", "text": ["取得した開催日数: 27\n", "['20240106', '20240107', '20240108', '20240113', '20240114']\n"]}], "source": ["# 例: 2024年1月〜2024年2月の開催日を取得\n", "from_date = '2024-01-01'\n", "to_date = '2024-03-01'\n", "kaisai_dates = scrape_netkeiba_race_dates(from_date, to_date)\n", "print(f'取得した開催日数: {len(kaisai_dates)}')\n", "print(kaisai_dates[:5])  # 最初の5件だけ表示"]}, {"cell_type": "markdown", "id": "2d4f70bf", "metadata": {}, "source": ["## レースIDリストの取得例（最初の開催日だけ）"]}, {"cell_type": "code", "execution_count": 3, "id": "518cdb46", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-10 16:12:39,270 - INFO - ====== WebDriver manager ======\n", "2025-06-10 16:12:40,325 - INFO - Get LATEST chromedriver version for google-chrome\n", "2025-06-10 16:12:40,365 - INFO - Get LATEST chromedriver version for google-chrome\n", "2025-06-10 16:12:40,391 - INFO - Driver [C:\\Users\\<USER>\\.wdm\\drivers\\chromedriver\\win64\\137.0.7151.68\\chromedriver-win32/chromedriver.exe] found in cache\n", "2025-06-10 16:12:41,457 - INFO - Chrome WebDriverをヘッドレスモードで初期化しました。\n", "2025-06-10 16:12:41,457 - INFO - ページロードタイムアウト: 180秒, スクリプトタイムアウト: 180秒\n", "2025-06-10 16:12:41,459 - INFO - デバッグモードで実行中 (scrape_netkeiba_race_ids)\n", "2025-06-10 16:12:41,460 - INFO - getting race_id_list\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "828e316943224b99894a32794a1e8147", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-10 16:12:41,469 - INFO - scraping: https://race.netkeiba.com/top/race_list.html?kaisai_date=20240106\n", "2025-06-10 16:12:44,080 - INFO - 4.80秒待機します。\n", "2025-06-10 16:12:51,050 - INFO - WebDriverを終了しました。\n", "2025-06-10 16:12:51,051 - INFO - 取得したレースID数: 24\n"]}, {"name": "stdout", "output_type": "stream", "text": ["取得したレースID数: 24\n", "['202406010101', '202406010102', '202406010103', '202406010104', '202406010105', '202406010106', '202406010107', '202406010108', '202406010109', '202406010110', '202406010111', '202406010112', '202408010101', '202408010102', '202408010103', '202408010104', '202408010105', '202408010106', '202408010107', '202408010108', '202408010109', '202408010110', '202408010111', '202408010112']\n"]}], "source": ["# 例: 最初の開催日だけでレースIDを取得\n", "if kaisai_dates:\n", "    try:\n", "        race_ids = scrape_netkeiba_race_ids([kaisai_dates[0]], debug=True)\n", "        if race_ids:\n", "            print(f'取得したレースID数: {len(race_ids)}')\n", "            print(race_ids)\n", "        else:\n", "            print('レースIDが取得できませんでした。ネットワークやWebDriverの設定を確認してください。')\n", "    except Exception as e:\n", "        print(f'エラーが発生しました: {e}')\n", "else:\n", "    print('開催日が取得できませんでした')"]}, {"cell_type": "markdown", "id": "3d4f70bf", "metadata": {}, "source": ["## レース情報のHTMLスクレイピング例"]}, {"cell_type": "code", "execution_count": 4, "id": "4d8cdb46", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-10 16:12:57,313 - INFO - レースHTMLのスクレイピングを開始します。対象レース数: 3\n"]}, {"name": "stdout", "output_type": "stream", "text": ["スクレイピング対象のレースID: ['202406010101', '202406010102', '202406010103']\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b6aef236bda949afa94ba7b0c4a05d48", "version_major": 2, "version_minor": 0}, "text/plain": ["レースHTML取得中（年代別保存）:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-10 16:12:57,326 - INFO - 年代別ディレクトリに保存したレースHTML数: 3\n"]}, {"name": "stdout", "output_type": "stream", "text": ["スクレイピングしたレース情報: 3件\n", "['202406010101', '202406010102', '202406010103']\n"]}], "source": ["# 最初の3つのレースIDだけを使用\n", "if race_ids and len(race_ids) > 0:\n", "    target_race_ids = race_ids[:3]\n", "    print(f'スクレイピング対象のレースID: {target_race_ids}')\n", "    \n", "    # レース情報のHTMLをスクレイピング\n", "    race_html_paths = scrape_html_race(target_race_ids, skip=True)\n", "    print(f'スクレイピングしたレース情報: {len(race_html_paths)}件')\n", "    print(race_html_paths)\n", "else:\n", "    print('スクレイピング対象のレースIDがありません')"]}, {"cell_type": "markdown", "id": "5d4f70bf", "metadata": {}, "source": ["## 馬情報のHTMLスクレイピング例"]}, {"cell_type": "code", "execution_count": 7, "id": "0624a6c3", "metadata": {}, "outputs": [], "source": ["race_processor = RaceProcessor()"]}, {"cell_type": "code", "execution_count": 9, "id": "6d8cdb46", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["処理対象のファイルが見つかりません: data//html/race/race_by_year\\2012\\*.bin\n", "馬HTML取得中（年代別保存）: 0it [00:00, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["スクレイピングした馬情報: 0件\n", "[]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["horse_ids = race_processor.extract_horse_ids_from_race_data(year=2012)\n", "# 馬情報のHTMLをスクレイピング\n", "horse_html_paths = scrape_html_horse(horse_ids, skip=True)\n", "print(f'スクレイピングした馬情報: {len(horse_html_paths)}件')\n", "print(horse_html_paths)"]}, {"cell_type": "code", "execution_count": null, "id": "84d5e192", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-30 06:40:10,244 - module.refactored_scrap - INFO - レースデータから馬IDを抽出します...\n", "2025-05-30 06:40:10,264 - module.refactored_scrap - INFO - キャッシュからデータを読み込みます: data/cache\\horse_ids_2019.pkl\n", "2025-05-30 06:40:10,295 - module.refactored_scrap - INFO - キャッシュから読み込んだ馬ID数: 11557\n", "2025-05-30 06:40:10,296 - module.refactored_scrap - INFO - 血統HTMLのスクレイピングを開始します。対象馬数: 11557\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "220004b53010401e895b1ccd9ad60cca", "version_major": 2, "version_minor": 0}, "text/plain": ["血統HTML取得中（年代別保存）:   0%|          | 0/11557 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["スクレイピングした馬血統情報: 11557件\n", "['2007100107', '2008100889', '2008104268', '2009100502', '2009100509', '2009100706', '2009100957', '2009101274', '2009102606', '2009103405', '2009105407', '2010100035', '2010100155', '2010100307', '2010100540', '2010100690', '2010100880', '2010100960', '2010101161', '2010101667', '2010101767', '2010101798', '2010101988', '2010102243', '2010102500', '2010102637', '2010102928', '2010103013', '2010103225', '2010103339', '2010104370', '2010104486', '2010104510', '2010104563', '2010105629', '2010105786', '2010105964', '2010106229', '2010109123', '2010200007', '2011100057', '2011100063', '2011100215', '2011100296', '2011100416', '2011100590', '2011100625', '2011100643', '2011100651', '2011100715', '2011100822', '2011100941', '2011101037', '2011101038', '2011101041', '2011101045', '2011101046', '2011101089', '2011101095', '2011101125', '2011101149', '2011101337', '2011101476', '2011101479', '2011101513', '2011101542', '2011101727', '2011101737', '2011101773', '2011101774', '2011101782', '2011101806', '2011101814', '2011101909', '2011102076', '2011102102', '2011102115', '2011102128', '2011102151', '2011102177', '2011102236', '2011102464', '2011102525', '2011102556', '2011102559', '2011102579', '2011102899', '2011102912', '2011103071', '2011103097', '2011103159', '2011103191', '2011103229', '2011103348', '2011103385', '2011103410', '2011103412', '2011103430', '2011103555', '2011103567', '2011103632', '2011103670', '2011103690', '2011103696', '2011103702', '2011103741', '2011103768', '2011103797', '2011103833', '2011103836', '2011103862', '2011103872', '2011103905', '2011103940', '2011103988', '2011103989', '2011104019', '2011104117', '2011104166', '2011104171', '2011104172', '2011104182', '2011104205', '2011104263', '2011104402', '2011104419', '2011104420', '2011104437', '2011104568', '2011104575', '2011104627', '2011104676', '2011104700', '2011104759', '2011104831', '2011104859', '2011104994', '2011105044', '2011105107', '2011105200', '2011105236', '2011105350', '2011105477', '2011105520', '2011105539', '2011105541', '2011105789', '2011105881', '2011105897', '2011105898', '2011105905', '2011105914', '2011105960', '2011105967', '2011105974', '2011105975', '2011106002', '2011106163', '2011106267', '2011106314', '2011106350', '2011106393', '2011106394', '2011106405', '2011106466', '2011106481', '2011106544', '2011106567', '2011106571', '2011106610', '2011109234', '2011110009', '2011110074', '2012100075', '2012100085', '2012100086', '2012100091', '2012100116', '2012100243', '2012100276', '2012100308', '2012100309', '2012100406', '2012100407', '2012100411', '2012100413', '2012100517', '2012100518', '2012100531', '2012100539', '2012100555', '2012100560', '2012100564', '2012100565', '2012100591', '2012100601', '2012100608', '2012100624', '2012100627', '2012100636', '2012100683', '2012100740', '2012100777', '2012100781', '2012100798', '2012100801', '2012100912', '2012100936', '2012100939', '2012100961', '2012100991', '2012100996', '2012100997', '2012100999', '2012101013', '2012101143', '2012101157', '2012101184', '2012101215', '2012101232', '2012101246', '2012101265', '2012101282', '2012101289', '2012101321', '2012101351', '2012101359', '2012101386', '2012101393', '2012101406', '2012101409', '2012101430', '2012101483', '2012101489', '2012101502', '2012101586', '2012101643', '2012101673', '2012101685', '2012101704', '2012101723', '2012101787', '2012101801', '2012101809', '2012101892', '2012101970', '2012102017', '2012102034', '2012102052', '2012102059', '2012102070', '2012102092', '2012102095', '2012102157', '2012102178', '2012102179', '2012102265', '2012102281', '2012102359', '2012102404', '2012102452', '2012102456', '2012102463', '2012102477', '2012102486', '2012102503', '2012102542', '2012102566', '2012102583', '2012102624', '2012102641', '2012102649', '2012102701', '2012102708', '2012102736', '2012102758', '2012102766', '2012102773', '2012102808', '2012102848', '2012102849', '2012102856', '2012102942', '2012103064', '2012103076', '2012103129', '2012103146', '2012103185', '2012103204', '2012103211', '2012103249', '2012103262', '2012103265', '2012103286', '2012103352', '2012103376', '2012103379', '2012103387', '2012103405', '2012103429', '2012103484', '2012103491', '2012103496', '2012103499', '2012103532', '2012103540', '2012103583', '2012103591', '2012103615', '2012103624', '2012103628', '2012103634', '2012103663', '2012103680', '2012103708', '2012103724', '2012103731', '2012103796', '2012103844', '2012103851', '2012103870', '2012103874', '2012103930', '2012103931', '2012103944', '2012103962', '2012103981', '2012103988', '2012104003', '2012104019', '2012104052', '2012104064', '2012104076', '2012104085', '2012104098', '2012104108', '2012104112', '2012104115', '2012104138', '2012104139', '2012104164', '2012104167', '2012104192', '2012104207', '2012104220', '2012104246', '2012104259', '2012104268', '2012104298', '2012104314', '2012104319', '2012104329', '2012104336', '2012104346', '2012104388', '2012104392', '2012104400', '2012104401', '2012104440', '2012104463', '2012104466', '2012104467', '2012104503', '2012104504', '2012104508', '2012104509', '2012104510', '2012104538', '2012104539', '2012104541', '2012104549', '2012104581', '2012104594', '2012104602', '2012104625', '2012104648', '2012104650', '2012104654', '2012104657', '2012104659', '2012104665', '2012104667', '2012104675', '2012104678', '2012104685', '2012104696', '2012104723', '2012104730', '2012104737', '2012104742', '2012104746', '2012104759', '2012104761', '2012104772', '2012104778', '2012104780', '2012104783', '2012104787', '2012104792', '2012104808', '2012104814', '2012104825', '2012104882', '2012104925', '2012104926', '2012104936', '2012105027', '2012105108', '2012105113', '2012105115', '2012105157', '2012105194', '2012105238', '2012105252', '2012105255', '2012105305', '2012105320', '2012105337', '2012105342', '2012105360', '2012105389', '2012105406', '2012105419', '2012105434', '2012105451', '2012105474', '2012105495', '2012105498', '2012105510', '2012105519', '2012105538', '2012105571', '2012105582', '2012105586', '2012105606', '2012105609', '2012105612', '2012105635', '2012105677', '2012105695', '2012105747', '2012105767', '2012105799', '2012105800', '2012105830', '2012105841', '2012105842', '2012105906', '2012105920', '2012105949', '2012105988', '2012106003', '2012106005', '2012106023', '2012106039', '2012106042', '2012106055', '2012106056', '2012106071', '2012106103', '2012106162', '2012106200', '2012106232', '2012106250', '2012106253', '2012106262', '2012106358', '2012106377', '2012106388', '2012106426', '2012106435', '2012109092', '2012109217', '2012110001', '2012110030', '2012110043', '2012110058', '2012110063', '2012110077', '2012110079', '2012110089', '2012110091', '2012110113', '2013100001', '2013100005', '2013100007', '2013100032', '2013100041', '2013100057', '2013100159', '2013100206', '2013100214', '2013100224', '2013100315', '2013100323', '2013100332', '2013100339', '2013100354', '2013100358', '2013100361', '2013100376', '2013100380', '2013100386', '2013100389', '2013100401', '2013100432', '2013100461', '2013100488', '2013100491', '2013100522', '2013100561', '2013100569', '2013100580', '2013100590', '2013100591', '2013100594', '2013100597', '2013100615', '2013100636', '2013100640', '2013100641', '2013100647', '2013100654', '2013100655', '2013100677', '2013100690', '2013100700', '2013100704', '2013100708', '2013100711', '2013100720', '2013100724', '2013100731', '2013100756', '2013100779', '2013100782', '2013100783', '2013100786', '2013100812', '2013100900', '2013100904', '2013100915', '2013100918', '2013100975', '2013100991', '2013100999', '2013101006', '2013101023', '2013101028', '2013101040', '2013101042', '2013101079', '2013101145', '2013101162', '2013101169', '2013101178', '2013101207', '2013101238', '2013101246', '2013101248', '2013101249', '2013101259', '2013101264', '2013101292', '2013101293', '2013101296', '2013101315', '2013101358', '2013101367', '2013101392', '2013101394', '2013101398', '2013101400', '2013101434', '2013101438', '2013101442', '2013101445', '2013101446', '2013101453', '2013101476', '2013101497', '2013101522', '2013101526', '2013101531', '2013101575', '2013101628', '2013101630', '2013101632', '2013101671', '2013101678', '2013101729', '2013101737', '2013101748', '2013101779', '2013101799', '2013101817', '2013101819', '2013101839', '2013101844', '2013101861', '2013101869', '2013101874', '2013101901', '2013101904', '2013101928', '2013101930', '2013101932', '2013101938', '2013101939', '2013101945', '2013101946', '2013101959', '2013101965', '2013101969', '2013101974', '2013101996', '2013101999', '2013102005', '2013102021', '2013102025', '2013102031', '2013102034', '2013102037', '2013102041', '2013102042', '2013102098', '2013102112', '2013102117', '2013102129', '2013102133', '2013102177', '2013102190', '2013102192', '2013102193', '2013102205', '2013102271', '2013102273', '2013102275', '2013102276', '2013102295', '2013102296', '2013102301', '2013102313', '2013102344', '2013102351', '2013102360', '2013102362', '2013102399', '2013102403', '2013102427', '2013102476', '2013102479', '2013102480', '2013102488', '2013102489', '2013102513', '2013102529', '2013102530', '2013102547', '2013102549', '2013102553', '2013102571', '2013102575', '2013102594', '2013102596', '2013102631', '2013102679', '2013102680', '2013102687', '2013102693', '2013102716', '2013102729', '2013102770', '2013102781', '2013102794', '2013102815', '2013102819', '2013102825', '2013102826', '2013102840', '2013102841', '2013102849', '2013102854', '2013102856', '2013102881', '2013102912', '2013102938', '2013102941', '2013102945', '2013102955', '2013102959', '2013102967', '2013102978', '2013102986', '2013103004', '2013103006', '2013103024', '2013103029', '2013103033', '2013103050', '2013103081', '2013103082', '2013103133', '2013103153', '2013103160', '2013103179', '2013103189', '2013103196', '2013103229', '2013103232', '2013103250', '2013103292', '2013103294', '2013103297', '2013103299', '2013103324', '2013103338', '2013103343', '2013103352', '2013103370', '2013103395', '2013103403', '2013103410', '2013103425', '2013103430', '2013103434', '2013103443', '2013103448', '2013103471', '2013103496', '2013103507', '2013103509', '2013103514', '2013103528', '2013103531', '2013103564', '2013103569', '2013103573', '2013103576', '2013103582', '2013103589', '2013103590', '2013103604', '2013103611', '2013103613', '2013103614', '2013103618', '2013103638', '2013103646', '2013103653', '2013103668', '2013103671', '2013103674', '2013103678', '2013103679', '2013103682', '2013103687', '2013103688', '2013103691', '2013103699', '2013103707', '2013103727', '2013103738', '2013103739', '2013103740', '2013103756', '2013103760', '2013103772', '2013103777', '2013103792', '2013103801', '2013103812', '2013103817', '2013103823', '2013103908', '2013103933', '2013103934', '2013103941', '2013103950', '2013103951', '2013103957', '2013103958', '2013103962', '2013103963', '2013103965', '2013103973', '2013103991', '2013103993', '2013103999', '2013104007', '2013104013', '2013104017', '2013104025', '2013104027', '2013104030', '2013104031', '2013104051', '2013104079', '2013104088', '2013104090', '2013104121', '2013104128', '2013104135', '2013104137', '2013104138', '2013104154', '2013104161', '2013104166', '2013104167', '2013104169', '2013104170', '2013104179', '2013104215', '2013104230', '2013104232', '2013104237', '2013104246', '2013104252', '2013104277', '2013104310', '2013104320', '2013104326', '2013104334', '2013104350', '2013104365', '2013104374', '2013104415', '2013104425', '2013104429', '2013104461', '2013104491', '2013104496', '2013104498', '2013104516', '2013104536', '2013104542', '2013104550', '2013104563', '2013104585', '2013104595', '2013104602', '2013104606', '2013104632', '2013104655', '2013104671', '2013104699', '2013104702', '2013104708', '2013104738', '2013104743', '2013104754', '2013104759', '2013104774', '2013104776', '2013104779', '2013104780', '2013104781', '2013104783', '2013104793', '2013104800', '2013104803', '2013104814', '2013104816', '2013104818', '2013104849', '2013104852', '2013104884', '2013104893', '2013104907', '2013104910', '2013104917', '2013104933', '2013104934', '2013104956', '2013104960', '2013104987', '2013105015', '2013105028', '2013105031', '2013105033', '2013105036', '2013105084', '2013105093', '2013105106', '2013105109', '2013105125', '2013105128', '2013105140', '2013105161', '2013105172', '2013105180', '2013105192', '2013105214', '2013105230', '2013105232', '2013105236', '2013105250', '2013105264', '2013105281', '2013105303', '2013105305', '2013105316', '2013105321', '2013105322', '2013105325', '2013105332', '2013105335', '2013105336', '2013105340', '2013105347', '2013105353', '2013105355', '2013105358', '2013105373', '2013105382', '2013105383', '2013105386', '2013105397', '2013105399', '2013105408', '2013105412', '2013105415', '2013105418', '2013105429', '2013105434', '2013105448', '2013105450', '2013105451', '2013105463', '2013105467', '2013105471', '2013105476', '2013105480', '2013105481', '2013105487', '2013105495', '2013105498', '2013105511', '2013105519', '2013105521', '2013105526', '2013105528', '2013105529', '2013105535', '2013105537', '2013105538', '2013105539', '2013105540', '2013105547', '2013105548', '2013105554', '2013105556', '2013105566', '2013105570', '2013105588', '2013105593', '2013105594', '2013105595', '2013105599', '2013105606', '2013105608', '2013105627', '2013105629', '2013105631', '2013105644', '2013105645', '2013105646', '2013105651', '2013105656', '2013105662', '2013105666', '2013105677', '2013105680', '2013105681', '2013105685', '2013105693', '2013105720', '2013105722', '2013105727', '2013105736', '2013105744', '2013105745', '2013105748', '2013105754', '2013105756', '2013105765', '2013105769', '2013105771', '2013105781', '2013105783', '2013105784', '2013105785', '2013105788', '2013105789', '2013105794', '2013105795', '2013105804', '2013105807', '2013105808', '2013105810', '2013105816', '2013105819', '2013105832', '2013105833', '2013105839', '2013105842', '2013105844', '2013105847', '2013105850', '2013105855', '2013105857', '2013105858', '2013105865', '2013105867', '2013105872', '2013105880', '2013105882', '2013105885', '2013105898', '2013105900', '2013105905', '2013105912', '2013105917', '2013105919', '2013105930', '2013105933', '2013105937', '2013105942', '2013105945', '2013105948', '2013105952', '2013105956', '2013105959', '2013105961', '2013105969', '2013105972', '2013105975', '2013105976', '2013105979', '2013105989', '2013105995', '2013105998', '2013106000', '2013106011', '2013106012', '2013106013', '2013106014', '2013106019', '2013106021', '2013106022', '2013106025', '2013106027', '2013106033', '2013106041', '2013106049', '2013106050', '2013106051', '2013106052', '2013106055', '2013106057', '2013106060', '2013106063', '2013106072', '2013106075', '2013106078', '2013106079', '2013106088', '2013106094', '2013106095', '2013106118', '2013106119', '2013106121', '2013106125', '2013106128', '2013106130', '2013106131', '2013106133', '2013106137', '2013106138', '2013106140', '2013106141', '2013106143', '2013106148', '2013106154', '2013106157', '2013106175', '2013106178', '2013106184', '2013106187', '2013106188', '2013106214', '2013106218', '2013106235', '2013106262', '2013106286', '2013106303', '2013106313', '2013106317', '2013106322', '2013106329', '2013106333', '2013106365', '2013106375', '2013106398', '2013106425', '2013106441', '2013106446', '2013106463', '2013106472', '2013106474', '2013106479', '2013109018', '2013109022', '2013109025', '2013109027', '2013109028', '2013109072', '2013109095', '2013110001', '2013110013', '2013110017', '2013110033', '2013110043', '2013110046', '2013110071', '2013110083', '2013110085', '2013110086', '2013110089', '2013110101', '2013110103', '2013110108', '2013110110', '2013110111', '2013110114', '2013110119', '2013110121', '2013110126', '2013110131', '2013110135', '2014100050', '2014100059', '2014100063', '2014100065', '2014100075', '2014100080', '2014100090', '2014100114', '2014100116', '2014100120', '2014100128', '2014100140', '2014100158', '2014100159', '2014100165', '2014100169', '2014100172', '2014100176', '2014100182', '2014100185', '2014100190', '2014100206', '2014100208', '2014100210', '2014100214', '2014100222', '2014100229', '2014100230', '2014100235', '2014100253', '2014100256', '2014100268', '2014100273', '2014100285', '2014100333', '2014100338', '2014100344', '2014100345', '2014100370', '2014100374', '2014100377', '2014100407', '2014100409', '2014100422', '2014100428', '2014100434', '2014100456', '2014100464', '2014100465', '2014100474', '2014100482', '2014100484', '2014100485', '2014100492', '2014100500', '2014100501', '2014100504', '2014100508', '2014100509', '2014100510', '2014100511', '2014100521', '2014100524', '2014100529', '2014100530', '2014100533', '2014100534', '2014100535', '2014100537', '2014100542', '2014100544', '2014100549', '2014100555', '2014100557', '2014100568', '2014100569', '2014100576', '2014100583', '2014100584', '2014100585', '2014100586', '2014100591', '2014100595', '2014100596', '2014100597', '2014100598', '2014100609', '2014100616', '2014100618', '2014100620', '2014100621', '2014100625', '2014100629', '2014100641', '2014100645', '2014100647', '2014100649', '2014100654', '2014100656', '2014100658', '2014100659', '2014100668', '2014100683', '2014100686', '2014100687', '2014100691', '2014100700', '2014100701', '2014100702', '2014100705', '2014100708', '2014100717', '2014100718', '2014100721', '2014100726', '2014100729', '2014100731', '2014100733', '2014100735', '2014100739', '2014100740', '2014100750', '2014100752', '2014100765', '2014100778', '2014100779', '2014100782', '2014100786', '2014100814', '2014100820', '2014100831', '2014100838', '2014100870', '2014100876', '2014100899', '2014100900', '2014100905', '2014100910', '2014100916', '2014100928', '2014100933', '2014100934', '2014100956', '2014100959', '2014100971', '2014100976', '2014100977', '2014100981', '2014100997', '2014101000', '2014101006', '2014101023', '2014101029', '2014101058', '2014101061', '2014101062', '2014101066', '2014101085', '2014101086', '2014101093', '2014101095', '2014101096', '2014101110', '2014101118', '2014101121', '2014101139', '2014101148', '2014101156', '2014101166', '2014101167', '2014101173', '2014101180', '2014101193', '2014101197', '2014101206', '2014101226', '2014101228', '2014101230', '2014101247', '2014101253', '2014101284', '2014101285', '2014101287', '2014101289', '2014101302', '2014101307', '2014101308', '2014101318', '2014101327', '2014101340', '2014101342', '2014101349', '2014101353', '2014101357', '2014101360', '2014101361', '2014101381', '2014101390', '2014101392', '2014101417', '2014101419', '2014101436', '2014101462', '2014101498', '2014101505', '2014101508', '2014101520', '2014101550', '2014101558', '2014101563', '2014101569', '2014101586', '2014101588', '2014101589', '2014101590', '2014101601', '2014101616', '2014101620', '2014101627', '2014101632', '2014101637', '2014101640', '2014101645', '2014101648', '2014101655', '2014101658', '2014101663', '2014101666', '2014101667', '2014101708', '2014101710', '2014101727', '2014101742', '2014101752', '2014101754', '2014101763', '2014101769', '2014101770', '2014101773', '2014101803', '2014101808', '2014101818', '2014101820', '2014101827', '2014101831', '2014101833', '2014101844', '2014101854', '2014101874', '2014101877', '2014101889', '2014101898', '2014101899', '2014101903', '2014101906', '2014101915', '2014101919', '2014101924', '2014101929', '2014101935', '2014101937', '2014101947', '2014101948', '2014101953', '2014101955', '2014101960', '2014101962', '2014101963', '2014101964', '2014101965', '2014101974', '2014101976', '2014101977', '2014101979', '2014101985', '2014101992', '2014101995', '2014102002', '2014102003', '2014102016', '2014102019', '2014102022', '2014102027', '2014102033', '2014102036', '2014102038', '2014102061', '2014102065', '2014102073', '2014102087', '2014102088', '2014102092', '2014102096', '2014102106', '2014102116', '2014102119', '2014102120', '2014102121', '2014102122', '2014102124', '2014102134', '2014102135', '2014102137', '2014102142', '2014102145', '2014102146', '2014102149', '2014102151', '2014102153', '2014102154', '2014102155', '2014102157', '2014102159', '2014102161', '2014102163', '2014102164', '2014102170', '2014102171', '2014102172', '2014102179', '2014102185', '2014102187', '2014102190', '2014102197', '2014102200', '2014102201', '2014102204', '2014102218', '2014102226', '2014102243', '2014102247', '2014102253', '2014102254', '2014102262', '2014102293', '2014102296', '2014102300', '2014102313', '2014102325', '2014102327', '2014102329', '2014102330', '2014102332', '2014102358', '2014102359', '2014102363', '2014102383', '2014102387', '2014102388', '2014102390', '2014102394', '2014102395', '2014102415', '2014102416', '2014102424', '2014102442', '2014102449', '2014102477', '2014102483', '2014102499', '2014102503', '2014102522', '2014102543', '2014102558', '2014102562', '2014102565', '2014102571', '2014102589', '2014102607', '2014102613', '2014102617', '2014102618', '2014102625', '2014102628', '2014102639', '2014102647', '2014102648', '2014102666', '2014102667', '2014102668', '2014102673', '2014102677', '2014102688', '2014102689', '2014102694', '2014102698', '2014102703', '2014102711', '2014102720', '2014102727', '2014102730', '2014102733', '2014102735', '2014102743', '2014102753', '2014102777', '2014102787', '2014102807', '2014102814', '2014102825', '2014102844', '2014102845', '2014102848', '2014102855', '2014102860', '2014102862', '2014102865', '2014102878', '2014102886', '2014102888', '2014102892', '2014102894', '2014102897', '2014102902', '2014102906', '2014102908', '2014102919', '2014102930', '2014102959', '2014102972', '2014102973', '2014102975', '2014102976', '2014102979', '2014102989', '2014103000', '2014103001', '2014103009', '2014103010', '2014103015', '2014103017', '2014103031', '2014103032', '2014103035', '2014103036', '2014103040', '2014103058', '2014103074', '2014103077', '2014103084', '2014103085', '2014103092', '2014103095', '2014103123', '2014103124', '2014103141', '2014103147', '2014103154', '2014103156', '2014103163', '2014103165', '2014103166', '2014103170', '2014103175', '2014103176', '2014103182', '2014103187', '2014103189', '2014103191', '2014103195', '2014103196', '2014103208', '2014103212', '2014103213', '2014103215', '2014103242', '2014103245', '2014103265', '2014103266', '2014103268', '2014103284', '2014103288', '2014103291', '2014103298', '2014103311', '2014103335', '2014103348', '2014103354', '2014103358', '2014103361', '2014103365', '2014103366', '2014103367', '2014103370', '2014103373', '2014103376', '2014103377', '2014103385', '2014103387', '2014103389', '2014103390', '2014103394', '2014103399', '2014103400', '2014103402', '2014103404', '2014103406', '2014103409', '2014103414', '2014103416', '2014103417', '2014103425', '2014103429', '2014103434', '2014103444', '2014103453', '2014103459', '2014103460', '2014103466', '2014103475', '2014103476', '2014103477', '2014103479', '2014103486', '2014103501', '2014103514', '2014103515', '2014103520', '2014103521', '2014103523', '2014103524', '2014103545', '2014103547', '2014103551', '2014103558', '2014103559', '2014103561', '2014103571', '2014103576', '2014103577', '2014103578', '2014103580', '2014103582', '2014103584', '2014103585', '2014103590', '2014103592', '2014103594', '2014103596', '2014103600', '2014103601', '2014103603', '2014103606', '2014103607', '2014103611', '2014103615', '2014103635', '2014103669', '2014103674', '2014103687', '2014103689', '2014103690', '2014103691', '2014103702', '2014103710', '2014103714', '2014103715', '2014103722', '2014103732', '2014103733', '2014103751', '2014103776', '2014103791', '2014103795', '2014103798', '2014103806', '2014103818', '2014103833', '2014103848', '2014103850', '2014103851', '2014103864', '2014103866', '2014103867', '2014103870', '2014103872', '2014103884', '2014103896', '2014103913', '2014103928', '2014103950', '2014103957', '2014103963', '2014103967', '2014103968', '2014103971', '2014103977', '2014103978', '2014103981', '2014103990', '2014103994', '2014104001', '2014104002', '2014104021', '2014104022', '2014104023', '2014104031', '2014104037', '2014104044', '2014104048', '2014104051', '2014104052', '2014104068', '2014104072', '2014104074', '2014104076', '2014104090', '2014104092', '2014104103', '2014104110', '2014104111', '2014104126', '2014104135', '2014104144', '2014104161', '2014104192', '2014104195', '2014104206', '2014104214', '2014104226', '2014104230', '2014104238', '2014104240', '2014104247', '2014104248', '2014104249', '2014104251', '2014104259', '2014104260', '2014104261', '2014104265', '2014104267', '2014104273', '2014104274', '2014104278', '2014104280', '2014104281', '2014104301', '2014104303', '2014104304', '2014104310', '2014104313', '2014104314', '2014104316', '2014104326', '2014104328', '2014104329', '2014104332', '2014104343', '2014104346', '2014104351', '2014104353', '2014104369', '2014104373', '2014104374', '2014104375', '2014104377', '2014104383', '2014104386', '2014104389', '2014104390', '2014104401', '2014104402', '2014104404', '2014104409', '2014104410', '2014104416', '2014104418', '2014104424', '2014104426', '2014104427', '2014104437', '2014104445', '2014104449', '2014104458', '2014104461', '2014104471', '2014104487', '2014104504', '2014104506', '2014104508', '2014104510', '2014104512', '2014104515', '2014104516', '2014104521', '2014104537', '2014104550', '2014104563', '2014104589', '2014104595', '2014104596', '2014104612', '2014104623', '2014104636', '2014104646', '2014104647', '2014104653', '2014104660', '2014104669', '2014104684', '2014104699', '2014104702', '2014104716', '2014104731', '2014104735', '2014104738', '2014104753', '2014104766', '2014104771', '2014104790', '2014104817', '2014104830', '2014104831', '2014104841', '2014104846', '2014104855', '2014104859', '2014104873', '2014104880', '2014104912', '2014104922', '2014104927', '2014104933', '2014104957', '2014104962', '2014104968', '2014104970', '2014104972', '2014104973', '2014104975', '2014104981', '2014104991', '2014105017', '2014105032', '2014105043', '2014105044', '2014105045', '2014105047', '2014105048', '2014105052', '2014105053', '2014105057', '2014105059', '2014105062', '2014105063', '2014105065', '2014105066', '2014105072', '2014105073', '2014105078', '2014105080', '2014105081', '2014105082', '2014105090', '2014105102', '2014105104', '2014105112', '2014105116', '2014105117', '2014105125', '2014105130', '2014105134', '2014105136', '2014105145', '2014105149', '2014105151', '2014105172', '2014105185', '2014105187', '2014105217', '2014105221', '2014105232', '2014105242', '2014105246', '2014105247', '2014105249', '2014105250', '2014105251', '2014105254', '2014105257', '2014105258', '2014105261', '2014105265', '2014105266', '2014105271', '2014105275', '2014105277', '2014105282', '2014105304', '2014105309', '2014105317', '2014105320', '2014105324', '2014105336', '2014105337', '2014105338', '2014105340', '2014105342', '2014105348', '2014105350', '2014105352', '2014105353', '2014105355', '2014105356', '2014105362', '2014105363', '2014105365', '2014105371', '2014105372', '2014105389', '2014105391', '2014105398', '2014105404', '2014105413', '2014105416', '2014105417', '2014105420', '2014105422', '2014105425', '2014105427', '2014105430', '2014105431', '2014105436', '2014105439', '2014105441', '2014105448', '2014105456', '2014105460', '2014105461', '2014105467', '2014105470', '2014105476', '2014105482', '2014105484', '2014105485', '2014105492', '2014105493', '2014105497', '2014105501', '2014105506', '2014105509', '2014105512', '2014105513', '2014105514', '2014105517', '2014105520', '2014105525', '2014105526', '2014105531', '2014105533', '2014105534', '2014105535', '2014105542', '2014105543', '2014105545', '2014105549', '2014105551', '2014105554', '2014105558', '2014105562', '2014105575', '2014105582', '2014105584', '2014105589', '2014105592', '2014105599', '2014105605', '2014105608', '2014105609', '2014105611', '2014105614', '2014105621', '2014105622', '2014105627', '2014105635', '2014105637', '2014105638', '2014105641', '2014105642', '2014105643', '2014105654', '2014105655', '2014105660', '2014105662', '2014105666', '2014105671', '2014105672', '2014105676', '2014105681', '2014105682', '2014105685', '2014105686', '2014105693', '2014105697', '2014105700', '2014105701', '2014105709', '2014105713', '2014105720', '2014105724', '2014105725', '2014105727', '2014105734', '2014105745', '2014105748', '2014105755', '2014105756', '2014105759', '2014105760', '2014105771', '2014105772', '2014105774', '2014105778', '2014105779', '2014105781', '2014105785', '2014105787', '2014105788', '2014105792', '2014105793', '2014105794', '2014105795', '2014105796', '2014105799', '2014105801', '2014105802', '2014105805', '2014105810', '2014105813', '2014105814', '2014105817', '2014105824', '2014105825', '2014105828', '2014105833', '2014105836', '2014105837', '2014105841', '2014105843', '2014105846', '2014105849', '2014105850', '2014105851', '2014105853', '2014105854', '2014105858', '2014105860', '2014105863', '2014105864', '2014105879', '2014105886', '2014105887', '2014105889', '2014105892', '2014105897', '2014105910', '2014105914', '2014105922', '2014105925', '2014105926', '2014105929', '2014105931', '2014105933', '2014105935', '2014105945', '2014105947', '2014105949', '2014105951', '2014105953', '2014105957', '2014105960', '2014105969', '2014105970', '2014105971', '2014105974', '2014105975', '2014105976', '2014105982', '2014105983', '2014105984', '2014105991', '2014105992', '2014105996', '2014105999', '2014106006', '2014106010', '2014106012', '2014106021', '2014106027', '2014106028', '2014106036', '2014106037', '2014106040', '2014106044', '2014106046', '2014106047', '2014106048', '2014106049', '2014106051', '2014106052', '2014106054', '2014106056', '2014106058', '2014106060', '2014106061', '2014106065', '2014106066', '2014106068', '2014106072', '2014106074', '2014106076', '2014106079', '2014106081', '2014106082', '2014106083', '2014106091', '2014106092', '2014106097', '2014106099', '2014106104', '2014106105', '2014106110', '2014106114', '2014106115', '2014106117', '2014106118', '2014106120', '2014106125', '2014106130', '2014106132', '2014106136', '2014106138', '2014106141', '2014106144', '2014106148', '2014106151', '2014106155', '2014106160', '2014106164', '2014106166', '2014106168', '2014106169', '2014106172', '2014106173', '2014106174', '2014106176', '2014106179', '2014106182', '2014106185', '2014106188', '2014106190', '2014106191', '2014106196', '2014106197', '2014106201', '2014106203', '2014106207', '2014106210', '2014106211', '2014106214', '2014106218', '2014106219', '2014106220', '2014106225', '2014106228', '2014106231', '2014106238', '2014106239', '2014106243', '2014106248', '2014106250', '2014106253', '2014106256', '2014106272', '2014106280', '2014106286', '2014106289', '2014106335', '2014106337', '2014106348', '2014106360', '2014106361', '2014106404', '2014106411', '2014106428', '2014106436', '2014106437', '2014106447', '2014106448', '2014106453', '2014106464', '2014106468', '2014106472', '2014106474', '2014106492', '2014106522', '2014106523', '2014106529', '2014106530', '2014106535', '2014109007', '2014109008', '2014109037', '2014109041', '2014109047', '2014109051', '2014109085', '2014109087', '2014109116', '2014109137', '2014109148', '2014109151', '2014109171', '2014110001', '2014110003', '2014110010', '2014110013', '2014110014', '2014110016', '2014110023', '2014110026', '2014110029', '2014110031', '2014110035', '2014110041', '2014110043', '2014110060', '2014110070', '2014110074', '2014110076', '2014110077', '2014110080', '2014110082', '2014110083', '2014110084', '2014110085', '2014110086', '2014110089', '2014110093', '2014110098', '2014110099', '2014110100', '2014110102', '2014110104', '2014110107', '2014110109', '2014110111', '2014110117', '2014110118', '2015100018', '2015100019', '2015100022', '2015100023', '2015100037', '2015100043', '2015100050', '2015100051', '2015100061', '2015100063', '2015100093', '2015100097', '2015100098', '2015100106', '2015100119', '2015100120', '2015100122', '2015100128', '2015100130', '2015100131', '2015100132', '2015100137', '2015100143', '2015100145', '2015100147', '2015100157', '2015100164', '2015100170', '2015100173', '2015100186', '2015100188', '2015100195', '2015100198', '2015100216', '2015100217', '2015100218', '2015100222', '2015100225', '2015100227', '2015100228', '2015100230', '2015100231', '2015100236', '2015100242', '2015100243', '2015100244', '2015100246', '2015100251', '2015100258', '2015100260', '2015100261', '2015100272', '2015100274', '2015100276', '2015100299', '2015100309', '2015100310', '2015100311', '2015100313', '2015100317', '2015100318', '2015100339', '2015100344', '2015100345', '2015100348', '2015100349', '2015100355', '2015100363', '2015100367', '2015100371', '2015100384', '2015100398', '2015100400', '2015100403', '2015100405', '2015100416', '2015100423', '2015100428', '2015100435', '2015100441', '2015100447', '2015100451', '2015100466', '2015100469', '2015100476', '2015100491', '2015100493', '2015100494', '2015100501', '2015100509', '2015100511', '2015100512', '2015100513', '2015100514', '2015100516', '2015100519', '2015100523', '2015100524', '2015100528', '2015100529', '2015100531', '2015100533', '2015100542', '2015100547', '2015100548', '2015100555', '2015100557', '2015100559', '2015100562', '2015100563', '2015100587', '2015100589', '2015100594', '2015100597', '2015100599', '2015100600', '2015100605', '2015100608', '2015100611', '2015100613', '2015100630', '2015100635', '2015100637', '2015100639', '2015100640', '2015100644', '2015100651', '2015100654', '2015100655', '2015100659', '2015100670', '2015100671', '2015100672', '2015100673', '2015100679', '2015100690', '2015100695', '2015100703', '2015100705', '2015100713', '2015100724', '2015100731', '2015100732', '2015100734', '2015100738', '2015100744', '2015100747', '2015100748', '2015100751', '2015100761', '2015100763', '2015100772', '2015100777', '2015100782', '2015100786', '2015100788', '2015100799', '2015100806', '2015100809', '2015100822', '2015100828', '2015100830', '2015100831', '2015100833', '2015100838', '2015100842', '2015100845', '2015100846', '2015100849', '2015100850', '2015100851', '2015100852', '2015100854', '2015100862', '2015100863', '2015100868', '2015100869', '2015100873', '2015100874', '2015100878', '2015100887', '2015100891', '2015100894', '2015100902', '2015100904', '2015100913', '2015100918', '2015100927', '2015100929', '2015100932', '2015100939', '2015100942', '2015100946', '2015100958', '2015100960', '2015100964', '2015100969', '2015100977', '2015100978', '2015100995', '2015101012', '2015101014', '2015101019', '2015101021', '2015101022', '2015101025', '2015101028', '2015101034', '2015101036', '2015101046', '2015101054', '2015101057', '2015101059', '2015101070', '2015101074', '2015101075', '2015101077', '2015101083', '2015101105', '2015101110', '2015101116', '2015101125', '2015101130', '2015101147', '2015101152', '2015101155', '2015101164', '2015101169', '2015101170', '2015101174', '2015101178', '2015101185', '2015101191', '2015101209', '2015101210', '2015101212', '2015101217', '2015101229', '2015101232', '2015101235', '2015101239', '2015101240', '2015101242', '2015101243', '2015101248', '2015101251', '2015101257', '2015101260', '2015101264', '2015101281', '2015101286', '2015101288', '2015101310', '2015101319', '2015101320', '2015101326', '2015101327', '2015101328', '2015101332', '2015101339', '2015101340', '2015101341', '2015101342', '2015101343', '2015101348', '2015101353', '2015101355', '2015101358', '2015101364', '2015101366', '2015101367', '2015101375', '2015101376', '2015101377', '2015101378', '2015101380', '2015101381', '2015101382', '2015101386', '2015101387', '2015101388', '2015101391', '2015101394', '2015101395', '2015101397', '2015101399', '2015101402', '2015101403', '2015101404', '2015101410', '2015101420', '2015101425', '2015101434', '2015101439', '2015101447', '2015101449', '2015101450', '2015101465', '2015101467', '2015101471', '2015101480', '2015101482', '2015101483', '2015101487', '2015101491', '2015101496', '2015101498', '2015101503', '2015101505', '2015101506', '2015101512', '2015101518', '2015101523', '2015101524', '2015101529', '2015101530', '2015101532', '2015101535', '2015101537', '2015101539', '2015101540', '2015101542', '2015101549', '2015101553', '2015101565', '2015101566', '2015101568', '2015101569', '2015101571', '2015101573', '2015101576', '2015101580', '2015101581', '2015101591', '2015101592', '2015101595', '2015101596', '2015101599', '2015101602', '2015101609', '2015101615', '2015101621', '2015101624', '2015101625', '2015101626', '2015101629', '2015101631', '2015101632', '2015101633', '2015101636', '2015101645', '2015101647', '2015101654', '2015101665', '2015101666', '2015101667', '2015101671', '2015101674', '2015101675', '2015101679', '2015101680', '2015101682', '2015101686', '2015101687', '2015101695', '2015101700', '2015101701', '2015101702', '2015101703', '2015101707', '2015101712', '2015101716', '2015101738', '2015101744', '2015101746', '2015101749', '2015101755', '2015101771', '2015101779', '2015101785', '2015101786', '2015101800', '2015101804', '2015101813', '2015101823', '2015101826', '2015101829', '2015101830', '2015101831', '2015101833', '2015101834', '2015101835', '2015101836', '2015101838', '2015101851', '2015101854', '2015101855', '2015101858', '2015101870', '2015101879', '2015101885', '2015101886', '2015101889', '2015101890', '2015101896', '2015101903', '2015101912', '2015101929', '2015101936', '2015101948', '2015101960', '2015101984', '2015101985', '2015101987', '2015101993', '2015101995', '2015102003', '2015102008', '2015102013', '2015102018', '2015102019', '2015102033', '2015102034', '2015102035', '2015102038', '2015102039', '2015102042', '2015102043', '2015102046', '2015102051', '2015102052', '2015102071', '2015102074', '2015102078', '2015102081', '2015102093', '2015102095', '2015102123', '2015102128', '2015102130', '2015102146', '2015102150', '2015102163', '2015102164', '2015102168', '2015102181', '2015102186', '2015102198', '2015102199', '2015102208', '2015102211', '2015102214', '2015102215', '2015102217', '2015102218', '2015102239', '2015102240', '2015102241', '2015102242', '2015102246', '2015102254', '2015102257', '2015102264', '2015102265', '2015102272', '2015102280', '2015102283', '2015102297', '2015102303', '2015102307', '2015102313', '2015102316', '2015102328', '2015102330', '2015102331', '2015102342', '2015102353', '2015102358', '2015102367', '2015102369', '2015102370', '2015102371', '2015102372', '2015102374', '2015102375', '2015102376', '2015102377', '2015102382', '2015102383', '2015102384', '2015102387', '2015102389', '2015102390', '2015102391', '2015102392', '2015102403', '2015102413', '2015102419', '2015102420', '2015102424', '2015102431', '2015102435', '2015102439', '2015102440', '2015102442', '2015102448', '2015102451', '2015102454', '2015102459', '2015102464', '2015102477', '2015102478', '2015102479', '2015102491', '2015102498', '2015102499', '2015102502', '2015102504', '2015102528', '2015102533', '2015102534', '2015102539', '2015102551', '2015102557', '2015102558', '2015102564', '2015102565', '2015102566', '2015102574', '2015102578', '2015102580', '2015102586', '2015102591', '2015102594', '2015102595', '2015102604', '2015102610', '2015102620', '2015102626', '2015102629', '2015102637', '2015102639', '2015102644', '2015102647', '2015102656', '2015102657', '2015102660', '2015102663', '2015102666', '2015102676', '2015102692', '2015102694', '2015102695', '2015102704', '2015102718', '2015102735', '2015102739', '2015102740', '2015102743', '2015102745', '2015102746', '2015102747', '2015102749', '2015102750', '2015102754', '2015102764', '2015102785', '2015102790', '2015102801', '2015102810', '2015102812', '2015102813', '2015102815', '2015102817', '2015102823', '2015102824', '2015102830', '2015102841', '2015102846', '2015102849', '2015102851', '2015102853', '2015102857', '2015102868', '2015102869', '2015102870', '2015102873', '2015102906', '2015102908', '2015102909', '2015102917', '2015102923', '2015102929', '2015102942', '2015102949', '2015102956', '2015102973', '2015102980', '2015102983', '2015102984', '2015102988', '2015102989', '2015102991', '2015102995', '2015102999', '2015103003', '2015103006', '2015103010', '2015103013', '2015103018', '2015103021', '2015103024', '2015103034', '2015103042', '2015103047', '2015103048', '2015103049', '2015103052', '2015103053', '2015103057', '2015103076', '2015103078', '2015103079', '2015103085', '2015103088', '2015103089', '2015103101', '2015103106', '2015103109', '2015103112', '2015103116', '2015103117', '2015103120', '2015103124', '2015103128', '2015103138', '2015103140', '2015103142', '2015103145', '2015103150', '2015103151', '2015103155', '2015103166', '2015103167', '2015103175', '2015103181', '2015103193', '2015103195', '2015103197', '2015103201', '2015103210', '2015103214', '2015103217', '2015103221', '2015103224', '2015103227', '2015103228', '2015103232', '2015103234', '2015103236', '2015103245', '2015103247', '2015103248', '2015103249', '2015103256', '2015103262', '2015103267', '2015103268', '2015103270', '2015103277', '2015103287', '2015103291', '2015103293', '2015103295', '2015103296', '2015103306', '2015103310', '2015103311', '2015103313', '2015103314', '2015103317', '2015103320', '2015103329', '2015103333', '2015103335', '2015103336', '2015103339', '2015103340', '2015103341', '2015103343', '2015103344', '2015103350', '2015103355', '2015103357', '2015103361', '2015103364', '2015103366', '2015103368', '2015103370', '2015103373', '2015103374', '2015103375', '2015103376', '2015103377', '2015103379', '2015103381', '2015103382', '2015103383', '2015103384', '2015103388', '2015103390', '2015103391', '2015103392', '2015103393', '2015103397', '2015103400', '2015103403', '2015103404', '2015103408', '2015103414', '2015103419', '2015103431', '2015103445', '2015103446', '2015103452', '2015103456', '2015103475', '2015103481', '2015103488', '2015103490', '2015103494', '2015103495', '2015103498', '2015103521', '2015103524', '2015103525', '2015103528', '2015103530', '2015103535', '2015103538', '2015103544', '2015103550', '2015103554', '2015103558', '2015103559', '2015103562', '2015103567', '2015103569', '2015103578', '2015103580', '2015103585', '2015103588', '2015103590', '2015103592', '2015103599', '2015103603', '2015103616', '2015103626', '2015103627', '2015103631', '2015103640', '2015103646', '2015103654', '2015103656', '2015103663', '2015103670', '2015103672', '2015103673', '2015103675', '2015103677', '2015103678', '2015103681', '2015103683', '2015103686', '2015103735', '2015103738', '2015103743', '2015103748', '2015103755', '2015103761', '2015103764', '2015103766', '2015103768', '2015103769', '2015103779', '2015103802', '2015103803', '2015103807', '2015103808', '2015103825', '2015103831', '2015103845', '2015103853', '2015103859', '2015103863', '2015103864', '2015103867', '2015103875', '2015103879', '2015103884', '2015103892', '2015103893', '2015103894', '2015103895', '2015103897', '2015103900', '2015103901', '2015103904', '2015103906', '2015103908', '2015103916', '2015103922', '2015103925', '2015103929', '2015103930', '2015103932', '2015103934', '2015103937', '2015103938', '2015103941', '2015103946', '2015103949', '2015103951', '2015103956', '2015103964', '2015103967', '2015103979', '2015103982', '2015103983', '2015103994', '2015104000', '2015104004', '2015104006', '2015104010', '2015104014', '2015104021', '2015104024', '2015104047', '2015104053', '2015104056', '2015104060', '2015104063', '2015104066', '2015104068', '2015104074', '2015104079', '2015104090', '2015104093', '2015104098', '2015104100', '2015104104', '2015104107', '2015104109', '2015104115', '2015104124', '2015104126', '2015104129', '2015104131', '2015104132', '2015104138', '2015104142', '2015104144', '2015104145', '2015104146', '2015104149', '2015104150', '2015104157', '2015104158', '2015104159', '2015104161', '2015104163', '2015104165', '2015104166', '2015104170', '2015104171', '2015104174', '2015104175', '2015104176', '2015104177', '2015104180', '2015104183', '2015104185', '2015104186', '2015104188', '2015104190', '2015104193', '2015104198', '2015104202', '2015104209', '2015104214', '2015104225', '2015104227', '2015104242', '2015104243', '2015104245', '2015104249', '2015104254', '2015104257', '2015104260', '2015104266', '2015104270', '2015104271', '2015104273', '2015104277', '2015104281', '2015104282', '2015104287', '2015104293', '2015104295', '2015104299', '2015104300', '2015104302', '2015104307', '2015104308', '2015104309', '2015104310', '2015104312', '2015104313', '2015104316', '2015104317', '2015104320', '2015104321', '2015104322', '2015104325', '2015104326', '2015104328', '2015104331', '2015104333', '2015104336', '2015104337', '2015104338', '2015104346', '2015104347', '2015104349', '2015104352', '2015104354', '2015104355', '2015104356', '2015104357', '2015104358', '2015104361', '2015104364', '2015104367', '2015104371', '2015104372', '2015104375', '2015104376', '2015104378', '2015104381', '2015104382', '2015104384', '2015104385', '2015104386', '2015104387', '2015104401', '2015104402', '2015104405', '2015104406', '2015104408', '2015104412', '2015104414', '2015104417', '2015104421', '2015104423', '2015104426', '2015104427', '2015104428', '2015104431', '2015104435', '2015104436', '2015104437', '2015104438', '2015104440', '2015104444', '2015104448', '2015104453', '2015104456', '2015104458', '2015104461', '2015104471', '2015104474', '2015104475', '2015104476', '2015104477', '2015104481', '2015104484', '2015104489', '2015104493', '2015104496', '2015104498', '2015104502', '2015104504', '2015104512', '2015104513', '2015104514', '2015104517', '2015104524', '2015104526', '2015104528', '2015104534', '2015104537', '2015104538', '2015104542', '2015104546', '2015104547', '2015104549', '2015104553', '2015104554', '2015104555', '2015104557', '2015104558', '2015104562', '2015104565', '2015104567', '2015104571', '2015104575', '2015104577', '2015104582', '2015104583', '2015104586', '2015104589', '2015104592', '2015104593', '2015104597', '2015104602', '2015104603', '2015104610', '2015104612', '2015104617', '2015104621', '2015104623', '2015104624', '2015104625', '2015104626', '2015104627', '2015104633', '2015104634', '2015104639', '2015104640', '2015104641', '2015104642', '2015104643', '2015104644', '2015104645', '2015104649', '2015104650', '2015104651', '2015104656', '2015104658', '2015104660', '2015104661', '2015104662', '2015104663', '2015104664', '2015104665', '2015104667', '2015104669', '2015104670', '2015104671', '2015104673', '2015104674', '2015104675', '2015104677', '2015104679', '2015104680', '2015104681', '2015104683', '2015104684', '2015104688', '2015104689', '2015104691', '2015104696', '2015104697', '2015104699', '2015104700', '2015104702', '2015104705', '2015104707', '2015104711', '2015104712', '2015104713', '2015104714', '2015104715', '2015104716', '2015104719', '2015104720', '2015104721', '2015104724', '2015104729', '2015104739', '2015104741', '2015104743', '2015104745', '2015104747', '2015104748', '2015104749', '2015104750', '2015104752', '2015104753', '2015104754', '2015104756', '2015104758', '2015104760', '2015104762', '2015104763', '2015104765', '2015104767', '2015104770', '2015104771', '2015104773', '2015104779', '2015104780', '2015104782', '2015104789', '2015104792', '2015104793', '2015104794', '2015104796', '2015104799', '2015104803', '2015104805', '2015104810', '2015104811', '2015104815', '2015104816', '2015104818', '2015104819', '2015104821', '2015104823', '2015104826', '2015104830', '2015104834', '2015104835', '2015104837', '2015104838', '2015104841', '2015104842', '2015104843', '2015104849', '2015104851', '2015104854', '2015104855', '2015104856', '2015104858', '2015104860', '2015104862', '2015104868', '2015104870', '2015104872', '2015104873', '2015104875', '2015104876', '2015104877', '2015104879', '2015104880', '2015104882', '2015104883', '2015104887', '2015104888', '2015104890', '2015104894', '2015104897', '2015104898', '2015104899', '2015104900', '2015104901', '2015104907', '2015104908', '2015104910', '2015104913', '2015104914', '2015104916', '2015104919', '2015104921', '2015104925', '2015104927', '2015104928', '2015104932', '2015104935', '2015104937', '2015104941', '2015104946', '2015104947', '2015104949', '2015104950', '2015104952', '2015104955', '2015104956', '2015104960', '2015104961', '2015104968', '2015104970', '2015104972', '2015104973', '2015104974', '2015104975', '2015104976', '2015104977', '2015104979', '2015104983', '2015104988', '2015104991', '2015104993', '2015104994', '2015104995', '2015104999', '2015105003', '2015105008', '2015105009', '2015105010', '2015105011', '2015105012', '2015105013', '2015105014', '2015105018', '2015105020', '2015105021', '2015105022', '2015105023', '2015105024', '2015105025', '2015105026', '2015105027', '2015105030', '2015105032', '2015105034', '2015105035', '2015105036', '2015105038', '2015105040', '2015105041', '2015105046', '2015105048', '2015105050', '2015105052', '2015105056', '2015105057', '2015105058', '2015105061', '2015105064', '2015105066', '2015105068', '2015105069', '2015105070', '2015105072', '2015105073', '2015105074', '2015105075', '2015105076', '2015105077', '2015105081', '2015105082', '2015105083', '2015105084', '2015105086', '2015105087', '2015105088', '2015105089', '2015105090', '2015105091', '2015105093', '2015105098', '2015105105', '2015105106', '2015105108', '2015105109', '2015105110', '2015105111', '2015105112', '2015105114', '2015105117', '2015105120', '2015105129', '2015105131', '2015105136', '2015105140', '2015105141', '2015105142', '2015105144', '2015105145', '2015105153', '2015105168', '2015105169', '2015105171', '2015105172', '2015105176', '2015105182', '2015105191', '2015105192', '2015105193', '2015105194', '2015105199', '2015105200', '2015105202', '2015105203', '2015105210', '2015105214', '2015105225', '2015105236', '2015105241', '2015105243', '2015105273', '2015105276', '2015105278', '2015105280', '2015105285', '2015105287', '2015105294', '2015105303', '2015105305', '2015105306', '2015105312', '2015105318', '2015105319', '2015105332', '2015105352', '2015105369', '2015105371', '2015105382', '2015105383', '2015105385', '2015105392', '2015105396', '2015105399', '2015105401', '2015105402', '2015105408', '2015105411', '2015105415', '2015105417', '2015105419', '2015105429', '2015105430', '2015105431', '2015105432', '2015105433', '2015105434', '2015105442', '2015105444', '2015105451', '2015105454', '2015105455', '2015105459', '2015105461', '2015105470', '2015105481', '2015105485', '2015105487', '2015105490', '2015105498', '2015105499', '2015105501', '2015105502', '2015105504', '2015105507', '2015105508', '2015105510', '2015105511', '2015105520', '2015105539', '2015105549', '2015105550', '2015105551', '2015105553', '2015105560', '2015105561', '2015105565', '2015105573', '2015105581', '2015105583', '2015105584', '2015105589', '2015105590', '2015105594', '2015105605', '2015105608', '2015105609', '2015105610', '2015105613', '2015105621', '2015105627', '2015105628', '2015105634', '2015105651', '2015105665', '2015105669', '2015105673', '2015105681', '2015105686', '2015105688', '2015105691', '2015105693', '2015105697', '2015105715', '2015105725', '2015105730', '2015105734', '2015105736', '2015105738', '2015105740', '2015105742', '2015105752', '2015105768', '2015105782', '2015105783', '2015105784', '2015105788', '2015105791', '2015105800', '2015105829', '2015105830', '2015105831', '2015105834', '2015105843', '2015105844', '2015105849', '2015105850', '2015105852', '2015105856', '2015105858', '2015105860', '2015105867', '2015105868', '2015105870', '2015105873', '2015105893', '2015105902', '2015105904', '2015105905', '2015105907', '2015105911', '2015105923', '2015105928', '2015105932', '2015105936', '2015105941', '2015105944', '2015105948', '2015105952', '2015105953', '2015105955', '2015105958', '2015105959', '2015105970', '2015105971', '2015105972', '2015105974', '2015105983', '2015105986', '2015105990', '2015106011', '2015106024', '2015106033', '2015106036', '2015106040', '2015106042', '2015106050', '2015106056', '2015106059', '2015106063', '2015106065', '2015106066', '2015106067', '2015106069', '2015106080', '2015106085', '2015106089', '2015106094', '2015106095', '2015106102', '2015106125', '2015106131', '2015106136', '2015106149', '2015106153', '2015106156', '2015106157', '2015106159', '2015106162', '2015106163', '2015106165', '2015106170', '2015106171', '2015106173', '2015106174', '2015106175', '2015106178', '2015106179', '2015106180', '2015106181', '2015106182', '2015106185', '2015106187', '2015106189', '2015106190', '2015106192', '2015106194', '2015106197', '2015106201', '2015106216', '2015106219', '2015106223', '2015106226', '2015106227', '2015106231', '2015106233', '2015106238', '2015106252', '2015106260', '2015106261', '2015106280', '2015106286', '2015106289', '2015106290', '2015106298', '2015106299', '2015106302', '2015106307', '2015106310', '2015106315', '2015106325', '2015106328', '2015106344', '2015106345', '2015106351', '2015106353', '2015106354', '2015106356', '2015106369', '2015106372', '2015106379', '2015106380', '2015106384', '2015106385', '2015106392', '2015106396', '2015106402', '2015106404', '2015106405', '2015106423', '2015106427', '2015106428', '2015106441', '2015106465', '2015106466', '2015106471', '2015106488', '2015106491', '2015106499', '2015106500', '2015106501', '2015106502', '2015106504', '2015106510', '2015106528', '2015109012', '2015109026', '2015109031', '2015109048', '2015109059', '2015109073', '2015109075', '2015109076', '2015109077', '2015109078', '2015109079', '2015109085', '2015109099', '2015109114', '2015109128', '2015109131', '2015109141', '2015109150', '2015110002', '2015110004', '2015110005', '2015110006', '2015110011', '2015110013', '2015110014', '2015110015', '2015110016', '2015110017', '2015110020', '2015110021', '2015110022', '2015110026', '2015110027', '2015110028', '2015110030', '2015110032', '2015110035', '2015110036', '2015110041', '2015110046', '2015110047', '2015110056', '2015110061', '2015110063', '2015110065', '2015110066', '2015110067', '2015110068', '2015110069', '2015110070', '2015110072', '2015110073', '2015110074', '2015110075', '2015110076', '2015110077', '2015110078', '2015110080', '2015110082', '2015110083', '2015110084', '2015110085', '2015110086', '2015110091', '2015110093', '2015110096', '2015110098', '2015110099', '2015110100', '2015110101', '2015110102', '2015110103', '2015110104', '2016100006', '2016100007', '2016100009', '2016100010', '2016100012', '2016100018', '2016100026', '2016100027', '2016100028', '2016100029', '2016100030', '2016100031', '2016100032', '2016100033', '2016100039', '2016100040', '2016100047', '2016100048', '2016100049', '2016100051', '2016100056', '2016100059', '2016100061', '2016100062', '2016100063', '2016100064', '2016100065', '2016100066', '2016100068', '2016100070', '2016100072', '2016100073', '2016100077', '2016100078', '2016100079', '2016100080', '2016100084', '2016100086', '2016100092', '2016100093', '2016100096', '2016100097', '2016100098', '2016100101', '2016100102', '2016100105', '2016100106', '2016100107', '2016100108', '2016100115', '2016100117', '2016100118', '2016100119', '2016100120', '2016100121', '2016100125', '2016100126', '2016100127', '2016100128', '2016100129', '2016100132', '2016100133', '2016100134', '2016100135', '2016100136', '2016100137', '2016100140', '2016100141', '2016100142', '2016100146', '2016100147', '2016100151', '2016100152', '2016100154', '2016100155', '2016100156', '2016100157', '2016100158', '2016100161', '2016100162', '2016100163', '2016100164', '2016100165', '2016100166', '2016100168', '2016100170', '2016100171', '2016100172', '2016100173', '2016100174', '2016100176', '2016100177', '2016100178', '2016100180', '2016100181', '2016100182', '2016100183', '2016100184', '2016100185', '2016100187', '2016100188', '2016100189', '2016100190', '2016100191', '2016100192', '2016100195', '2016100196', '2016100197', '2016100198', '2016100201', '2016100203', '2016100204', '2016100206', '2016100207', '2016100208', '2016100210', '2016100211', '2016100212', '2016100213', '2016100214', '2016100215', '2016100216', '2016100218', '2016100219', '2016100220', '2016100221', '2016100224', '2016100225', '2016100231', '2016100232', '2016100233', '2016100235', '2016100237', '2016100238', '2016100239', '2016100242', '2016100248', '2016100249', '2016100251', '2016100253', '2016100254', '2016100255', '2016100256', '2016100257', '2016100258', '2016100260', '2016100269', '2016100272', '2016100273', '2016100274', '2016100276', '2016100277', '2016100279', '2016100280', '2016100285', '2016100286', '2016100288', '2016100289', '2016100290', '2016100291', '2016100296', '2016100297', '2016100298', '2016100302', '2016100303', '2016100305', '2016100306', '2016100309', '2016100311', '2016100312', '2016100313', '2016100314', '2016100317', '2016100318', '2016100319', '2016100320', '2016100321', '2016100322', '2016100325', '2016100327', '2016100338', '2016100340', '2016100342', '2016100343', '2016100345', '2016100346', '2016100348', '2016100349', '2016100350', '2016100354', '2016100356', '2016100357', '2016100358', '2016100359', '2016100360', '2016100361', '2016100365', '2016100368', '2016100369', '2016100376', '2016100382', '2016100383', '2016100384', '2016100385', '2016100386', '2016100388', '2016100390', '2016100391', '2016100393', '2016100401', '2016100402', '2016100404', '2016100406', '2016100407', '2016100410', '2016100411', '2016100412', '2016100413', '2016100415', '2016100416', '2016100418', '2016100419', '2016100420', '2016100421', '2016100422', '2016100423', '2016100424', '2016100425', '2016100427', '2016100429', '2016100430', '2016100431', '2016100433', '2016100435', '2016100436', '2016100437', '2016100440', '2016100444', '2016100447', '2016100448', '2016100450', '2016100452', '2016100455', '2016100456', '2016100457', '2016100466', '2016100467', '2016100469', '2016100470', '2016100471', '2016100472', '2016100474', '2016100476', '2016100481', '2016100483', '2016100484', '2016100485', '2016100490', '2016100491', '2016100493', '2016100494', '2016100500', '2016100501', '2016100505', '2016100506', '2016100507', '2016100509', '2016100510', '2016100511', '2016100512', '2016100513', '2016100514', '2016100515', '2016100516', '2016100517', '2016100518', '2016100519', '2016100520', '2016100521', '2016100523', '2016100525', '2016100527', '2016100529', '2016100530', '2016100532', '2016100533', '2016100534', '2016100536', '2016100537', '2016100538', '2016100539', '2016100543', '2016100544', '2016100545', '2016100546', '2016100548', '2016100549', '2016100550', '2016100552', '2016100553', '2016100554', '2016100555', '2016100556', '2016100558', '2016100559', '2016100560', '2016100562', '2016100563', '2016100564', '2016100565', '2016100566', '2016100567', '2016100568', '2016100569', '2016100571', '2016100573', '2016100574', '2016100575', '2016100576', '2016100577', '2016100578', '2016100579', '2016100580', '2016100581', '2016100582', '2016100583', '2016100584', '2016100585', '2016100586', '2016100587', '2016100588', '2016100589', '2016100590', '2016100592', '2016100594', '2016100596', '2016100597', '2016100598', '2016100599', '2016100600', '2016100601', '2016100602', '2016100603', '2016100605', '2016100606', '2016100607', '2016100608', '2016100609', '2016100610', '2016100611', '2016100612', '2016100613', '2016100614', '2016100615', '2016100616', '2016100617', '2016100618', '2016100619', '2016100621', '2016100622', '2016100624', '2016100625', '2016100626', '2016100627', '2016100628', '2016100629', '2016100630', '2016100634', '2016100636', '2016100637', '2016100638', '2016100642', '2016100644', '2016100645', '2016100646', '2016100647', '2016100648', '2016100649', '2016100650', '2016100652', '2016100653', '2016100658', '2016100659', '2016100662', '2016100664', '2016100665', '2016100666', '2016100667', '2016100668', '2016100669', '2016100670', '2016100671', '2016100674', '2016100678', '2016100679', '2016100680', '2016100682', '2016100684', '2016100686', '2016100687', '2016100689', '2016100690', '2016100691', '2016100692', '2016100694', '2016100695', '2016100696', '2016100699', '2016100701', '2016100703', '2016100704', '2016100705', '2016100706', '2016100709', '2016100710', '2016100714', '2016100715', '2016100717', '2016100718', '2016100720', '2016100723', '2016100724', '2016100725', '2016100727', '2016100729', '2016100730', '2016100731', '2016100733', '2016100734', '2016100735', '2016100736', '2016100737', '2016100738', '2016100741', '2016100742', '2016100744', '2016100745', '2016100746', '2016100747', '2016100748', '2016100749', '2016100750', '2016100751', '2016100752', '2016100753', '2016100757', '2016100758', '2016100759', '2016100761', '2016100762', '2016100763', '2016100764', '2016100765', '2016100767', '2016100769', '2016100774', '2016100780', '2016100782', '2016100784', '2016100786', '2016100787', '2016100788', '2016100790', '2016100794', '2016100795', '2016100797', '2016100798', '2016100799', '2016100800', '2016100802', '2016100803', '2016100804', '2016100805', '2016100806', '2016100808', '2016100810', '2016100811', '2016100813', '2016100815', '2016100817', '2016100818', '2016100820', '2016100823', '2016100824', '2016100825', '2016100827', '2016100830', '2016100834', '2016100835', '2016100840', '2016100841', '2016100842', '2016100844', '2016100845', '2016100846', '2016100848', '2016100849', '2016100851', '2016100853', '2016100854', '2016100855', '2016100856', '2016100857', '2016100859', '2016100860', '2016100861', '2016100862', '2016100865', '2016100866', '2016100867', '2016100869', '2016100870', '2016100872', '2016100874', '2016100875', '2016100877', '2016100879', '2016100881', '2016100882', '2016100883', '2016100884', '2016100885', '2016100887', '2016100888', '2016100889', '2016100890', '2016100893', '2016100894', '2016100897', '2016100899', '2016100901', '2016100904', '2016100908', '2016100909', '2016100912', '2016100913', '2016100915', '2016100916', '2016100917', '2016100920', '2016100923', '2016100924', '2016100926', '2016100927', '2016100936', '2016100937', '2016100938', '2016100939', '2016100941', '2016100942', '2016100943', '2016100947', '2016100948', '2016100949', '2016100950', '2016100951', '2016100952', '2016100953', '2016100954', '2016100955', '2016100959', '2016100960', '2016100961', '2016100962', '2016100963', '2016100964', '2016100966', '2016100968', '2016100969', '2016100970', '2016100971', '2016100972', '2016100973', '2016100976', '2016100978', '2016100979', '2016100981', '2016100984', '2016100985', '2016100986', '2016100989', '2016100990', '2016100993', '2016100994', '2016100995', '2016100996', '2016100997', '2016100999', '2016101002', '2016101003', '2016101006', '2016101009', '2016101014', '2016101015', '2016101017', '2016101021', '2016101022', '2016101023', '2016101026', '2016101029', '2016101031', '2016101035', '2016101038', '2016101039', '2016101041', '2016101042', '2016101043', '2016101044', '2016101046', '2016101048', '2016101049', '2016101051', '2016101053', '2016101054', '2016101055', '2016101057', '2016101058', '2016101062', '2016101064', '2016101066', '2016101067', '2016101070', '2016101073', '2016101075', '2016101080', '2016101081', '2016101082', '2016101084', '2016101088', '2016101089', '2016101091', '2016101093', '2016101095', '2016101098', '2016101100', '2016101102', '2016101105', '2016101114', '2016101122', '2016101126', '2016101128', '2016101130', '2016101131', '2016101132', '2016101134', '2016101135', '2016101139', '2016101141', '2016101143', '2016101144', '2016101145', '2016101146', '2016101147', '2016101148', '2016101149', '2016101150', '2016101151', '2016101152', '2016101153', '2016101154', '2016101155', '2016101156', '2016101157', '2016101158', '2016101159', '2016101160', '2016101161', '2016101162', '2016101163', '2016101164', '2016101165', '2016101166', '2016101167', '2016101168', '2016101169', '2016101170', '2016101171', '2016101172', '2016101176', '2016101180', '2016101182', '2016101185', '2016101186', '2016101191', '2016101192', '2016101194', '2016101199', '2016101202', '2016101204', '2016101205', '2016101206', '2016101207', '2016101208', '2016101209', '2016101210', '2016101211', '2016101214', '2016101218', '2016101219', '2016101220', '2016101221', '2016101222', '2016101223', '2016101224', '2016101225', '2016101226', '2016101228', '2016101231', '2016101232', '2016101233', '2016101234', '2016101236', '2016101239', '2016101240', '2016101241', '2016101242', '2016101244', '2016101245', '2016101250', '2016101251', '2016101252', '2016101254', '2016101257', '2016101258', '2016101259', '2016101260', '2016101263', '2016101264', '2016101266', '2016101267', '2016101268', '2016101269', '2016101270', '2016101275', '2016101276', '2016101278', '2016101279', '2016101280', '2016101283', '2016101284', '2016101285', '2016101286', '2016101287', '2016101288', '2016101289', '2016101290', '2016101291', '2016101292', '2016101293', '2016101301', '2016101302', '2016101303', '2016101305', '2016101308', '2016101309', '2016101312', '2016101313', '2016101315', '2016101321', '2016101322', '2016101323', '2016101324', '2016101325', '2016101328', '2016101329', '2016101331', '2016101333', '2016101334', '2016101335', '2016101337', '2016101339', '2016101340', '2016101341', '2016101342', '2016101344', '2016101345', '2016101346', '2016101347', '2016101348', '2016101349', '2016101350', '2016101352', '2016101355', '2016101359', '2016101361', '2016101362', '2016101366', '2016101367', '2016101368', '2016101370', '2016101371', '2016101372', '2016101373', '2016101374', '2016101376', '2016101377', '2016101379', '2016101380', '2016101381', '2016101382', '2016101383', '2016101384', '2016101385', '2016101388', '2016101390', '2016101391', '2016101393', '2016101394', '2016101395', '2016101398', '2016101399', '2016101400', '2016101402', '2016101404', '2016101407', '2016101409', '2016101413', '2016101416', '2016101417', '2016101418', '2016101421', '2016101423', '2016101424', '2016101427', '2016101428', '2016101430', '2016101432', '2016101435', '2016101436', '2016101437', '2016101438', '2016101439', '2016101440', '2016101441', '2016101442', '2016101443', '2016101444', '2016101448', '2016101450', '2016101451', '2016101452', '2016101453', '2016101455', '2016101457', '2016101459', '2016101460', '2016101461', '2016101465', '2016101467', '2016101469', '2016101473', '2016101474', '2016101477', '2016101479', '2016101480', '2016101481', '2016101485', '2016101490', '2016101491', '2016101492', '2016101493', '2016101494', '2016101495', '2016101496', '2016101497', '2016101498', '2016101500', '2016101501', '2016101507', '2016101509', '2016101512', '2016101513', '2016101514', '2016101524', '2016101534', '2016101535', '2016101537', '2016101539', '2016101540', '2016101542', '2016101543', '2016101544', '2016101545', '2016101547', '2016101548', '2016101549', '2016101550', '2016101551', '2016101553', '2016101555', '2016101556', '2016101559', '2016101560', '2016101561', '2016101562', '2016101566', '2016101570', '2016101573', '2016101575', '2016101579', '2016101580', '2016101581', '2016101583', '2016101584', '2016101585', '2016101589', '2016101591', '2016101592', '2016101593', '2016101595', '2016101597', '2016101598', '2016101599', '2016101601', '2016101603', '2016101604', '2016101605', '2016101607', '2016101608', '2016101609', '2016101610', '2016101611', '2016101613', '2016101615', '2016101616', '2016101619', '2016101620', '2016101622', '2016101623', '2016101625', '2016101626', '2016101628', '2016101629', '2016101630', '2016101632', '2016101633', '2016101636', '2016101638', '2016101639', '2016101640', '2016101642', '2016101643', '2016101645', '2016101646', '2016101647', '2016101648', '2016101650', '2016101653', '2016101654', '2016101658', '2016101661', '2016101666', '2016101672', '2016101674', '2016101675', '2016101676', '2016101677', '2016101679', '2016101680', '2016101681', '2016101682', '2016101684', '2016101686', '2016101687', '2016101690', '2016101691', '2016101692', '2016101695', '2016101696', '2016101698', '2016101699', '2016101700', '2016101702', '2016101704', '2016101705', '2016101706', '2016101707', '2016101708', '2016101710', '2016101711', '2016101712', '2016101713', '2016101716', '2016101717', '2016101718', '2016101720', '2016101721', '2016101722', '2016101724', '2016101726', '2016101728', '2016101733', '2016101743', '2016101744', '2016101745', '2016101748', '2016101750', '2016101751', '2016101752', '2016101753', '2016101755', '2016101756', '2016101757', '2016101758', '2016101759', '2016101760', '2016101761', '2016101762', '2016101764', '2016101766', '2016101767', '2016101768', '2016101769', '2016101770', '2016101771', '2016101773', '2016101775', '2016101776', '2016101777', '2016101778', '2016101779', '2016101780', '2016101781', '2016101782', '2016101783', '2016101784', '2016101786', '2016101787', '2016101789', '2016101790', '2016101791', '2016101792', '2016101794', '2016101795', '2016101796', '2016101800', '2016101807', '2016101810', '2016101812', '2016101814', '2016101815', '2016101816', '2016101819', '2016101821', '2016101822', '2016101823', '2016101825', '2016101827', '2016101830', '2016101831', '2016101832', '2016101835', '2016101837', '2016101838', '2016101839', '2016101840', '2016101841', '2016101842', '2016101843', '2016101844', '2016101845', '2016101846', '2016101847', '2016101848', '2016101853', '2016101855', '2016101857', '2016101858', '2016101859', '2016101860', '2016101864', '2016101865', '2016101866', '2016101868', '2016101870', '2016101871', '2016101879', '2016101880', '2016101883', '2016101884', '2016101885', '2016101886', '2016101887', '2016101888', '2016101889', '2016101891', '2016101892', '2016101893', '2016101894', '2016101895', '2016101897', '2016101900', '2016101901', '2016101902', '2016101906', '2016101907', '2016101908', '2016101909', '2016101911', '2016101913', '2016101915', '2016101917', '2016101918', '2016101919', '2016101920', '2016101921', '2016101922', '2016101923', '2016101924', '2016101926', '2016101927', '2016101929', '2016101930', '2016101934', '2016101936', '2016101938', '2016101939', '2016101941', '2016101943', '2016101946', '2016101947', '2016101948', '2016101950', '2016101951', '2016101952', '2016101953', '2016101956', '2016101958', '2016101960', '2016101961', '2016101964', '2016101965', '2016101968', '2016101971', '2016101972', '2016101973', '2016101976', '2016101977', '2016101978', '2016101979', '2016101982', '2016101983', '2016101984', '2016101985', '2016101986', '2016101987', '2016101988', '2016101989', '2016101992', '2016101994', '2016101998', '2016101999', '2016102000', '2016102001', '2016102003', '2016102004', '2016102005', '2016102006', '2016102007', '2016102008', '2016102009', '2016102011', '2016102012', '2016102013', '2016102014', '2016102015', '2016102017', '2016102018', '2016102020', '2016102021', '2016102024', '2016102026', '2016102028', '2016102029', '2016102030', '2016102031', '2016102032', '2016102033', '2016102034', '2016102035', '2016102038', '2016102039', '2016102040', '2016102041', '2016102042', '2016102043', '2016102044', '2016102045', '2016102048', '2016102049', '2016102050', '2016102051', '2016102052', '2016102053', '2016102054', '2016102055', '2016102056', '2016102058', '2016102066', '2016102069', '2016102070', '2016102071', '2016102073', '2016102074', '2016102075', '2016102076', '2016102077', '2016102078', '2016102080', '2016102081', '2016102082', '2016102083', '2016102085', '2016102087', '2016102088', '2016102089', '2016102091', '2016102092', '2016102093', '2016102094', '2016102095', '2016102096', '2016102097', '2016102100', '2016102102', '2016102103', '2016102104', '2016102105', '2016102107', '2016102108', '2016102109', '2016102110', '2016102111', '2016102112', '2016102113', '2016102116', '2016102118', '2016102119', '2016102120', '2016102121', '2016102122', '2016102123', '2016102126', '2016102127', '2016102128', '2016102129', '2016102133', '2016102134', '2016102135', '2016102137', '2016102139', '2016102140', '2016102142', '2016102143', '2016102144', '2016102145', '2016102146', '2016102147', '2016102150', '2016102151', '2016102152', '2016102153', '2016102154', '2016102155', '2016102156', '2016102157', '2016102158', '2016102161', '2016102162', '2016102163', '2016102164', '2016102165', '2016102166', '2016102167', '2016102168', '2016102169', '2016102170', '2016102171', '2016102172', '2016102173', '2016102174', '2016102175', '2016102176', '2016102177', '2016102178', '2016102179', '2016102180', '2016102181', '2016102182', '2016102183', '2016102184', '2016102185', '2016102187', '2016102188', '2016102189', '2016102190', '2016102191', '2016102192', '2016102193', '2016102194', '2016102197', '2016102198', '2016102199', '2016102200', '2016102201', '2016102203', '2016102207', '2016102209', '2016102210', '2016102211', '2016102212', '2016102213', '2016102214', '2016102215', '2016102216', '2016102217', '2016102218', '2016102219', '2016102221', '2016102222', '2016102224', '2016102225', '2016102227', '2016102228', '2016102229', '2016102230', '2016102232', '2016102233', '2016102234', '2016102235', '2016102236', '2016102237', '2016102238', '2016102239', '2016102241', '2016102242', '2016102248', '2016102252', '2016102257', '2016102258', '2016102259', '2016102261', '2016102262', '2016102266', '2016102267', '2016102268', '2016102269', '2016102270', '2016102272', '2016102273', '2016102274', '2016102275', '2016102276', '2016102280', '2016102281', '2016102282', '2016102284', '2016102285', '2016102286', '2016102287', '2016102289', '2016102291', '2016102293', '2016102294', '2016102295', '2016102296', '2016102297', '2016102298', '2016102299', '2016102303', '2016102304', '2016102305', '2016102307', '2016102310', '2016102311', '2016102312', '2016102313', '2016102316', '2016102317', '2016102319', '2016102320', '2016102321', '2016102323', '2016102324', '2016102325', '2016102327', '2016102328', '2016102329', '2016102331', '2016102332', '2016102333', '2016102334', '2016102335', '2016102336', '2016102337', '2016102339', '2016102340', '2016102341', '2016102342', '2016102343', '2016102344', '2016102345', '2016102346', '2016102347', '2016102348', '2016102351', '2016102353', '2016102356', '2016102361', '2016102362', '2016102364', '2016102365', '2016102368', '2016102369', '2016102370', '2016102372', '2016102375', '2016102376', '2016102377', '2016102378', '2016102379', '2016102380', '2016102381', '2016102382', '2016102383', '2016102384', '2016102385', '2016102386', '2016102388', '2016102389', '2016102390', '2016102395', '2016102396', '2016102397', '2016102398', '2016102399', '2016102400', '2016102401', '2016102402', '2016102404', '2016102405', '2016102406', '2016102407', '2016102408', '2016102409', '2016102410', '2016102412', '2016102413', '2016102414', '2016102416', '2016102417', '2016102418', '2016102419', '2016102420', '2016102421', '2016102422', '2016102424', '2016102425', '2016102426', '2016102427', '2016102429', '2016102430', '2016102431', '2016102432', '2016102433', '2016102434', '2016102435', '2016102436', '2016102438', '2016102439', '2016102440', '2016102442', '2016102443', '2016102444', '2016102446', '2016102447', '2016102448', '2016102450', '2016102451', '2016102452', '2016102453', '2016102455', '2016102456', '2016102457', '2016102458', '2016102459', '2016102460', '2016102463', '2016102467', '2016102469', '2016102470', '2016102471', '2016102472', '2016102474', '2016102475', '2016102476', '2016102477', '2016102479', '2016102480', '2016102483', '2016102486', '2016102487', '2016102489', '2016102490', '2016102492', '2016102493', '2016102497', '2016102499', '2016102500', '2016102504', '2016102506', '2016102507', '2016102508', '2016102510', '2016102512', '2016102513', '2016102515', '2016102516', '2016102520', '2016102523', '2016102524', '2016102525', '2016102526', '2016102528', '2016102529', '2016102530', '2016102532', '2016102533', '2016102534', '2016102535', '2016102543', '2016102544', '2016102548', '2016102550', '2016102551', '2016102552', '2016102553', '2016102554', '2016102555', '2016102556', '2016102557', '2016102561', '2016102562', '2016102563', '2016102564', '2016102565', '2016102568', '2016102569', '2016102570', '2016102571', '2016102572', '2016102574', '2016102575', '2016102576', '2016102583', '2016102584', '2016102586', '2016102587', '2016102588', '2016102592', '2016102597', '2016102598', '2016102600', '2016102602', '2016102604', '2016102605', '2016102607', '2016102610', '2016102611', '2016102612', '2016102613', '2016102614', '2016102615', '2016102617', '2016102618', '2016102619', '2016102620', '2016102621', '2016102623', '2016102624', '2016102625', '2016102626', '2016102627', '2016102628', '2016102629', '2016102631', '2016102632', '2016102633', '2016102636', '2016102637', '2016102638', '2016102641', '2016102642', '2016102643', '2016102645', '2016102646', '2016102650', '2016102651', '2016102652', '2016102655', '2016102658', '2016102659', '2016102661', '2016102663', '2016102664', '2016102665', '2016102668', '2016102670', '2016102674', '2016102675', '2016102677', '2016102678', '2016102681', '2016102687', '2016102691', '2016102692', '2016102694', '2016102695', '2016102696', '2016102697', '2016102698', '2016102700', '2016102701', '2016102702', '2016102703', '2016102705', '2016102706', '2016102707', '2016102709', '2016102710', '2016102711', '2016102712', '2016102713', '2016102714', '2016102715', '2016102717', '2016102718', '2016102719', '2016102720', '2016102721', '2016102722', '2016102723', '2016102726', '2016102727', '2016102729', '2016102730', '2016102731', '2016102733', '2016102734', '2016102735', '2016102736', '2016102737', '2016102738', '2016102741', '2016102742', '2016102744', '2016102745', '2016102747', '2016102749', '2016102751', '2016102753', '2016102760', '2016102763', '2016102764', '2016102767', '2016102768', '2016102769', '2016102771', '2016102772', '2016102773', '2016102775', '2016102776', '2016102777', '2016102778', '2016102780', '2016102781', '2016102782', '2016102783', '2016102784', '2016102785', '2016102786', '2016102787', '2016102790', '2016102791', '2016102793', '2016102794', '2016102795', '2016102796', '2016102797', '2016102799', '2016102801', '2016102805', '2016102806', '2016102807', '2016102808', '2016102809', '2016102810', '2016102811', '2016102812', '2016102813', '2016102814', '2016102815', '2016102817', '2016102818', '2016102819', '2016102820', '2016102824', '2016102827', '2016102828', '2016102829', '2016102830', '2016102832', '2016102835', '2016102837', '2016102841', '2016102843', '2016102847', '2016102849', '2016102852', '2016102853', '2016102854', '2016102855', '2016102857', '2016102858', '2016102859', '2016102863', '2016102865', '2016102866', '2016102867', '2016102868', '2016102870', '2016102871', '2016102872', '2016102873', '2016102875', '2016102884', '2016102886', '2016102888', '2016102889', '2016102890', '2016102891', '2016102894', '2016102895', '2016102896', '2016102897', '2016102898', '2016102899', '2016102900', '2016102901', '2016102904', '2016102905', '2016102906', '2016102907', '2016102909', '2016102915', '2016102917', '2016102918', '2016102924', '2016102925', '2016102926', '2016102928', '2016102929', '2016102930', '2016102931', '2016102934', '2016102935', '2016102936', '2016102938', '2016102941', '2016102942', '2016102944', '2016102945', '2016102949', '2016102950', '2016102951', '2016102952', '2016102953', '2016102954', '2016102955', '2016102956', '2016102963', '2016102964', '2016102965', '2016102967', '2016102969', '2016102970', '2016102972', '2016102973', '2016102974', '2016102975', '2016102977', '2016102978', '2016102979', '2016102980', '2016102983', '2016102988', '2016102989', '2016102991', '2016102993', '2016102996', '2016102998', '2016102999', '2016103000', '2016103001', '2016103002', '2016103007', '2016103017', '2016103018', '2016103019', '2016103020', '2016103021', '2016103022', '2016103023', '2016103024', '2016103025', '2016103028', '2016103029', '2016103030', '2016103031', '2016103032', '2016103036', '2016103038', '2016103039', '2016103041', '2016103042', '2016103043', '2016103045', '2016103046', '2016103050', '2016103054', '2016103056', '2016103059', '2016103062', '2016103063', '2016103064', '2016103065', '2016103066', '2016103067', '2016103068', '2016103069', '2016103071', '2016103072', '2016103073', '2016103074', '2016103075', '2016103078', '2016103079', '2016103080', '2016103082', '2016103083', '2016103084', '2016103085', '2016103086', '2016103087', '2016103088', '2016103091', '2016103092', '2016103093', '2016103094', '2016103096', '2016103097', '2016103098', '2016103099', '2016103100', '2016103101', '2016103102', '2016103103', '2016103105', '2016103106', '2016103107', '2016103108', '2016103109', '2016103111', '2016103115', '2016103117', '2016103119', '2016103122', '2016103123', '2016103124', '2016103125', '2016103126', '2016103127', '2016103128', '2016103129', '2016103131', '2016103132', '2016103133', '2016103134', '2016103136', '2016103137', '2016103138', '2016103140', '2016103141', '2016103142', '2016103143', '2016103146', '2016103148', '2016103150', '2016103154', '2016103155', '2016103156', '2016103158', '2016103160', '2016103162', '2016103163', '2016103166', '2016103167', '2016103179', '2016103188', '2016103190', '2016103191', '2016103192', '2016103193', '2016103195', '2016103196', '2016103197', '2016103198', '2016103199', '2016103200', '2016103201', '2016103202', '2016103203', '2016103204', '2016103205', '2016103206', '2016103208', '2016103210', '2016103211', '2016103212', '2016103213', '2016103214', '2016103215', '2016103217', '2016103219', '2016103220', '2016103221', '2016103222', '2016103224', '2016103226', '2016103227', '2016103228', '2016103229', '2016103230', '2016103233', '2016103234', '2016103235', '2016103236', '2016103237', '2016103238', '2016103242', '2016103244', '2016103248', '2016103249', '2016103250', '2016103251', '2016103252', '2016103254', '2016103255', '2016103256', '2016103257', '2016103258', '2016103260', '2016103261', '2016103262', '2016103263', '2016103264', '2016103267', '2016103270', '2016103272', '2016103273', '2016103274', '2016103275', '2016103277', '2016103278', '2016103280', '2016103281', '2016103282', '2016103283', '2016103285', '2016103286', '2016103288', '2016103289', '2016103290', '2016103291', '2016103292', '2016103293', '2016103294', '2016103295', '2016103296', '2016103297', '2016103298', '2016103300', '2016103301', '2016103302', '2016103303', '2016103305', '2016103306', '2016103307', '2016103308', '2016103309', '2016103310', '2016103311', '2016103312', '2016103313', '2016103314', '2016103315', '2016103316', '2016103317', '2016103319', '2016103321', '2016103322', '2016103323', '2016103324', '2016103326', '2016103327', '2016103328', '2016103329', '2016103330', '2016103331', '2016103334', '2016103338', '2016103339', '2016103341', '2016103342', '2016103343', '2016103344', '2016103345', '2016103346', '2016103347', '2016103348', '2016103349', '2016103350', '2016103351', '2016103353', '2016103354', '2016103355', '2016103356', '2016103357', '2016103358', '2016103359', '2016103360', '2016103362', '2016103363', '2016103368', '2016103370', '2016103371', '2016103374', '2016103375', '2016103376', '2016103378', '2016103379', '2016103380', '2016103382', '2016103383', '2016103385', '2016103386', '2016103387', '2016103388', '2016103389', '2016103398', '2016103399', '2016103400', '2016103401', '2016103402', '2016103403', '2016103408', '2016103409', '2016103410', '2016103411', '2016103414', '2016103415', '2016103416', '2016103418', '2016103421', '2016103423', '2016103424', '2016103434', '2016103438', '2016103439', '2016103440', '2016103442', '2016103443', '2016103444', '2016103446', '2016103447', '2016103448', '2016103449', '2016103450', '2016103451', '2016103453', '2016103454', '2016103456', '2016103458', '2016103459', '2016103460', '2016103462', '2016103463', '2016103465', '2016103467', '2016103474', '2016103476', '2016103478', '2016103480', '2016103481', '2016103485', '2016103487', '2016103488', '2016103489', '2016103491', '2016103492', '2016103493', '2016103495', '2016103496', '2016103499', '2016103501', '2016103502', '2016103504', '2016103505', '2016103507', '2016103509', '2016103510', '2016103511', '2016103512', '2016103514', '2016103515', '2016103516', '2016103518', '2016103522', '2016103523', '2016103524', '2016103526', '2016103527', '2016103529', '2016103530', '2016103533', '2016103534', '2016103535', '2016103538', '2016103539', '2016103542', '2016103543', '2016103544', '2016103545', '2016103547', '2016103548', '2016103550', '2016103551', '2016103553', '2016103555', '2016103556', '2016103564', '2016103566', '2016103569', '2016103570', '2016103573', '2016103574', '2016103575', '2016103578', '2016103579', '2016103580', '2016103583', '2016103584', '2016103587', '2016103590', '2016103591', '2016103594', '2016103595', '2016103596', '2016103597', '2016103598', '2016103599', '2016103607', '2016103611', '2016103612', '2016103615', '2016103618', '2016103620', '2016103621', '2016103622', '2016103624', '2016103626', '2016103628', '2016103629', '2016103630', '2016103631', '2016103641', '2016103646', '2016103647', '2016103648', '2016103649', '2016103652', '2016103653', '2016103656', '2016103657', '2016103663', '2016103668', '2016103669', '2016103671', '2016103672', '2016103673', '2016103674', '2016103679', '2016103680', '2016103681', '2016103682', '2016103683', '2016103684', '2016103685', '2016103686', '2016103689', '2016103690', '2016103693', '2016103694', '2016103695', '2016103696', '2016103699', '2016103702', '2016103703', '2016103704', '2016103705', '2016103706', '2016103710', '2016103711', '2016103713', '2016103714', '2016103715', '2016103716', '2016103717', '2016103718', '2016103719', '2016103720', '2016103721', '2016103723', '2016103724', '2016103728', '2016103729', '2016103730', '2016103731', '2016103732', '2016103733', '2016103736', '2016103739', '2016103741', '2016103744', '2016103745', '2016103748', '2016103752', '2016103753', '2016103754', '2016103755', '2016103756', '2016103757', '2016103758', '2016103759', '2016103760', '2016103761', '2016103762', '2016103764', '2016103765', '2016103766', '2016103767', '2016103768', '2016103769', '2016103770', '2016103771', '2016103772', '2016103773', '2016103774', '2016103775', '2016103777', '2016103779', '2016103785', '2016103787', '2016103788', '2016103790', '2016103791', '2016103801', '2016103803', '2016103806', '2016103807', '2016103809', '2016103810', '2016103812', '2016103813', '2016103815', '2016103820', '2016103822', '2016103825', '2016103831', '2016103836', '2016103842', '2016103849', '2016103854', '2016103856', '2016103861', '2016103864', '2016103865', '2016103870', '2016103872', '2016103874', '2016103875', '2016103878', '2016103879', '2016103880', '2016103881', '2016103884', '2016103885', '2016103886', '2016103888', '2016103892', '2016103895', '2016103897', '2016103898', '2016103900', '2016103904', '2016103906', '2016103907', '2016103908', '2016103909', '2016103910', '2016103911', '2016103912', '2016103913', '2016103914', '2016103915', '2016103916', '2016103918', '2016103919', '2016103920', '2016103921', '2016103922', '2016103923', '2016103926', '2016103927', '2016103929', '2016103930', '2016103933', '2016103935', '2016103936', '2016103937', '2016103938', '2016103939', '2016103940', '2016103941', '2016103942', '2016103943', '2016103945', '2016103946', '2016103947', '2016103950', '2016103952', '2016103953', '2016103954', '2016103957', '2016103958', '2016103960', '2016103961', '2016103962', '2016103963', '2016103964', '2016103967', '2016103969', '2016103970', '2016103971', '2016103973', '2016103974', '2016103975', '2016103976', '2016103977', '2016103978', '2016103981', '2016103983', '2016103984', '2016103985', '2016103989', '2016103990', '2016103994', '2016103997', '2016103999', '2016104000', '2016104001', '2016104002', '2016104003', '2016104005', '2016104006', '2016104013', '2016104016', '2016104018', '2016104023', '2016104027', '2016104029', '2016104031', '2016104032', '2016104033', '2016104034', '2016104035', '2016104036', '2016104037', '2016104038', '2016104040', '2016104042', '2016104044', '2016104045', '2016104051', '2016104053', '2016104057', '2016104058', '2016104061', '2016104062', '2016104063', '2016104064', '2016104066', '2016104069', '2016104075', '2016104076', '2016104078', '2016104080', '2016104081', '2016104082', '2016104083', '2016104085', '2016104086', '2016104087', '2016104089', '2016104090', '2016104091', '2016104092', '2016104093', '2016104096', '2016104098', '2016104100', '2016104103', '2016104105', '2016104107', '2016104112', '2016104114', '2016104121', '2016104122', '2016104123', '2016104126', '2016104128', '2016104136', '2016104137', '2016104141', '2016104146', '2016104147', '2016104148', '2016104149', '2016104150', '2016104151', '2016104152', '2016104153', '2016104154', '2016104155', '2016104157', '2016104159', '2016104166', '2016104167', '2016104169', '2016104173', '2016104174', '2016104177', '2016104180', '2016104183', '2016104185', '2016104188', '2016104189', '2016104192', '2016104193', '2016104195', '2016104198', '2016104201', '2016104205', '2016104207', '2016104208', '2016104209', '2016104210', '2016104211', '2016104214', '2016104215', '2016104217', '2016104218', '2016104219', '2016104220', '2016104221', '2016104222', '2016104223', '2016104224', '2016104225', '2016104226', '2016104227', '2016104229', '2016104230', '2016104231', '2016104232', '2016104233', '2016104234', '2016104235', '2016104236', '2016104237', '2016104238', '2016104239', '2016104240', '2016104241', '2016104242', '2016104243', '2016104244', '2016104245', '2016104246', '2016104247', '2016104248', '2016104251', '2016104252', '2016104253', '2016104256', '2016104257', '2016104258', '2016104259', '2016104260', '2016104261', '2016104262', '2016104263', '2016104264', '2016104265', '2016104267', '2016104268', '2016104269', '2016104271', '2016104272', '2016104273', '2016104274', '2016104275', '2016104276', '2016104278', '2016104280', '2016104281', '2016104282', '2016104283', '2016104285', '2016104286', '2016104289', '2016104291', '2016104292', '2016104293', '2016104295', '2016104297', '2016104298', '2016104299', '2016104302', '2016104304', '2016104305', '2016104306', '2016104307', '2016104308', '2016104309', '2016104310', '2016104311', '2016104312', '2016104314', '2016104315', '2016104317', '2016104318', '2016104319', '2016104320', '2016104321', '2016104322', '2016104325', '2016104326', '2016104327', '2016104328', '2016104330', '2016104334', '2016104335', '2016104336', '2016104337', '2016104338', '2016104339', '2016104340', '2016104341', '2016104342', '2016104343', '2016104344', '2016104345', '2016104347', '2016104348', '2016104349', '2016104350', '2016104351', '2016104352', '2016104353', '2016104354', '2016104355', '2016104356', '2016104357', '2016104358', '2016104359', '2016104361', '2016104362', '2016104363', '2016104364', '2016104365', '2016104367', '2016104368', '2016104370', '2016104371', '2016104372', '2016104373', '2016104374', '2016104375', '2016104376', '2016104377', '2016104378', '2016104379', '2016104380', '2016104381', '2016104382', '2016104383', '2016104385', '2016104386', '2016104387', '2016104388', '2016104389', '2016104390', '2016104391', '2016104392', '2016104393', '2016104397', '2016104398', '2016104399', '2016104400', '2016104401', '2016104403', '2016104404', '2016104405', '2016104407', '2016104408', '2016104409', '2016104410', '2016104412', '2016104413', '2016104414', '2016104415', '2016104416', '2016104420', '2016104421', '2016104422', '2016104423', '2016104424', '2016104425', '2016104426', '2016104428', '2016104429', '2016104430', '2016104431', '2016104433', '2016104434', '2016104435', '2016104436', '2016104438', '2016104439', '2016104440', '2016104442', '2016104444', '2016104445', '2016104446', '2016104447', '2016104448', '2016104449', '2016104450', '2016104451', '2016104452', '2016104453', '2016104454', '2016104455', '2016104456', '2016104458', '2016104459', '2016104460', '2016104461', '2016104462', '2016104463', '2016104464', '2016104465', '2016104467', '2016104468', '2016104469', '2016104470', '2016104471', '2016104472', '2016104473', '2016104474', '2016104475', '2016104476', '2016104477', '2016104478', '2016104480', '2016104481', '2016104482', '2016104483', '2016104484', '2016104485', '2016104486', '2016104487', '2016104489', '2016104490', '2016104491', '2016104492', '2016104494', '2016104495', '2016104496', '2016104497', '2016104498', '2016104499', '2016104500', '2016104501', '2016104502', '2016104503', '2016104505', '2016104506', '2016104507', '2016104508', '2016104509', '2016104511', '2016104512', '2016104513', '2016104514', '2016104515', '2016104516', '2016104517', '2016104518', '2016104519', '2016104520', '2016104521', '2016104523', '2016104524', '2016104525', '2016104526', '2016104527', '2016104528', '2016104529', '2016104530', '2016104531', '2016104532', '2016104533', '2016104534', '2016104535', '2016104536', '2016104537', '2016104538', '2016104539', '2016104540', '2016104541', '2016104542', '2016104544', '2016104546', '2016104547', '2016104548', '2016104549', '2016104552', '2016104553', '2016104554', '2016104556', '2016104557', '2016104559', '2016104561', '2016104563', '2016104564', '2016104565', '2016104566', '2016104567', '2016104568', '2016104569', '2016104570', '2016104573', '2016104574', '2016104576', '2016104579', '2016104580', '2016104581', '2016104583', '2016104584', '2016104585', '2016104587', '2016104588', '2016104589', '2016104590', '2016104591', '2016104593', '2016104594', '2016104595', '2016104596', '2016104597', '2016104598', '2016104599', '2016104600', '2016104602', '2016104603', '2016104604', '2016104605', '2016104606', '2016104608', '2016104609', '2016104610', '2016104611', '2016104612', '2016104613', '2016104614', '2016104615', '2016104617', '2016104618', '2016104620', '2016104621', '2016104622', '2016104623', '2016104624', '2016104625', '2016104626', '2016104627', '2016104628', '2016104629', '2016104630', '2016104631', '2016104632', '2016104633', '2016104634', '2016104635', '2016104636', '2016104637', '2016104638', '2016104639', '2016104640', '2016104641', '2016104642', '2016104643', '2016104644', '2016104645', '2016104646', '2016104648', '2016104649', '2016104650', '2016104652', '2016104653', '2016104654', '2016104655', '2016104656', '2016104657', '2016104658', '2016104659', '2016104660', '2016104661', '2016104662', '2016104663', '2016104664', '2016104665', '2016104666', '2016104667', '2016104668', '2016104669', '2016104670', '2016104671', '2016104672', '2016104673', '2016104674', '2016104675', '2016104676', '2016104677', '2016104679', '2016104680', '2016104681', '2016104682', '2016104685', '2016104686', '2016104687', '2016104689', '2016104691', '2016104692', '2016104693', '2016104694', '2016104695', '2016104696', '2016104697', '2016104698', '2016104699', '2016104700', '2016104701', '2016104702', '2016104704', '2016104705', '2016104706', '2016104707', '2016104708', '2016104709', '2016104710', '2016104711', '2016104713', '2016104714', '2016104715', '2016104716', '2016104717', '2016104718', '2016104719', '2016104720', '2016104721', '2016104722', '2016104723', '2016104724', '2016104725', '2016104726', '2016104727', '2016104728', '2016104729', '2016104730', '2016104731', '2016104733', '2016104734', '2016104735', '2016104736', '2016104737', '2016104738', '2016104739', '2016104740', '2016104741', '2016104742', '2016104743', '2016104744', '2016104745', '2016104746', '2016104747', '2016104748', '2016104749', '2016104750', '2016104751', '2016104753', '2016104755', '2016104756', '2016104758', '2016104759', '2016104760', '2016104761', '2016104762', '2016104765', '2016104767', '2016104768', '2016104769', '2016104770', '2016104771', '2016104772', '2016104773', '2016104775', '2016104776', '2016104777', '2016104778', '2016104779', '2016104780', '2016104781', '2016104782', '2016104783', '2016104784', '2016104785', '2016104786', '2016104787', '2016104788', '2016104789', '2016104790', '2016104791', '2016104792', '2016104793', '2016104794', '2016104795', '2016104796', '2016104797', '2016104798', '2016104799', '2016104800', '2016104801', '2016104802', '2016104803', '2016104804', '2016104805', '2016104806', '2016104807', '2016104808', '2016104809', '2016104812', '2016104813', '2016104814', '2016104815', '2016104816', '2016104817', '2016104818', '2016104819', '2016104820', '2016104821', '2016104822', '2016104823', '2016104826', '2016104827', '2016104828', '2016104829', '2016104830', '2016104831', '2016104832', '2016104834', '2016104835', '2016104836', '2016104837', '2016104838', '2016104839', '2016104840', '2016104841', '2016104843', '2016104844', '2016104846', '2016104848', '2016104849', '2016104851', '2016104852', '2016104853', '2016104854', '2016104855', '2016104856', '2016104857', '2016104858', '2016104860', '2016104861', '2016104862', '2016104864', '2016104865', '2016104866', '2016104867', '2016104868', '2016104869', '2016104870', '2016104871', '2016104872', '2016104874', '2016104875', '2016104876', '2016104877', '2016104878', '2016104879', '2016104880', '2016104881', '2016104883', '2016104884', '2016104885', '2016104886', '2016104887', '2016104888', '2016104889', '2016104890', '2016104891', '2016104894', '2016104895', '2016104896', '2016104897', '2016104898', '2016104899', '2016104902', '2016104903', '2016104904', '2016104905', '2016104907', '2016104908', '2016104909', '2016104910', '2016104911', '2016104913', '2016104915', '2016104916', '2016104919', '2016104920', '2016104921', '2016104922', '2016104923', '2016104925', '2016104926', '2016104927', '2016104928', '2016104929', '2016104933', '2016104936', '2016104937', '2016104938', '2016104939', '2016104941', '2016104942', '2016104943', '2016104944', '2016104945', '2016104946', '2016104947', '2016104950', '2016104952', '2016104953', '2016104954', '2016104955', '2016104957', '2016104958', '2016104959', '2016104960', '2016104961', '2016104962', '2016104963', '2016104965', '2016104966', '2016104967', '2016104968', '2016104969', '2016104970', '2016104971', '2016104973', '2016104974', '2016104975', '2016104976', '2016104977', '2016104978', '2016104979', '2016104981', '2016104982', '2016104983', '2016104984', '2016104985', '2016104987', '2016104988', '2016104989', '2016104990', '2016104991', '2016104992', '2016104994', '2016104995', '2016104996', '2016104997', '2016104998', '2016105001', '2016105002', '2016105003', '2016105004', '2016105005', '2016105006', '2016105007', '2016105008', '2016105009', '2016105010', '2016105011', '2016105013', '2016105014', '2016105015', '2016105016', '2016105017', '2016105019', '2016105020', '2016105021', '2016105022', '2016105024', '2016105025', '2016105026', '2016105027', '2016105028', '2016105029', '2016105030', '2016105031', '2016105032', '2016105033', '2016105035', '2016105037', '2016105038', '2016105039', '2016105040', '2016105042', '2016105043', '2016105045', '2016105047', '2016105048', '2016105049', '2016105051', '2016105052', '2016105054', '2016105055', '2016105056', '2016105058', '2016105059', '2016105060', '2016105062', '2016105063', '2016105064', '2016105065', '2016105066', '2016105067', '2016105068', '2016105069', '2016105070', '2016105071', '2016105072', '2016105074', '2016105076', '2016105077', '2016105081', '2016105082', '2016105083', '2016105084', '2016105085', '2016105086', '2016105087', '2016105088', '2016105089', '2016105090', '2016105091', '2016105093', '2016105095', '2016105096', '2016105097', '2016105098', '2016105100', '2016105101', '2016105103', '2016105104', '2016105106', '2016105107', '2016105108', '2016105111', '2016105114', '2016105116', '2016105117', '2016105118', '2016105121', '2016105122', '2016105125', '2016105126', '2016105129', '2016105130', '2016105131', '2016105132', '2016105133', '2016105134', '2016105135', '2016105136', '2016105137', '2016105139', '2016105141', '2016105142', '2016105143', '2016105144', '2016105146', '2016105147', '2016105149', '2016105150', '2016105151', '2016105153', '2016105154', '2016105155', '2016105156', '2016105157', '2016105158', '2016105159', '2016105161', '2016105162', '2016105163', '2016105164', '2016105165', '2016105166', '2016105167', '2016105168', '2016105169', '2016105170', '2016105171', '2016105172', '2016105174', '2016105175', '2016105176', '2016105177', '2016105178', '2016105179', '2016105180', '2016105181', '2016105182', '2016105183', '2016105184', '2016105186', '2016105187', '2016105188', '2016105189', '2016105194', '2016105195', '2016105196', '2016105198', '2016105199', '2016105200', '2016105201', '2016105202', '2016105203', '2016105204', '2016105205', '2016105206', '2016105207', '2016105209', '2016105210', '2016105211', '2016105212', '2016105213', '2016105214', '2016105215', '2016105216', '2016105218', '2016105219', '2016105220', '2016105221', '2016105222', '2016105223', '2016105224', '2016105225', '2016105226', '2016105227', '2016105228', '2016105229', '2016105230', '2016105231', '2016105232', '2016105234', '2016105235', '2016105236', '2016105238', '2016105239', '2016105242', '2016105244', '2016105245', '2016105247', '2016105248', '2016105250', '2016105251', '2016105253', '2016105254', '2016105255', '2016105256', '2016105258', '2016105259', '2016105260', '2016105261', '2016105263', '2016105264', '2016105265', '2016105266', '2016105267', '2016105268', '2016105269', '2016105270', '2016105271', '2016105272', '2016105273', '2016105275', '2016105276', '2016105277', '2016105278', '2016105279', '2016105280', '2016105281', '2016105282', '2016105283', '2016105284', '2016105285', '2016105286', '2016105289', '2016105290', '2016105291', '2016105292', '2016105293', '2016105294', '2016105296', '2016105297', '2016105298', '2016105299', '2016105301', '2016105303', '2016105305', '2016105308', '2016105310', '2016105311', '2016105312', '2016105313', '2016105315', '2016105316', '2016105317', '2016105318', '2016105319', '2016105320', '2016105322', '2016105323', '2016105324', '2016105325', '2016105327', '2016105328', '2016105329', '2016105337', '2016105338', '2016105340', '2016105346', '2016105348', '2016105349', '2016105350', '2016105351', '2016105353', '2016105359', '2016105360', '2016105361', '2016105364', '2016105365', '2016105366', '2016105367', '2016105369', '2016105371', '2016105372', '2016105373', '2016105374', '2016105377', '2016105378', '2016105381', '2016105384', '2016105386', '2016105388', '2016105392', '2016105393', '2016105396', '2016105404', '2016105409', '2016105410', '2016105412', '2016105414', '2016105416', '2016105421', '2016105424', '2016105426', '2016105428', '2016105429', '2016105431', '2016105433', '2016105434', '2016105436', '2016105439', '2016105440', '2016105446', '2016105450', '2016105453', '2016105456', '2016105457', '2016105458', '2016105459', '2016105461', '2016105462', '2016105464', '2016105465', '2016105466', '2016105467', '2016105468', '2016105469', '2016105470', '2016105472', '2016105473', '2016105474', '2016105475', '2016105476', '2016105477', '2016105478', '2016105479', '2016105480', '2016105481', '2016105482', '2016105483', '2016105487', '2016105488', '2016105490', '2016105491', '2016105492', '2016105493', '2016105494', '2016105496', '2016105497', '2016105498', '2016105500', '2016105502', '2016105503', '2016105507', '2016105509', '2016105510', '2016105511', '2016105513', '2016105514', '2016105515', '2016105516', '2016105517', '2016105520', '2016105521', '2016105522', '2016105523', '2016105525', '2016105526', '2016105527', '2016105528', '2016105529', '2016105530', '2016105531', '2016105532', '2016105533', '2016105534', '2016105535', '2016105536', '2016105537', '2016105538', '2016105542', '2016105543', '2016105545', '2016105547', '2016105550', '2016105551', '2016105553', '2016105554', '2016105556', '2016105558', '2016105560', '2016105563', '2016105564', '2016105566', '2016105567', '2016105569', '2016105573', '2016105576', '2016105578', '2016105579', '2016105580', '2016105582', '2016105583', '2016105584', '2016105585', '2016105586', '2016105587', '2016105588', '2016105590', '2016105593', '2016105596', '2016105598', '2016105600', '2016105601', '2016105604', '2016105605', '2016105606', '2016105614', '2016105615', '2016105618', '2016105620', '2016105621', '2016105622', '2016105624', '2016105625', '2016105628', '2016105630', '2016105631', '2016105634', '2016105636', '2016105637', '2016105638', '2016105639', '2016105642', '2016105644', '2016105645', '2016105646', '2016105651', '2016105652', '2016105653', '2016105660', '2016105661', '2016105662', '2016105664', '2016105665', '2016105666', '2016105667', '2016105668', '2016105669', '2016105670', '2016105671', '2016105673', '2016105674', '2016105675', '2016105676', '2016105679', '2016105681', '2016105682', '2016105684', '2016105685', '2016105686', '2016105687', '2016105689', '2016105692', '2016105693', '2016105699', '2016105700', '2016105702', '2016105703', '2016105704', '2016105708', '2016105711', '2016105713', '2016105715', '2016105716', '2016105718', '2016105723', '2016105726', '2016105728', '2016105729', '2016105730', '2016105731', '2016105733', '2016105734', '2016105735', '2016105739', '2016105740', '2016105741', '2016105749', '2016105750', '2016105751', '2016105755', '2016105756', '2016105758', '2016105759', '2016105761', '2016105762', '2016105764', '2016105765', '2016105766', '2016105767', '2016105768', '2016105769', '2016105772', '2016105773', '2016105775', '2016105777', '2016105778', '2016105779', '2016105780', '2016105781', '2016105782', '2016105783', '2016105784', '2016105785', '2016105786', '2016105787', '2016105788', '2016105790', '2016105791', '2016105793', '2016105796', '2016105797', '2016105798', '2016105801', '2016105802', '2016105803', '2016105804', '2016105805', '2016105809', '2016105810', '2016105812', '2016105815', '2016105816', '2016105817', '2016105818', '2016105819', '2016105823', '2016105824', '2016105828', '2016105829', '2016105844', '2016105847', '2016105849', '2016105854', '2016105855', '2016105858', '2016105861', '2016105863', '2016105864', '2016105865', '2016105866', '2016105867', '2016105868', '2016105869', '2016105871', '2016105872', '2016105873', '2016105874', '2016105875', '2016105876', '2016105879', '2016105880', '2016105881', '2016105883', '2016105884', '2016105885', '2016105886', '2016105887', '2016105890', '2016105892', '2016105895', '2016105897', '2016105898', '2016105901', '2016105902', '2016105904', '2016105905', '2016105906', '2016105909', '2016105913', '2016105917', '2016105920', '2016105921', '2016105922', '2016105926', '2016105928', '2016105929', '2016105933', '2016105937', '2016105938', '2016105944', '2016105946', '2016105948', '2016105949', '2016105951', '2016105952', '2016105953', '2016105956', '2016105963', '2016105966', '2016105967', '2016105969', '2016105970', '2016105971', '2016105972', '2016105973', '2016105974', '2016105975', '2016105977', '2016105978', '2016105979', '2016105982', '2016105983', '2016105985', '2016105986', '2016105987', '2016105989', '2016105990', '2016105991', '2016105992', '2016105994', '2016105995', '2016105996', '2016105998', '2016105999', '2016106000', '2016106001', '2016106002', '2016106003', '2016106004', '2016106007', '2016106008', '2016106009', '2016106011', '2016106012', '2016106013', '2016106014', '2016106015', '2016106016', '2016106018', '2016106019', '2016106020', '2016106021', '2016106022', '2016106024', '2016106025', '2016106026', '2016106027', '2016106028', '2016106029', '2016106030', '2016106031', '2016106032', '2016106034', '2016106035', '2016106037', '2016106038', '2016106039', '2016106040', '2016106041', '2016106043', '2016106044', '2016106045', '2016106046', '2016106047', '2016106049', '2016106051', '2016106052', '2016106054', '2016106055', '2016106057', '2016106059', '2016106060', '2016106061', '2016106062', '2016106063', '2016106064', '2016106065', '2016106068', '2016106069', '2016106071', '2016106072', '2016106073', '2016106075', '2016106076', '2016106077', '2016106078', '2016106080', '2016106087', '2016106095', '2016106097', '2016106099', '2016106100', '2016106104', '2016106106', '2016106107', '2016106109', '2016106110', '2016106114', '2016106115', '2016106116', '2016106117', '2016106118', '2016106119', '2016106120', '2016106121', '2016106126', '2016106127', '2016106129', '2016106130', '2016106131', '2016106132', '2016106133', '2016106134', '2016106137', '2016106138', '2016106139', '2016106141', '2016106144', '2016106146', '2016106147', '2016106150', '2016106152', '2016106153', '2016106154', '2016106156', '2016106161', '2016106162', '2016106164', '2016106165', '2016106166', '2016106167', '2016106168', '2016106169', '2016106171', '2016106172', '2016106173', '2016106176', '2016106177', '2016106180', '2016106181', '2016106182', '2016106188', '2016106200', '2016106201', '2016106202', '2016106203', '2016106211', '2016106213', '2016106214', '2016106216', '2016106217', '2016106218', '2016106219', '2016106220', '2016106221', '2016106223', '2016106224', '2016106225', '2016106226', '2016106227', '2016106228', '2016106229', '2016106232', '2016106233', '2016106234', '2016106235', '2016106237', '2016106239', '2016106240', '2016106241', '2016106242', '2016106245', '2016106247', '2016106248', '2016106249', '2016106250', '2016106252', '2016106253', '2016106255', '2016106257', '2016106258', '2016106259', '2016106260', '2016106261', '2016106262', '2016106263', '2016106266', '2016106267', '2016106268', '2016106269', '2016106271', '2016106272', '2016106273', '2016106274', '2016106275', '2016106276', '2016106277', '2016106278', '2016106283', '2016106286', '2016106287', '2016106288', '2016106291', '2016106292', '2016106295', '2016106298', '2016106299', '2016106301', '2016106302', '2016106303', '2016106304', '2016106305', '2016106306', '2016106307', '2016106308', '2016106310', '2016106311', '2016106312', '2016106313', '2016106314', '2016106315', '2016106316', '2016106317', '2016106319', '2016106320', '2016106321', '2016106324', '2016106325', '2016106327', '2016106330', '2016106332', '2016106334', '2016106335', '2016106336', '2016106338', '2016106339', '2016106340', '2016106341', '2016106342', '2016106344', '2016106345', '2016106348', '2016106349', '2016106350', '2016106351', '2016106352', '2016106354', '2016106357', '2016106358', '2016106360', '2016106362', '2016106363', '2016106364', '2016106365', '2016106366', '2016106367', '2016106368', '2016106373', '2016106374', '2016106375', '2016106376', '2016106377', '2016106378', '2016106379', '2016106381', '2016106389', '2016106391', '2016106394', '2016106395', '2016106396', '2016106397', '2016106398', '2016106399', '2016106400', '2016106402', '2016106403', '2016106404', '2016106405', '2016106406', '2016106407', '2016106408', '2016106410', '2016106411', '2016106414', '2016106415', '2016106417', '2016106418', '2016106419', '2016106420', '2016106421', '2016106422', '2016106423', '2016106424', '2016106425', '2016106426', '2016106428', '2016106429', '2016106430', '2016106431', '2016106432', '2016106434', '2016106435', '2016106437', '2016106438', '2016106441', '2016106442', '2016106443', '2016106444', '2016106447', '2016106448', '2016106450', '2016106452', '2016106453', '2016106455', '2016106456', '2016106457', '2016106458', '2016106459', '2016106463', '2016106464', '2016106469', '2016106470', '2016106471', '2016106474', '2016106477', '2016106478', '2016106481', '2016106483', '2016106485', '2016106486', '2016106488', '2016106489', '2016106492', '2016106494', '2016106495', '2016106496', '2016106500', '2016106501', '2016106504', '2016106505', '2016106506', '2016106507', '2016106509', '2016106510', '2016106512', '2016106513', '2016106514', '2016106518', '2016106527', '2016106531', '2016106532', '2016106533', '2016106534', '2016106535', '2016106541', '2016106546', '2016106547', '2016106548', '2016106550', '2016106564', '2016106565', '2016106568', '2016106570', '2016106571', '2016106574', '2016106575', '2016106577', '2016106578', '2016106579', '2016106580', '2016106582', '2016106585', '2016106589', '2016106592', '2016106594', '2016106595', '2016106596', '2016106599', '2016106603', '2016106604', '2016106606', '2016106607', '2016106608', '2016106609', '2016106611', '2016106612', '2016109004', '2016109005', '2016109006', '2016109007', '2016109008', '2016109010', '2016109013', '2016109014', '2016109016', '2016109019', '2016109020', '2016109022', '2016109023', '2016109024', '2016109025', '2016109026', '2016109037', '2016109040', '2016109047', '2016109066', '2016109072', '2016109074', '2016109075', '2016109076', '2016109083', '2016109087', '2016109093', '2016109095', '2016109096', '2016109097', '2016109099', '2016109102', '2016109104', '2016109107', '2016109108', '2016109109', '2016109110', '2016109115', '2016109117', '2016109118', '2016109123', '2016109125', '2016109126', '2016109127', '2016109128', '2016109136', '2016109137', '2016109141', '2016109148', '2016109149', '2016109151', '2016110001', '2016110002', '2016110003', '2016110004', '2016110005', '2016110006', '2016110007', '2016110008', '2016110009', '2016110010', '2016110011', '2016110012', '2016110013', '2016110015', '2016110016', '2016110017', '2016110018', '2016110020', '2016110021', '2016110022', '2016110023', '2016110024', '2016110025', '2016110026', '2016110028', '2016110029', '2016110030', '2016110031', '2016110032', '2016110033', '2016110034', '2016110035', '2016110036', '2016110037', '2016110038', '2016110039', '2016110040', '2016110041', '2016110043', '2016110044', '2016110045', '2016110046', '2016110047', '2016110048', '2016110049', '2016110050', '2016110051', '2016110052', '2016110054', '2016110055', '2016110056', '2016110057', '2016110058', '2016110059', '2016110061', '2016110062', '2016110063', '2016110064', '2016110065', '2016110066', '2016110067', '2016110068', '2016110069', '2016110070', '2016110071', '2016110072', '2016110073', '2016110074', '2016110075', '2016110076', '2016110077', '2016110078', '2016110079', '2016110080', '2016110083', '2016110084', '2016110085', '2016110086', '2016110087', '2016110088', '2016110089', '2016110090', '2016110091', '2016110092', '2016110093', '2016110094', '2016110095', '2016110096', '2016110097', '2016110098', '2016110100', '2016110101', '2016110102', '2016110103', '2016110106', '2016110107', '2016110109', '2016110110', '2016110111', '2016110112', '2016110113', '2016110114', '2016110115', '2016110116', '2016110117', '2016110118', '2016110119', '2016110120', '2016110121', '2016110122', '2016110124', '2016110125', '2016110126', '2016110127', '2016110128', '2016110129', '2016110130', '2016110131', '2016110132', '2016110133', '2016110134', '2016110135', '2016110136', '2016110138', '2016110139', '2016110140', '2016110141', '2016110142', '2016110143', '2016110145', '2016110146', '2016110147', '2016110148', '2016110149', '2016110150', '2016110151', '2016110152', '2016110153', '2016110154', '2016110155', '2016110156', '2016110157', '2016110158', '2016110159', '2016110160', '2016110161', '2016110162', '2016110163', '2016110164', '2016110165', '2017100005', '2017100006', '2017100008', '2017100010', '2017100019', '2017100020', '2017100024', '2017100025', '2017100026', '2017100029', '2017100033', '2017100035', '2017100036', '2017100037', '2017100039', '2017100040', '2017100041', '2017100043', '2017100044', '2017100048', '2017100050', '2017100056', '2017100057', '2017100059', '2017100060', '2017100066', '2017100067', '2017100072', '2017100078', '2017100081', '2017100082', '2017100083', '2017100084', '2017100085', '2017100086', '2017100087', '2017100088', '2017100090', '2017100095', '2017100096', '2017100097', '2017100098', '2017100101', '2017100104', '2017100105', '2017100106', '2017100108', '2017100112', '2017100115', '2017100116', '2017100120', '2017100126', '2017100127', '2017100128', '2017100130', '2017100131', '2017100132', '2017100136', '2017100141', '2017100146', '2017100149', '2017100151', '2017100154', '2017100155', '2017100159', '2017100160', '2017100161', '2017100163', '2017100164', '2017100165', '2017100166', '2017100167', '2017100168', '2017100169', '2017100172', '2017100176', '2017100182', '2017100184', '2017100186', '2017100189', '2017100190', '2017100191', '2017100194', '2017100195', '2017100197', '2017100199', '2017100200', '2017100201', '2017100203', '2017100205', '2017100206', '2017100207', '2017100211', '2017100212', '2017100217', '2017100218', '2017100222', '2017100224', '2017100225', '2017100228', '2017100230', '2017100231', '2017100232', '2017100236', '2017100240', '2017100242', '2017100244', '2017100247', '2017100251', '2017100252', '2017100257', '2017100262', '2017100265', '2017100275', '2017100277', '2017100278', '2017100279', '2017100281', '2017100284', '2017100290', '2017100291', '2017100292', '2017100297', '2017100299', '2017100300', '2017100305', '2017100306', '2017100315', '2017100316', '2017100320', '2017100323', '2017100324', '2017100327', '2017100337', '2017100339', '2017100340', '2017100342', '2017100347', '2017100348', '2017100349', '2017100353', '2017100354', '2017100355', '2017100357', '2017100358', '2017100364', '2017100366', '2017100369', '2017100371', '2017100372', '2017100373', '2017100376', '2017100377', '2017100378', '2017100382', '2017100394', '2017100396', '2017100399', '2017100402', '2017100403', '2017100404', '2017100406', '2017100413', '2017100414', '2017100415', '2017100418', '2017100422', '2017100423', '2017100426', '2017100428', '2017100431', '2017100433', '2017100436', '2017100437', '2017100439', '2017100440', '2017100445', '2017100448', '2017100451', '2017100453', '2017100455', '2017100458', '2017100461', '2017100463', '2017100466', '2017100469', '2017100474', '2017100475', '2017100476', '2017100477', '2017100492', '2017100495', '2017100508', '2017100511', '2017100514', '2017100519', '2017100521', '2017100522', '2017100524', '2017100525', '2017100527', '2017100529', '2017100531', '2017100532', '2017100533', '2017100535', '2017100537', '2017100538', '2017100539', '2017100540', '2017100541', '2017100542', '2017100543', '2017100545', '2017100550', '2017100551', '2017100552', '2017100553', '2017100554', '2017100555', '2017100556', '2017100557', '2017100558', '2017100559', '2017100560', '2017100564', '2017100567', '2017100581', '2017100583', '2017100585', '2017100588', '2017100590', '2017100592', '2017100594', '2017100598', '2017100599', '2017100600', '2017100601', '2017100602', '2017100604', '2017100605', '2017100607', '2017100609', '2017100610', '2017100611', '2017100613', '2017100617', '2017100620', '2017100622', '2017100623', '2017100624', '2017100626', '2017100627', '2017100630', '2017100633', '2017100641', '2017100643', '2017100650', '2017100654', '2017100656', '2017100659', '2017100661', '2017100662', '2017100665', '2017100666', '2017100667', '2017100672', '2017100673', '2017100675', '2017100680', '2017100682', '2017100684', '2017100697', '2017100699', '2017100702', '2017100712', '2017100716', '2017100717', '2017100720', '2017100721', '2017100722', '2017100723', '2017100726', '2017100727', '2017100728', '2017100729', '2017100730', '2017100742', '2017100746', '2017100747', '2017100748', '2017100751', '2017100752', '2017100755', '2017100757', '2017100760', '2017100761', '2017100763', '2017100764', '2017100765', '2017100766', '2017100768', '2017100772', '2017100776', '2017100777', '2017100779', '2017100784', '2017100787', '2017100794', '2017100795', '2017100796', '2017100800', '2017100801', '2017100804', '2017100809', '2017100811', '2017100813', '2017100817', '2017100821', '2017100822', '2017100823', '2017100825', '2017100827', '2017100829', '2017100830', '2017100834', '2017100835', '2017100836', '2017100838', '2017100839', '2017100845', '2017100846', '2017100847', '2017100850', '2017100854', '2017100855', '2017100858', '2017100859', '2017100861', '2017100862', '2017100870', '2017100873', '2017100874', '2017100875', '2017100877', '2017100878', '2017100882', '2017100887', '2017100889', '2017100894', '2017100895', '2017100900', '2017100913', '2017100914', '2017100915', '2017100916', '2017100917', '2017100918', '2017100919', '2017100920', '2017100922', '2017100924', '2017100925', '2017100926', '2017100931', '2017100936', '2017100938', '2017100940', '2017100943', '2017100958', '2017100959', '2017100960', '2017100963', '2017100965', '2017100967', '2017100970', '2017100973', '2017100978', '2017100985', '2017100987', '2017100988', '2017100990', '2017100992', '2017100993', '2017100996', '2017100997', '2017101003', '2017101008', '2017101010', '2017101011', '2017101015', '2017101016', '2017101017', '2017101020', '2017101028', '2017101030', '2017101032', '2017101034', '2017101041', '2017101051', '2017101057', '2017101058', '2017101059', '2017101068', '2017101071', '2017101073', '2017101077', '2017101078', '2017101080', '2017101082', '2017101085', '2017101089', '2017101091', '2017101096', '2017101103', '2017101104', '2017101105', '2017101106', '2017101107', '2017101108', '2017101109', '2017101110', '2017101111', '2017101112', '2017101113', '2017101116', '2017101117', '2017101121', '2017101124', '2017101128', '2017101131', '2017101132', '2017101135', '2017101136', '2017101138', '2017101146', '2017101148', '2017101149', '2017101150', '2017101151', '2017101152', '2017101155', '2017101157', '2017101158', '2017101159', '2017101160', '2017101161', '2017101162', '2017101167', '2017101168', '2017101171', '2017101173', '2017101174', '2017101177', '2017101179', '2017101180', '2017101181', '2017101183', '2017101186', '2017101192', '2017101193', '2017101195', '2017101197', '2017101200', '2017101201', '2017101210', '2017101212', '2017101213', '2017101214', '2017101215', '2017101223', '2017101224', '2017101226', '2017101227', '2017101231', '2017101238', '2017101239', '2017101242', '2017101243', '2017101244', '2017101246', '2017101248', '2017101253', '2017101255', '2017101256', '2017101262', '2017101263', '2017101272', '2017101274', '2017101276', '2017101288', '2017101289', '2017101291', '2017101292', '2017101293', '2017101295', '2017101298', '2017101299', '2017101303', '2017101310', '2017101311', '2017101318', '2017101321', '2017101322', '2017101323', '2017101334', '2017101342', '2017101350', '2017101351', '2017101357', '2017101362', '2017101365', '2017101367', '2017101368', '2017101369', '2017101370', '2017101373', '2017101375', '2017101376', '2017101377', '2017101379', '2017101380', '2017101381', '2017101383', '2017101385', '2017101386', '2017101389', '2017101390', '2017101392', '2017101396', '2017101398', '2017101399', '2017101401', '2017101403', '2017101404', '2017101409', '2017101411', '2017101412', '2017101415', '2017101417', '2017101419', '2017101420', '2017101423', '2017101424', '2017101426', '2017101427', '2017101428', '2017101429', '2017101430', '2017101431', '2017101433', '2017101438', '2017101439', '2017101442', '2017101444', '2017101445', '2017101446', '2017101448', '2017101449', '2017101451', '2017101452', '2017101457', '2017101459', '2017101461', '2017101462', '2017101464', '2017101466', '2017101473', '2017101474', '2017101475', '2017101476', '2017101478', '2017101482', '2017101485', '2017101489', '2017101491', '2017101503', '2017101506', '2017101509', '2017101510', '2017101516', '2017101519', '2017101523', '2017101524', '2017101525', '2017101528', '2017101530', '2017101535', '2017101537', '2017101538', '2017101539', '2017101540', '2017101546', '2017101547', '2017101548', '2017101552', '2017101553', '2017101554', '2017101555', '2017101556', '2017101558', '2017101561', '2017101562', '2017101563', '2017101564', '2017101566', '2017101568', '2017101571', '2017101572', '2017101579', '2017101580', '2017101581', '2017101582', '2017101584', '2017101585', '2017101590', '2017101593', '2017101594', '2017101596', '2017101597', '2017101602', '2017101603', '2017101605', '2017101607', '2017101610', '2017101612', '2017101613', '2017101617', '2017101624', '2017101625', '2017101626', '2017101629', '2017101637', '2017101639', '2017101641', '2017101646', '2017101649', '2017101658', '2017101667', '2017101669', '2017101674', '2017101675', '2017101677', '2017101679', '2017101680', '2017101681', '2017101682', '2017101685', '2017101686', '2017101691', '2017101693', '2017101697', '2017101701', '2017101704', '2017101706', '2017101707', '2017101708', '2017101713', '2017101714', '2017101715', '2017101718', '2017101719', '2017101720', '2017101721', '2017101724', '2017101725', '2017101726', '2017101731', '2017101733', '2017101738', '2017101739', '2017101742', '2017101743', '2017101746', '2017101747', '2017101748', '2017101749', '2017101750', '2017101751', '2017101752', '2017101753', '2017101754', '2017101755', '2017101756', '2017101757', '2017101758', '2017101761', '2017101762', '2017101764', '2017101765', '2017101768', '2017101771', '2017101777', '2017101783', '2017101785', '2017101787', '2017101788', '2017101789', '2017101790', '2017101791', '2017101794', '2017101795', '2017101796', '2017101797', '2017101798', '2017101799', '2017101804', '2017101805', '2017101807', '2017101809', '2017101813', '2017101815', '2017101816', '2017101821', '2017101822', '2017101823', '2017101824', '2017101825', '2017101827', '2017101828', '2017101829', '2017101830', '2017101831', '2017101832', '2017101833', '2017101835', '2017101839', '2017101842', '2017101844', '2017101845', '2017101846', '2017101847', '2017101848', '2017101849', '2017101850', '2017101853', '2017101857', '2017101858', '2017101859', '2017101860', '2017101861', '2017101863', '2017101866', '2017101867', '2017101869', '2017101871', '2017101875', '2017101877', '2017101881', '2017101882', '2017101883', '2017101884', '2017101885', '2017101889', '2017101890', '2017101894', '2017101895', '2017101896', '2017101897', '2017101898', '2017101899', '2017101900', '2017101901', '2017101902', '2017101903', '2017101904', '2017101905', '2017101906', '2017101907', '2017101908', '2017101909', '2017101911', '2017101913', '2017101914', '2017101915', '2017101916', '2017101918', '2017101919', '2017101920', '2017101921', '2017101922', '2017101923', '2017101924', '2017101926', '2017101927', '2017101928', '2017101929', '2017101930', '2017101931', '2017101932', '2017101933', '2017101935', '2017101936', '2017101937', '2017101940', '2017101941', '2017101942', '2017101945', '2017101946', '2017101947', '2017101948', '2017101949', '2017101951', '2017101952', '2017101954', '2017101955', '2017101956', '2017101957', '2017101959', '2017101960', '2017101962', '2017101963', '2017101964', '2017101965', '2017101966', '2017101967', '2017101968', '2017101969', '2017101972', '2017101973', '2017101974', '2017101976', '2017101977', '2017101978', '2017101979', '2017101980', '2017101982', '2017101983', '2017101984', '2017101985', '2017101987', '2017101988', '2017101990', '2017101991', '2017101992', '2017101993', '2017101994', '2017101997', '2017101999', '2017102002', '2017102003', '2017102007', '2017102009', '2017102010', '2017102012', '2017102014', '2017102015', '2017102016', '2017102017', '2017102021', '2017102022', '2017102027', '2017102030', '2017102031', '2017102033', '2017102034', '2017102037', '2017102038', '2017102039', '2017102040', '2017102042', '2017102043', '2017102045', '2017102046', '2017102047', '2017102050', '2017102052', '2017102053', '2017102055', '2017102060', '2017102061', '2017102066', '2017102067', '2017102073', '2017102076', '2017102077', '2017102078', '2017102079', '2017102081', '2017102082', '2017102083', '2017102084', '2017102086', '2017102088', '2017102089', '2017102090', '2017102091', '2017102092', '2017102093', '2017102095', '2017102096', '2017102097', '2017102100', '2017102101', '2017102102', '2017102103', '2017102104', '2017102105', '2017102107', '2017102108', '2017102110', '2017102112', '2017102116', '2017102118', '2017102119', '2017102120', '2017102126', '2017102129', '2017102130', '2017102131', '2017102132', '2017102134', '2017102135', '2017102137', '2017102141', '2017102142', '2017102143', '2017102145', '2017102146', '2017102154', '2017102157', '2017102158', '2017102160', '2017102163', '2017102164', '2017102165', '2017102168', '2017102170', '2017102171', '2017102175', '2017102177', '2017102178', '2017102179', '2017102184', '2017102186', '2017102190', '2017102192', '2017102194', '2017102195', '2017102196', '2017102199', '2017102200', '2017102201', '2017102202', '2017102204', '2017102205', '2017102207', '2017102210', '2017102211', '2017102212', '2017102218', '2017102221', '2017102232', '2017102234', '2017102236', '2017102237', '2017102244', '2017102247', '2017102251', '2017102253', '2017102254', '2017102256', '2017102257', '2017102262', '2017102263', '2017102264', '2017102268', '2017102281', '2017102287', '2017102289', '2017102290', '2017102293', '2017102294', '2017102296', '2017102297', '2017102304', '2017102306', '2017102307', '2017102309', '2017102312', '2017102314', '2017102315', '2017102317', '2017102323', '2017102336', '2017102342', '2017102343', '2017102344', '2017102345', '2017102350', '2017102353', '2017102356', '2017102357', '2017102358', '2017102360', '2017102361', '2017102362', '2017102364', '2017102371', '2017102372', '2017102374', '2017102375', '2017102376', '2017102378', '2017102379', '2017102380', '2017102381', '2017102385', '2017102386', '2017102387', '2017102389', '2017102391', '2017102392', '2017102393', '2017102395', '2017102396', '2017102397', '2017102398', '2017102400', '2017102401', '2017102402', '2017102403', '2017102405', '2017102406', '2017102408', '2017102412', '2017102418', '2017102421', '2017102424', '2017102426', '2017102428', '2017102429', '2017102430', '2017102431', '2017102432', '2017102435', '2017102436', '2017102443', '2017102444', '2017102447', '2017102450', '2017102454', '2017102455', '2017102457', '2017102460', '2017102461', '2017102465', '2017102466', '2017102469', '2017102473', '2017102482', '2017102484', '2017102488', '2017102489', '2017102490', '2017102491', '2017102493', '2017102497', '2017102498', '2017102500', '2017102509', '2017102510', '2017102511', '2017102514', '2017102516', '2017102517', '2017102518', '2017102519', '2017102523', '2017102525', '2017102528', '2017102530', '2017102533', '2017102536', '2017102543', '2017102544', '2017102545', '2017102550', '2017102554', '2017102557', '2017102558', '2017102560', '2017102565', '2017102567', '2017102570', '2017102575', '2017102577', '2017102578', '2017102579', '2017102580', '2017102581', '2017102586', '2017102588', '2017102595', '2017102597', '2017102599', '2017102603', '2017102604', '2017102606', '2017102607', '2017102610', '2017102611', '2017102612', '2017102613', '2017102618', '2017102620', '2017102621', '2017102622', '2017102624', '2017102625', '2017102627', '2017102628', '2017102630', '2017102632', '2017102633', '2017102634', '2017102637', '2017102638', '2017102639', '2017102641', '2017102645', '2017102646', '2017102648', '2017102649', '2017102652', '2017102659', '2017102663', '2017102665', '2017102666', '2017102669', '2017102671', '2017102673', '2017102675', '2017102678', '2017102686', '2017102687', '2017102688', '2017102689', '2017102690', '2017102692', '2017102693', '2017102694', '2017102695', '2017102697', '2017102699', '2017102701', '2017102703', '2017102704', '2017102705', '2017102707', '2017102708', '2017102709', '2017102714', '2017102716', '2017102719', '2017102722', '2017102723', '2017102726', '2017102729', '2017102730', '2017102732', '2017102744', '2017102748', '2017102749', '2017102752', '2017102765', '2017102767', '2017102768', '2017102771', '2017102772', '2017102775', '2017102776', '2017102784', '2017102786', '2017102788', '2017102789', '2017102790', '2017102791', '2017102792', '2017102797', '2017102800', '2017102801', '2017102802', '2017102803', '2017102804', '2017102805', '2017102806', '2017102807', '2017102809', '2017102810', '2017102811', '2017102813', '2017102815', '2017102817', '2017102819', '2017102822', '2017102823', '2017102832', '2017102833', '2017102834', '2017102835', '2017102836', '2017102837', '2017102838', '2017102840', '2017102841', '2017102844', '2017102848', '2017102850', '2017102852', '2017102854', '2017102856', '2017102858', '2017102860', '2017102868', '2017102871', '2017102872', '2017102873', '2017102874', '2017102875', '2017102876', '2017102878', '2017102879', '2017102880', '2017102881', '2017102883', '2017102884', '2017102885', '2017102886', '2017102887', '2017102889', '2017102892', '2017102893', '2017102899', '2017102900', '2017102901', '2017102902', '2017102903', '2017102907', '2017102909', '2017102911', '2017102913', '2017102918', '2017102920', '2017102921', '2017102923', '2017102925', '2017102926', '2017102930', '2017102932', '2017102933', '2017102934', '2017102935', '2017102936', '2017102937', '2017102939', '2017102940', '2017102941', '2017102942', '2017102945', '2017102946', '2017102949', '2017102951', '2017102952', '2017102953', '2017102954', '2017102955', '2017102959', '2017102960', '2017102962', '2017102968', '2017102971', '2017102975', '2017102977', '2017102978', '2017102981', '2017102984', '2017102985', '2017102989', '2017102990', '2017102992', '2017103000', '2017103001', '2017103002', '2017103003', '2017103005', '2017103007', '2017103008', '2017103009', '2017103011', '2017103017', '2017103018', '2017103020', '2017103023', '2017103025', '2017103030', '2017103035', '2017103037', '2017103040', '2017103042', '2017103043', '2017103044', '2017103046', '2017103051', '2017103054', '2017103055', '2017103056', '2017103058', '2017103059', '2017103060', '2017103062', '2017103064', '2017103066', '2017103067', '2017103072', '2017103079', '2017103084', '2017103091', '2017103092', '2017103093', '2017103094', '2017103097', '2017103102', '2017103104', '2017103105', '2017103110', '2017103111', '2017103112', '2017103113', '2017103115', '2017103117', '2017103119', '2017103123', '2017103129', '2017103133', '2017103134', '2017103137', '2017103142', '2017103143', '2017103144', '2017103146', '2017103147', '2017103149', '2017103150', '2017103152', '2017103153', '2017103154', '2017103155', '2017103156', '2017103157', '2017103159', '2017103160', '2017103163', '2017103164', '2017103165', '2017103166', '2017103167', '2017103174', '2017103175', '2017103177', '2017103181', '2017103183', '2017103185', '2017103186', '2017103189', '2017103191', '2017103193', '2017103194', '2017103195', '2017103197', '2017103198', '2017103199', '2017103201', '2017103202', '2017103204', '2017103205', '2017103210', '2017103211', '2017103213', '2017103214', '2017103218', '2017103221', '2017103223', '2017103224', '2017103233', '2017103235', '2017103236', '2017103238', '2017103239', '2017103240', '2017103242', '2017103253', '2017103255', '2017103257', '2017103258', '2017103260', '2017103261', '2017103263', '2017103272', '2017103274', '2017103276', '2017103277', '2017103278', '2017103279', '2017103280', '2017103281', '2017103283', '2017103286', '2017103287', '2017103288', '2017103290', '2017103291', '2017103292', '2017103293', '2017103294', '2017103295', '2017103298', '2017103299', '2017103302', '2017103304', '2017103305', '2017103306', '2017103307', '2017103310', '2017103312', '2017103314', '2017103315', '2017103319', '2017103320', '2017103321', '2017103322', '2017103323', '2017103325', '2017103328', '2017103331', '2017103333', '2017103334', '2017103336', '2017103337', '2017103339', '2017103340', '2017103341', '2017103343', '2017103345', '2017103350', '2017103351', '2017103357', '2017103358', '2017103359', '2017103361', '2017103362', '2017103363', '2017103364', '2017103366', '2017103368', '2017103370', '2017103373', '2017103376', '2017103378', '2017103381', '2017103382', '2017103383', '2017103385', '2017103387', '2017103391', '2017103394', '2017103395', '2017103398', '2017103401', '2017103402', '2017103403', '2017103404', '2017103422', '2017103423', '2017103425', '2017103429', '2017103430', '2017103432', '2017103436', '2017103437', '2017103438', '2017103439', '2017103442', '2017103448', '2017103450', '2017103454', '2017103456', '2017103461', '2017103464', '2017103467', '2017103472', '2017103473', '2017103476', '2017103482', '2017103489', '2017103491', '2017103493', '2017103498', '2017103500', '2017103501', '2017103504', '2017103505', '2017103509', '2017103510', '2017103512', '2017103513', '2017103518', '2017103524', '2017103525', '2017103526', '2017103527', '2017103529', '2017103530', '2017103538', '2017103539', '2017103548', '2017103549', '2017103554', '2017103556', '2017103558', '2017103561', '2017103564', '2017103569', '2017103570', '2017103572', '2017103583', '2017103588', '2017103590', '2017103595', '2017103603', '2017103604', '2017103605', '2017103606', '2017103607', '2017103609', '2017103613', '2017103615', '2017103617', '2017103618', '2017103619', '2017103621', '2017103622', '2017103624', '2017103632', '2017103641', '2017103647', '2017103650', '2017103653', '2017103655', '2017103656', '2017103658', '2017103660', '2017103661', '2017103663', '2017103664', '2017103665', '2017103666', '2017103667', '2017103669', '2017103670', '2017103672', '2017103673', '2017103674', '2017103677', '2017103679', '2017103680', '2017103681', '2017103683', '2017103685', '2017103688', '2017103693', '2017103694', '2017103695', '2017103697', '2017103702', '2017103703', '2017103704', '2017103708', '2017103709', '2017103710', '2017103712', '2017103714', '2017103715', '2017103718', '2017103722', '2017103723', '2017103725', '2017103733', '2017103734', '2017103736', '2017103741', '2017103748', '2017103751', '2017103755', '2017103756', '2017103759', '2017103760', '2017103761', '2017103763', '2017103764', '2017103765', '2017103766', '2017103769', '2017103772', '2017103773', '2017103774', '2017103775', '2017103776', '2017103777', '2017103779', '2017103780', '2017103781', '2017103784', '2017103785', '2017103787', '2017103789', '2017103790', '2017103791', '2017103792', '2017103793', '2017103794', '2017103798', '2017103800', '2017103802', '2017103803', '2017103804', '2017103805', '2017103807', '2017103808', '2017103810', '2017103814', '2017103818', '2017103819', '2017103823', '2017103824', '2017103825', '2017103826', '2017103827', '2017103829', '2017103831', '2017103832', '2017103833', '2017103835', '2017103839', '2017103841', '2017103842', '2017103843', '2017103844', '2017103850', '2017103852', '2017103854', '2017103855', '2017103856', '2017103857', '2017103858', '2017103859', '2017103860', '2017103863', '2017103864', '2017103868', '2017103871', '2017103872', '2017103874', '2017103875', '2017103876', '2017103878', '2017103879', '2017103881', '2017103882', '2017103883', '2017103884', '2017103885', '2017103886', '2017103888', '2017103890', '2017103893', '2017103894', '2017103895', '2017103901', '2017103903', '2017103910', '2017103912', '2017103915', '2017103917', '2017103919', '2017103920', '2017103927', '2017103933', '2017103935', '2017103936', '2017103937', '2017103938', '2017103939', '2017103942', '2017103947', '2017103949', '2017103950', '2017103952', '2017103955', '2017103957', '2017103961', '2017103963', '2017103966', '2017103971', '2017103974', '2017103978', '2017103980', '2017103981', '2017103982', '2017103984', '2017103986', '2017103987', '2017103988', '2017103989', '2017103990', '2017103991', '2017103992', '2017103996', '2017103997', '2017103998', '2017104000', '2017104004', '2017104006', '2017104007', '2017104010', '2017104012', '2017104013', '2017104018', '2017104026', '2017104033', '2017104039', '2017104041', '2017104042', '2017104044', '2017104047', '2017104050', '2017104054', '2017104055', '2017104057', '2017104058', '2017104059', '2017104061', '2017104062', '2017104065', '2017104067', '2017104068', '2017104069', '2017104070', '2017104071', '2017104074', '2017104077', '2017104080', '2017104085', '2017104086', '2017104087', '2017104089', '2017104091', '2017104093', '2017104095', '2017104096', '2017104097', '2017104098', '2017104099', '2017104101', '2017104102', '2017104104', '2017104105', '2017104106', '2017104113', '2017104117', '2017104119', '2017104120', '2017104121', '2017104122', '2017104125', '2017104128', '2017104129', '2017104131', '2017104132', '2017104133', '2017104136', '2017104137', '2017104138', '2017104139', '2017104140', '2017104141', '2017104142', '2017104144', '2017104147', '2017104148', '2017104151', '2017104153', '2017104155', '2017104157', '2017104160', '2017104163', '2017104164', '2017104165', '2017104166', '2017104167', '2017104168', '2017104169', '2017104170', '2017104172', '2017104174', '2017104175', '2017104176', '2017104177', '2017104178', '2017104181', '2017104185', '2017104186', '2017104189', '2017104190', '2017104191', '2017104192', '2017104193', '2017104194', '2017104195', '2017104198', '2017104201', '2017104204', '2017104206', '2017104207', '2017104209', '2017104211', '2017104215', '2017104218', '2017104219', '2017104220', '2017104221', '2017104224', '2017104225', '2017104227', '2017104232', '2017104233', '2017104234', '2017104236', '2017104238', '2017104239', '2017104242', '2017104243', '2017104245', '2017104246', '2017104252', '2017104253', '2017104254', '2017104255', '2017104256', '2017104257', '2017104258', '2017104259', '2017104260', '2017104261', '2017104262', '2017104263', '2017104264', '2017104265', '2017104266', '2017104267', '2017104268', '2017104270', '2017104273', '2017104279', '2017104280', '2017104281', '2017104282', '2017104284', '2017104287', '2017104288', '2017104289', '2017104292', '2017104294', '2017104296', '2017104298', '2017104300', '2017104302', '2017104303', '2017104305', '2017104309', '2017104314', '2017104315', '2017104317', '2017104326', '2017104329', '2017104330', '2017104331', '2017104336', '2017104342', '2017104344', '2017104345', '2017104349', '2017104350', '2017104359', '2017104368', '2017104369', '2017104371', '2017104372', '2017104373', '2017104375', '2017104376', '2017104377', '2017104379', '2017104381', '2017104382', '2017104383', '2017104384', '2017104385', '2017104386', '2017104388', '2017104392', '2017104397', '2017104398', '2017104400', '2017104401', '2017104402', '2017104404', '2017104405', '2017104406', '2017104407', '2017104408', '2017104410', '2017104411', '2017104413', '2017104415', '2017104417', '2017104419', '2017104420', '2017104421', '2017104422', '2017104423', '2017104425', '2017104426', '2017104428', '2017104430', '2017104431', '2017104433', '2017104434', '2017104435', '2017104437', '2017104440', '2017104441', '2017104442', '2017104443', '2017104444', '2017104445', '2017104446', '2017104450', '2017104453', '2017104454', '2017104455', '2017104456', '2017104461', '2017104469', '2017104470', '2017104472', '2017104473', '2017104475', '2017104478', '2017104480', '2017104481', '2017104483', '2017104484', '2017104487', '2017104488', '2017104495', '2017104497', '2017104502', '2017104507', '2017104512', '2017104513', '2017104526', '2017104527', '2017104528', '2017104532', '2017104535', '2017104539', '2017104541', '2017104544', '2017104545', '2017104546', '2017104548', '2017104549', '2017104550', '2017104551', '2017104553', '2017104558', '2017104559', '2017104563', '2017104564', '2017104565', '2017104568', '2017104570', '2017104572', '2017104573', '2017104574', '2017104575', '2017104578', '2017104581', '2017104585', '2017104586', '2017104589', '2017104591', '2017104592', '2017104595', '2017104598', '2017104599', '2017104601', '2017104604', '2017104605', '2017104608', '2017104609', '2017104610', '2017104611', '2017104612', '2017104614', '2017104615', '2017104616', '2017104619', '2017104620', '2017104621', '2017104622', '2017104623', '2017104625', '2017104626', '2017104627', '2017104629', '2017104630', '2017104631', '2017104632', '2017104634', '2017104635', '2017104636', '2017104640', '2017104641', '2017104642', '2017104644', '2017104645', '2017104646', '2017104649', '2017104652', '2017104653', '2017104656', '2017104659', '2017104662', '2017104665', '2017104667', '2017104670', '2017104671', '2017104673', '2017104675', '2017104677', '2017104678', '2017104679', '2017104680', '2017104683', '2017104684', '2017104685', '2017104686', '2017104687', '2017104688', '2017104690', '2017104691', '2017104694', '2017104697', '2017104698', '2017104699', '2017104700', '2017104702', '2017104703', '2017104704', '2017104705', '2017104706', '2017104707', '2017104709', '2017104710', '2017104711', '2017104712', '2017104713', '2017104714', '2017104715', '2017104716', '2017104717', '2017104718', '2017104719', '2017104721', '2017104723', '2017104725', '2017104726', '2017104727', '2017104729', '2017104731', '2017104732', '2017104733', '2017104734', '2017104735', '2017104739', '2017104740', '2017104743', '2017104744', '2017104746', '2017104747', '2017104748', '2017104750', '2017104753', '2017104755', '2017104756', '2017104760', '2017104764', '2017104766', '2017104767', '2017104770', '2017104771', '2017104774', '2017104775', '2017104776', '2017104778', '2017104780', '2017104781', '2017104782', '2017104785', '2017104787', '2017104789', '2017104791', '2017104793', '2017104795', '2017104796', '2017104797', '2017104798', '2017104799', '2017104800', '2017104801', '2017104803', '2017104804', '2017104806', '2017104808', '2017104809', '2017104810', '2017104812', '2017104814', '2017104815', '2017104818', '2017104819', '2017104820', '2017104823', '2017104824', '2017104825', '2017104829', '2017104831', '2017104833', '2017104836', '2017104841', '2017104844', '2017104846', '2017104847', '2017104849', '2017104850', '2017104851', '2017104859', '2017104863', '2017104864', '2017104865', '2017104867', '2017104869', '2017104873', '2017104879', '2017104881', '2017104882', '2017104884', '2017104885', '2017104888', '2017104889', '2017104890', '2017104891', '2017104895', '2017104898', '2017104899', '2017104900', '2017104902', '2017104903', '2017104905', '2017104906', '2017104907', '2017104908', '2017104909', '2017104911', '2017104912', '2017104914', '2017104917', '2017104924', '2017104925', '2017104926', '2017104930', '2017104934', '2017104935', '2017104936', '2017104937', '2017104938', '2017104939', '2017104940', '2017104941', '2017104944', '2017104945', '2017104946', '2017104947', '2017104948', '2017104949', '2017104950', '2017104953', '2017104954', '2017104956', '2017104959', '2017104962', '2017104963', '2017104964', '2017104966', '2017104967', '2017104970', '2017104971', '2017104973', '2017104974', '2017104975', '2017104976', '2017104977', '2017104979', '2017104981', '2017104982', '2017104985', '2017104986', '2017104987', '2017104988', '2017104989', '2017104991', '2017104993', '2017104996', '2017104997', '2017104998', '2017104999', '2017105000', '2017105001', '2017105003', '2017105004', '2017105005', '2017105011', '2017105014', '2017105017', '2017105018', '2017105019', '2017105020', '2017105021', '2017105022', '2017105023', '2017105024', '2017105025', '2017105026', '2017105027', '2017105028', '2017105029', '2017105031', '2017105033', '2017105036', '2017105037', '2017105038', '2017105039', '2017105040', '2017105041', '2017105043', '2017105044', '2017105045', '2017105047', '2017105048', '2017105050', '2017105051', '2017105054', '2017105055', '2017105057', '2017105059', '2017105060', '2017105067', '2017105072', '2017105073', '2017105074', '2017105075', '2017105076', '2017105077', '2017105078', '2017105079', '2017105080', '2017105081', '2017105083', '2017105084', '2017105086', '2017105087', '2017105088', '2017105089', '2017105090', '2017105091', '2017105092', '2017105095', '2017105096', '2017105097', '2017105100', '2017105101', '2017105102', '2017105103', '2017105105', '2017105106', '2017105107', '2017105108', '2017105109', '2017105111', '2017105114', '2017105115', '2017105116', '2017105117', '2017105118', '2017105119', '2017105123', '2017105125', '2017105126', '2017105127', '2017105132', '2017105133', '2017105135', '2017105136', '2017105137', '2017105140', '2017105141', '2017105142', '2017105143', '2017105144', '2017105150', '2017105151', '2017105152', '2017105153', '2017105155', '2017105157', '2017105160', '2017105161', '2017105162', '2017105163', '2017105164', '2017105165', '2017105170', '2017105172', '2017105173', '2017105174', '2017105177', '2017105180', '2017105181', '2017105182', '2017105183', '2017105186', '2017105187', '2017105188', '2017105190', '2017105192', '2017105193', '2017105194', '2017105196', '2017105198', '2017105201', '2017105202', '2017105204', '2017105206', '2017105207', '2017105208', '2017105210', '2017105211', '2017105215', '2017105216', '2017105219', '2017105224', '2017105226', '2017105228', '2017105229', '2017105231', '2017105232', '2017105233', '2017105234', '2017105236', '2017105238', '2017105240', '2017105241', '2017105246', '2017105248', '2017105249', '2017105250', '2017105252', '2017105253', '2017105255', '2017105257', '2017105259', '2017105260', '2017105261', '2017105262', '2017105263', '2017105267', '2017105268', '2017105269', '2017105270', '2017105271', '2017105272', '2017105273', '2017105275', '2017105276', '2017105277', '2017105278', '2017105279', '2017105280', '2017105281', '2017105282', '2017105283', '2017105286', '2017105287', '2017105289', '2017105292', '2017105293', '2017105295', '2017105297', '2017105298', '2017105300', '2017105301', '2017105302', '2017105304', '2017105307', '2017105308', '2017105309', '2017105312', '2017105313', '2017105314', '2017105315', '2017105316', '2017105317', '2017105318', '2017105319', '2017105321', '2017105323', '2017105324', '2017105326', '2017105327', '2017105328', '2017105329', '2017105331', '2017105332', '2017105334', '2017105336', '2017105337', '2017105338', '2017105339', '2017105340', '2017105342', '2017105343', '2017105344', '2017105345', '2017105346', '2017105347', '2017105348', '2017105350', '2017105351', '2017105353', '2017105354', '2017105356', '2017105358', '2017105359', '2017105360', '2017105362', '2017105363', '2017105364', '2017105365', '2017105366', '2017105367', '2017105368', '2017105370', '2017105371', '2017105372', '2017105374', '2017105375', '2017105376', '2017105377', '2017105378', '2017105379', '2017105380', '2017105383', '2017105386', '2017105387', '2017105388', '2017105390', '2017105391', '2017105393', '2017105394', '2017105395', '2017105396', '2017105397', '2017105398', '2017105400', '2017105401', '2017105402', '2017105403', '2017105404', '2017105406', '2017105407', '2017105408', '2017105409', '2017105410', '2017105412', '2017105413', '2017105414', '2017105415', '2017105416', '2017105417', '2017105419', '2017105421', '2017105422', '2017105423', '2017105424', '2017105425', '2017105426', '2017105427', '2017105429', '2017105430', '2017105431', '2017105433', '2017105434', '2017105435', '2017105436', '2017105438', '2017105439', '2017105441', '2017105443', '2017105444', '2017105446', '2017105447', '2017105448', '2017105449', '2017105451', '2017105452', '2017105453', '2017105455', '2017105457', '2017105458', '2017105459', '2017105460', '2017105463', '2017105464', '2017105465', '2017105466', '2017105470', '2017105471', '2017105472', '2017105473', '2017105474', '2017105475', '2017105476', '2017105477', '2017105478', '2017105480', '2017105482', '2017105483', '2017105484', '2017105485', '2017105490', '2017105491', '2017105493', '2017105494', '2017105495', '2017105496', '2017105497', '2017105500', '2017105501', '2017105502', '2017105504', '2017105506', '2017105508', '2017105509', '2017105511', '2017105513', '2017105514', '2017105515', '2017105516', '2017105517', '2017105518', '2017105522', '2017105525', '2017105530', '2017105532', '2017105533', '2017105535', '2017105536', '2017105537', '2017105538', '2017105539', '2017105541', '2017105542', '2017105544', '2017105545', '2017105546', '2017105548', '2017105550', '2017105553', '2017105554', '2017105558', '2017105559', '2017105560', '2017105561', '2017105563', '2017105565', '2017105566', '2017105567', '2017105568', '2017105570', '2017105571', '2017105572', '2017105574', '2017105576', '2017105577', '2017105579', '2017105581', '2017105583', '2017105584', '2017105585', '2017105587', '2017105588', '2017105589', '2017105590', '2017105591', '2017105594', '2017105599', '2017105601', '2017105602', '2017105604', '2017105607', '2017105608', '2017105610', '2017105611', '2017105612', '2017105613', '2017105615', '2017105616', '2017105617', '2017105619', '2017105620', '2017105621', '2017105622', '2017105623', '2017105624', '2017105625', '2017105626', '2017105627', '2017105628', '2017105630', '2017105631', '2017105632', '2017105634', '2017105636', '2017105637', '2017105638', '2017105640', '2017105641', '2017105642', '2017105643', '2017105644', '2017105645', '2017105647', '2017105649', '2017105650', '2017105651', '2017105652', '2017105654', '2017105655', '2017105657', '2017105659', '2017105660', '2017105661', '2017105662', '2017105663', '2017105665', '2017105666', '2017105667', '2017105669', '2017105671', '2017105672', '2017105673', '2017105674', '2017105695', '2017105703', '2017105712', '2017105714', '2017105718', '2017105720', '2017105721', '2017105722', '2017105723', '2017105726', '2017105730', '2017105736', '2017105738', '2017105743', '2017105752', '2017105753', '2017105755', '2017105761', '2017105766', '2017105767', '2017105768', '2017105773', '2017105774', '2017105776', '2017105780', '2017105781', '2017105785', '2017105789', '2017105790', '2017105792', '2017105793', '2017105794', '2017105795', '2017105797', '2017105798', '2017105800', '2017105806', '2017105807', '2017105814', '2017105817', '2017105818', '2017105819', '2017105820', '2017105821', '2017105822', '2017105824', '2017105825', '2017105827', '2017105828', '2017105829', '2017105830', '2017105832', '2017105833', '2017105834', '2017105838', '2017105844', '2017105854', '2017105863', '2017105867', '2017105868', '2017105874', '2017105881', '2017105883', '2017105889', '2017105891', '2017105892', '2017105893', '2017105897', '2017105898', '2017105899', '2017105901', '2017105902', '2017105903', '2017105905', '2017105907', '2017105915', '2017105916', '2017105918', '2017105924', '2017105925', '2017105927', '2017105929', '2017105931', '2017105933', '2017105936', '2017105937', '2017105938', '2017105940', '2017105943', '2017105944', '2017105945', '2017105953', '2017105954', '2017105955', '2017105956', '2017105958', '2017105959', '2017105960', '2017105961', '2017105962', '2017105963', '2017105965', '2017105967', '2017105968', '2017105971', '2017105972', '2017105973', '2017105974', '2017105976', '2017105977', '2017105978', '2017105980', '2017105982', '2017105984', '2017105988', '2017105990', '2017105991', '2017105992', '2017105993', '2017105994', '2017105997', '2017106002', '2017106009', '2017106012', '2017106015', '2017106017', '2017106018', '2017106019', '2017106020', '2017106021', '2017106024', '2017106027', '2017106028', '2017106029', '2017106030', '2017106031', '2017106033', '2017106034', '2017106035', '2017106038', '2017106043', '2017106045', '2017106047', '2017106048', '2017106049', '2017106053', '2017106055', '2017106056', '2017106057', '2017106059', '2017106060', '2017106061', '2017106063', '2017106068', '2017106069', '2017106071', '2017106078', '2017106079', '2017106080', '2017106081', '2017106084', '2017106086', '2017106088', '2017106089', '2017106093', '2017106096', '2017106100', '2017106103', '2017106104', '2017106105', '2017106114', '2017106115', '2017106116', '2017106117', '2017106118', '2017106120', '2017106121', '2017106125', '2017106126', '2017106128', '2017106130', '2017106131', '2017106136', '2017106137', '2017106138', '2017106145', '2017106153', '2017106160', '2017106161', '2017106162', '2017106163', '2017106166', '2017106169', '2017106170', '2017106172', '2017106185', '2017106187', '2017106195', '2017106196', '2017106198', '2017106201', '2017106203', '2017106207', '2017106208', '2017106209', '2017106214', '2017106217', '2017106220', '2017106221', '2017106226', '2017106227', '2017106230', '2017106233', '2017106237', '2017106239', '2017106240', '2017106241', '2017106243', '2017106244', '2017106246', '2017106247', '2017106248', '2017106249', '2017106255', '2017106257', '2017106259', '2017106260', '2017106261', '2017106264', '2017106270', '2017106275', '2017106276', '2017106278', '2017106279', '2017106282', '2017106283', '2017106285', '2017106287', '2017106290', '2017106291', '2017106292', '2017106294', '2017106295', '2017106296', '2017106297', '2017106302', '2017106303', '2017106304', '2017106305', '2017106309', '2017106311', '2017106313', '2017106316', '2017106317', '2017106319', '2017106320', '2017106321', '2017106325', '2017106327', '2017106330', '2017106333', '2017106334', '2017106335', '2017106336', '2017106337', '2017106338', '2017106341', '2017106343', '2017106345', '2017106347', '2017106348', '2017106350', '2017106352', '2017106355', '2017106356', '2017106357', '2017106360', '2017106361', '2017106362', '2017106365', '2017106366', '2017106367', '2017106372', '2017106374', '2017106377', '2017106379', '2017106380', '2017106385', '2017106387', '2017106389', '2017106390', '2017106392', '2017106393', '2017106394', '2017106395', '2017106398', '2017106399', '2017106400', '2017106402', '2017106403', '2017106405', '2017106406', '2017106407', '2017106408', '2017106409', '2017106414', '2017106417', '2017106418', '2017106422', '2017106424', '2017106427', '2017106432', '2017106433', '2017106434', '2017106437', '2017106441', '2017106442', '2017106444', '2017106450', '2017106451', '2017106452', '2017106453', '2017106456', '2017106459', '2017106461', '2017106463', '2017106464', '2017106468', '2017106469', '2017106470', '2017106476', '2017106478', '2017106479', '2017106483', '2017106485', '2017106489', '2017106491', '2017106492', '2017106495', '2017106497', '2017106498', '2017106499', '2017106500', '2017106502', '2017106505', '2017106512', '2017106516', '2017106521', '2017106522', '2017106527', '2017106533', '2017106535', '2017106536', '2017106537', '2017106539', '2017106540', '2017106541', '2017106542', '2017106544', '2017106547', '2017106550', '2017106551', '2017106552', '2017106553', '2017106554', '2017106555', '2017106557', '2017106558', '2017106560', '2017106562', '2017106563', '2017106564', '2017106565', '2017106566', '2017106567', '2017106568', '2017106570', '2017106571', '2017106572', '2017106573', '2017106575', '2017106581', '2017106583', '2017106584', '2017106585', '2017106586', '2017106587', '2017106590', '2017106593', '2017106595', '2017106596', '2017106598', '2017106600', '2017106601', '2017106602', '2017106605', '2017106606', '2017106609', '2017106611', '2017106612', '2017106613', '2017106619', '2017106623', '2017106626', '2017106630', '2017106631', '2017106632', '2017106637', '2017106638', '2017106640', '2017106642', '2017106643', '2017106644', '2017106649', '2017106650', '2017106651', '2017106652', '2017106653', '2017106662', '2017106664', '2017106666', '2017106673', '2017106676', '2017106679', '2017106681', '2017106688', '2017106690', '2017106693', '2017106700', '2017106704', '2017106707', '2017106709', '2017106711', '2017106716', '2017106717', '2017106720', '2017106721', '2017106722', '2017106723', '2017106729', '2017106735', '2017106736', '2017106747', '2017106748', '2017106755', '2017106762', '2017106764', '2017106768', '2017106772', '2017106776', '2017106777', '2017106778', '2017106779', '2017106780', '2017106784', '2017109002', '2017109007', '2017109008', '2017109009', '2017109010', '2017109011', '2017109012', '2017109019', '2017109021', '2017109024', '2017109026', '2017109028', '2017109029', '2017109037', '2017109043', '2017109044', '2017109047', '2017109048', '2017109054', '2017109057', '2017109062', '2017109064', '2017109067', '2017109068', '2017109070', '2017109071', '2017109072', '2017109074', '2017109075', '2017109076', '2017109077', '2017109078', '2017109079', '2017109086', '2017109090', '2017109092', '2017109094', '2017109097', '2017109098', '2017109099', '2017109100', '2017109105', '2017109110', '2017109111', '2017109113', '2017109115', '2017109116', '2017109120', '2017109122', '2017109123', '2017109124', '2017109125', '2017109126', '2017109127', '2017109129', '2017109130', '2017109131', '2017109132', '2017109133', '2017109134', '2017109139', '2017109140', '2017109141', '2017109142', '2017109144', '2017109145', '2017109146', '2017109147', '2017109148', '2017109149', '2017109151', '2017109156', '2017109159', '2017109160', '2017109161', '2017110002', '2017110004', '2017110006', '2017110007', '2017110008', '2017110013', '2017110015', '2017110016', '2017110017', '2017110019', '2017110021', '2017110022', '2017110026', '2017110027', '2017110028', '2017110029', '2017110030', '2017110031', '2017110033', '2017110034', '2017110035', '2017110038', '2017110040', '2017110041', '2017110042', '2017110043', '2017110045', '2017110046', '2017110047', '2017110048', '2017110050', '2017110052', '2017110054', '2017110055', '2017110060', '2017110061', '2017110062', '2017110064', '2017110065', '2017110066', '2017110067', '2017110068', '2017110071', '2017110072', '2017110074', '2017110075', '2017110076', '2017110079', '2017110082', '2017110083', '2017110084', '2017110085', '2017110086', '2017110088', '2017110089', '2017110090', '2017110091', '2017110092', '2017110093', '2017110098', '2017110099', '2017110103', '2017110104', '2017110105', '2017110106', '2017110107', '2017110108', '2017110110', '2017110111', '2017110112', '2017110115', '2017110117', '2017110119', '2017110120', '2017110123', '2017110125', '2017110128', '2017110130', '2017110132', '2017110134', '2017110135', '2017110136', '2017110138', '2017110140', '2017110141', '2017110144', '2017110145', '2017110146', '2017110147', '2017110148', '2017110149', '2017110150', '2017110151', '2017110152', '2017110154', '2017110156', '2017110157', '2017110158', '2017110159', '2017110160', '2017110161', '2017110166', '2017110167', '2017110169', '2017110170', '2017110171', '2017110172', '2017110173', '2017110175']\n"]}], "source": ["horse_ids = race_processor.extract_horse_ids_from_race_data(year=2019)\n", "# 馬血統情報のHTMLをスクレイピング\n", "horse_html_paths = scrape_html_ped(horse_ids, skip=True)\n", "print(f'スクレイピングした馬血統情報: {len(horse_html_paths)}件')\n", "print(horse_html_paths)"]}, {"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["## リファクタリングの利点\n", "\n", "このノートブックでは、リファクタリングされた `refactored_scrap.py` の関数を使用しています。リファクタリングの主な利点は以下の通りです：\n", "\n", "1. **コードの重複削減**: 3つのスクレイピング関数（`scrape_html_race`, `scrape_html_horse`, `scrape_html_ped`）で重複していたコードが1つの共通関数にまとまりました\n", "2. **保守性の向上**: 共通ロジックの変更が1箇所で済むようになりました\n", "3. **拡張性の向上**: 新しいタイプのスクレイピングを追加する際も、共通関数を再利用できます\n", "4. **一貫性の確保**: エラーハンドリングやログ出力が統一されました\n", "5. **テスト容易性**: 共通関数を単体でテストしやすくなりました\n", "\n", "これらの改善により、コードの品質と保守性が向上し、将来的な機能追加も容易になります。"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.9"}}, "nbformat": 4, "nbformat_minor": 5}