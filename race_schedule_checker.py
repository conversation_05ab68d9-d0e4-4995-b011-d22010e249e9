#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
競馬開催スケジュール確認ツール
実際の開催日とレースIDを取得
"""

import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import pandas as pd

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RaceScheduleChecker:
    """競馬開催スケジュール確認クラス"""
    
    def __init__(self):
        logger.info("RaceScheduleChecker初期化完了")
    
    def get_race_dates_in_period(self, start_date, end_date):
        """指定期間の競馬開催日を取得"""
        try:
            from core.scrapers.scraper import scrape_netkeiba_race_dates
            
            start_str = start_date.strftime('%Y-%m-%d')
            end_str = end_date.strftime('%Y-%m-%d')
            
            logger.info(f"🗓️ {start_str} から {end_str} までの開催日を取得中...")
            
            race_dates = scrape_netkeiba_race_dates(start_str, end_str)
            
            if race_dates:
                logger.info(f"✅ {len(race_dates)}日の開催日を取得しました")
                
                # 日付をdatetimeオブジェクトに変換
                date_objects = []
                for date_str in race_dates:
                    try:
                        date_obj = datetime.strptime(date_str, '%Y%m%d')
                        date_objects.append(date_obj)
                    except ValueError:
                        logger.warning(f"⚠️ 無効な日付形式: {date_str}")
                
                return sorted(date_objects)
            else:
                logger.warning(f"⚠️ 指定期間に開催日が見つかりません")
                return []
                
        except Exception as e:
            logger.error(f"❌ 開催日取得エラー: {e}")
            return []
    
    def get_race_ids_for_dates(self, race_dates):
        """指定日のレースID一括取得"""
        try:
            from core.scrapers.scraper import scrape_netkeiba_race_ids
            
            # 日付文字列リストに変換
            date_strs = [date.strftime('%Y%m%d') for date in race_dates]
            
            logger.info(f"🔍 {len(date_strs)}日分のレースIDを取得中...")
            
            race_ids = scrape_netkeiba_race_ids(
                race_date_list=date_strs,
                debug=False,
                connection_timeout=180,
                max_retries=3
            )
            
            if race_ids:
                logger.info(f"✅ {len(race_ids)}件のレースIDを取得しました")
                return race_ids
            else:
                logger.warning("⚠️ レースIDが取得できませんでした")
                return []
                
        except Exception as e:
            logger.error(f"❌ レースID取得エラー: {e}")
            return []
    
    def get_weekly_schedule(self, base_date=None):
        """週間スケジュール取得"""
        try:
            if base_date is None:
                base_date = datetime.now()
            elif isinstance(base_date, str):
                base_date = datetime.strptime(base_date, '%Y-%m-%d')
            
            # その週の月曜日から日曜日まで
            weekday = base_date.weekday()  # 0=月曜, 6=日曜
            monday = base_date - timedelta(days=weekday)
            sunday = monday + timedelta(days=6)
            
            print(f"🗓️ {monday.strftime('%Y年%m月%d日')} - {sunday.strftime('%Y年%m月%d日')} の週間スケジュール")
            print("=" * 60)
            
            # 週間の開催日を取得
            race_dates = self.get_race_dates_in_period(monday, sunday)
            
            if not race_dates:
                print("❌ この週は競馬の開催がありません")
                return None
            
            # 日別レース数を確認
            weekly_schedule = {}
            total_races = 0
            
            for race_date in race_dates:
                date_str = race_date.strftime('%Y%m%d')
                weekday_name = ['月', '火', '水', '木', '金', '土', '日'][race_date.weekday()]
                
                print(f"\n📅 {race_date.strftime('%Y年%m月%d日')} ({weekday_name})")
                
                # その日のレースID取得
                race_ids = self.get_race_ids_for_dates([race_date])
                
                if race_ids:
                    # レースIDを競馬場別に分類
                    venue_races = self._classify_races_by_venue(race_ids)
                    
                    daily_total = len(race_ids)
                    total_races += daily_total
                    
                    print(f"   開催レース数: {daily_total}レース")
                    
                    for venue_code, venue_races_list in venue_races.items():
                        venue_name = self._get_venue_name(venue_code)
                        print(f"   - {venue_name}: {len(venue_races_list)}レース")
                    
                    weekly_schedule[race_date] = race_ids
                else:
                    print("   開催なし")
            
            print(f"\n📊 週間合計: {total_races}レース")
            return weekly_schedule
            
        except Exception as e:
            logger.error(f"❌ 週間スケジュール取得エラー: {e}")
            return None
    
    def _classify_races_by_venue(self, race_ids):
        """レースIDを競馬場別に分類"""
        venue_races = {}
        
        for race_id in race_ids:
            if len(race_id) >= 10:
                venue_code = race_id[8:10]
                if venue_code not in venue_races:
                    venue_races[venue_code] = []
                venue_races[venue_code].append(race_id)
        
        return venue_races
    
    def _get_venue_name(self, venue_code):
        """競馬場コードから名前を取得"""
        venue_names = {
            '01': '札幌',
            '02': '函館',
            '03': '福島', 
            '04': '新潟',
            '05': '東京',
            '06': '中山',
            '07': '中京',
            '08': '京都',
            '09': '阪神',
            '10': '小倉',
            '11': '門別',
            '12': '盛岡',
            '13': '水沢',
            '14': '浦和',
            '15': '船橋',
            '16': '大井',
            '17': '川崎',
            '18': '金沢',
            '19': '笠松',
            '20': '名古屋',
            '21': '園田',
            '22': '姫路',
            '23': '福山',
            '24': '高知',
            '25': '佐賀'
        }
        return venue_names.get(venue_code, f"競馬場{venue_code}")
    
    def save_schedule_to_file(self, weekly_schedule, filename=None):
        """スケジュールをファイルに保存"""
        try:
            if not weekly_schedule:
                return False
            
            if filename is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"race_schedule_{timestamp}.csv"
            
            schedule_data = []
            
            for race_date, race_ids in weekly_schedule.items():
                for race_id in race_ids:
                    schedule_data.append({
                        'date': race_date.strftime('%Y-%m-%d'),
                        'weekday': ['月', '火', '水', '木', '金', '土', '日'][race_date.weekday()],
                        'race_id': race_id,
                        'venue_code': race_id[8:10] if len(race_id) >= 10 else '',
                        'venue_name': self._get_venue_name(race_id[8:10]) if len(race_id) >= 10 else '',
                        'race_num': int(race_id[10:12]) if len(race_id) >= 12 else 0
                    })
            
            df = pd.DataFrame(schedule_data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            print(f"\n💾 スケジュール保存: {filename}")
            logger.info(f"スケジュール保存完了: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"❌ スケジュール保存エラー: {e}")
            return False

def main():
    """メイン実行"""
    import argparse
    
    parser = argparse.ArgumentParser(description='競馬開催スケジュール確認ツール')
    parser.add_argument('--date', help='基準日 (YYYY-MM-DD形式、省略時は今日)')
    parser.add_argument('--save', action='store_true', help='スケジュールをCSVファイルに保存')
    
    args = parser.parse_args()
    
    try:
        print("🏇 競馬開催スケジュール確認ツール")
        print("=" * 50)
        
        checker = RaceScheduleChecker()
        
        # 週間スケジュール取得
        weekly_schedule = checker.get_weekly_schedule(args.date)
        
        if weekly_schedule and args.save:
            checker.save_schedule_to_file(weekly_schedule)
        
        return 0 if weekly_schedule else 1
        
    except KeyboardInterrupt:
        print(f"\n🛑 ユーザーによって中断されました")
        return 1
    except Exception as e:
        logger.error(f"実行エラー: {e}")
        print(f"❌ エラーが発生しました: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())