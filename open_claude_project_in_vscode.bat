@echo off
REM WSLのディストリビューション名を指定してください（例: Ubuntu, Debianなど）
set "WSL_DISTRO=Ubuntu" 

REM プロジェクトのパスを指定してください（WSL内のパス）
REM 例: /home/<USER>/yourproject または /mnt/c/Users/<USER>/Documents/yourproject
set "PROJECT_PATH=/mnt/h/AI/keiba_ai_system" 

REM VS Codeの実行パスを指定してください
REM 通常は以下のパスですが、環境によって異なります
set "VSCODE_PATH=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe"

IF NOT EXIST "%VSCODE_PATH%" (
    echo VS Codeの実行ファイルが見つかりません: "%VSCODE_PATH%"
    echo パスを確認してください。
    pause
    exit /b 1
)

echo VS CodeをWSLリモートモードで起動します...
start "" "%VSCODE_PATH%" --remote wsl+%WSL_DISTRO% "%PROJECT_PATH%"