# core/processors/race_html_parser.py
import io
import logging
import os
import re
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import pandas as pd
from bs4 import BeautifulSoup
from core.utils.constants import Master, RaceInfoCols, RaceProcessorConstants, ResultsCols

class RaceHtmlParser:
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def parse_race_html(self, html_path: str) -> <PERSON><PERSON>[pd.DataFrame, pd.DataFrame]:
        """レース情報のHTMLファイルを解析して、レース情報とレース結果のDataFrameを返す"""
        if not os.path.exists(html_path):
            self.logger.warning(f"ファイルが見つかりません: {html_path}")
            return pd.DataFrame(), pd.DataFrame()

        with open(html_path, "rb") as f:
            html_content = f.read()

        soup = self._get_soup_from_html(html_content, html_path)
        if not soup:
            return pd.DataFrame(), pd.DataFrame()

        race_id = os.path.basename(html_path).replace(".bin", "")

        race_info_data = self._parse_race_info_block(soup, race_id)
        race_info_df = pd.DataFrame([race_info_data]) if race_info_data else pd.DataFrame()

        race_results_df = self._parse_race_results_table(soup, race_id) # html_content_bytes は不要に
        if not race_results_df.empty: # _parse_race_results_table 内で既にID抽出とフォーマットが行われるように修正
            table_soup_for_ids = soup.find("table", summary=RaceProcessorConstants._RE_TABLE_SUMMARY)
            if not table_soup_for_ids:
                table_soup_for_ids = soup.find("table", class_=re.compile(r"(race_table_01|Shutuba_Table|Result_Table)"))
            race_results_df = self._format_and_extract_ids_from_results_df(race_results_df, table_soup_for_ids, race_id)
        return race_info_df, race_results_df

    def _get_soup_from_html(self, html_content: bytes, file_path_for_log: str) -> Optional[BeautifulSoup]:
        """HTMLコンテンツからBeautifulSoupオブジェクトを取得する"""
        soup: Optional[BeautifulSoup] = None
        try:
            soup = BeautifulSoup(html_content, "lxml", from_encoding="euc-jp")
        except Exception as e_eucjp:
            self.logger.warning(f"euc-jpでのデコードに失敗したため、utf-8で試行します: {file_path_for_log}, エラー: {e_eucjp}")
            try:
                soup = BeautifulSoup(html_content, "lxml", from_encoding="utf-8")
            except Exception as e_utf8:
                self.logger.error(f"HTMLのデコード/パースに失敗しました: {file_path_for_log}, エラー: {e_utf8}", exc_info=True)
                return None
        if not soup or not soup.body:
            self.logger.warning(f"HTMLのパースに失敗しました（bodyタグなし）: {file_path_for_log}")
            return None
        return soup

    def _extract_ids_from_cell_content(self, cell_content: Any) -> Dict[str, Optional[str]]:
        """テーブルのセル内容から関連するIDを抽出する"""
        ids = {"horse_id": None, "jockey_id": None, "trainer_id": None}
        links = []
        if isinstance(cell_content, str):
            temp_soup = BeautifulSoup(cell_content, 'html.parser')
            links = temp_soup.find_all("a", href=True)
        elif hasattr(cell_content, 'find_all'):
            links = cell_content.find_all("a", href=True)

        for link_tag in links:
            href = link_tag.get("href", "")
            if "/horse/" in href and not ids["horse_id"]:
                match = RaceProcessorConstants._RE_HORSE_ID.search(href)
                if match: ids["horse_id"] = match.group(1)
            elif "/jockey/" in href and not ids["jockey_id"]:
                match = RaceProcessorConstants._RE_JOCKEY_ID.search(href)
                if match: ids["jockey_id"] = match.group(1)
            elif "/trainer/" in href and not ids["trainer_id"]:
                match = RaceProcessorConstants._RE_TRAINER_ID.search(href)
                if match: ids["trainer_id"] = match.group(1)
        return ids

    def _parse_race_info_block(self, soup: BeautifulSoup, race_id: str) -> Dict[str, Any]:
        """HTML(soup)からレース基本情報を抽出する"""
        self.logger.debug(f"レース情報ブロックのパースを開始: {race_id}")
        race_info = {
            "race_id": race_id, RaceInfoCols.VENUE: "", RaceInfoCols.DATE: "",
            RaceInfoCols.RACE_NAME: "", RaceInfoCols.RACE_TYPE: "", RaceInfoCols.DISTANCE: 0,
            RaceInfoCols.RACE_CLASS: "", RaceInfoCols.GROUND_STATE: "", RaceInfoCols.WEATHER: "",
            RaceInfoCols.AROUND: "", "is_female_only_race": False, "is_new_horse_race": False,
        }
        race_name_elem = soup.select_one(','.join(RaceProcessorConstants.RACE_NAME_SELECTORS))
        if race_name_elem:
            race_info[RaceInfoCols.RACE_NAME] = race_name_elem.text.strip()

        data_intro = soup.find("div", attrs={"class": "data_intro"})
        if data_intro:
            p_tags = data_intro.find_all("p")
            if p_tags:
                all_text = " ".join(p.get_text(strip=True) for p in p_tags)
                race_info['data_intro_text'] = all_text
                if "牝限定" in all_text or "牝馬限定" in all_text: race_info["is_female_only_race"] = True
                elif "牝" in all_text and "牡" not in all_text : race_info["is_female_only_race"] = True
                if "新馬" in race_info.get(RaceInfoCols.RACE_NAME, "") or "新馬" in all_text: race_info["is_new_horse_race"] = True
                # 周回方向の抽出ロジック
                if "右" in all_text:
                    race_info["track_direction"] = "右"
                elif "左" in all_text:
                    race_info["track_direction"] = "左"
                elif "障害" in all_text:
                    race_info["track_direction"] = "障害"
                elif "直線" in all_text:
                    race_info["track_direction"] = "直線"
                else:
                    race_info["track_direction"] = "不明"
                date_match = RaceProcessorConstants._RE_DATE_IN_DETAILS.search(all_text)
                if date_match:
                    race_info[RaceInfoCols.DATE] = f"{date_match.group(1)}年{date_match.group(2)}月{date_match.group(3)}日"
                words = set(re.findall(r'\w+', all_text))
                self._extract_race_details_from_words(words, race_info)
                if race_info[RaceInfoCols.RACE_NAME]:
                    for pattern, class_val in RaceProcessorConstants.CLASS_MAP:
                        if re.search(pattern, race_info[RaceInfoCols.RACE_NAME]):
                            race_info[RaceInfoCols.RACE_CLASS] = class_val
                            break
        missing_fields = self._check_missing_fields(race_info)
        if missing_fields:
            self._fallback_extraction(soup, race_info, missing_fields)
        if not race_info[RaceInfoCols.DATE]:
            try:
                if len(race_id) >= 8:
                    year, month, day = race_id[:4], race_id[4:6], race_id[6:8]
                    race_info[RaceInfoCols.DATE] = f"{year}年{int(month)}月{int(day)}日"
            except (ValueError, IndexError):
                self.logger.warning(f"レースID '{race_id}' から日付の推測に失敗。")
        self.logger.debug(f"抽出されたレース情報 ({race_id}): {race_info}")
        return race_info

    def _extract_race_details_from_words(self, words: set, race_info: Dict[str, Any]) -> None:
        """単語セットから各種レース情報を効率的に抽出"""
        race_types = {"芝", "ダート"}
        ground_states = set(Master.GROUND_STATE_LIST)
        weather_list = set(Master.WEATHER_LIST)
        around_list = set(Master.AROUND_LIST)
        for word in words:
            if not race_info[RaceInfoCols.RACE_TYPE]:
                if word in race_types: race_info[RaceInfoCols.RACE_TYPE] = word
                elif "障" in word: race_info[RaceInfoCols.RACE_TYPE] = "障害"
            if not race_info[RaceInfoCols.DISTANCE] or race_info[RaceInfoCols.DISTANCE] == 0:
                if word.endswith("m") and len(word) > 1:
                    distance_str = word[:-1]
                    if distance_str.isdigit():
                        try: race_info[RaceInfoCols.DISTANCE] = int(distance_str)
                        except ValueError: pass
            if not race_info[RaceInfoCols.GROUND_STATE] and word in ground_states:
                race_info[RaceInfoCols.GROUND_STATE] = word
            if not race_info[RaceInfoCols.WEATHER] and word in weather_list:
                race_info[RaceInfoCols.WEATHER] = word
            if not race_info[RaceInfoCols.AROUND] and word in around_list:
                race_info[RaceInfoCols.AROUND] = word

    def _check_missing_fields(self, race_info: Dict[str, Any]) -> List[str]:
        """不足している主要フィールドをチェック"""
        required_fields_map = {
            RaceInfoCols.RACE_TYPE: race_info.get(RaceInfoCols.RACE_TYPE),
            RaceInfoCols.DATE: race_info.get(RaceInfoCols.DATE),
            RaceInfoCols.WEATHER: race_info.get(RaceInfoCols.WEATHER),
            RaceInfoCols.GROUND_STATE: race_info.get(RaceInfoCols.GROUND_STATE),
            RaceInfoCols.DISTANCE: race_info.get(RaceInfoCols.DISTANCE)
        }
        missing = []
        for field_key, field_value in required_fields_map.items():
            if field_key == RaceInfoCols.DISTANCE:
                if not field_value or field_value == 0: missing.append(field_key)
            elif not field_value: missing.append(field_key)
        return missing

    def _fallback_extraction(self, soup: BeautifulSoup, race_info: Dict[str, Any], missing_fields: List[str]) -> None:
        """バックアップ方法で不足情報を抽出"""
        race_data_text = ""
        for selector in RaceProcessorConstants.RACE_DATA_SELECTORS:
            race_data_elem = soup.select_one(selector)
            if race_data_elem:
                race_data_text = race_data_elem.text.strip()
                break
        if not race_data_text: return
        if RaceInfoCols.DATE in missing_fields and not race_info[RaceInfoCols.DATE]:
            date_match = RaceProcessorConstants._RE_DATE_IN_DETAILS.search(race_data_text)
            if date_match: race_info[RaceInfoCols.DATE] = f"{date_match.group(1)}年{date_match.group(2)}月{date_match.group(3)}日"
        if RaceInfoCols.RACE_TYPE in missing_fields and not race_info[RaceInfoCols.RACE_TYPE]:
            if '芝' in race_data_text: race_info[RaceInfoCols.RACE_TYPE] = '芝'
            elif 'ダ' in race_data_text: race_info[RaceInfoCols.RACE_TYPE] = 'ダート'
            elif '障' in race_data_text: race_info[RaceInfoCols.RACE_TYPE] = '障害'
        if RaceInfoCols.DISTANCE in missing_fields and (not race_info[RaceInfoCols.DISTANCE] or race_info[RaceInfoCols.DISTANCE] == 0) :
            distance_match = re.search(r'(\d+)m', race_data_text)
            if distance_match:
                try: race_info[RaceInfoCols.DISTANCE] = int(distance_match.group(1))
                except ValueError: pass
        if RaceInfoCols.WEATHER in missing_fields and not race_info[RaceInfoCols.WEATHER]:
            weather_match = RaceProcessorConstants._RE_WEATHER_IN_DETAILS.search(race_data_text)
            if weather_match: race_info[RaceInfoCols.WEATHER] = weather_match.group(1)
        if RaceInfoCols.GROUND_STATE in missing_fields and not race_info[RaceInfoCols.GROUND_STATE]:
            track_match = RaceProcessorConstants._RE_TRACK_CONDITION_IN_DETAILS.search(race_data_text)
            if track_match: race_info[RaceInfoCols.GROUND_STATE] = track_match.group(1)

    def _parse_race_results_table(self, soup: BeautifulSoup, race_id: str) -> pd.DataFrame:
        """HTML(soup)からレース結果テーブルをパースし、DataFrameを生成する"""
        table_soup = soup.find("table", summary=RaceProcessorConstants._RE_TABLE_SUMMARY)
        if not table_soup:
            table_soup = soup.find("table", class_=re.compile(r"(race_table_01|Shutuba_Table|Result_Table)"))
        if not table_soup: # summary属性で見つからなければclass属性で探す
                self.logger.warning(f"レース結果/出馬表テーブルが見つかりません: {race_id}")
                return pd.DataFrame()
        # <br>タグをスペースに置換してpd.read_htmlが正しくパースできるようにする
        html_str = str(table_soup).replace('<br>', ' ').replace('<br/>', ' ').replace('<br />', ' ')
        try:
            df = pd.read_html(io.StringIO(html_str), flavor='bs4', header=0)[0]
            # パース成功後、ID抽出とカラム整形を行う
            df = self._format_and_extract_ids_from_results_df(df, table_soup, race_id)
        except Exception as e:
            self.logger.error(f"pd.read_html またはその後の処理でのレース結果テーブルのパースに失敗: {race_id}, エラー: {e}")
            return pd.DataFrame()
        return df

    def _map_header_to_standard_col(self, header_text: str, col_index: int) -> str:
        """ヘッダーテキストをResultsColsの標準的なカラム名にマッピングする"""
        header_text_norm = header_text.strip().replace(' ', '').replace('　', '')
        mapping_priority = [
            (ResultsCols.RANK, ['着順', '順位']), (ResultsCols.WAKUBAN, ['枠番', '枠']),
            (ResultsCols.UMABAN, ['馬番', '番号']), (ResultsCols.HORSE_NAME, ['馬名', '名前']),
            (ResultsCols.SEX_AGE, ['性齢', '性/齢', '性別', '年齢']), (ResultsCols.KINRYO, ['斤量']),
            (ResultsCols.JOCKEY, ['騎手']), (ResultsCols.TIME, ['タイム']),
            (ResultsCols.RANK_DIFF, ['着差']), (ResultsCols.POPULARITY, ['人気']),
            (ResultsCols.TANSHO_ODDS, ['単勝']), (ResultsCols.WEIGHT_AND_DIFF, ['馬体重', '体重']),
            (ResultsCols.TRAINER, ['調教師'])
        ]
        for standard_col, keywords in mapping_priority:
            if any(kw in header_text_norm for kw in keywords):
                return standard_col
        if col_index == 0 and not header_text_norm.isdigit(): return ResultsCols.RANK
        if col_index == 1 and '枠' in header_text_norm: return ResultsCols.WAKUBAN
        return header_text

    def _format_and_extract_ids_from_results_df(self, df: pd.DataFrame, table_soup_for_ids: Any, race_id: str) -> pd.DataFrame:
        """pd.read_htmlで取得したDataFrameを整形し、IDを抽出・追加する"""
        if df.empty: return df
        df['race_id'] = race_id
        new_columns = {}
        for i, col_original in enumerate(df.columns):
            col_str_norm = str(col_original).strip().replace(' ', '').replace('　', '')
            standard_name = self._map_header_to_standard_col(col_str_norm, i)
            final_name = standard_name
            counter = 1
            while final_name in new_columns.values():
                final_name = f"{standard_name}_{counter}"
                counter += 1
            new_columns[col_original] = final_name
        df = df.rename(columns=new_columns)
        for id_col in [ResultsCols.HORSE_ID, ResultsCols.JOCKEY_ID, ResultsCols.TRAINER_ID]:
            if id_col not in df.columns: df[id_col] = None

        if table_soup_for_ids:
            rows_in_soup = table_soup_for_ids.find_all('tr')
            data_rows_in_soup = rows_in_soup[1:] if len(rows_in_soup) > 1 else []
            if len(data_rows_in_soup) == len(df):
                extracted_ids = [self._extract_ids_from_cell_content(row) for row in data_rows_in_soup]
                df[ResultsCols.HORSE_ID] = [ids.get("horse_id") for ids in extracted_ids]
                df[ResultsCols.JOCKEY_ID] = [ids.get("jockey_id") for ids in extracted_ids]
                df[ResultsCols.TRAINER_ID] = [ids.get("trainer_id") for ids in extracted_ids]
            else:
                self.logger.warning(f"HTML行数 ({len(data_rows_in_soup)}) とDataFrame行数 ({len(df)}) が不一致。セル毎抽出にフォールバック。 ({race_id})")
                for index, row_series in df.iterrows():
                    if ResultsCols.HORSE_NAME in df.columns and pd.notna(row_series[ResultsCols.HORSE_NAME]):
                        ids = self._extract_ids_from_cell_content(str(row_series[ResultsCols.HORSE_NAME]))
                        if ids["horse_id"]: df.loc[index, ResultsCols.HORSE_ID] = ids["horse_id"]
                    if ResultsCols.JOCKEY in df.columns and pd.notna(row_series[ResultsCols.JOCKEY]):
                        ids = self._extract_ids_from_cell_content(str(row_series[ResultsCols.JOCKEY]))
                        if ids["jockey_id"]: df.loc[index, ResultsCols.JOCKEY_ID] = ids["jockey_id"]
                    if ResultsCols.TRAINER in df.columns and pd.notna(row_series[ResultsCols.TRAINER]):
                        ids = self._extract_ids_from_cell_content(str(row_series[ResultsCols.TRAINER]))
                        if ids["trainer_id"]: df.loc[index, ResultsCols.TRAINER_ID] = ids["trainer_id"]
        if isinstance(df.columns, pd.MultiIndex):
            df.columns = df.columns.get_level_values(0)
        df = df.loc[:, ~df.columns.str.contains('^Unnamed')]
        return df

    def parse_race_info_from_soup(self, soup: BeautifulSoup, race_id: str) -> pd.DataFrame:
        """
        BeautifulSoupオブジェクトからレース基本情報を抽出してDataFrameとして返す。
        """
        race_info_data = self._parse_race_info_block(soup, race_id)
        return pd.DataFrame([race_info_data]) if race_info_data else pd.DataFrame()

    def parse_race_results_from_soup(self, soup: BeautifulSoup, race_id: str) -> pd.DataFrame:
        """
        BeautifulSoupオブジェクトからレース結果テーブルをパースし、
        ID抽出と整形を行ったDataFrameとして返す。
        """
        # _parse_race_results_table は既にID抽出と整形を含むように修正済み
        return self._parse_race_results_table(soup, race_id)

    # 重複した extract_horse_ids_from_html メソッドは削除
    # 下記の完全な実装を使用

    def extract_horse_ids_from_html(self, html_path: str) -> List[str]:
        """単一のレースHTMLファイルから馬IDのリストを抽出する"""
        if not os.path.exists(html_path):
            self.logger.warning(f"ファイルが見つかりません: {html_path}")
            return []
        with open(html_path, "rb") as f:
            html_content = f.read()
        soup = self._get_soup_from_html(html_content, html_path)
        return self._extract_horse_ids_from_soup(soup)

    def _extract_horse_ids_from_soup(self, soup: Optional[BeautifulSoup]) -> List[str]:
        """BeautifulSoupオブジェクトから馬IDのリストを抽出する内部ヘルパー"""
        if not soup or not soup.body:
            self.logger.warning(f"HTMLのパース結果が空です（馬ID抽出）")
            return []
        search_area: Union[BeautifulSoup, Any] = soup
        main_result_table = soup.find("table", summary=RaceProcessorConstants._RE_TABLE_SUMMARY)
        if main_result_table:
            search_area = main_result_table
        else:
            possible_selectors = ["table.race_table_01", "table.Shutuba_Table", "table.Result_Table", ".HorseList"]
            for selector in possible_selectors:
                candidate_area = soup.select_one(selector)
                if candidate_area and candidate_area.find("a", href=RaceProcessorConstants._RE_HORSE_ID):
                    search_area = candidate_area
                    break
            if search_area is soup: # 特定のエリアが見つからなかった場合
                self.logger.warning(f"特定エリアが見つからずHTML全体から馬IDを検索")
        horse_ids: Set[str] = set()
        for a_tag in search_area.find_all("a", href=RaceProcessorConstants._RE_HORSE_ID):
            match = RaceProcessorConstants._RE_HORSE_ID.search(a_tag.get("href", ""))
            if match: horse_ids.add(match.group(1))
        if not horse_ids: self.logger.info(f"馬IDが見つかりませんでした")
        return sorted(list(horse_ids))

