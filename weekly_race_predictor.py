#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
週間レース一括予測スタンドアロンツール（最新戦績スクレイピング機能付き）
指定した週の土日の全レースを自動予測
- リアルタイム出馬表取得
- 馬の最新戦績リアルタイムスクレイピング
- 改善された予測精度
"""

import sys
import argparse
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import json
import time
import random

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WeeklyRacePredictor:
    """週間レース予測クラス（最新戦績スクレイピング機能付き）"""
    
    def __init__(self, model_dir="models", enable_live_scraping=True):
        self.model_dir = Path(model_dir)
        self.predictor = None
        self.enable_live_scraping = enable_live_scraping
        logger.info(f"WeeklyRacePredictor初期化完了（最新戦績スクレイピング: {enable_live_scraping}）")
    
    def initialize_predictor(self):
        """予測システム初期化"""
        try:
            from enhanced_live_predictor_with_scraping import EnhancedLiveRacePredictorWithScraping
            
            self.predictor = EnhancedLiveRacePredictorWithScraping(
                use_selenium=False,  # Seleniumは使用しない（高速化）
                enable_live_scraping=self.enable_live_scraping  # 最新戦績スクレイピング設定
            )
            if not self.predictor.load_latest_model():
                raise RuntimeError("モデル読み込み失敗")
            
            logger.info("予測システム初期化完了")
            return True
            
        except Exception as e:
            logger.error(f"予測システム初期化エラー: {e}")
            return False
    
    def get_week_dates(self, base_date_str=None, week_offset=0):
        """週の土日を取得"""
        try:
            if base_date_str:
                base_date = datetime.strptime(base_date_str, '%Y-%m-%d')
            else:
                base_date = datetime.now()
            
            # 週のオフセットを適用
            base_date = base_date + timedelta(days=week_offset * 7)
            
            # 週の土日を計算
            weekday = base_date.weekday()  # 0=月曜, 6=日曜
            
            # 土曜日を計算 (5 = 土曜)
            days_to_saturday = (5 - weekday) % 7
            if weekday == 6:  # 日曜日の場合は前日の土曜
                days_to_saturday = -1
            
            saturday = base_date + timedelta(days=days_to_saturday)
            sunday = saturday + timedelta(days=1)
            
            return [saturday, sunday]
            
        except Exception as e:
            logger.error(f"日付計算エラー: {e}")
            return None
    
    def show_week_selection_menu(self):
        """対話式週選択メニュー"""
        try:
            print("\n📅 週選択メニュー")
            print("=" * 40)
            
            # 過去2週間+今週+来週のオプションを表示
            today = datetime.now()
            week_options = []
            
            # 週のオフセット: [-2, -1, 0, 1] (過去2週、過去1週、今週、来週)
            week_offsets = [-2, -1, 0, 1]
            week_names = ["2週間前", "先週", "今週", "来週"]
            
            for i, offset in enumerate(week_offsets):
                week_dates = self.get_week_dates(week_offset=offset)
                if week_dates:
                    saturday, sunday = week_dates
                    week_name = week_names[i]
                    
                    # 開催状況の判定
                    if offset < 0:  # 過去
                        status = "📊"  # 過去データ
                    else:  # 現在・未来
                        status = "🏇"  # 開催予定
                    
                    week_options.append({
                        'name': week_name,
                        'dates': week_dates,
                        'display': f"{saturday.strftime('%m/%d')}(土)-{sunday.strftime('%m/%d')}(日)"
                    })
                    
                    print(f"{i + 1}. {week_name}: {saturday.strftime('%m/%d')}(土)-{sunday.strftime('%m/%d')}(日) {status}")
            
            print("5. 日付を指定")
            print("6. カスタム期間")
            print("0. キャンセル")
            
            choice = input("\n選択してください (0-6): ").strip()
            
            if choice == "0":
                return None
            elif choice in ["1", "2", "3", "4"]:
                week_index = int(choice) - 1
                if 0 <= week_index < len(week_options):
                    selected = week_options[week_index]
                    print(f"\n✅ {selected['name']} ({selected['display']}) を選択しました")
                    return selected['dates']
            elif choice == "5":
                return self._get_dates_by_input()
            elif choice == "6":
                return self._get_custom_date_range()
            
            print("❌ 無効な選択です")
            return None
            
        except Exception as e:
            logger.error(f"週選択メニューエラー: {e}")
            return None
    
    def _get_dates_by_input(self):
        """日付入力による選択"""
        try:
            date_input = input("\n基準日を入力してください (YYYY-MM-DD): ").strip()
            
            try:
                base_date = datetime.strptime(date_input, '%Y-%m-%d')
            except ValueError:
                print("❌ 無効な日付形式です")
                return None
            
            week_dates = self.get_week_dates(date_input)
            if week_dates:
                saturday, sunday = week_dates
                print(f"✅ {date_input}の週: {saturday.strftime('%m/%d')}(土)-{sunday.strftime('%m/%d')}(日)")
                return week_dates
            
            return None
            
        except Exception as e:
            logger.error(f"日付入力エラー: {e}")
            return None
    
    def _get_custom_date_range(self):
        """カスタム日付範囲"""
        try:
            print("\n📅 カスタム期間設定")
            start_input = input("開始日 (YYYY-MM-DD): ").strip()
            end_input = input("終了日 (YYYY-MM-DD): ").strip()
            
            try:
                start_date = datetime.strptime(start_input, '%Y-%m-%d')
                end_date = datetime.strptime(end_input, '%Y-%m-%d')
            except ValueError:
                print("❌ 無効な日付形式です")
                return None
            
            if start_date > end_date:
                print("❌ 開始日が終了日より後です")
                return None
            
            # 日付範囲の全日を生成
            dates = []
            current_date = start_date
            
            while current_date <= end_date:
                dates.append(current_date)
                current_date += timedelta(days=1)
            
            print(f"✅ カスタム期間: {start_date.strftime('%m/%d')} - {end_date.strftime('%m/%d')} ({len(dates)}日間)")
            return dates
            
        except Exception as e:
            logger.error(f"カスタム期間設定エラー: {e}")
            return None
    
    def generate_race_ids_for_date(self, target_date):
        """指定日の実際のレースID取得（scraper.pyを使用）"""
        try:
            from datetime import datetime
            
            # 未来の日付の場合はフォールバック方式を使用
            if target_date > datetime.now():
                logger.info(f"🔮 {target_date.strftime('%Y年%m月%d日')} は未来の日付のため、フォールバック方式を使用")
                return self._generate_fallback_race_ids(target_date)
            
            date_str = target_date.strftime('%Y%m%d')
            logger.info(f"🔍 {target_date.strftime('%Y年%m月%d日')} の実際のレースIDを取得中...")
            
            # scraper.pyを使用して実際のレースIDを取得
            from core.scrapers.scraper import scrape_netkeiba_race_ids
            
            race_ids = scrape_netkeiba_race_ids(
                race_date_list=[date_str],
                debug=False,
                connection_timeout=120,
                max_retries=3
            )
            
            if race_ids:
                # レースIDの年度チェック
                target_year = target_date.year
                valid_race_ids = []
                
                for race_id in race_ids:
                    if len(race_id) >= 4:
                        race_year = int(race_id[:4])
                        if race_year == target_year:
                            valid_race_ids.append(race_id)
                
                if valid_race_ids:
                    logger.info(f"✅ {len(valid_race_ids)}件の正しい年度のレースIDを取得しました")
                    return valid_race_ids
                else:
                    logger.warning(f"⚠️ 正しい年度のレースIDが見つかりません（取得: {len(race_ids)}件）")
                    return self._generate_fallback_race_ids(target_date)
            else:
                logger.warning(f"⚠️ {target_date.strftime('%Y年%m月%d日')} の開催レースが見つかりません")
                return self._generate_fallback_race_ids(target_date)
                
        except Exception as e:
            logger.error(f"❌ レースID取得エラー: {e}")
            return self._generate_fallback_race_ids(target_date)
    
    def _generate_fallback_race_ids(self, target_date):
        """フォールバック用レースID生成（従来方式）"""
        logger.info("📋 フォールバック方式でレースIDを生成中...")
        
        date_code = target_date.strftime('%Y%m%d')
        race_ids = []
        
        # 主要競馬場コード（実際の競馬場に対応）
        venue_codes = ['01', '02', '03', '04', '05', '06']  # 主要6場所に限定
        
        # 各競馬場×各レース
        for venue_code in venue_codes:
            for race_num in range(1, 13):  # 1R-12R
                race_id = f"{date_code}{venue_code}{race_num:02d}"
                race_ids.append(race_id)
        
        logger.info(f"📋 フォールバック: {len(race_ids)}件のレースIDを生成")
        return race_ids
    
    def predict_week(self, base_date_str=None, max_races_per_day=None, save_results=True, interactive=False):
        """週間予測実行"""
        try:
            # 初期化
            if not self.predictor:
                if not self.initialize_predictor():
                    return None
            
            # 対象日取得
            if interactive:
                # 対話式週選択
                target_dates = self.show_week_selection_menu()
                if not target_dates:
                    print("❌ 週選択がキャンセルされました")
                    return None
            else:
                # 従来方式
                target_dates = self.get_week_dates(base_date_str)
                if not target_dates:
                    return None
            
            print(f"🗓️ 週間レース一括予測")
            print("=" * 60)
            print(f"📅 対象期間: {target_dates[0].strftime('%Y年%m月%d日')} - {target_dates[1].strftime('%Y年%m月%d日')}")
            
            weekly_results = []
            total_races = 0
            successful_predictions = 0
            
            for date in target_dates:
                date_str = date.strftime('%Y-%m-%d')
                weekday_name = ['月', '火', '水', '木', '金', '土', '日'][date.weekday()]
                
                print(f"\n📅 {date_str} ({weekday_name}曜日) の予測")
                print("-" * 40)
                
                # レースID生成
                race_ids = self.generate_race_ids_for_date(date)
                
                # 制限がある場合は適用
                if max_races_per_day:
                    race_ids = race_ids[:max_races_per_day]
                
                print(f"🎯 対象レース: {len(race_ids)}レース")
                
                daily_results = []
                
                for i, race_id in enumerate(race_ids, 1):
                    try:
                        print(f"予測中... ({i:2d}/{len(race_ids)}) {race_id}", end=" ")
                        
                        # 予測実行（最新戦績スクレイピング機能付き）
                        results, race_info = self.predictor.predict_race(race_id)
                        
                        if not results.empty:
                            # 上位3頭を取得
                            top3 = results.head(3)
                            race_result = {
                                'race_id': race_id,
                                'date': date_str,
                                'venue': race_id[8:10],
                                'race_num': int(race_id[10:12]),
                                'top1_horse': top3.iloc[0]['馬名'] if len(top3) > 0 else 'N/A',
                                'top1_odds': float(top3.iloc[0]['単勝オッズ']) if len(top3) > 0 and '単勝オッズ' in top3.columns else 0.0,
                                'top1_popularity': int(top3.iloc[0]['人気']) if len(top3) > 0 and '人気' in top3.columns else 0,
                                'top1_win_rate': float(top3.iloc[0]['勝率']) if len(top3) > 0 else 0.0,
                                'top3_horses': top3['馬名'].tolist(),
                                'prediction_scores': top3['予測スコア'].tolist() if len(top3) > 0 else [],
                                'distance': race_info.get('course_len', 'N/A'),
                                'horses_count': len(results),
                                'scraped_latest': self.enable_live_scraping  # 最新戦績スクレイピング使用フラグ
                            }
                            daily_results.append(race_result)
                            successful_predictions += 1
                            print("✅")
                        else:
                            print("❌")
                        
                        total_races += 1
                        
                        # 過負荷防止
                        time.sleep(random.uniform(0.3, 0.7))
                        
                    except Exception as e:
                        print(f"❌ ({str(e)[:20]})")
                        continue
                
                weekly_results.extend(daily_results)
                print(f"📊 {date_str}: {len(daily_results)}/{len(race_ids)} レース予測成功")
            
            # 結果サマリー表示
            self.display_results_summary(weekly_results, total_races, successful_predictions)
            
            # 結果保存
            if save_results and weekly_results:
                self.save_results(weekly_results)
            
            return weekly_results
            
        except Exception as e:
            logger.error(f"週間予測エラー: {e}")
            return None
    
    def display_results_summary(self, weekly_results, total_races, successful_predictions):
        """結果サマリー表示"""
        if not weekly_results:
            print("\n❌ 予測結果がありません")
            return
        
        print(f"\n🎉 週間予測完了!")
        print("=" * 80)
        print(f"📊 予測サマリー")
        print(f"   総レース数: {total_races}")
        print(f"   成功予測数: {successful_predictions}")
        print(f"   成功率: {successful_predictions/total_races*100:.1f}%")
        print(f"\n💡 機能: リアルタイム出馬表取得 + 最新馬戦績スクレイピング")
        
        # 日別高配当狙い馬（オッズ5倍以上で勝率20%以上）
        print(f"\n🎯 注目馬（高配当狙い）")
        print("-" * 60)
        
        from collections import defaultdict
        daily_summary = defaultdict(list)
        
        for result in weekly_results:
            daily_summary[result['date']].append(result)
        
        for date, day_results in daily_summary.items():
            print(f"\n📅 {date}")
            print("レース | 本命馬           | 人気 | オッズ | 勝率")
            print("-" * 55)
            
            # 高配当候補（オッズ3倍以上）をソート
            promising_races = [r for r in day_results if r['top1_odds'] >= 3.0 and r['top1_win_rate'] >= 15.0]
            promising_races.sort(key=lambda x: x['top1_win_rate'], reverse=True)
            
            for result in promising_races[:8]:  # 上位8レース
                venue_race = f"{result['venue']}-{result['race_num']:02d}R"
                horse_name = result['top1_horse'][:12]
                popularity = result['top1_popularity']
                odds = result['top1_odds']
                win_rate = result['top1_win_rate']
                
                print(f"{venue_race:6} | {horse_name:15} | {popularity:2d}番 | {odds:5.1f} | {win_rate:4.1f}%")
    
    def save_results(self, weekly_results):
        """結果保存"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # CSV保存
            df = pd.DataFrame(weekly_results)
            csv_filename = f"weekly_prediction_{timestamp}.csv"
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            print(f"\n💾 CSV保存: {csv_filename}")
            
            # JSON保存（詳細データ）
            json_filename = f"weekly_prediction_{timestamp}.json"
            # 保存データに実行環境情報を追加
            save_data = {
                'execution_info': {
                    'timestamp': timestamp,
                    'features': '最新戦績スクレイピング機能付き',
                    'live_scraping_enabled': self.enable_live_scraping,
                    'total_races': len(weekly_results)
                },
                'results': weekly_results
            }
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            print(f"💾 JSON保存: {json_filename}")
            
            # 買い目リスト生成
            self.generate_betting_list(weekly_results, timestamp)
            
            # 機能情報を出力
            if self.enable_live_scraping:
                print(f"✨ 最新戦績スクレイピング機能を使用して予測精度を向上")
            
        except Exception as e:
            logger.error(f"結果保存エラー: {e}")
    
    def generate_betting_list(self, weekly_results, timestamp):
        """買い目リスト生成"""
        try:
            betting_list = []
            
            for result in weekly_results:
                # 条件：オッズ2.5-8.0倍、勝率15%以上、人気5位以内（データが存在する場合）
                odds = result.get('top1_odds', 0)
                win_rate = result.get('top1_win_rate', 0)
                popularity = result.get('top1_popularity', 99)
                
                if (2.5 <= odds <= 8.0 and 
                    win_rate >= 15.0 and 
                    popularity <= 5):
                    
                    betting_list.append({
                        'date': result['date'],
                        'race_id': result['race_id'],
                        'venue_race': f"{result['venue']}-{result['race_num']:02d}R",
                        'horse': result['top1_horse'],
                        'odds': result['top1_odds'],
                        'popularity': result['top1_popularity'],
                        'win_rate': result['top1_win_rate'],
                        'recommendation': '単勝'
                    })
            
            if betting_list:
                # 買い目CSV保存
                betting_df = pd.DataFrame(betting_list)
                betting_filename = f"betting_recommendations_{timestamp}.csv"
                betting_df.to_csv(betting_filename, index=False, encoding='utf-8-sig')
                print(f"🎯 買い目リスト: {betting_filename} ({len(betting_list)}レース)")
                if self.enable_live_scraping:
                    print(f"   ※最新戦績データを基に推奨")
            
        except Exception as e:
            logger.error(f"買い目リスト生成エラー: {e}")

def main():
    """メイン実行"""
    parser = argparse.ArgumentParser(description='週間レース一括予測ツール（最新戦績スクレイピング機能付き）')
    parser.add_argument('--date', help='基準日 (YYYY-MM-DD形式、省略時は今日)')
    parser.add_argument('--max-races', type=int, help='1日あたりの最大予測レース数')
    parser.add_argument('--no-save', action='store_true', help='結果ファイルを保存しない')
    parser.add_argument('--quiet', action='store_true', help='ログ出力を最小限にする')
    parser.add_argument('--interactive', '-i', action='store_true', help='対話式週選択メニューを使用')
    parser.add_argument('--disable-scraping', action='store_true', help='最新戦績スクレイピングを無効化（高速化）')
    
    args = parser.parse_args()
    
    # ログレベル設定
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    
    try:
        print("🏇 週間レース一括予測ツール（最新戦績スクレイピング機能付き）")
        print("🔍 リアルタイム出馬表 + 最新馬戦績スクレイピング対応")
        print("=" * 50)
        
        predictor = WeeklyRacePredictor(
            enable_live_scraping=not args.disable_scraping
        )
        
        # 対話式モード確認
        interactive_mode = args.interactive or (args.date is None and not args.quiet)
        
        if interactive_mode:
            print("📅 対話式週選択モードで実行します")
        
        # 予測実行
        results = predictor.predict_week(
            base_date_str=args.date,
            max_races_per_day=args.max_races,
            save_results=not args.no_save,
            interactive=interactive_mode
        )
        
        if results:
            print(f"\n✅ 週間予測が正常に完了しました")
            print(f"📈 予測結果: {len(results)}レース")
            if not args.disable_scraping:
                print(f"🔍 最新戦績スクレイピング: 有効")
            else:
                print(f"⚡ 高速モード: 最新戦績スクレイピング無効")
        else:
            print(f"\n❌ 週間予測に失敗しました")
            return 1
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\n🛑 ユーザーによって中断されました")
        return 1
    except Exception as e:
        logger.error(f"実行エラー: {e}")
        print(f"❌ エラーが発生しました: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())