import re
import pandas as pd
import numpy as np
import logging
import os # os.path.basename を使用するため
from typing import Dict, List, Optional, Any # Any を追加
from bs4 import BeautifulSoup
import io # io.StringIO を使用するため
from concurrent.futures import ThreadPoolExecutor, as_completed # 並列処理のため

from core.utils.constants import RaceInfoCols, ResultsCols, RaceProcessorConstants, CornerDiffConstants, CornerOrder # 定数をインポート
from core.processors.race_html_parser import RaceHtmlParser # RaceHtmlParser をインポート

logger = logging.getLogger(__name__)
from tqdm.auto import tqdm # tqdm をインポート

class CornerAnalyzer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.html_parser = RaceHtmlParser() # RaceHtmlParser のインスタンスを保持
        self.logger.debug("CornerAnalyzer インスタンスが作成されました。")

    @staticmethod
    def parse_passing_order(pass_str: str) -> pd.DataFrame:
        """
        競馬のコーナー通過順位文字列を解析し、Pandas DataFrameに変換します。
        DataFrameには 'horse_no' (馬番), 'diff' (先頭からの累積差),
        'side' (内からの位置、1始まり) が含まれます。
        pyparsingを使用しないバージョンです。

        Args:
            pass_str (str): コーナー通過順位文字列 (例: "*1(2,3)-5=6,4")

        Returns:
            pd.DataFrame: 解析結果のDataFrame。
                          列: ['diff', 'horse_no', 'side']
                          インデックス: 'rank_in_corner_string' (1始まり)
        """
        if not pass_str or pd.isna(pass_str):
            return pd.DataFrame(columns=['diff', 'horse_no', 'side'])

        pass_str = str(pass_str) # 入力が数値等である可能性を考慮
        if pass_str.startswith('*'):
            pass_str = pass_str[1:]

        # 正規表現でトークンを抽出: グループ `(...)`、数字 `\d+`、区切り文字 `[,-=]`
        tokens = re.findall(r'(\(\s*\d+(?:\s*,\s*\d+)*\s*\)|\d+|[,-=])', pass_str)

        parsed_data = []
        accumulated_diff = 0.0
        first_element_processed = False # 最初の馬番/グループが処理されたかのフラグ

        for i, token in enumerate(tokens):
            is_separator_token = token in {',', '-', '='} # リストの代わりにセットを使用すると、検索がわずかに効率的になります

            if not first_element_processed and not is_separator_token:
                # 最初の要素（馬番またはグループ）の場合、前の要素との差分は考慮しない
                pass
            elif not is_separator_token:
                # 2番目以降の馬番/グループで、直前が区切り文字でない場合 (例: "1 2" のような暗黙の区切り)
                if i > 0 and tokens[i-1] not in [',', '-', '=']:
                    accumulated_diff += CornerDiffConstants.IMPLICIT_OR_COMMA # 暗黙の区切り文字の処理

            if token.isdigit(): # 単独の馬番
                parsed_data.append({'diff': accumulated_diff, 'horse_no': int(token), 'side': 1})
                first_element_processed = True
            elif token.startswith('(') and token.endswith(')'): # グループ内の馬番 (例: "(1,2,3)")
                group_diff_for_these_horses = accumulated_diff
                # カンマ区切りで馬番を抽出し、整数に変換
                try:
                    horse_numbers_in_group = [int(hn.strip()) for hn in token[1:-1].split(',') if hn.strip().isdigit()]
                    if not horse_numbers_in_group: # 有効な馬番がグループ内にない場合
                        logger.warning(f"グループ内に有効な馬番が見つかりません: トークン='{token}'。このトークンをスキップします。")
                        continue
                    for side_idx, horse_no in enumerate(horse_numbers_in_group):
                        parsed_data.append({
                            'diff': group_diff_for_these_horses,
                            'horse_no': horse_no,
                            'side': side_idx + 1 # 内からの位置 (1始まり)
                        })
                    first_element_processed = True
                except ValueError as e:
                    logger.warning(f"グループ内の馬番変換に失敗: トークン='{token}', エラー='{e}'。このトークンをスキップします。")
                    continue
            elif token == ',':
                accumulated_diff += CornerDiffConstants.IMPLICIT_OR_COMMA
            elif token == '-':
                accumulated_diff += CornerDiffConstants.HYPHEN
            elif token == '=':
                accumulated_diff += CornerDiffConstants.EQUAL

        if not parsed_data:
            df = pd.DataFrame(columns=['diff', 'horse_no', 'side'])
        else:
            df = pd.DataFrame(parsed_data)

        # コーナー文字列内での通過順をインデックスとして設定
        df.index = pd.RangeIndex(start=1, stop=len(df) + 1, name='rank_in_corner_string')
        return df

    def _extract_corner_passing_data_from_soup(self, soup: BeautifulSoup) -> Dict[str, pd.DataFrame]:
        """
        BeautifulSoupオブジェクトからコーナー通過順位データを抽出する
        """
        corner_data_map = {}
        corner_table_selectors = [
            "table[summary*='通過']", "table[summary*='コーナー']", "table.corner_table",
            "table.passing_table", ".corner_data table", ".passing_data table"
        ]
        corner_table = None
        for selector in corner_table_selectors:
            corner_table = soup.select_one(selector)
            if corner_table: break

        if not corner_table:
            all_tables = soup.find_all("table")
            for table in all_tables:
                if any(kw in table.get_text() for kw in ["通過", "コーナー", "1角", "2角", "3角", "4角"]): # .get_text() を使用する方が意図通りかもしれません
                    corner_table = table
                    break
        if not corner_table: return corner_data_map

        rows = corner_table.find_all("tr")
        for row in rows: # type: ignore # rows は Tag のリスト
            cells = row.find_all(["td", "th"])
            if len(cells) < 2: continue
            corner_label_text = cells[0].get_text(strip=True)
            corner_label = self._normalize_corner_label(corner_label_text)
            if not corner_label: continue

            for cell in cells[1:]:
                passing_data_text = cell.get_text(strip=True)
                if passing_data_text and passing_data_text != "-":
                    parsed_df = self.parse_passing_order(passing_data_text) # クラスメソッド呼び出しに変更
                    if not parsed_df.empty:
                        corner_data_map[corner_label] = parsed_df
                        break
        return corner_data_map

    def _normalize_corner_label(self, label_text: str) -> Optional[str]:
        """コーナーラベルテキストを正規化する""" # docstring はそのまま
        if not label_text: return None
        number_match = re.search(r'(\d+)', label_text)
        if number_match: return f"{number_match.group(1)}コーナー"
        # マッピングのキーをより具体的に、または正規表現を使用して柔軟性を持たせることを検討
        label_mapping = {
            "1角": "1コーナー", "2角": "2コーナー", "3角": "3コーナー", "4角": "4コーナー",
            "第1": "1コーナー", "第2": "2コーナー", "第3": "3コーナー", "第4": "4コーナー",
            "C1": "1コーナー", "C2": "2コーナー", "C3": "3コーナー", "C4": "4コーナー"
        }
        for pattern, normalized in label_mapping.items(): # より多くのパターンに対応できるよう、ここを拡張することも可能です
            if pattern in label_text: return normalized
        return None

    def _get_horse_numbers_from_corner_data(self, corner_data_map: Dict[str, pd.DataFrame]) -> List[int]:
        """コーナーデータから出走馬番のリストを取得する""" # docstring はそのまま
        all_horse_numbers = set()
        for corner_df in corner_data_map.values():
            if 'horse_no' in corner_df.columns:
                all_horse_numbers.update(corner_df['horse_no'].unique())
        return sorted(list(all_horse_numbers))

    def _extract_race_info_for_corner_analysis_from_df(self, race_info_df_full: pd.DataFrame, race_id: str) -> pd.DataFrame:
        """コーナー分析に必要な最小限のレース情報を抽出（日付など）""" # docstring はそのまま
        if race_info_df_full.empty or RaceInfoCols.DATE not in race_info_df_full.columns:
            # race_idから日付を推測するフォールバック
            try:
                date_val = f"{race_id[:4]}年{int(race_id[4:6])}月{int(race_id[6:8])}日" if len(race_id) >= 8 else None
            except (IndexError, ValueError): # race_id の形式が不正な場合のエラーハンドリング
                date_val = None
            return pd.DataFrame([{"race_id": race_id, RaceInfoCols.DATE: date_val}])
        return race_info_df_full[["race_id", RaceInfoCols.DATE]].copy()

    def _extract_results_for_corner_analysis_from_df(self, results_df_full: pd.DataFrame, race_id: str) -> pd.DataFrame:
        """コーナー分析に必要な最小限のレース結果（馬番と馬IDのマッピング）を抽出""" # docstring はそのまま
        if results_df_full.empty or not {ResultsCols.UMABAN, ResultsCols.HORSE_ID}.issubset(results_df_full.columns):
            return pd.DataFrame()
        return results_df_full[[ResultsCols.UMABAN, ResultsCols.HORSE_ID]].copy().dropna()

    def _extract_corner_metrics(self, horse_no: int, corner_data_map: Dict[str, pd.DataFrame], sorted_corner_labels: List[str], features: Dict, horse_metrics_timeseries: Dict[str, Dict[str, float]]) -> None:
        """個別の馬の各コーナーでの指標を抽出し、featuresとhorse_metrics_timeseriesを更新する"""
        for corner_label in sorted_corner_labels:
            df_corner = corner_data_map.get(corner_label, pd.DataFrame()) # 空のDataFrameをデフォルト値に
            horse_data_in_corner = df_corner[df_corner['horse_no'] == horse_no]

            if not horse_data_in_corner.empty:
                rank_val = int(horse_data_in_corner.index[0])
                diff_val = float(horse_data_in_corner['diff'].iloc[0])
                side_val = int(horse_data_in_corner['side'].iloc[0])

                features[f'rank_{corner_label}'] = rank_val
                features[f'diff_{corner_label}'] = diff_val
                features[f'side_{corner_label}'] = side_val

                horse_metrics_timeseries['rank'][corner_label] = rank_val
                horse_metrics_timeseries['diff'][corner_label] = diff_val
                horse_metrics_timeseries['side'][corner_label] = side_val
            else:
                for metric_name in ['rank', 'diff', 'side']:
                    features[f'{metric_name}_{corner_label}'] = np.nan
                    horse_metrics_timeseries[metric_name][corner_label] = np.nan

    def _calculate_corner_changes(self, horse_metrics_timeseries: Dict[str, Dict[str, float]], sorted_corner_labels: List[str], features: Dict) -> None:
        """コーナー間の変化量を計算し、featuresを更新する"""
        for i in range(len(sorted_corner_labels) - 1):
            c1_label, c2_label = sorted_corner_labels[i], sorted_corner_labels[i+1]
            for metric_name in ['rank', 'diff', 'side']:
                m1 = horse_metrics_timeseries[metric_name].get(c1_label, np.nan) # 存在しないキーの場合はnp.nan
                m2 = horse_metrics_timeseries[metric_name].get(c2_label, np.nan) # 存在しないキーの場合はnp.nan
                change_val = float(m2 - m1) if pd.notna(m1) and pd.notna(m2) else np.nan
                features[f'{metric_name}_change_{c1_label}_to_{c2_label}'] = change_val

    def _calculate_aggregate_features(self, horse_metrics_timeseries: Dict[str, Dict[str, float]], sorted_corner_labels: List[str], features: Dict) -> None:
        """集計特徴量を計算し、featuresを更新する"""
        for metric_name in ['rank', 'diff', 'side']:
            valid_values = [
                horse_metrics_timeseries[metric_name][cl]
                for cl in sorted_corner_labels
                if cl in horse_metrics_timeseries[metric_name] and pd.notna(horse_metrics_timeseries[metric_name][cl]) # キーの存在確認とNaNチェック
            ]
            if valid_values:
                features[f'avg_{metric_name}'] = float(np.nanmean(valid_values))
                features[f'min_{metric_name}'] = float(np.nanmin(valid_values))
                features[f'max_{metric_name}'] = float(np.nanmax(valid_values))
                features[f'std_{metric_name}'] = float(np.nanstd(valid_values)) if len(valid_values) > 1 else 0.0
                features[f'first_{metric_name}'] = float(valid_values[0])
                features[f'last_{metric_name}'] = float(valid_values[-1])
                features[f'total_{metric_name}_change'] = float(valid_values[-1] - valid_values[0]) if len(valid_values) > 1 else 0.0
                features[f'available_corners_count_{metric_name}'] = int(len(valid_values))
                features[f'missing_corners_count_{metric_name}'] = int(len(sorted_corner_labels) - len(valid_values))
            else:
                for agg_func_prefix in ['avg', 'min', 'max', 'std', 'first', 'last']:
                    features[f'{agg_func_prefix}_{metric_name}'] = np.nan
                features[f'total_{metric_name}_change'] = np.nan
                features[f'available_corners_count_{metric_name}'] = int(0)
                features[f'missing_corners_count_{metric_name}'] = int(len(sorted_corner_labels))


    def generate_corner_features_per_horse(self, corner_data_map: Dict[str, pd.DataFrame], all_horse_numbers: Optional[List[int]] = None) -> pd.DataFrame:
        """
        複数のコーナーデータから、馬番ごとに特徴量を生成します。
        """
        if not corner_data_map:
            self.logger.warning("コーナーデータが空のため、特徴量生成をスキップします。")
            return pd.DataFrame()

        if all_horse_numbers is None:
            unique_horses = set()
            for df_corner in corner_data_map.values():
                if 'horse_no' in df_corner.columns:
                    unique_horses.update(df_corner['horse_no'].unique())
            all_horse_numbers = sorted(list(unique_horses))

        if not all_horse_numbers:
            self.logger.warning("対象の馬番が存在しないため、特徴量生成をスキップします。")
            return pd.DataFrame()

        horse_features_list = []
        sorted_corner_labels = sorted(corner_data_map.keys())

        for horse_no in all_horse_numbers:
            features = {'horse_no': horse_no}
            horse_metrics_timeseries: Dict[str, Dict[str, float]] = {metric: {} for metric in ['rank', 'diff', 'side']}

            self._extract_corner_metrics(horse_no, corner_data_map, sorted_corner_labels, features, horse_metrics_timeseries)
            self._calculate_corner_changes(horse_metrics_timeseries, sorted_corner_labels, features)
            self._calculate_aggregate_features(horse_metrics_timeseries, sorted_corner_labels, features)

            horse_features_list.append(features)

        final_df = pd.DataFrame(horse_features_list)
        if 'horse_no' in final_df.columns:
            final_df = final_df.set_index('horse_no')
            final_df.index.name = 'horse_no'
        return final_df


    def extract_corner_features_from_html_file(self, html_path: str) -> Optional[pd.DataFrame]:
        """
        単一のレースHTMLファイルからコーナー特徴量を抽出する
        RaceProcessor の _extract_single_race_corner_features を移植・改変
        """
        self.logger.debug(f"単一HTMLからのコーナー特徴量抽出開始: {html_path}")
        race_id = os.path.basename(html_path).replace(".bin", "")
        soup = None
        try:
            with open(html_path, "rb") as f:
                html_content = f.read() # ファイル読み込み
            soup = self.html_parser._get_soup_from_html(html_content, html_path) # RaceHtmlParserのメソッドを利用
        except Exception as e:
            self.logger.error(f"HTMLファイル読み込みまたは初期パース失敗 ({html_path}): {e}", exc_info=True)
            return None

        if not soup:
            self.logger.warning(f"HTMLパース失敗、コーナー特徴量抽出スキップ: {html_path}")
            return None

        # soup が None でない場合の処理をここから開始
        try:
            corner_data_map = self._extract_corner_passing_data_from_soup(soup)
            if not corner_data_map:
                self.logger.debug(f"コーナー通過順位データが見つかりません: {html_path}")
                return None

            horse_numbers = self._get_horse_numbers_from_corner_data(corner_data_map)
            if not horse_numbers:
                self.logger.debug(f"出走馬番が見つかりません: {html_path}")
                return None

            corner_features_df = self.generate_corner_features_per_horse(corner_data_map, horse_numbers)
            if corner_features_df.empty:
                self.logger.debug(f"コーナー特徴量DataFrameが空です: {html_path}")
                return None

            corner_features_df = corner_features_df.reset_index()
            corner_features_df['race_id'] = race_id

            # RaceHtmlParser を使ってレース情報と結果をパース
            # parse_race_html の代わりに、soupを受け取るメソッドを使用
            race_info_df_full = self.html_parser.parse_race_info_from_soup(soup, race_id)
            results_df_full = self.html_parser.parse_race_results_from_soup(soup, race_id)
            results_df = self._extract_results_for_corner_analysis_from_df(results_df_full, race_id) # results_df_full は pd.DataFrame

            if not results_df.empty and {ResultsCols.UMABAN, ResultsCols.HORSE_ID}.issubset(results_df.columns):
                results_df[ResultsCols.UMABAN] = pd.to_numeric(results_df[ResultsCols.UMABAN], errors='coerce')
                # corner_features_df の horse_no も数値に変換
                corner_features_df['horse_no'] = pd.to_numeric(corner_features_df['horse_no'], errors='coerce')

                valid_mapping_rows = results_df.dropna(subset=[ResultsCols.UMABAN, ResultsCols.HORSE_ID])
                if not valid_mapping_rows.empty:
                    horse_id_map_series = valid_mapping_rows.set_index(ResultsCols.UMABAN)[ResultsCols.HORSE_ID]
                    # 重複する馬番がある場合（通常はないはずだが）、最初に見つかったものを使用
                    if not horse_id_map_series.index.is_unique:
                        horse_id_map_series = horse_id_map_series[~horse_id_map_series.index.duplicated(keep='first')]
                    corner_features_df['horse_id'] = corner_features_df['horse_no'].map(horse_id_map_series.to_dict())
            else:
                corner_features_df['horse_id'] = pd.NA
            
            race_date_val = None
            # race_info_df ではなく race_info_df_full を使用すべき
            if not race_info_df_full.empty and RaceInfoCols.DATE in race_info_df_full.columns and len(race_info_df_full) > 0:
                race_date_val = race_info_df_full[RaceInfoCols.DATE].iloc[0]

            if race_date_val is not None:
                try:
                    # まず特定のフォーマットで試行
                    dt_converted = pd.to_datetime(race_date_val, format="%Y年%m月%d日", errors='raise')
                except (ValueError, TypeError): # TypeErrorも考慮（race_date_valが予期せぬ型の場合）
                    # 特定のフォーマットで失敗した場合、Pandasの自動推論に任せる
                    dt_converted = pd.to_datetime(race_date_val, errors='coerce')

                if pd.isna(dt_converted):
                    # それでもパースできない場合はrace_idから推測
                    self.logger.warning(f"日付のパースに失敗 ({race_id}): 元の値='{race_date_val}'。race_idから推測します。")
                    dt_converted = pd.to_datetime(race_id[:8], format='%Y%m%d', errors='coerce')

                if not pd.isna(dt_converted):
                    corner_features_df['date'] = dt_converted
            else:
                corner_features_df['date'] = pd.to_datetime(race_id[:8], format='%Y%m%d', errors='coerce') if len(race_id) >= 8 else pd.NaT # type: ignore

            return corner_features_df
        except Exception as e:
            self.logger.error(f"単一HTMLからのコーナー特徴量抽出処理エラー ({html_path}): {e}", exc_info=True)
            return None


    def extract_corner_features_from_html_files(self,
        html_paths: List[str],
        parallel: bool = True,
        max_workers: Optional[int] = None
    ) -> Optional[pd.DataFrame]:
        self.logger.info(f"extract_corner_features_from_html_files が呼び出されました。HTMLファイル数: {len(html_paths)}")
        """
        複数のHTMLファイルからコーナー特徴量を抽出し、結合して返す。

        Args:
            html_paths (List[str]): HTMLファイルのパスのリスト。
            parallel (bool, optional): 並列処理を行うかどうか。デフォルトはTrue。
            max_workers (Optional[int], optional): 並列処理の最大ワーカー数。
                                                Noneの場合はCPUコア数。デフォルトはNone。

        Returns:
            Optional[pd.DataFrame]: 抽出されたコーナー特徴量を含むDataFrame。
                                    エラーが発生した場合はNoneを返す。
        """
        self.logger.info(f"複数のHTMLファイルからのコーナー特徴量抽出を開始 ({len(html_paths)}件)")
        if not html_paths:
            self.logger.warning("処理対象のHTMLファイルがありません。")
            return None

        all_corner_features_list = []

        num_workers = max_workers if max_workers is not None else (os.cpu_count() or 1)

        if parallel and len(html_paths) > 1:
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                future_to_path = {
                    executor.submit(self.extract_corner_features_from_html_file, path): path
                    for path in html_paths
                }
                for future in tqdm(as_completed(future_to_path), total=len(html_paths), desc="コーナー特徴量抽出(並列)"):
                    path_completed = future_to_path[future]
                    try:
                        result_df = future.result()
                        if result_df is not None and not result_df.empty:
                            all_corner_features_list.append(result_df)
                    except Exception as e:
                        self.logger.error(f"ファイル {path_completed} のコーナー特徴量抽出中にエラー: {e}", exc_info=True)
        else:
            for path in tqdm(html_paths, desc="コーナー特徴量抽出(逐次)"):
                try:
                    result_df = self.extract_corner_features_from_html_file(path)
                    if result_df is not None and not result_df.empty:
                        all_corner_features_list.append(result_df)
                except Exception as e:
                    self.logger.error(f"ファイル {path} のコーナー特徴量抽出中にエラー: {e}", exc_info=True)

        if not all_corner_features_list:
            self.logger.warning("有効なコーナー特徴量が抽出されませんでした。")
            return None

        final_df = pd.concat(all_corner_features_list, ignore_index=True)
        self.logger.info(f"コーナー特徴量抽出完了。合計 {len(final_df)}件")
        return final_df

    def _recalculate_aggregate_features_after_imputation(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        欠損値補完後に集計特徴量を再計算
        """
        corner_order = CornerOrder.ORDERED_LABELS # 定数を使用

        for metric in ['rank', 'diff', 'side']:
            corner_cols = [f'{metric}_{corner}' for corner in corner_order if f'{metric}_{corner}' in df.columns]

            if corner_cols:
                for idx in df.index:
                    values = [df.loc[idx, col] for col in corner_cols if pd.notna(df.loc[idx, col])]
                    if values:
                        # np.nanmean を使用して欠損値を無視した平均を計算する方が堅牢です
                        df.loc[idx, f'avg_{metric}'] = float(np.nanmean(values))
                        df.loc[idx, f'min_{metric}'] = float(np.min(values))
                        df.loc[idx, f'max_{metric}'] = float(np.max(values))
                        df.loc[idx, f'std_{metric}'] = float(np.std(values)) if len(values) > 1 else 0.0
                        df.loc[idx, f'first_{metric}'] = float(values[0])
                        df.loc[idx, f'last_{metric}'] = float(values[-1])
                        df.loc[idx, f'total_{metric}_change'] = float(values[-1] - values[0]) if len(values) > 1 else 0.0
                        df.loc[idx, f'available_corners_count_{metric}'] = int(len(values))
                        df.loc[idx, f'missing_corners_count_{metric}'] = int(len(corner_cols) - len(values))
                    else:
                        for agg_func_prefix in ['avg', 'min', 'max', 'std', 'first', 'last']:
                            df.loc[idx, f'{agg_func_prefix}_{metric}'] = np.nan
                        df.loc[idx, f'total_{metric}_change'] = np.nan
                        df.loc[idx, f'available_corners_count_{metric}'] = int(0)
                        df.loc[idx, f'missing_corners_count_{metric}'] = int(len(corner_cols))
        return df


    def apply_corner_imputation(self, features_df: pd.DataFrame, strategy: str) -> pd.DataFrame:
        """
        コーナー特徴量の欠損値補完を実行

        Parameters
        ----------
        features_df : pd.DataFrame
            特徴量DataFrame
        strategy : str
            補完戦略 ("forward_fill", "backward_fill", "interpolate", "race_average", "none")

        Returns
        -------
        pd.DataFrame
            補完済みのDataFrame
        """
        df = features_df.copy()
        if strategy == "none" or df.empty:
            return df

        self.logger.info(f"コーナー特徴量の欠損値補完を開始 (戦略: {strategy})")
        original_missing_count = df.isnull().sum().sum()

        corner_order = CornerOrder.ORDERED_LABELS # 定数を使用

        if strategy == "forward_fill":
            for metric in ['rank', 'diff', 'side']:
                corner_cols = [f'{metric}_{corner}' for corner in corner_order if f'{metric}_{corner}' in df.columns]
                if corner_cols: df[corner_cols] = df[corner_cols].ffill(axis=1)
        elif strategy == "backward_fill":
            for metric in ['rank', 'diff', 'side']:
                corner_cols = [f'{metric}_{corner}' for corner in corner_order if f'{metric}_{corner}' in df.columns]
                if corner_cols: df[corner_cols] = df[corner_cols].bfill(axis=1)
        elif strategy == "interpolate":
            for metric in ['rank', 'diff', 'side']:
                corner_cols = [f'{metric}_{corner}' for corner in corner_order if f'{metric}_{corner}' in df.columns]
                if len(corner_cols) >= 2: df[corner_cols] = df[corner_cols].interpolate(method='linear', axis=1)
        elif strategy == "race_average":
            if 'race_id' in df.columns:
                for metric in ['rank', 'diff', 'side']:
                    corner_cols = [f'{metric}_{corner}' for corner in corner_order if f'{metric}_{corner}' in df.columns]
                    for col in corner_cols:
                        if col in df.columns:
                            df[col] = df.groupby('race_id')[col].transform(lambda x: x.fillna(x.mean()))
            else:
                self.logger.warning("race_idカラムがないため、race_average補完はスキップされます。")

        df = self._recalculate_aggregate_features_after_imputation(df)

        final_missing_count = df.isnull().sum().sum()
        self.logger.info(f"欠損値補完完了。欠損数: {original_missing_count} -> {final_missing_count} (減少: {original_missing_count - final_missing_count})")
        return df