#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
レースデータに関する高レベルな操作を提供するモジュール
ファイルI/Oや複雑な特徴量エンジニアリングは他のクラスに委譲する
"""

import datetime
import glob
import io
import logging
import os
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import pickle
import numpy as np
import pandas as pd
from bs4 import BeautifulSoup
from tqdm.auto import tqdm # type: ignore
from core.utils.constants import LocalPaths, Master, RaceInfoCols, RaceProcessorConstants, ResultsCols, CornerDiffConstants
from core.processors.race_html_parser import RaceHtmlParser
from core.processors.race_file_handler import RaceFileHandler
from core.processors.corner_analyzer import CornerAnalyzer
from core.processors.race_data_preprocessor import RaceDataPreprocessor

class RaceProcessor: # RaceProcessorConstants から正規表現をインポートして利用
    """
    レース情報と結果を統合して処理するクラス
    HTMLファイルからのデータ抽出と前処理を行う
    """
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初期化

        Parameters
        ----------
        config : Dict[str, Any], optional
            処理の設定情報
        """ # type: ignore
        self._config: Dict[str, Any] = config or {}
        self._race_info_df = pd.DataFrame()
        self._race_results_df = pd.DataFrame()
        self.logger = logging.getLogger(__name__)
        # 依存するパーサーとプリプロセッサを初期化
        self._html_parser: RaceHtmlParser = RaceHtmlParser()
        self._preprocessor: RaceDataPreprocessor = RaceDataPreprocessor()
        self._file_handler: Optional[RaceFileHandler] = None # ファイルハンドラは必要に応じて初期化


    def extract_horse_ids_from_race_data(
        self,
        year: Optional[str] = None,
        race_id: Optional[str] = None,
        save_csv: bool = False,
        use_cache: bool = True,
        parallel: bool = True,
        max_workers: int = 4,
    ) -> List[str]:
        """
        レース結果から馬IDを抽出する（高速化版）

        Args:
            year (Optional[str], optional): 処理する年
            race_id (Optional[str], optional): 処理する特定のレースID
            save_csv (bool, optional): 抽出した馬IDをCSVとして保存するかどうか
            use_cache (bool, optional): キャッシュを使用するかどうか
            parallel (bool, optional): 並列処理を使用するかどうか
            max_workers (int, optional): 並列処理の最大ワーカー数

        Returns:
            list: 抽出した馬IDのリスト
        """
        # ファイルハンドラが未初期化の場合、初期化する
        if self._file_handler is None:
            self._file_handler = RaceFileHandler(html_parser=self._html_parser, corner_analyzer=CornerAnalyzer(), config=self._config)
        return self._file_handler.extract_horse_ids_from_race_data(year, race_id, save_csv, use_cache, parallel, max_workers)



    # extract_horse_ids_from_html メソッドは削除
    # RaceHtmlParser.extract_horse_ids_from_html() を直接使用してください


    # parse_race_html メソッドは削除
    # RaceHtmlParser.parse_race_html() を直接使用してください

    # merge_past_horse_performance メソッドは削除
    # RaceFeatureEngineer.merge_past_horse_performance() を直接使用してください

    # extract_horse_sex_age_mapping メソッドは削除
    # RaceFeatureEngineer.extract_horse_sex_age_mapping() を直接使用してください


    def preprocess_data(self) -> pd.DataFrame:
        """
        レースデータの前処理を行い、結果を返す

        Returns
        -------
        pd.DataFrame
            前処理後の結合データ
        """
        # _preprocessor は __init__ で初期化済みのため、ここで再初期化する必要はありません。
        # self._preprocessor = RaceDataPreprocessor()
        return self._preprocessor.preprocess_data(self._race_info_df, self._race_results_df) # RaceDataPreprocessorに委譲


    def get_race_features(self) -> pd.DataFrame:
        """
        レース特徴量を取得

        Returns
        -------
        pd.DataFrame
            レース特徴量
        """
        if self._race_info_df.empty:
            self.logger.warning("レース情報がありません。process_race_bin_filesメソッドを先に実行してください。")
            return pd.DataFrame()

        # レース特徴量を抽出
        race_features = self._race_info_df.copy()

        # レース特徴量として使用するカラム
        race_feature_cols = [
            'race_id',
            RaceInfoCols.DATE,
            RaceInfoCols.VENUE,
            RaceInfoCols.WEATHER,
            RaceInfoCols.GROUND_STATE,
        ]

        # 存在するカラムのみを選択
        existing_columns = [col for col in race_feature_cols if col in race_features.columns]

        return race_features[existing_columns]

    def select_columns(self, df=None, columns=None) -> pd.DataFrame:
        """
        必要なカラムを選択

        Parameters
        ----------
        df : pd.DataFrame, optional
            選択対象のDataFrame。指定しない場合はpreprocess_dataメソッドの結果を使用。
        columns : List[str], optional
            選択するカラムのリスト。Noneの場合はデフォルトのカラムを使用

        Returns
        -------
        pd.DataFrame
            選択後のデータフレーム
        """
        if df is None:
            df = self.preprocess_data()

        if df.empty:
            return pd.DataFrame()

        # 選択するカラムが指定されていない場合はデフォルトのカラムを使用
        if columns is None:
            columns = self._config.get('selected_columns', [
                ResultsCols.WAKUBAN,
                ResultsCols.UMABAN,
                ResultsCols.KINRYO,
                ResultsCols.TANSHO_ODDS,
                'horse_id',
                '性',
                '年齢',
                'n_horses',
                'rank_binary' # _preprocess_rank で生成される列名 'rank_binary'
            ])

        # 存在するカラムのみを選択
        existing_columns = [col for col in columns if col in df.columns]

        return df[existing_columns]

    def process_race_bin_files(self, year: Optional[str] = None, race_id: Optional[str] = None,
                               parallel: bool = True, max_workers: Optional[int] = None,
                               max_files: Optional[int] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        指定された年またはレースIDのレース情報binファイルを処理し、
        レース情報とレース結果のDataFrameを生成・更新する。

        Parameters
        ----------
        year : str, optional
            処理する年。Noneの場合はrace_idが必須。
        race_id : str, optional
            処理する特定のレースID。Noneの場合はyearが必須。
        parallel : bool, default True
            並列処理を使用するかどうか。
        max_workers : int, optional
            並列処理の最大ワーカー数。Noneの場合はCPUコア数。
        max_files : int, optional
            処理するファイルの最大数（年度指定時）。Noneの場合は全ファイルを処理。

        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            (レース情報のDataFrame, レース結果のDataFrame)
        """
        # ファイルハンドラが未初期化の場合、初期化する
        if self._file_handler is None:
            self._file_handler = RaceFileHandler(html_parser=self._html_parser, corner_analyzer=CornerAnalyzer(), config=self._config)
        # RaceFileHandler に処理を委譲し、結果をインスタンス変数に格納
        race_info_df, race_results_df = self._file_handler.process_race_bin_files(
            year=year, race_id=race_id, parallel=parallel, max_workers=max_workers, max_files=max_files
        )

        self._race_info_df = race_info_df
        self._race_results_df = race_results_df

        if not self._race_info_df.empty:
            self.logger.info(f"レース情報: {len(self._race_info_df)}件")
        else:
            self.logger.info("レース情報データは空です。")
        if not self._race_results_df.empty:
            self.logger.info(f"レース結果: {len(self._race_results_df)}件")
        else:
            self.logger.info("レース結果データは空です。")

        return self._race_info_df.copy(), self._race_results_df.copy()

    def _save_batch_results_to_pickle(self, race_info_df: pd.DataFrame,
                                     race_results_df: pd.DataFrame,
                                     corner_features_df: Optional[pd.DataFrame],
                                     year: Optional[str] = None,
                                     race_id: Optional[str] = None,
                                     pickle_dir: Optional[str] = None) -> None:
        """
        バッチ処理結果をpickleファイルに保存する

        Parameters
        ----------
        race_info_df : pd.DataFrame
            レース情報DataFrame
        race_results_df : pd.DataFrame
            レース結果DataFrame
        corner_features_df : Optional[pd.DataFrame]
            コーナー特徴量DataFrame
        year : str, optional
            年
        race_id : str, optional
            レースID
        pickle_dir : str, optional
            保存先ディレクトリ
        """
        # RaceFileHandler に処理を委譲
        # ファイルハンドラが未初期化の場合、初期化する
        if self._file_handler is None:
            self._file_handler = RaceFileHandler(html_parser=self._html_parser, corner_analyzer=CornerAnalyzer(), config=self._config)
        self._file_handler._save_batch_results_to_pickle(
            race_info_df=race_info_df,
            race_results_df=race_results_df,
            corner_features_df=corner_features_df,
            year=year,
            race_id=race_id,
            pickle_dir=pickle_dir
        )


    def save_race_data_to_csv(self, year: Optional[str] = None, race_id: Optional[str] = None,
                              prefix: str = "race_data") -> None:
        """
        処理されたレース情報と結果をCSVファイルに保存する。
        ファイル名には年またはレースID、およびタイムスタンプが含まれる。

        Parameters
        ----------
        year : str, optional
            ファイル名に含める年。
        race_id : str, optional
            ファイル名に含めるレースID。yearより優先される。
        prefix : str, default "race_data"
            ファイル名のプレフィックス。
        """
        # ファイルハンドラが未初期化の場合、初期化する
        if self._file_handler is None:
            self._file_handler = RaceFileHandler(html_parser=self._html_parser, corner_analyzer=CornerAnalyzer(), config=self._config)
        self._file_handler.save_race_data_to_csv(self._race_info_df, self._race_results_df, year, race_id, prefix) # RaceFileHandlerに委譲

    def process_race_pickle_files(self, # 戻り値にコーナー特徴量DataFrameを追加

                                 year: Optional[str] = None,
                                 race_id: Optional[str] = None,
                                 pickle_base_dir: str = os.path.join(LocalPaths.DATA_DIR, "processed"),
                                 race_info_filename_pattern: str = "race_info_{year}.pickle",
                                 race_results_filename_pattern: str = "race_results_{year}.pickle"
                                 ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        指定された年またはレースIDのレース情報pickleファイルを処理し、
        レース情報とレース結果のDataFrameを生成・更新する。

        Parameters
        ----------
        year : str, optional
            処理する年。race_idが指定されていない場合は必須。
        race_id : str, optional
            処理する特定のレースID。指定された場合、yearのデータからフィルタリングする。
        pickle_base_dir : str, optional
            pickleファイルが保存されているベースディレクトリ。
            デフォルトは LocalPaths.DATA_DIR / "processed"
        race_info_filename_pattern : str, optional
            レース情報pickleファイル名のパターン。'{year}'プレースホルダを含む。
            デフォルトは "race_info_{year}.pickle"
        race_results_filename_pattern : str, optional
            レース結果pickleファイル名のパターン。'{year}'プレースホルダを含む。
            デフォルトは "race_results_{year}.pickle"

        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            (レース情報のDataFrame, レース結果のDataFrame)
        """
        # ファイルハンドラが未初期化の場合、初期化する
        if self._file_handler is None:
            self._file_handler = RaceFileHandler(html_parser=self._html_parser, corner_analyzer=CornerAnalyzer(), config=self._config)
        # RaceFileHandler に処理を委譲

        # process_race_pickle_files はコーナー特徴量を返さないため、ここでは無視
        race_info_df, race_results_df, _ = self._file_handler.process_race_pickle_files(
            year=year,
            race_id=race_id,
            pickle_base_dir=pickle_base_dir,
            race_info_filename_pattern=race_info_filename_pattern,
            race_results_filename_pattern=race_results_filename_pattern
            # corner_features_filename_pattern はこのメソッドのシグネチャにないのでデフォルト値が使われる
        )

        self._race_info_df = race_info_df
        self._race_results_df = race_results_df

        return self._race_info_df.copy(), self._race_results_df.copy()

    # process_race_bin_to_yearly_pickles メソッドを RaceFileHandler に移動
    def process_race_bin_to_yearly_pickles(self,
                                           years: List[str],
                                           bin_base_dir: Optional[str] = None,
                                           output_dir: Optional[str] = None,
                                           parallel: bool = True,
                                           max_workers: Optional[int] = None,
                                           max_files_per_year: Optional[int] = None, # 各年で処理する最大ファイル数を追加
                                           include_corner_features: bool = False, # デフォルトをFalseに変更（呼び出し側で明示的に指定する想定）
                                           corner_imputation_strategy: str = "none"
                                           ) -> Tuple[pd.DataFrame, pd.DataFrame, Optional[pd.DataFrame]]:
        """
        指定された複数年のレース情報binファイルを処理し、年ごとにレース情報、
        レース結果、およびオプションでコーナー特徴量のDataFrameをpickleファイルとして保存する。

        Parameters
        ----------
        years : List[str]
            処理する年のリスト。
        bin_base_dir : str, optional
            .binファイルが格納されているベースディレクトリ。
            デフォルトは LocalPaths.HTML_RACE_DIR。
        output_dir : str, optional
            pickleファイルの保存先ディレクトリ。
            デフォルトは LocalPaths.DATA_DIR / "processed"。
        parallel : bool, default True
            並列処理を使用するかどうか。
        max_workers : int, optional
            並列処理の最大ワーカー数。Noneの場合はCPUコア数。
        max_files_per_year : int, optional
            各年で処理する最大のファイル数。Noneの場合は全ファイルを処理。
        include_corner_features : bool, default True
            コーナー特徴量を生成し保存するかどうか。
        corner_imputation_strategy : str, default "none"
            コーナー特徴量の欠損値補完戦略。
        """
        # ファイルハンドラが未初期化の場合、初期化する
        if self._file_handler is None:
            self._file_handler = RaceFileHandler(html_parser=self._html_parser, corner_analyzer=CornerAnalyzer(), config=self._config)
        return self._file_handler.process_race_bin_to_yearly_pickles(
            years=years,
            bin_base_dir=bin_base_dir,
            output_dir=output_dir,
            parallel=parallel,
            max_workers=max_workers,
            max_files_per_year=max_files_per_year,
            include_corner_features=include_corner_features,
            corner_imputation_strategy=corner_imputation_strategy
        )