import io
import logging
import os
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd
from bs4 import BeautifulSoup, Tag
from tqdm.auto import tqdm

from core.utils.constants import HorseInfoCols, HorseResultsCols


HORSE_INFO_KEYWORDS = ['生年月日', '調教師', '馬主', '生産者', '産地', '獲得賞金']
PEDIGREE_FATHER_ROWSPAN = "16" # 血統表の父のrowspan値 (例)
PEDIGREE_MOTHER_ROWSPAN = "16" # 血統表の母のrowspan値 (例)
PEDIGREE_MOTHER_FATHER_ROWSPAN = "8"  # 血統表の母父のrowspan値 (例)

class HorseHtmlParser:
    """
    馬関連のHTMLファイル（基本情報、過去成績）をパースし、
    前処理前のDataFrameを生成するクラス。
    """

    def __init__(self):
        """
        初期化
        """
        self.logger = logging.getLogger(__name__)

    def _search_horse_id_by_name(self, horse_name: str, soup: BeautifulSoup) -> str:
        """
        馬名からHTMLページ内で馬IDを検索する

        Parameters
        ----------
        horse_name : str
            検索する馬名
        soup : BeautifulSoup
            HTMLのBeautifulSoupオブジェクト

        Returns
        -------
        str
            見つかった馬ID、見つからない場合は空文字
        """
        try:
            # 全ての馬のリンクを検索
            # /horse/12345 や /horse/ped/abcdef の両方に対応
            all_horse_links = soup.find_all("a", href=re.compile(r"/horse/(?:ped/)?\w+"))

            for link in all_horse_links:
                link_text = link.get_text(strip=True)
                if link_text == horse_name:
                    href = link.get("href")
                    if not href or not isinstance(href, str):
                        continue

                    # 血統ページのリンクから馬IDを抽出 (例: /horse/ped/foobar)
                    ped_match = re.search(r"/horse/ped/(\w+)", href)
                    if ped_match:
                        return ped_match.group(1)

                    # 通常の馬ページのリンクから馬IDを抽出 (例: /horse/1234567890)
                    horse_match = re.search(r"/horse/(\d+)", href)
                    if horse_match:
                        return horse_match.group(1)

            return ""
        except Exception as e:
            self.logger.warning(f"馬名 '{horse_name}' から馬IDの検索中にエラー: {e}")
            return ""

    def parse_horse_info_html(self, html_path: str) -> Optional[pd.DataFrame]:
        """
        単一の馬の基本情報HTMLファイルをパースする。

        Parameters
        ----------
        html_path : str
            馬の基本情報のHTMLファイルパス

        Returns
        -------
        Optional[pd.DataFrame]
            パース結果のDataFrame。失敗した場合はNone。
        """
        try:
            with open(html_path, 'rb') as f:
                html_content = f.read()

            soup: Optional[BeautifulSoup] = None
            try:
                soup = BeautifulSoup(html_content, "lxml", from_encoding="euc-jp")
            except Exception as e_eucjp:
                self.logger.warning(f"euc-jpでのデコードに失敗したため、utf-8で試行します: {html_path}, エラー: {e_eucjp}")
                try:
                    soup = BeautifulSoup(html_content, "lxml", from_encoding="utf-8")
                except Exception as e_utf8:
                    self.logger.error(f"HTMLのデコード/パースに失敗しました: {html_path}, エラー: {e_utf8}", exc_info=True)
                    return None

            if not soup or not soup.body: # HTMLとしてパースできなかった場合（空のファイルなど）
                self.logger.warning(f"HTMLのパース結果が空です: {html_path}")
                return None

            # テーブルを読み込む
            # BeautifulSoupでパースしたHTMLをStringIOでpd.read_htmlに渡す
            tables = pd.read_html(io.StringIO(str(soup)))
            if not tables:
                self.logger.warning(f"HTMLからテーブルを読み込めませんでした: {html_path}")
                return None

            # 馬の基本情報テーブルを探す
            base_df_from_html = None
            table_index = -1

            # 各テーブルをチェックして、馬の基本情報らしいテーブルを見つける
            for i, table in enumerate(tables):
                if table.empty or len(table.columns) < 2:
                    continue

                # 0列目に馬の基本情報項目があるかチェック
                if 0 in table.columns:
                    first_col_values = table[0].dropna().astype(str).tolist()
                    # 馬の基本情報項目が含まれているかチェック
                    if any(keyword in ' '.join(first_col_values) for keyword in HORSE_INFO_KEYWORDS):
                        base_df_from_html = table
                        table_index = i
                        break

            # 馬の基本情報テーブルが見つからない場合、従来通りtables[1]を使用
            if base_df_from_html is None:
                if len(tables) >= 2:
                    base_df_from_html = tables[1]
                    table_index = 1
                else:
                    self.logger.warning(f"{html_path} で馬の基本情報テーブルが見つかりません。スキップします。")
                    return None

            # テーブルが空、または0列目が存在しない、または0列目がすべてNaNなどの場合をチェック (KeyError対策も)
            if base_df_from_html.empty or 0 not in base_df_from_html.columns or base_df_from_html[0].isnull().all():
                self.logger.warning(f"{html_path} のテーブル[{table_index}]が実質的に空か、0列目が有効ではありません。スキップします。")
                return None

            # 重複がある場合は重複を削除してから処理
            if base_df_from_html[0].duplicated().any():
                base_df_from_html = base_df_from_html.drop_duplicates(subset=[0], keep='first')

            # 0列目をインデックスにして転置
            df_info_raw = base_df_from_html.set_index(0).T

            # 抽出した情報を格納する辞書
            df_info = df_info_raw.iloc[0].to_dict() if not df_info_raw.empty else {}

            self._extract_basic_ids_from_profile_table(soup, df_info)
            self._extract_pedigree_info(soup, df_info)
            self._extract_sibling_info(soup, df_info)

            # 最終的なDataFrameを作成
            horse_id = os.path.basename(html_path).replace(".bin", "")
            df_info_final = pd.DataFrame([df_info])
            df_info_final.index = [horse_id]
            df_info_final.index.name = 'horse_id' # インデックス名を明示的に設定

            return df_info_final

        except Exception as e:
            self.logger.error(f"馬基本情報HTMLパースエラー: {html_path} - {e}", exc_info=True)
            return None

    def _extract_basic_ids_from_profile_table(self, soup: BeautifulSoup, df_info: Dict[str, Any]):
        """
        プロフィールテーブルから調教師、馬主、生産者のIDを抽出する。
        """
        try:
            # 馬名をHTMLタイトルから取得
            horse_name = ""
            title_tag = soup.find("title")
            if title_tag:
                title_text = title_tag.get_text(strip=True)
                if "|" in title_text:
                    horse_name = title_text.split("|")[0].strip()
                else:
                    horse_name = title_text.strip()
            df_info[HorseInfoCols.HORSE_NAME] = horse_name

            profile_table = soup.find("table", attrs={"summary": "のプロフィール"})
            if not profile_table:
                return

            # 調教師ID
            trainer_links = profile_table.find_all("a", attrs={"href": re.compile("^/trainer")})
            if trainer_links and trainer_links[0].get("href"):
                href = trainer_links[0].get("href")
                if isinstance(href, str):
                    match = re.search(r"trainer/(\w+)", href)
                    if match:
                        df_info['trainer_id'] = match.group(1)

            # 馬主ID
            owner_links = profile_table.find_all("a", attrs={"href": re.compile("^/owner")})
            if owner_links and owner_links[0].get("href"):
                href = owner_links[0].get("href")
                if isinstance(href, str):
                    match = re.search(r"owner/(\w+)", href)
                    if match:
                        df_info['owner_id'] = match.group(1)

            # 生産者ID
            breeder_links = profile_table.find_all("a", attrs={"href": re.compile("^/breeder")})
            if breeder_links and breeder_links[0].get("href"):
                href = breeder_links[0].get("href")
                if isinstance(href, str):
                    match = re.search(r"breeder/(\w+)", href)
                    if match:
                        df_info['breeder_id'] = match.group(1)
        except Exception as e:
            # ログレベルをwarningに上げるか、エラー内容に応じて調整
            self.logger.warning(f"プロフィールテーブルからの基本ID抽出エラー: {e}", exc_info=True)


    def _extract_pedigree_info(self, soup: BeautifulSoup, df_info: Dict[str, Any]):
        """
        馬情報ページの血統表から父、母、母父の情報を抽出する。
        """
        try:
            # 血統表テーブルを class="blood_table" で取得
            pedigree_table = soup.find("table", class_="blood_table")
            if not pedigree_table:
                return

            rows = pedigree_table.find_all("tr") # type: ignore

            # デバッグ用ログ出力
            # self.logger.debug('【血統表デバッグ】')
            # for i, row in enumerate(rows):
            #     cells = row.find_all(['td', 'th']) if hasattr(row, 'find_all') else []
            #     cell_texts = [cell.get_text(strip=True) for cell in cells]
            #     self.logger.debug(f'row[{i}]: {cell_texts}')

            # 父 (通常は最初の行、最初のtd)
            if len(rows) > 0 and rows[0]:
                first_td = rows[0].find("td")
                if first_td:
                    father_link = first_td.find("a", href=re.compile(r"/horse/ped/"))
                    if father_link:
                        df_info[HorseInfoCols.FATHER_NAME] = father_link.get_text(strip=True)
                        href = father_link.get("href")
                        if href and isinstance(href, str):
                            match = re.search(r"/horse/ped/(\w+)", href)
                            if match:
                                df_info[HorseInfoCols.FATHER_ID] = match.group(1)

            # 母と母父の情報は、HTML構造によって取得方法が変わる可能性がある
            # 一般的な5代血統表の構造を想定 (母が2行目、母父が3行目、または母がrowspanで大きく取られている場合など)
            # ここでは、提供されたHTMLの構造が不明なため、より汎用的な探索は難しいですが、
            # rowspanを考慮した複雑な構造でなければ、以下のようにtdタグを直接探す方がシンプルです。

            # 母 (2行目、最初のtdを想定)
            if len(rows) > int(PEDIGREE_MOTHER_ROWSPAN) / int(PEDIGREE_MOTHER_FATHER_ROWSPAN): # 母のセルが存在しうる行か
                # rowspan="8" のtdが母、その次のtdが母父、という構造を想定
                # もしくは、単純に2行目、3行目と続く場合
                mother_row_index = 1 # デフォルトは2行目
                # 父のrowspanが1より大きい場合、母の行インデックスを調整する必要がある
                # 例: 父のrowspanが2なら、母はrows[2]から始まる
                
                # 簡略化のため、ここでは単純にインデックスでアクセス
                if len(rows) > mother_row_index and rows[mother_row_index]:
                    mother_tds = rows[mother_row_index].find_all("td")
                    if mother_tds: # 最初のtdが母
                        mother_link = mother_tds[0].find("a", href=re.compile(r"/horse/ped/"))
                        if mother_link:
                            df_info[HorseInfoCols.MOTHER_NAME] = mother_link.get_text(strip=True)
                            href = mother_link.get("href")
                            if href and isinstance(href, str):
                                match = re.search(r"/horse/ped/(\w+)", href)
                                if match:
                                    df_info[HorseInfoCols.MOTHER_ID] = match.group(1)
                        
                        # 同じ行に母父がいる場合 (2番目のtd)
                        if len(mother_tds) > 1:
                             mf_link = mother_tds[1].find("a", href=re.compile(r"/horse/ped/"))
                             if mf_link:
                                df_info[HorseInfoCols.MOTHER_FATHER_NAME] = mf_link.get_text(strip=True)
                                href = mf_link.get("href")
                                if href and isinstance(href, str):
                                    match = re.search(r"/horse/ped/(\w+)", href)
                                    if match:
                                        df_info[HorseInfoCols.MOTHER_FATHER_ID] = match.group(1)
            
            # 母父 (母の次の行、または母のセルの次のセルなど、構造に依存)
            # もし母父が別の行にある場合 (例: 3行目)
            mother_father_row_index = 1 + (int(PEDIGREE_MOTHER_ROWSPAN) // int(PEDIGREE_MOTHER_FATHER_ROWSPAN)) // 2 # 仮
            if (HorseInfoCols.MOTHER_FATHER_ID not in df_info or not df_info[HorseInfoCols.MOTHER_FATHER_ID]) and \
               len(rows) > mother_father_row_index and rows[mother_father_row_index]:
                mf_td = rows[mother_father_row_index].find("td") # 最初のtdを母父と仮定
                if mf_td:
                    mf_link = mf_td.find("a", href=re.compile(r"/horse/ped/"))
                    if mf_link:
                        df_info[HorseInfoCols.MOTHER_FATHER_NAME] = mf_link.get_text(strip=True)
                        href = mf_link.get("href")
                        if href and isinstance(href, str):
                            match = re.search(r"/horse/ped/(\w+)", href)
                            if match:
                                df_info[HorseInfoCols.MOTHER_FATHER_ID] = match.group(1)

        except Exception as e:
            # ログレベルをwarningに上げるか、エラー内容に応じて調整
            self.logger.warning(f"血統情報取得エラー: {e}", exc_info=True)

    def _extract_sibling_info(self, soup: BeautifulSoup, df_info: Dict[str, Any]):
        """
        プロフィールテーブルと近親馬欄から兄弟・近親馬情報を抽出する。
        """
        try:
            profile_table = soup.find("table", attrs={"summary": "のプロフィール"})
            # 馬名をHTMLタイトルから取得
            horse_name = ""
            try:
                title_tag = soup.find("title")
                if title_tag:
                    title_text = title_tag.get_text(strip=True)
                    # タイトルから馬名を抽出（例：「馬名 | 競馬データベース」→「馬名」）
                    if "|" in title_text:
                        horse_name = title_text.split("|")[0].strip()
                    else:
                        horse_name = title_text.strip()
            except Exception:
                pass
            df_info[HorseInfoCols.HORSE_NAME] = horse_name

            # 近親馬情報を取得（プロフィールテーブルのリンクと近親馬欄の両方から）
            sibling_names = []
            sibling_ids = []

            # 方法1: プロフィールテーブルのリンクから兄弟馬を取得
            if profile_table:
                sibling_links = profile_table.find_all("a", attrs={"href": re.compile("^/horse/\\d+/")})
                if len(sibling_links) >= 2:
                    # リンク[0]は通常成績なので、リンク[1]以降が兄弟馬
                    for link in sibling_links[1:]:  # 全ての兄弟馬を取得
                        sibling_name = link.get_text(strip=True)
                        sibling_names.append(sibling_name)

                        # 兄弟馬のIDを取得
                        sibling_href = str(link.get("href", ""))
                        if isinstance(sibling_href, str):
                            sibling_match = re.search(r"horse/(\d+)", sibling_href)
                            if sibling_match:
                                sibling_ids.append(sibling_match.group(1))


            # 方法2: 近親馬欄から追加の近親馬情報を取得
            relative_horse_text = ""
            # df_info は辞書なので、キーの存在を確認
            if HorseInfoCols.RELATIVE_HORSE in df_info:
                relative_horse_value = df_info[HorseInfoCols.RELATIVE_HORSE]
                if pd.notna(relative_horse_value):
                    relative_horse_text = str(relative_horse_value)

                    # 近親馬欄の馬名を区切り文字で分割
                    if relative_horse_text and relative_horse_text != "":
                        # カンマ、全角カンマ、スペースで分割
                        relative_names = re.split(r'[,，、\s]+', relative_horse_text)
                        relative_names = [name.strip() for name in relative_names if name.strip()]

                        # 既存の兄弟馬名と重複しないものを追加
                        for name in relative_names:
                            if name and name not in sibling_names:
                                sibling_names.append(name)

                                # 近親馬欄の馬名からIDを検索する
                                horse_id_from_name = self._search_horse_id_by_name(name, soup)
                                sibling_ids.append(horse_id_from_name if horse_id_from_name else "")

            # 近親馬情報をDataFrameに追加（既存の近親馬列を上書き）
            df_info[HorseInfoCols.RELATIVE_HORSE] = ', '.join(sibling_names) if sibling_names else np.nan
            df_info[HorseInfoCols.SIBLING_IDS] = ', '.join(sibling_ids) if sibling_ids else np.nan
        except Exception as e:
            self.logger.warning(f"近親馬情報取得エラー: {e}", exc_info=True)

    def parse_horse_results_html(self, html_path: str) -> Optional[pd.DataFrame]:
        """
        単一の馬の過去成績HTMLファイルをパースする。

        Parameters
        ----------
        html_path : str
            馬の過去成績のHTMLファイルパス

        Returns
        -------
        Optional[pd.DataFrame]
            パース結果のDataFrame。失敗した場合はNone。
        """
        horse_id = os.path.basename(html_path).replace(".bin", "")
        try:
            with open(html_path, 'rb') as f:
                html_content = f.read()

            soup: Optional[BeautifulSoup] = None
            try:
                soup = BeautifulSoup(html_content, "lxml", from_encoding="euc-jp")
            except Exception as e_eucjp:
                self.logger.warning(f"euc-jpでのデコードに失敗したため、utf-8で試行します: {html_path}, エラー: {e_eucjp}")
                try:
                    soup = BeautifulSoup(html_content, "lxml", from_encoding="utf-8")
                except Exception as e_utf8:
                    self.logger.error(f"HTMLのデコード/パースに失敗しました: {html_path}, エラー: {e_utf8}", exc_info=True)
                    return None

            if not soup or not soup.body: # パース成功しても中身がない場合
                self.logger.warning(f"HTMLのパース結果が空です: {html_path}")
                return None

            df_target = pd.DataFrame()

            # 試行1: BeautifulSoupで特定のクラスを持つテーブルを探し、それをパース
            target_table_html_for_pandas = None
            # より具体的なクラス名から順に試す
            candidate_table_elements = soup.find_all("table", class_=re.compile(r'(db_h_race_results|race_table_01)\s*nk_tb_common'))
            if not candidate_table_elements:
                candidate_table_elements = soup.find_all("table", class_="db_h_race_results")
            if not candidate_table_elements: # さらにフォールバック
                candidate_table_elements = soup.find_all("table", class_="race_table_01")

            if candidate_table_elements:
                target_table_html_for_pandas = str(candidate_table_elements[0])
                self.logger.debug(f"特定のクラスを持つテーブルを発見: {html_path}")

            if target_table_html_for_pandas:
                try:
                    dfs_from_specific_table = pd.read_html(io.StringIO(target_table_html_for_pandas))
                    if dfs_from_specific_table:
                        df_target = dfs_from_specific_table[0]
                        self.logger.debug(f"特定のテーブルからDataFrameをパース成功: {html_path}")
                except ValueError as e:
                    self.logger.warning(f"特定のテーブルからのHTMLパースに失敗: {html_path}, エラー: {e}")
                    df_target = pd.DataFrame() # 失敗時は空のDataFrame

            # 試行2: 試行1で見つからない/パース失敗した場合、HTML全体をpd.read_htmlに渡し、元のロジックで探す
            if df_target.empty: # このifブロックの中のtry-exceptを修正
                self.logger.debug(f"特定のテーブルが見つからないかパース失敗。HTML全体をパース試行: {html_path}")
                try:
                    all_dfs_from_html = pd.read_html(io.StringIO(str(soup))) # soup全体を渡す
                    self.logger.debug(f"HTML全体から {len(all_dfs_from_html)} 個のテーブルを検出: {html_path}")

                    # HorseResultsCols の値を使用
                    expected_keywords = {
                        HorseResultsCols.RANK, HorseResultsCols.HORSE_NAME, HorseResultsCols.JOCKEY,
                        HorseResultsCols.TIME, HorseResultsCols.POPULARITY, HorseResultsCols.TANSHO_ODDS
                    }
                    # 過去成績テーブルらしいテーブルを探す heuristic
                    # (コメントはここに移動しても良い)
                    for idx, candidate_df in enumerate(all_dfs_from_html): # forループはtryの中
                        self.logger.debug(f"候補テーブル {idx}: カラム数={len(candidate_df.columns)}, 行数={len(candidate_df)}, カラム={candidate_df.columns.tolist()}")
                        if len(candidate_df.columns) >= 10 and len(candidate_df) > 0:
                            first_col_name = str(candidate_df.columns[0])
                            if '受賞歴' in first_col_name:
                                self.logger.debug(f"テーブル {idx} は受賞歴テーブルのためスキップ")
                                continue
                            # カラム名に期待されるキーワードが含まれるかチェック
                            if any(keyword in ' '.join(map(str, candidate_df.columns)) for keyword in expected_keywords):
                                df_target = candidate_df
                                self.logger.info(f"HTML全体から過去成績テーブルを発見 (テーブル {idx} - キーワード一致): {html_path}")
                                break
                except (ValueError, IndexError) as e:
                    self.logger.warning(f"HTML全体からのテーブルパースに失敗 (pd.read_html): {html_path}, エラー: {e}")
                    df_target = pd.DataFrame() # 失敗時は空のDataFrame

            if df_target is None or df_target.empty:
                self.logger.info(f'過去成績テーブルが見つかりませんでした: {html_path}')
                return None

            # 新馬の競走馬レビューが付いた場合、列名に0が付与されるため除外
            if len(df_target.columns) > 0 and str(df_target.columns[0]) == '0':
                self.logger.info(f'新馬レビューのためスキップ: {html_path}')
                return None

            # 列名に半角スペースがあれば除去する
            df_target = df_target.rename(columns=lambda x: str(x).replace(' ', ''))

            # インデックスをhorse_idにする
            df_target.index = [horse_id] * len(df_target)
            df_target.index.name = 'horse_id' # インデックス名を明示的に設定

            return df_target

        except FileNotFoundError:
            self.logger.error(f"ファイルが見つかりません: {html_path}")
            return None
        except IndexError:
            self.logger.info(f'成績テーブルなし (IndexError): {html_path}')
            return None
        except ValueError as e: # pd.read_html が "No tables found" を出す場合など
            self.logger.info(f'テーブルなし (ValueError): {html_path} - {e}')
            return None
        except Exception as e:
            self.logger.error(f'馬過去成績のパース中に予期せぬエラー: {html_path} - {e}', exc_info=True)
            return None