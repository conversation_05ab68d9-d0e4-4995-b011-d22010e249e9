#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
データリーケージ検証テストスクリプト（包括版）

特徴量計算でデータリーケージが発生していないことを確認するテスト
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
import logging

# プロジェクトパスの追加
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from core.features.calculators import FeatureCalculators

# ログ設定
logging.basicConfig(level=logging.WARNING)  # ノイズを減らすため


class TestDataLeakagePrevention(unittest.TestCase):
    """データリーケージ防止テスト"""
    
    def setUp(self):
        """テスト前の準備"""
        self.calculator = FeatureCalculators()
        self.base_date = datetime(2024, 1, 1)
        self.target_date = datetime(2024, 6, 1)
        
        # テストデータの作成
        self.horse_results_df, self.target_races, self.future_results = self._create_test_data()
        
    def _create_test_data(self):
        """テスト用のサンプルデータを作成"""
        
        # 過去のレース結果データ
        horse_results = []
        
        # 馬1の過去レース（10レース）
        for i in range(10):
            race_date = self.base_date + timedelta(days=i*30)
            horse_results.append({
                'horse_id': 'horse_001',
                'jockey_id': 'jockey_001',
                'trainer_id': 'trainer_001',
                '日付': race_date,
                '着順': np.random.choice([1, 2, 3, 4, 5, 6, 7, 8], p=[0.2, 0.15, 0.15, 0.15, 0.1, 0.1, 0.1, 0.05]),
                '賞金': np.random.randint(0, 1000) * 10000
            })
        
        # 馬2の過去レース（8レース）
        for i in range(8):
            race_date = self.base_date + timedelta(days=i*30 + 15)
            horse_results.append({
                'horse_id': 'horse_002',
                'jockey_id': 'jockey_002',
                'trainer_id': 'trainer_001',
                '日付': race_date,
                '着順': np.random.choice([1, 2, 3, 4, 5, 6, 7, 8], p=[0.1, 0.1, 0.2, 0.2, 0.15, 0.1, 0.1, 0.05]),
                '賞金': np.random.randint(0, 500) * 10000
            })
        
        horse_results_df = pd.DataFrame(horse_results)
        
        # 予測対象レースデータ
        target_races = pd.DataFrame([
            {
                'horse_id': 'horse_001',
                'jockey_id': 'jockey_001',
                'trainer_id': 'trainer_001',
                'date': self.target_date,
                '馬番': 1,
                '枠番': 1,
                '性齢': '牡3',
                '斤量': 56.0,
                '人気': 1
            },
            {
                'horse_id': 'horse_002',
                'jockey_id': 'jockey_002',
                'trainer_id': 'trainer_001',
                'date': self.target_date,
                '馬番': 2,
                '枠番': 1,
                '性齢': '牝3',
                '斤量': 54.0,
                '人気': 2
            }
        ])
        
        # 未来のレースデータ（リーケージテスト用）
        future_date = datetime(2024, 7, 1)
        future_results = pd.DataFrame([
            {
                'horse_id': 'horse_001',
                'jockey_id': 'jockey_001',
                'trainer_id': 'trainer_001',
                '日付': future_date,
                '着順': 1,  # 未来の1着
                '賞金': 5000000
            },
            {
                'horse_id': 'horse_002',
                'jockey_id': 'jockey_002',
                'trainer_id': 'trainer_001',
                '日付': future_date,
                '着順': 2,
                '賞金': 2000000
            }
        ])
        
        return horse_results_df, target_races, future_results
    
    def test_filter_past_data_basic(self):
        """filter_past_data関数の基本テスト"""
        all_results = pd.concat([self.horse_results_df, self.future_results], ignore_index=True)
        
        # 馬1のフィルタリング
        filtered = self.calculator.filter_past_data(
            all_results, 
            self.target_date, 
            horse_id='horse_001',
            exclude_current_date=True
        )
        
        # アサーション
        self.assertGreater(len(filtered), 0, "過去データが存在するはず")
        self.assertTrue(all(filtered['日付'] < self.target_date), "全て過去のデータであるべき")
        self.assertTrue(all(filtered['horse_id'] == 'horse_001'), "指定された馬のデータのみであるべき")
    
    def test_calculate_win_rate_no_leakage(self):
        """勝率計算でデータリーケージが発生しないことを確認"""
        all_results = pd.concat([self.horse_results_df, self.future_results], ignore_index=True)
        
        # 修正版で勝率を計算
        win_rates = self.calculator.calculate_win_rate(
            self.target_races, 
            all_results,
            column='horse_id',
            race_date_column='date'
        )
        
        # 結果の検証
        self.assertEqual(len(win_rates), len(self.target_races), "出力サイズが一致するべき")
        self.assertTrue(all(win_rates >= 0), "勝率は0以上であるべき")
        self.assertTrue(all(win_rates <= 1), "勝率は1以下であるべき")
        
        # 各馬について、過去データのみが使用されていることを確認
        for idx, row in self.target_races.iterrows():
            horse_id = row['horse_id']
            race_date = row['date']
            
            # 手動で過去データの勝率を計算
            past_data = all_results[
                (all_results['horse_id'] == horse_id) & 
                (all_results['日付'] < race_date)
            ]
            
            if len(past_data) > 0:
                expected_win_rate = (past_data['着順'] == 1).sum() / len(past_data)
                actual_win_rate = win_rates.iloc[idx]
                
                self.assertAlmostEqual(
                    actual_win_rate, expected_win_rate, places=3,
                    msg=f"馬{horse_id}の勝率が期待値と一致しない"
                )
    
    def test_calculate_race_count_no_leakage(self):
        """出走回数計算でデータリーケージが発生しないことを確認"""
        all_results = pd.concat([self.horse_results_df, self.future_results], ignore_index=True)
        
        race_counts = self.calculator.calculate_race_count(
            self.target_races,
            all_results,
            column='horse_id',
            race_date_column='date'
        )
        
        # 各馬について検証
        for idx, row in self.target_races.iterrows():
            horse_id = row['horse_id']
            race_date = row['date']
            
            # 手動で過去データの出走回数を計算
            past_data = all_results[
                (all_results['horse_id'] == horse_id) & 
                (all_results['日付'] < race_date)
            ]
            
            expected_count = len(past_data)
            actual_count = race_counts.iloc[idx]
            
            self.assertEqual(
                actual_count, expected_count,
                msg=f"馬{horse_id}の出走回数が期待値と一致しない"
            )
    
    def test_calculate_avg_rank_no_leakage(self):
        """平均着順計算でデータリーケージが発生しないことを確認"""
        all_results = pd.concat([self.horse_results_df, self.future_results], ignore_index=True)
        
        avg_ranks = self.calculator.calculate_avg_rank(
            self.target_races,
            all_results,
            column='horse_id',
            race_date_column='date'
        )
        
        # 各馬について検証
        for idx, row in self.target_races.iterrows():
            horse_id = row['horse_id']
            race_date = row['date']
            
            # 手動で過去データの平均着順を計算
            past_data = all_results[
                (all_results['horse_id'] == horse_id) & 
                (all_results['日付'] < race_date)
            ]
            
            if len(past_data) > 0:
                expected_avg = past_data['着順'].mean()
                actual_avg = avg_ranks.iloc[idx]
                
                self.assertAlmostEqual(
                    actual_avg, expected_avg, places=3,
                    msg=f"馬{horse_id}の平均着順が期待値と一致しない"
                )
            else:
                self.assertTrue(
                    pd.isna(avg_ranks.iloc[idx]),
                    "過去データがない場合はNaNであるべき"
                )
    
    def test_jockey_win_rate_time_period(self):
        """騎手勝率計算の統計期間制限テスト"""
        all_results = pd.concat([self.horse_results_df, self.future_results], ignore_index=True)
        
        # 365日間の統計期間で計算
        jockey_win_rates = self.calculator.calculate_jockey_win_rate(
            self.target_races,
            all_results,
            column='jockey_id',
            race_date_column='date',
            statistics_period_days=365
        )
        
        # 結果の妥当性確認
        self.assertEqual(len(jockey_win_rates), len(self.target_races))
        self.assertTrue(all(jockey_win_rates >= 0))
        self.assertTrue(all(jockey_win_rates <= 1))
    
    def test_edge_case_same_day_races(self):
        """同日複数レースのエッジケーステスト"""
        # 同日に複数レースがあるデータを作成
        same_day_data = pd.DataFrame([
            {'horse_id': 'horse_001', '日付': self.target_date - timedelta(days=1), '着順': 1, 'race_time': '10:00'},
            {'horse_id': 'horse_001', '日付': self.target_date - timedelta(days=1), '着順': 3, 'race_time': '14:00'},
            {'horse_id': 'horse_001', '日付': self.target_date - timedelta(days=1), '着順': 2, 'race_time': '16:00'},
        ])
        
        race_counts = self.calculator.calculate_race_count(
            self.target_races.head(1),  # 1頭だけテスト
            same_day_data,
            column='horse_id',
            race_date_column='date'
        )
        
        # 同日の全レースがカウントされることを確認
        self.assertEqual(race_counts.iloc[0], 3)
    
    def test_missing_date_column_error(self):
        """日付カラムが存在しない場合のエラーハンドリング"""
        # 日付カラムのないターゲットデータ
        target_no_date = self.target_races.drop('date', axis=1)
        
        # エラーログが出力されることを確認（例外は発生しない）
        with self.assertLogs(level='ERROR') as cm:
            win_rates = self.calculator.calculate_win_rate(
                target_no_date,
                self.horse_results_df,
                column='horse_id',
                race_date_column='date'
            )
            
        # エラーメッセージの確認
        self.assertTrue(any('データリーケージの可能性' in message for message in cm.output))
        
        # デフォルト値が返されることを確認
        self.assertEqual(len(win_rates), len(target_no_date))
        self.assertTrue(all(win_rates == 0))
    
    def test_empty_horse_results(self):
        """空の過去成績データの場合のテスト"""
        empty_results = pd.DataFrame()
        
        win_rates = self.calculator.calculate_win_rate(
            self.target_races,
            empty_results,
            column='horse_id',
            race_date_column='date'
        )
        
        # デフォルト値が返されることを確認
        self.assertEqual(len(win_rates), len(self.target_races))
        # 空のデータフレームの場合は0.0が返される
        self.assertTrue(all(win_rates == 0.0))


class TestDataLeakageIntegration(unittest.TestCase):
    """統合テスト"""
    
    def setUp(self):
        """テスト前の準備"""
        self.calculator = FeatureCalculators()
        
    def test_real_world_scenario(self):
        """実世界のシナリオでのテスト"""
        # より現実的なデータでテスト
        dates = pd.date_range('2023-01-01', '2024-06-30', freq='W')
        
        # 複数の馬の成績データ
        horse_results = []
        horses = ['horse_001', 'horse_002', 'horse_003']
        
        for horse_id in horses:
            for date in dates[:20]:  # 各馬20レース
                horse_results.append({
                    'horse_id': horse_id,
                    'jockey_id': f'jockey_{np.random.randint(1, 5):03d}',
                    'trainer_id': f'trainer_{np.random.randint(1, 3):03d}',
                    '日付': date,
                    '着順': np.random.randint(1, 13),
                    '賞金': np.random.randint(0, 1000) * 10000
                })
        
        horse_results_df = pd.DataFrame(horse_results)
        
        # 予測対象レース（2024年6月1日）
        prediction_date = datetime(2024, 6, 1)
        target_races = pd.DataFrame([
            {'horse_id': horse_id, 'date': prediction_date} 
            for horse_id in horses
        ])
        
        # 特徴量計算
        features = {}
        features['win_rate'] = self.calculator.calculate_win_rate(
            target_races, horse_results_df, race_date_column='date'
        )
        features['avg_rank'] = self.calculator.calculate_avg_rank(
            target_races, horse_results_df, race_date_column='date'
        )
        features['race_count'] = self.calculator.calculate_race_count(
            target_races, horse_results_df, race_date_column='date'
        )
        
        # 全ての特徴量が妥当な範囲内であることを確認
        for feature_name, values in features.items():
            self.assertEqual(len(values), len(horses), f"{feature_name}の出力サイズが不正")
            
            if feature_name == 'win_rate':
                self.assertTrue(all(values >= 0) and all(values <= 1), f"{feature_name}の範囲が不正")
            elif feature_name == 'race_count':
                self.assertTrue(all(values >= 0), f"{feature_name}の値が負")
            elif feature_name == 'avg_rank':
                self.assertTrue(all(values >= 1) or all(pd.isna(values)), f"{feature_name}の値が不正")


def run_comprehensive_tests():
    """包括的なテストの実行"""
    print("🧪 データリーケージ防止の包括的テストを開始")
    print("=" * 70)
    
    # テストスイートの作成
    suite = unittest.TestSuite()
    
    # 基本テスト
    suite.addTest(unittest.makeSuite(TestDataLeakagePrevention))
    
    # 統合テスト
    suite.addTest(unittest.makeSuite(TestDataLeakageIntegration))
    
    # テスト実行
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 結果のサマリー
    print(f"\n📊 テスト結果サマリー:")
    print(f"  実行したテスト数: {result.testsRun}")
    print(f"  成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"  失敗: {len(result.failures)}")
    print(f"  エラー: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失敗したテスト:")
        for test, error in result.failures:
            print(f"  - {test}: {error}")
    
    if result.errors:
        print(f"\n💥 エラーが発生したテスト:")
        for test, error in result.errors:
            print(f"  - {test}: {error}")
    
    if result.wasSuccessful():
        print(f"\n🎉 全てのテストが成功しました！")
        print(f"\n✅ データリーケージ対策が正常に動作しています")
        return True
    else:
        print(f"\n⚠️ 一部のテストが失敗しました")
        return False


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)