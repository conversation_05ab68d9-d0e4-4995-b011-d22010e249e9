#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
競馬レース解説システム

このモジュールは競馬レースの専門的な解説を生成します。
- レース展開の分析
- 各馬の特徴と評価
- 的中要因の解説
- 戦略的アドバイス
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import datetime

class RaceCommentator:
    """競馬レース解説クラス"""
    
    def __init__(self):
        """初期化"""
        self.logger = logging.getLogger(__name__)
        
        # 距離分類
        self.distance_categories = {
            'sprint': (1000, 1400),
            'mile': (1401, 1800),
            'middle': (1801, 2200),
            'classic': (2201, 2600),
            'long': (2601, 4000)
        }
        
        # 馬場状態の影響
        self.ground_impact = {
            '良': '標準的',
            '稍重': 'やや不利',
            '重': '大幅不利',
            '不良': '極めて不利'
        }
    
    def generate_race_commentary(self, 
                                predicted_results: Dict[str, Any],
                                actual_results: pd.DataFrame,
                                race_info: Dict[str, Any],
                                analysis_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        総合レース解説生成
        
        Parameters
        ----------
        predicted_results : Dict[str, Any]
            AI予想結果
        actual_results : pd.DataFrame
            実際のレース結果
        race_info : Dict[str, Any]
            レース基本情報
        analysis_result : Dict[str, Any], optional
            詳細分析結果
            
        Returns
        -------
        Dict[str, Any]
            解説内容
        """
        commentary = {
            'race_overview': self._generate_race_overview(race_info, actual_results),
            'pre_race_analysis': self._generate_pre_race_analysis(predicted_results, race_info),
            'result_analysis': self._generate_result_analysis(predicted_results, actual_results),
            'performance_commentary': self._generate_performance_commentary(
                predicted_results, actual_results, race_info),
            'betting_insights': self._generate_betting_insights(predicted_results, actual_results),
            'technical_analysis': self._generate_technical_analysis(analysis_result) if analysis_result else {},
            'strategic_advice': self._generate_strategic_advice(predicted_results, actual_results, race_info)
        }
        
        return commentary
    
    def _generate_race_overview(self, race_info: Dict[str, Any], 
                               actual_results: pd.DataFrame) -> Dict[str, str]:
        """レース概要解説"""
        
        race_id = race_info.get('race_id', 'Unknown')
        distance = race_info.get('course_len', 0)
        track_type = race_info.get('race_type', 'Unknown')
        ground_condition = race_info.get('ground_state', 'Unknown')
        weather = race_info.get('weather', 'Unknown')
        
        # 距離カテゴリー判定
        distance_category = self._classify_distance(distance)
        
        # レース特性分析
        field_size = len(actual_results)
        
        overview = {
            'basic_info': f"""
レースID: {race_id}
距離: {distance}m ({distance_category})
コース: {track_type}
馬場: {ground_condition} ({self.ground_impact.get(ground_condition, '不明')})
天気: {weather}
出走頭数: {field_size}頭
""",
            'race_characteristics': self._analyze_race_characteristics(
                distance, track_type, ground_condition, field_size),
            'conditions_impact': self._analyze_conditions_impact(
                distance, track_type, ground_condition, weather)
        }
        
        return overview
    
    def _classify_distance(self, distance: int) -> str:
        """距離分類"""
        for category, (min_dist, max_dist) in self.distance_categories.items():
            if min_dist <= distance <= max_dist:
                category_names = {
                    'sprint': 'スプリント',
                    'mile': 'マイル',
                    'middle': '中距離',
                    'classic': 'クラシック',
                    'long': '長距離'
                }
                return category_names.get(category, '不明')
        return '特殊距離'
    
    def _analyze_race_characteristics(self, distance: int, track_type: str, 
                                    ground_condition: str, field_size: int) -> str:
        """レース特性分析"""
        
        characteristics = []
        
        # 距離による特性
        if distance <= 1400:
            characteristics.append("スピード勝負の展開が予想される")
        elif distance <= 1800:
            characteristics.append("スピードとスタミナのバランスが重要")
        elif distance >= 2200:
            characteristics.append("スタミナが重視される消耗戦")
        
        # 馬場による特性
        if ground_condition in ['重', '不良']:
            characteristics.append("パワータイプの馬が有利")
            characteristics.append("前残りの展開になりやすい")
        elif ground_condition == '良':
            characteristics.append("能力通りの結果になりやすい")
        
        # 出走頭数による特性
        if field_size >= 16:
            characteristics.append("大混戦でアクシデントの可能性")
        elif field_size <= 10:
            characteristics.append("少頭数で実力差が出やすい")
        
        return "、".join(characteristics) + "。"
    
    def _analyze_conditions_impact(self, distance: int, track_type: str,
                                 ground_condition: str, weather: str) -> str:
        """条件影響分析"""
        
        impacts = []
        
        # 馬場状態の影響
        if ground_condition != '良':
            impacts.append(f"馬場状態({ground_condition})により、差し馬には不利な条件")
        
        # 天気の影響
        if weather in ['雨', '小雨']:
            impacts.append("雨により馬場が時間経過とともに悪化する可能性")
        elif weather == '晴':
            impacts.append("好天により馬場状態は安定")
        
        # コース特性
        if track_type == '芝':
            impacts.append("芝コースで繊細な脚質適性が重要")
        elif track_type == 'ダート':
            impacts.append("ダートでパワーと気性が重要")
        
        return "、".join(impacts) + "。" if impacts else "特別な条件の影響は少ない。"
    
    def _generate_pre_race_analysis(self, predicted_results: Dict[str, Any],
                                   race_info: Dict[str, Any]) -> Dict[str, str]:
        """レース前分析"""
        
        top3_predictions = predicted_results['馬番'][:3]
        top3_names = predicted_results['馬名'][:3]
        top3_rates = predicted_results['予想勝率'][:3]
        
        analysis = {
            'ai_prediction_summary': f"""
AI予想では、{top3_names[0]}(馬番{top3_predictions[0]})を1番手に評価。
予想勝率{top3_rates[0]:.1f}%で、{self._evaluate_confidence_level(top3_rates[0])}な信頼度。
続いて{top3_names[1]}(馬番{top3_predictions[1]})、{top3_names[2]}(馬番{top3_predictions[2]})が続く。
""",
            'prediction_reasoning': self._generate_prediction_reasoning(predicted_results),
            'key_factors': self._identify_key_factors(predicted_results, race_info)
        }
        
        return analysis
    
    def _evaluate_confidence_level(self, win_rate: float) -> str:
        """信頼度評価"""
        if win_rate >= 15:
            return "非常に高い"
        elif win_rate >= 10:
            return "高い"
        elif win_rate >= 7:
            return "中程度"
        elif win_rate >= 5:
            return "やや低い"
        else:
            return "低い"
    
    def _generate_prediction_reasoning(self, predicted_results: Dict[str, Any]) -> str:
        """予想根拠解説"""
        
        win_rates = predicted_results['予想勝率']
        
        # 勝率分布分析
        avg_rate = np.mean(win_rates)
        max_rate = max(win_rates)
        min_rate = min(win_rates)
        
        reasoning_parts = []
        
        if max_rate - min_rate > 2.0:
            reasoning_parts.append("能力差が明確で、上位陣が優勢")
        else:
            reasoning_parts.append("混戦模様で、どの馬にもチャンスがある")
        
        if max_rate > 8.0:
            reasoning_parts.append("有力馬の存在が際立つ")
        elif max_rate < 6.0:
            reasoning_parts.append("抜けた存在がなく、荒れる可能性")
        
        return "、".join(reasoning_parts) + "。"
    
    def _identify_key_factors(self, predicted_results: Dict[str, Any],
                            race_info: Dict[str, Any]) -> str:
        """重要要因特定"""
        
        factors = []
        
        distance = race_info.get('course_len', 0)
        track_type = race_info.get('race_type', '')
        
        # 距離適性
        if distance <= 1400:
            factors.append("スプリント能力")
        elif distance >= 2200:
            factors.append("スタミナ")
        else:
            factors.append("バランス能力")
        
        # コース適性
        if track_type == '芝':
            factors.append("芝適性")
        elif track_type == 'ダート':
            factors.append("ダート適性")
        
        # 一般的要因
        factors.extend(["調子", "騎手の技量", "枠順"])
        
        return "、".join(factors[:4]) + "が重要なポイント。"
    
    def _generate_result_analysis(self, predicted_results: Dict[str, Any],
                                actual_results: pd.DataFrame) -> Dict[str, str]:
        """結果分析"""
        
        if actual_results.empty:
            return {'analysis': "結果データが利用できません。"}
        
        # 実際の上位3着
        actual_top3 = actual_results.head(3)
        actual_horses = actual_top3['umaban'].tolist() if 'umaban' in actual_top3.columns else []
        actual_names = actual_top3['horse_name'].tolist() if 'horse_name' in actual_top3.columns else []
        
        # 予想との比較
        predicted_top3 = predicted_results['馬番'][:3]
        matches = len(set(predicted_top3) & set(actual_horses))
        
        analysis = {
            'result_summary': self._generate_result_summary(actual_top3),
            'prediction_accuracy': self._evaluate_prediction_accuracy(matches, predicted_results, actual_results),
            'upset_analysis': self._analyze_upset_level(actual_results),
            'winner_analysis': self._analyze_winner(actual_results)
        }
        
        return analysis
    
    def _generate_result_summary(self, actual_top3: pd.DataFrame) -> str:
        """結果サマリー"""
        
        if actual_top3.empty:
            return "結果データが不十分です。"
        
        winner = actual_top3.iloc[0]
        winner_horse = winner.get('umaban', '?')
        winner_name = winner.get('horse_name', '不明')
        winner_odds = winner.get('tansho_odds', 0)
        winner_popularity = winner.get('popularity', 0)
        
        summary = f"""
勝利馬: {winner_name}(馬番{winner_horse})
オッズ: {winner_odds:.1f}倍 (人気: {winner_popularity}番人気)
"""
        
        if len(actual_top3) >= 3:
            second = actual_top3.iloc[1]
            third = actual_top3.iloc[2]
            
            summary += f"""
2着: {second.get('horse_name', '不明')}(馬番{second.get('umaban', '?')})
3着: {third.get('horse_name', '不明')}(馬番{third.get('umaban', '?')})
"""
        
        return summary
    
    def _evaluate_prediction_accuracy(self, matches: int, 
                                    predicted_results: Dict[str, Any],
                                    actual_results: pd.DataFrame) -> str:
        """予想精度評価"""
        
        if matches == 3:
            accuracy = "完璧な予想的中"
        elif matches == 2:
            accuracy = "ほぼ的中の高精度予想"
        elif matches == 1:
            accuracy = "部分的中の中程度精度"
        else:
            accuracy = "予想が外れ"
        
        # 1位の的中状況
        predicted_winner = predicted_results['馬番'][0]
        if not actual_results.empty and 'umaban' in actual_results.columns:
            actual_winner = actual_results.iloc[0]['umaban']
            if predicted_winner == actual_winner:
                accuracy += "、1位も的中"
            else:
                # 予想1位の実際の着順
                predicted_winner_result = actual_results[actual_results['umaban'] == predicted_winner]
                if not predicted_winner_result.empty:
                    actual_rank = predicted_winner_result['rank'].iloc[0]
                    accuracy += f"、予想1位は実際{actual_rank}着"
        
        return accuracy + "。"
    
    def _analyze_upset_level(self, actual_results: pd.DataFrame) -> str:
        """波乱度分析"""
        
        if actual_results.empty or 'popularity' not in actual_results.columns:
            return "人気データが不十分で波乱度を判定できません。"
        
        winner_popularity = actual_results.iloc[0]['popularity']
        
        if winner_popularity == 1:
            upset_level = "堅実決着"
        elif winner_popularity <= 3:
            upset_level = "やや堅い決着"
        elif winner_popularity <= 6:
            upset_level = "中程度の波乱"
        elif winner_popularity <= 10:
            upset_level = "大波乱"
        else:
            upset_level = "超大穴決着"
        
        # 3着内の人気分析
        top3_popularities = actual_results.head(3)['popularity'].tolist()
        popular_in_top3 = len([p for p in top3_popularities if p <= 3])
        
        if popular_in_top3 == 0:
            upset_level += "（3着内に人気馬不在の大混乱）"
        elif popular_in_top3 == 1:
            upset_level += "（人気薄が上位独占）"
        
        return upset_level + "となった。"
    
    def _analyze_winner(self, actual_results: pd.DataFrame) -> str:
        """勝ち馬分析"""
        
        if actual_results.empty:
            return "勝ち馬データが不十分です。"
        
        winner = actual_results.iloc[0]
        winner_name = winner.get('horse_name', '不明')
        winner_odds = winner.get('tansho_odds', 0)
        winner_popularity = winner.get('popularity', 0)
        
        analysis_parts = []
        
        # オッズによる評価
        if winner_odds < 3.0:
            analysis_parts.append("圧倒的人気に応えた")
        elif winner_odds < 6.0:
            analysis_parts.append("人気に応えた")
        elif winner_odds < 15.0:
            analysis_parts.append("中穴を開けた")
        else:
            analysis_parts.append("大穴を演出した")
        
        # 人気による評価
        if winner_popularity == 1:
            analysis_parts.append("1番人気が順当勝利")
        elif winner_popularity <= 3:
            analysis_parts.append("上位人気が実力発揮")
        else:
            analysis_parts.append("人気薄が番狂わせ")
        
        return f"{winner_name}が" + "、".join(analysis_parts) + "。"
    
    def _generate_performance_commentary(self, predicted_results: Dict[str, Any],
                                       actual_results: pd.DataFrame,
                                       race_info: Dict[str, Any]) -> Dict[str, str]:
        """パフォーマンス解説"""
        
        commentary = {
            'ai_model_performance': self._evaluate_ai_performance(predicted_results, actual_results),
            'market_vs_ai': self._compare_market_vs_ai(predicted_results, actual_results),
            'learning_points': self._identify_learning_points(predicted_results, actual_results, race_info)
        }
        
        return commentary
    
    def _evaluate_ai_performance(self, predicted_results: Dict[str, Any],
                               actual_results: pd.DataFrame) -> str:
        """AI性能評価"""
        
        if actual_results.empty:
            return "結果データが不十分でAI性能を評価できません。"
        
        predicted_top3 = predicted_results['馬番'][:3]
        actual_top3 = actual_results['umaban'].head(3).tolist() if 'umaban' in actual_results.columns else []
        matches = len(set(predicted_top3) & set(actual_top3))
        
        performance_score = matches / 3 * 100
        
        if performance_score >= 100:
            evaluation = "完璧なパフォーマンス"
        elif performance_score >= 67:
            evaluation = "優秀なパフォーマンス"
        elif performance_score >= 33:
            evaluation = "標準的なパフォーマンス"
        else:
            evaluation = "改善が必要なパフォーマンス"
        
        # 予想信頼度の妥当性
        top_confidence = predicted_results['予想勝率'][0]
        predicted_winner = predicted_results['馬番'][0]
        
        if not actual_results.empty and 'umaban' in actual_results.columns:
            actual_winner = actual_results.iloc[0]['umaban']
            if predicted_winner == actual_winner:
                confidence_evaluation = f"信頼度{top_confidence:.1f}%の予想が的中し、AIの判断が正確だった"
            else:
                confidence_evaluation = f"信頼度{top_confidence:.1f}%の予想が外れ、要因分析が必要"
        else:
            confidence_evaluation = "信頼度の妥当性を評価できません"
        
        return f"AIは{evaluation}を示した。{confidence_evaluation}。"
    
    def _compare_market_vs_ai(self, predicted_results: Dict[str, Any],
                            actual_results: pd.DataFrame) -> str:
        """市場vsAI比較"""
        
        if actual_results.empty or 'popularity' not in actual_results.columns:
            return "市場データが不十分で比較できません。"
        
        # AI予想1位vs市場1番人気
        ai_pick = predicted_results['馬番'][0]
        market_favorite = None
        
        if not actual_results.empty:
            favorite_data = actual_results[actual_results['popularity'] == 1]
            if not favorite_data.empty:
                market_favorite = favorite_data.iloc[0]['umaban']
        
        if market_favorite is None:
            return "市場の1番人気データが取得できません。"
        
        if ai_pick == market_favorite:
            comparison = "AIと市場の見解が一致"
        else:
            comparison = "AIと市場で異なる評価"
            
            # どちらが的中したか
            if not actual_results.empty and 'umaban' in actual_results.columns:
                actual_winner = actual_results.iloc[0]['umaban']
                if ai_pick == actual_winner:
                    comparison += "、AIの独自判断が的中"
                elif market_favorite == actual_winner:
                    comparison += "、市場の評価が正解"
                else:
                    comparison += "、両者とも外れ"
        
        return comparison + "。"
    
    def _identify_learning_points(self, predicted_results: Dict[str, Any],
                                actual_results: pd.DataFrame,
                                race_info: Dict[str, Any]) -> str:
        """学習ポイント特定"""
        
        learning_points = []
        
        # 予想精度から
        predicted_top3 = predicted_results['馬番'][:3]
        if not actual_results.empty and 'umaban' in actual_results.columns:
            actual_top3 = actual_results['umaban'].head(3).tolist()
            matches = len(set(predicted_top3) & set(actual_top3))
            
            if matches == 0:
                learning_points.append("特徴量エンジニアリングの見直しが必要")
            elif matches == 1:
                learning_points.append("モデルのパラメータ調整で精度向上の余地")
        
        # レース条件から
        ground_condition = race_info.get('ground_state', '')
        if ground_condition in ['重', '不良']:
            learning_points.append("悪馬場での適性評価精度の向上が課題")
        
        distance = race_info.get('course_len', 0)
        if distance >= 2200:
            learning_points.append("長距離レースでのスタミナ評価精度の検証")
        elif distance <= 1400:
            learning_points.append("スプリント戦での瞬発力評価の精度確認")
        
        # 市場との乖離から
        if not actual_results.empty and 'popularity' not in actual_results.columns:
            learning_points.append("人気データの活用でモデル精度向上の可能性")
        
        if not learning_points:
            learning_points.append("現在のモデルは良好に機能している")
        
        return "今後の改善点として、" + "、".join(learning_points[:3]) + "が挙げられる。"
    
    def _generate_betting_insights(self, predicted_results: Dict[str, Any],
                                 actual_results: pd.DataFrame) -> Dict[str, str]:
        """投資洞察"""
        
        insights = {
            'profitability_analysis': self._analyze_profitability(predicted_results, actual_results),
            'risk_assessment': self._assess_betting_risk(predicted_results, actual_results),
            'strategy_recommendations': self._recommend_betting_strategy(predicted_results, actual_results)
        }
        
        return insights
    
    def _analyze_profitability(self, predicted_results: Dict[str, Any],
                             actual_results: pd.DataFrame) -> str:
        """収益性分析"""
        
        if actual_results.empty:
            return "結果データが不十分で収益性を分析できません。"
        
        # 単勝的中の場合の収益
        ai_pick = predicted_results['馬番'][0]
        ai_confidence = predicted_results['予想勝率'][0]
        
        if 'umaban' not in actual_results.columns or 'tansho_odds' not in actual_results.columns:
            return "オッズデータが不十分です。"
        
        winner_data = actual_results.iloc[0]
        actual_winner = winner_data['umaban']
        winner_odds = winner_data.get('tansho_odds', 0)
        
        if ai_pick == actual_winner:
            profit = (winner_odds - 1) * 100  # 100円賭けた場合の利益
            roi = (winner_odds - 1) * 100  # ROI%
            profitability = f"的中により{profit:.0f}円の利益（ROI: {roi:.1f}%）"
        else:
            profitability = "不的中により100円の損失（ROI: -100%）"
        
        # 期待値計算
        expected_value = (ai_confidence / 100) * winner_odds - 1
        ev_analysis = f"期待値: {expected_value:.2f}"
        
        if expected_value > 0:
            ev_analysis += "（プラス期待値で理論的に有利）"
        else:
            ev_analysis += "（マイナス期待値で理論的に不利）"
        
        return f"{profitability}。{ev_analysis}。"
    
    def _assess_betting_risk(self, predicted_results: Dict[str, Any],
                           actual_results: pd.DataFrame) -> str:
        """賭けリスク評価"""
        
        top_confidence = predicted_results['予想勝率'][0]
        confidence_variance = np.var(predicted_results['予想勝率'][:5])
        
        # 信頼度によるリスク評価
        if top_confidence >= 15:
            risk_level = "低リスク"
        elif top_confidence >= 10:
            risk_level = "中リスク"
        elif top_confidence >= 6:
            risk_level = "高リスク"
        else:
            risk_level = "極高リスク"
        
        # 予想のばらつきによる評価
        if confidence_variance < 0.5:
            stability = "予想が安定"
        elif confidence_variance < 1.0:
            stability = "やや不安定"
        else:
            stability = "予想が不安定"
        
        return f"リスクレベル: {risk_level}（最高信頼度{top_confidence:.1f}%）。{stability}で分散投資も考慮すべき。"
    
    def _recommend_betting_strategy(self, predicted_results: Dict[str, Any],
                                  actual_results: pd.DataFrame) -> str:
        """投資戦略推奨"""
        
        top_confidence = predicted_results['予想勝率'][0]
        top3_confidences = predicted_results['予想勝率'][:3]
        
        recommendations = []
        
        # 単勝戦略
        if top_confidence >= 12:
            recommendations.append("単勝集中投資が有効")
        elif top_confidence >= 8:
            recommendations.append("単勝メイン、複勝保険が適切")
        else:
            recommendations.append("単勝は避けて複勝中心")
        
        # 複勝戦略
        confidence_gap = max(top3_confidences) - min(top3_confidences)
        if confidence_gap < 1.0:
            recommendations.append("3連複BOXで幅広くカバー")
        else:
            recommendations.append("上位2頭中心の馬連・馬単")
        
        # 資金管理
        if top_confidence >= 10:
            recommendations.append("資金の10-15%投入可能")
        elif top_confidence >= 7:
            recommendations.append("資金の5-10%投入が妥当")
        else:
            recommendations.append("資金の1-3%以下に抑制")
        
        return "推奨戦略: " + "、".join(recommendations[:3]) + "。"
    
    def _generate_technical_analysis(self, analysis_result: Dict[str, Any]) -> Dict[str, str]:
        """技術分析"""
        
        if not analysis_result:
            return {'analysis': "詳細分析データが利用できません。"}
        
        technical = {
            'model_performance': self._analyze_model_technical_performance(analysis_result),
            'feature_insights': self._analyze_feature_technical_insights(analysis_result),
            'statistical_summary': self._generate_statistical_summary(analysis_result)
        }
        
        return technical
    
    def _analyze_model_technical_performance(self, analysis_result: Dict[str, Any]) -> str:
        """モデル技術性能分析"""
        
        accuracy = analysis_result.get('prediction_accuracy', {})
        matches = accuracy.get('top3_matches', 0)
        
        performance_metrics = f"Top3適中率: {matches}/3 ({matches/3*100:.1f}%)"
        
        # モデル洞察
        model_insights = analysis_result.get('model_insights', {})
        complexity = model_insights.get('model_complexity', 0)
        stability = model_insights.get('prediction_stability', 0)
        
        technical_summary = f"{performance_metrics}。"
        technical_summary += f"モデル複雑度: {complexity}特徴量、"
        technical_summary += f"予想安定性: {stability:.3f}。"
        
        return technical_summary
    
    def _analyze_feature_technical_insights(self, analysis_result: Dict[str, Any]) -> str:
        """特徴量技術洞察"""
        
        model_insights = analysis_result.get('model_insights', {})
        most_influential = model_insights.get('most_influential_feature', '')
        
        insights = f"最重要特徴量: {most_influential}。"
        
        # 相関分析
        correlation_insights = model_insights.get('feature_correlation_insights', {})
        if correlation_insights:
            high_corr_pairs = correlation_insights.get('high_correlation_pairs', [])
            if high_corr_pairs:
                insights += f"高相関ペア数: {len(high_corr_pairs)}組。"
        
        return insights
    
    def _generate_statistical_summary(self, analysis_result: Dict[str, Any]) -> str:
        """統計サマリー"""
        
        odds_analysis = analysis_result.get('odds_analysis', {})
        
        if not odds_analysis:
            return "統計データが不十分です。"
        
        summary_parts = []
        
        # 勝者オッズ
        winner_odds = odds_analysis.get('winner_odds')
        if winner_odds:
            summary_parts.append(f"勝利馬オッズ: {winner_odds:.1f}倍")
        
        # 市場効率性
        market_efficiency = odds_analysis.get('market_efficiency', {})
        overround = market_efficiency.get('overround', 0)
        if overround:
            summary_parts.append(f"控除率: {overround:.1f}%")
        
        return "統計概要: " + "、".join(summary_parts) + "。"
    
    def _generate_strategic_advice(self, predicted_results: Dict[str, Any],
                                 actual_results: pd.DataFrame,
                                 race_info: Dict[str, Any]) -> Dict[str, str]:
        """戦略的アドバイス"""
        
        advice = {
            'future_predictions': self._generate_future_prediction_advice(predicted_results, actual_results),
            'model_improvements': self._suggest_model_improvements(predicted_results, actual_results, race_info),
            'betting_discipline': self._advise_betting_discipline(predicted_results, actual_results)
        }
        
        return advice
    
    def _generate_future_prediction_advice(self, predicted_results: Dict[str, Any],
                                         actual_results: pd.DataFrame) -> str:
        """今後の予想アドバイス"""
        
        advice_points = []
        
        # 予想精度から
        if not actual_results.empty and 'umaban' in actual_results.columns:
            predicted_top3 = predicted_results['馬番'][:3]
            actual_top3 = actual_results['umaban'].head(3).tolist()
            matches = len(set(predicted_top3) & set(actual_top3))
            
            if matches >= 2:
                advice_points.append("現在のモデル精度を維持")
            elif matches == 1:
                advice_points.append("特徴量の重み付け調整を検討")
            else:
                advice_points.append("根本的なアプローチ見直しが必要")
        
        # 信頼度分布から
        confidence_range = max(predicted_results['予想勝率']) - min(predicted_results['予想勝率'])
        if confidence_range < 1.5:
            advice_points.append("予想の差別化を図る必要")
        
        return "今後の予想では、" + "、".join(advice_points[:2]) + "ことが重要。"
    
    def _suggest_model_improvements(self, predicted_results: Dict[str, Any],
                                  actual_results: pd.DataFrame,
                                  race_info: Dict[str, Any]) -> str:
        """モデル改善提案"""
        
        suggestions = []
        
        # レース条件による改善提案
        ground_condition = race_info.get('ground_state', '')
        if ground_condition in ['重', '不良']:
            suggestions.append("悪馬場特化の特徴量追加")
        
        distance = race_info.get('course_len', 0)
        if distance >= 2200:
            suggestions.append("長距離適性の重み増加")
        elif distance <= 1400:
            suggestions.append("スプリント能力の評価強化")
        
        # 精度による提案
        if not actual_results.empty and 'umaban' in actual_results.columns:
            predicted_winner = predicted_results['馬番'][0]
            actual_winner = actual_results.iloc[0]['umaban']
            
            if predicted_winner != actual_winner:
                suggestions.append("上位予想精度の向上")
        
        if not suggestions:
            suggestions.append("現行モデルの継続使用")
        
        return "モデル改善として、" + "、".join(suggestions[:2]) + "が有効。"
    
    def _advise_betting_discipline(self, predicted_results: Dict[str, Any],
                                 actual_results: pd.DataFrame) -> str:
        """投資規律アドバイス"""
        
        discipline_advice = []
        
        # 信頼度による規律
        top_confidence = predicted_results['予想勝率'][0]
        if top_confidence < 8:
            discipline_advice.append("低信頼度時は投資額を抑制")
        
        # 結果による学習
        if not actual_results.empty and 'umaban' in actual_results.columns:
            predicted_winner = predicted_results['馬番'][0]
            actual_winner = actual_results.iloc[0]['umaban']
            
            if predicted_winner == actual_winner:
                discipline_advice.append("的中時も過信せず冷静な判断を維持")
            else:
                discipline_advice.append("不的中時は感情的にならず分析重視")
        
        # 一般的規律
        discipline_advice.append("資金管理を最優先")
        discipline_advice.append("長期的視点での投資")
        
        return "投資規律: " + "、".join(discipline_advice[:3]) + "が重要。"
    
    def generate_full_commentary_report(self, commentary: Dict[str, Any]) -> str:
        """完全解説レポート生成"""
        
        report_lines = []
        report_lines.append("=" * 100)
        report_lines.append("競馬レース専門解説レポート")
        report_lines.append("=" * 100)
        
        # レース概要
        race_overview = commentary.get('race_overview', {})
        if race_overview:
            report_lines.append("\n🏇 レース概要")
            report_lines.append("-" * 50)
            report_lines.append(race_overview.get('basic_info', ''))
            report_lines.append(f"特性: {race_overview.get('race_characteristics', '')}")
            report_lines.append(f"条件影響: {race_overview.get('conditions_impact', '')}")
        
        # レース前分析
        pre_race = commentary.get('pre_race_analysis', {})
        if pre_race:
            report_lines.append("\n📊 レース前分析")
            report_lines.append("-" * 50)
            report_lines.append(pre_race.get('ai_prediction_summary', ''))
            report_lines.append(f"予想根拠: {pre_race.get('prediction_reasoning', '')}")
            report_lines.append(f"重要要因: {pre_race.get('key_factors', '')}")
        
        # 結果分析
        result_analysis = commentary.get('result_analysis', {})
        if result_analysis:
            report_lines.append("\n🏆 結果分析")
            report_lines.append("-" * 50)
            report_lines.append(result_analysis.get('result_summary', ''))
            report_lines.append(f"予想精度: {result_analysis.get('prediction_accuracy', '')}")
            report_lines.append(f"波乱度: {result_analysis.get('upset_analysis', '')}")
            report_lines.append(f"勝ち馬: {result_analysis.get('winner_analysis', '')}")
        
        # パフォーマンス解説
        performance = commentary.get('performance_commentary', {})
        if performance:
            report_lines.append("\n⚡ パフォーマンス解説")
            report_lines.append("-" * 50)
            report_lines.append(f"AI性能: {performance.get('ai_model_performance', '')}")
            report_lines.append(f"市場比較: {performance.get('market_vs_ai', '')}")
            report_lines.append(f"学習点: {performance.get('learning_points', '')}")
        
        # 投資洞察
        betting_insights = commentary.get('betting_insights', {})
        if betting_insights:
            report_lines.append("\n💰 投資洞察")
            report_lines.append("-" * 50)
            report_lines.append(f"収益性: {betting_insights.get('profitability_analysis', '')}")
            report_lines.append(f"リスク: {betting_insights.get('risk_assessment', '')}")
            report_lines.append(f"戦略: {betting_insights.get('strategy_recommendations', '')}")
        
        # 技術分析
        technical = commentary.get('technical_analysis', {})
        if technical:
            report_lines.append("\n🔬 技術分析")
            report_lines.append("-" * 50)
            report_lines.append(f"モデル性能: {technical.get('model_performance', '')}")
            report_lines.append(f"特徴量洞察: {technical.get('feature_insights', '')}")
            report_lines.append(f"統計概要: {technical.get('statistical_summary', '')}")
        
        # 戦略的アドバイス
        strategic = commentary.get('strategic_advice', {})
        if strategic:
            report_lines.append("\n🎯 戦略的アドバイス")
            report_lines.append("-" * 50)
            report_lines.append(f"今後の予想: {strategic.get('future_predictions', '')}")
            report_lines.append(f"モデル改善: {strategic.get('model_improvements', '')}")
            report_lines.append(f"投資規律: {strategic.get('betting_discipline', '')}")
        
        report_lines.append("\n" + "=" * 100)
        report_lines.append("解説完了 - Professional Race Commentary System")
        report_lines.append("=" * 100)
        
        return "\n".join(report_lines)