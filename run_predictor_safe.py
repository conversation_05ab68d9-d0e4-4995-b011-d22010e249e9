#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL問題を回避して競馬予測を実行するスクリプト
"""

import warnings
import urllib3
import os

# SSL警告を無効化
warnings.filterwarnings('ignore')
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
os.environ['PYTHONWARNINGS'] = 'ignore:Unverified HTTPS request'

def main():
    print("🐎 競馬AI予測システム（SSL問題回避版）")
    print("=" * 60)
    
    # モジュールをインポート
    from enhanced_live_predictor import EnhancedLiveRacePredictor
    
    # 実行方法を選択
    print("\n実行方法を選択してください：")
    print("1. Seleniumを使用する（詳細データ取得）")
    print("2. Requestsのみ使用する（安定動作）")
    
    choice = input("\n選択 (1 or 2): ").strip()
    
    use_selenium = choice == "1"
    
    if use_selenium:
        print("\n⚠️  SeleniumでSSLエラーが発生する場合があります")
        print("エラーが続く場合は、2番を選択してください")
    
    # 予測器を初期化
    predictor = EnhancedLiveRacePredictor(use_selenium=use_selenium)
    
    # レースIDを入力
    race_id = input("\nレースIDを入力してください (例: 202505021211): ").strip()
    
    if not race_id:
        print("レースIDが入力されませんでした。")
        return
    
    print(f"\nレース予測を実行中: {race_id}")
    print("取得方式:", "Selenium" if use_selenium else "Requests/BeautifulSoup")
    
    try:
        # 予測実行
        results, race_info = predictor.predict_race(race_id)
        
        if not results.empty:
            predictor.display_prediction_results(results, race_info)
        else:
            print("\n❌ 予測に失敗しました")
            print("考えられる原因：")
            print("・レースIDが正しくない")
            print("・レースがまだ開催されていない")
            print("・ネットワーク接続の問題")
            
            if use_selenium:
                print("\n💡 ヒント: Requestsのみ（オプション2）で再試行してみてください")
    
    except Exception as e:
        print(f"\n❌ エラーが発生しました: {e}")
        
        if use_selenium and "SSL" in str(e):
            print("\n💡 SSL関連のエラーです。Requestsのみ（オプション2）で再試行してください")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n処理が中断されました")
    except Exception as e:
        print(f"\n予期しないエラー: {e}")
    
    input("\n\nEnterキーを押して終了...")
