import os
import pandas as pd
import pytest
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator, ComprehensiveIntegratorConfig
from core.processors.horse_processor import HorseProcessor

class DummyLogger:
    def info(self, msg): print(msg)
    def warning(self, msg): print(msg)
    def error(self, msg): print(msg)

@pytest.fixture
def dummy_config(tmp_path):
    # テスト用のダミー設定
    return ComprehensiveIntegratorConfig(
        pickle_base_dir=str(tmp_path),
        # 必要に応じて他のパラメータも追加
    )

@pytest.fixture
def dummy_corner_features(tmp_path):
    # テスト用のコーナー特徴量データ
    df = pd.DataFrame({
        'race_id': ['R1', 'R2', 'R3', 'R1', 'R2'],
        'horse_id': ['H1', 'H1', 'H1', 'H2', 'H2'],
        'date': pd.to_datetime(['2022-01-01', '2022-01-10', '2022-01-20', '2022-01-05', '2022-01-15']),
        'rank_1コーナー': [1, 2, 3, 1, 2],
    })
    path = os.path.join(tmp_path, 'corner_features_2022.pickle')
    df.to_pickle(path)
    return path, df

@pytest.fixture
def dummy_base_df():
    # テスト用のベースデータ
    return pd.DataFrame({
        'race_id': ['R1', 'R2', 'R3', 'R1', 'R2'],
        'horse_id': ['H1', 'H1', 'H1', 'H2', 'H2'],
        'date': pd.to_datetime(['2022-01-01', '2022-01-10', '2022-01-20', '2022-01-05', '2022-01-15']),
        'result': [5, 4, 3, 2, 1],
    })

def test_merge_corner_features(dummy_config, dummy_corner_features, dummy_base_df):
    path, corner_df = dummy_corner_features
    integrator = ComprehensiveDataIntegrator()
    integrator.logger = DummyLogger()
    integrator.horse_processor = HorseProcessor()
    # テスト用にmerge_latest_corner_featuresを直接呼ぶ
    merged = integrator.horse_processor.merge_latest_corner_features(
        base_df=dummy_base_df,
        corner_features_df=corner_df,
        n_races=2
    )
    # H1, H2ごとに2レース分だけcorner特徴量が付与されているか
    assert not merged.empty
    assert 'rank_1コーナー' in merged.columns
    # H1, H2の各レースでcorner特徴量が正しくマージされているか
    for _, row in merged.iterrows():
        assert row['horse_id'] in ['H1', 'H2']
        assert row['rank_1コーナー'] in [1, 2, 3]
