#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightGBM + TensorFlow Ranking ハイブリッド予測システム
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# TensorFlow関連のインポート
try:
    import tensorflow as tf
    import tensorflow_ranking as tfr
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False
    tf = None
    tfr = None

# LightGBM関連
try:
    import lightgbm as lgb
    LGB_AVAILABLE = True
except ImportError:
    LGB_AVAILABLE = False
    lgb = None

import joblib

# 既存の予測システム
from enhanced_live_predictor_with_scraping import EnhancedLiveRacePredictorWithScraping

# TensorFlow Ranking アナライザー
try:
    from core.analysis.tensorflow_ranking_analyzer import TensorFlowRankingAnalyzer
    TFR_ANALYZER_AVAILABLE = True
except ImportError:
    TFR_ANALYZER_AVAILABLE = False

logger = logging.getLogger(__name__)

class ModelSelectionStrategy:
    """モデル選択戦略クラス"""
    
    @staticmethod
    def assess_data_completeness(race_data: pd.DataFrame) -> float:
        """データ完全性を評価"""
        if race_data.empty:
            return 0.0
        
        total_cells = race_data.size
        missing_cells = race_data.isna().sum().sum()
        completeness = (total_cells - missing_cells) / total_cells
        
        return completeness
    
    @staticmethod
    def detect_race_importance(race_info: Dict[str, Any]) -> str:
        """レースの重要度を判定"""
        race_class = race_info.get('race_class', '')
        race_name = race_info.get('race_name', '')
        
        if any(grade in str(race_class).upper() for grade in ['G1', 'G2', 'G3']):
            return 'high'
        elif any(keyword in str(race_name) for keyword in ['重賞', 'ステークス', '特別']):
            return 'medium'
        else:
            return 'low'
    
    @staticmethod
    def select_optimal_models(race_data: pd.DataFrame, race_info: Dict[str, Any]) -> List[str]:
        """最適なモデル組み合わせを選択"""
        completeness = ModelSelectionStrategy.assess_data_completeness(race_data)
        importance = ModelSelectionStrategy.detect_race_importance(race_info)
        field_size = len(race_data)
        
        models = []
        
        # LightGBMは常に含める（欠損値処理の優秀さ）
        models.append('lightgbm')
        
        # TensorFlow Rankingの追加条件
        if TF_AVAILABLE and completeness > 0.6:
            models.append('tensorflow_ranking')
        
        # 重要レースの場合は必ずTFRを含める
        if importance in ['high', 'medium'] and TF_AVAILABLE:
            if 'tensorflow_ranking' not in models:
                models.append('tensorflow_ranking')
        
        # 少頭数ではLightGBMのみ
        if field_size < 8:
            models = ['lightgbm']
        
        logger.info(f"選択されたモデル: {models} (完全性: {completeness:.2f}, 重要度: {importance}, 頭数: {field_size})")
        
        return models

class HybridRacePredictionSystem:
    """LightGBM + TensorFlow Ranking ハイブリッド予測システム"""
    
    def __init__(self, model_dir: str = "models", config: Dict[str, Any] = None):
        """
        初期化
        
        Parameters
        ----------
        model_dir : str
            モデルディレクトリ
        config : Dict[str, Any]
            設定辞書
        """
        self.model_dir = Path(model_dir)
        self.config = config or self._get_default_config()
        
        # LightGBM予測システム
        self.lgb_predictor = None
        
        # TensorFlow Ranking関連
        self.tfr_model = None
        self.tfr_analyzer = None
        
        # モデル選択戦略
        self.strategy = ModelSelectionStrategy()
        
        # 初期化
        self._initialize_systems()
        
        logger.info(f"ハイブリッド予測システム初期化完了")
        logger.info(f"LightGBM利用可能: {LGB_AVAILABLE}")
        logger.info(f"TensorFlow利用可能: {TF_AVAILABLE}")
        logger.info(f"TFRアナライザー利用可能: {TFR_ANALYZER_AVAILABLE}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """デフォルト設定を取得"""
        return {
            'ensemble': {
                'lgb_weight': 0.6,
                'tfr_weight': 0.4,
                'enable_dynamic_weights': True
            },
            'selection': {
                'data_completeness_threshold': 0.6,
                'min_field_size_for_tfr': 8,
                'force_tfr_for_grades': ['G1', 'G2', 'G3']
            },
            'missing_values': {
                'lgb_strategy': 'native',  # LightGBMの自動処理
                'tfr_strategy': 'impute'   # TFR用補完処理
            }
        }
    
    def _initialize_systems(self):
        """予測システムを初期化"""
        try:
            # LightGBM予測システム
            self.lgb_predictor = EnhancedLiveRacePredictorWithScraping(
                model_dir=str(self.model_dir),
                use_selenium=False,
                enable_live_scraping=True
            )
            
            # モデル読み込み
            if not self.lgb_predictor.load_latest_model():
                logger.warning("LightGBMモデルの読み込みに失敗")
            else:
                logger.info("LightGBM予測システム初期化完了")
            
            # TensorFlow Ranking関連
            if TF_AVAILABLE:
                self._initialize_tfr_system()
            
            # TFRアナライザー
            if TFR_ANALYZER_AVAILABLE:
                self.tfr_analyzer = TensorFlowRankingAnalyzer()
                logger.info("TFRアナライザー初期化完了")
            
        except Exception as e:
            logger.error(f"システム初期化エラー: {e}")
    
    def _initialize_tfr_system(self):
        """TensorFlow Rankingシステムを初期化"""
        try:
            # 最新のTFRモデルを検索
            tfr_model_dirs = list(self.model_dir.glob("*tfr_model*"))
            if tfr_model_dirs:
                latest_tfr_model = max(tfr_model_dirs, key=lambda x: x.stat().st_mtime)
                self.tfr_model = tf.saved_model.load(str(latest_tfr_model))
                logger.info(f"TFRモデル読み込み完了: {latest_tfr_model.name}")
            else:
                logger.warning("TFRモデルが見つかりません")
                
        except Exception as e:
            logger.error(f"TFRシステム初期化エラー: {e}")
    
    def preprocess_for_tfr(self, data: pd.DataFrame) -> pd.DataFrame:
        """TensorFlow Ranking用データ前処理"""
        try:
            data_tfr = data.copy()
            
            # 数値特徴量: 中央値補完
            numeric_cols = data_tfr.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if data_tfr[col].isna().any():
                    median_val = data_tfr[col].median()
                    if pd.isna(median_val):
                        median_val = 0.0
                    data_tfr[col] = data_tfr[col].fillna(median_val)
            
            # カテゴリカル特徴量: 'UNKNOWN'補完
            categorical_cols = data_tfr.select_dtypes(include=['object']).columns
            for col in categorical_cols:
                data_tfr[col] = data_tfr[col].fillna('UNKNOWN')
            
            # 無限値の処理
            data_tfr = data_tfr.replace([np.inf, -np.inf], np.nan)
            data_tfr = data_tfr.fillna(0)
            
            logger.debug(f"TFR前処理完了: {data_tfr.shape}")
            return data_tfr
            
        except Exception as e:
            logger.error(f"TFR前処理エラー: {e}")
            return data
    
    def predict_with_lightgbm(self, race_data: pd.DataFrame, race_info: Dict[str, Any]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """LightGBMで予測"""
        try:
            if not self.lgb_predictor:
                raise RuntimeError("LightGBM予測システムが初期化されていません")
            
            # 予測実行（enhanced_live_predictor_with_scrapingの機能を使用）
            results, processed_race_info = self.lgb_predictor.predict_race(race_info['race_id'])
            
            if not results.empty:
                scores = results['予測スコア'].values
                analysis = {
                    'model': 'lightgbm',
                    'scores': scores,
                    'results': results,
                    'race_info': processed_race_info
                }
                logger.info(f"LightGBM予測完了: {len(scores)}頭")
                return scores, analysis
            else:
                raise RuntimeError("LightGBM予測結果が空です")
                
        except Exception as e:
            logger.error(f"LightGBM予測エラー: {e}")
            # フォールバック: 均等確率
            field_size = len(race_data)
            fallback_scores = np.random.uniform(0.1, 0.9, field_size)
            return fallback_scores, {'model': 'lightgbm_fallback', 'error': str(e)}
    
    def predict_with_tfr(self, race_data: pd.DataFrame, race_info: Dict[str, Any]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """TensorFlow Rankingで予測"""
        try:
            if not TF_AVAILABLE or not self.tfr_model:
                raise RuntimeError("TensorFlow Rankingが利用できません")
            
            # TFR用データ前処理
            tfr_data = self.preprocess_for_tfr(race_data)
            
            # 特徴量準備（LightGBMの特徴量を流用）
            if hasattr(self.lgb_predictor, 'features'):
                available_features = [col for col in self.lgb_predictor.features if col in tfr_data.columns]
                if available_features:
                    X_tfr = tfr_data[available_features]
                else:
                    # フォールバック: 基本特徴量のみ
                    basic_features = ['枠番', '馬番', '斤量', 'course_len']
                    X_tfr = tfr_data[[col for col in basic_features if col in tfr_data.columns]]
            else:
                X_tfr = tfr_data.select_dtypes(include=[np.number])
            
            # TFR予測実行
            X_tensor = tf.constant(X_tfr.values, dtype=tf.float32)
            X_tensor = tf.expand_dims(X_tensor, 0)  # バッチ次元追加
            
            # 予測
            predictions = self.tfr_model(X_tensor)
            scores = tf.nn.softmax(predictions).numpy().flatten()
            
            analysis = {
                'model': 'tensorflow_ranking',
                'scores': scores,
                'features_used': list(X_tfr.columns),
                'preprocessed_shape': X_tfr.shape
            }
            
            logger.info(f"TFR予測完了: {len(scores)}頭")
            return scores, analysis
            
        except Exception as e:
            logger.error(f"TFR予測エラー: {e}")
            # フォールバック: 均等確率
            field_size = len(race_data)
            fallback_scores = np.random.uniform(0.1, 0.9, field_size)
            return fallback_scores, {'model': 'tfr_fallback', 'error': str(e)}
    
    def predict_ensemble(self, race_data: pd.DataFrame, race_info: Dict[str, Any]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """アンサンブル予測"""
        try:
            # 各モデルで予測
            lgb_scores, lgb_analysis = self.predict_with_lightgbm(race_data, race_info)
            tfr_scores, tfr_analysis = self.predict_with_tfr(race_data, race_info)
            
            # 動的重み調整
            if self.config['ensemble']['enable_dynamic_weights']:
                lgb_weight, tfr_weight = self._calculate_dynamic_weights(
                    race_data, race_info, lgb_analysis, tfr_analysis
                )
            else:
                lgb_weight = self.config['ensemble']['lgb_weight']
                tfr_weight = self.config['ensemble']['tfr_weight']
            
            # アンサンブル計算
            ensemble_scores = (lgb_weight * lgb_scores + tfr_weight * tfr_scores)
            
            # 正規化
            ensemble_scores = ensemble_scores / ensemble_scores.sum()
            
            analysis = {
                'model': 'ensemble',
                'scores': ensemble_scores,
                'weights': {'lightgbm': lgb_weight, 'tensorflow_ranking': tfr_weight},
                'component_analyses': {
                    'lightgbm': lgb_analysis,
                    'tensorflow_ranking': tfr_analysis
                }
            }
            
            logger.info(f"アンサンブル予測完了: LGB重み={lgb_weight:.2f}, TFR重み={tfr_weight:.2f}")
            return ensemble_scores, analysis
            
        except Exception as e:
            logger.error(f"アンサンブル予測エラー: {e}")
            # フォールバック: LightGBMのみ
            return self.predict_with_lightgbm(race_data, race_info)
    
    def _calculate_dynamic_weights(self, race_data: pd.DataFrame, race_info: Dict[str, Any], 
                                 lgb_analysis: Dict, tfr_analysis: Dict) -> Tuple[float, float]:
        """動的重み計算"""
        # データ完全性に基づく重み調整
        completeness = self.strategy.assess_data_completeness(race_data)
        importance = self.strategy.detect_race_importance(race_info)
        
        # 基本重み
        lgb_weight = self.config['ensemble']['lgb_weight']
        tfr_weight = self.config['ensemble']['tfr_weight']
        
        # 調整
        if completeness < 0.5:
            # データ不完全 → LightGBM重視
            lgb_weight = min(0.8, lgb_weight + 0.2)
            tfr_weight = 1.0 - lgb_weight
        elif importance == 'high':
            # 重要レース → TFR重視
            tfr_weight = min(0.7, tfr_weight + 0.2)
            lgb_weight = 1.0 - tfr_weight
        
        return lgb_weight, tfr_weight
    
    def predict_hybrid(self, race_id: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """ハイブリッド予測のメインインターフェース"""
        try:
            logger.info(f"ハイブリッド予測開始: {race_id}")
            
            # 出馬表データ取得（LightGBM予測システムを使用）
            if not self.lgb_predictor:
                raise RuntimeError("LightGBM予測システムが初期化されていません")
            
            race_data = self.lgb_predictor.scrape_race_card_requests(race_id)
            if race_data.empty:
                raise ValueError("出馬表データが取得できませんでした")
            
            # レース情報構築
            race_info = {
                'race_id': race_id,
                'course_len': self.lgb_predictor._get_smart_default_distance(race_id),
                **self.lgb_predictor._get_smart_default_conditions(race_id)
            }
            
            # 最適モデル選択
            selected_models = self.strategy.select_optimal_models(race_data, race_info)
            
            # 予測実行
            if len(selected_models) == 1:
                model_name = selected_models[0]
                if model_name == 'lightgbm':
                    scores, analysis = self.predict_with_lightgbm(race_data, race_info)
                else:
                    scores, analysis = self.predict_with_tfr(race_data, race_info)
            else:
                # アンサンブル予測
                scores, analysis = self.predict_ensemble(race_data, race_info)
            
            # 結果整理
            results = self._format_prediction_results(race_data, scores, analysis)
            
            logger.info(f"ハイブリッド予測完了: {len(results)}頭")
            return results, analysis
            
        except Exception as e:
            logger.error(f"ハイブリッド予測エラー: {e}")
            return pd.DataFrame(), {'error': str(e)}
    
    def _format_prediction_results(self, race_data: pd.DataFrame, scores: np.ndarray, 
                                 analysis: Dict[str, Any]) -> pd.DataFrame:
        """予測結果をフォーマット"""
        try:
            # 基本結果
            results = race_data[['枠番', '馬番', '馬名']].copy()
            
            # 予測結果追加
            results['予測スコア'] = scores
            results['予測順位'] = scores.argsort()[::-1].argsort() + 1
            
            # 勝率正規化
            total_score = scores.sum()
            if total_score > 0:
                results['勝率'] = (scores / total_score * 100).round(1)
            else:
                results['勝率'] = (np.ones(len(scores)) / len(scores) * 100).round(1)
            
            # 3着以内確率
            results['3着以内確率'] = (scores * 100).round(1)
            
            # モデル情報
            results['使用モデル'] = analysis.get('model', 'unknown')
            
            # 順位でソート
            results = results.sort_values('予測順位')
            
            return results
            
        except Exception as e:
            logger.error(f"結果フォーマットエラー: {e}")
            return pd.DataFrame()

def main():
    """テスト実行"""
    try:
        print("=== ハイブリッド予測システムテスト ===")
        
        # システム初期化
        hybrid_system = HybridRacePredictionSystem()
        
        # テスト予測
        test_race_id = "202406080101"
        results, analysis = hybrid_system.predict_hybrid(test_race_id)
        
        if not results.empty:
            print(f"\n予測結果 (レースID: {test_race_id}):")
            print(results.to_string(index=False))
            print(f"\n使用モデル: {analysis.get('model', 'unknown')}")
        else:
            print("予測に失敗しました")
        
    except Exception as e:
        print(f"テストエラー: {e}")

if __name__ == "__main__":
    main()