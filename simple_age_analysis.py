#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
シンプルな年齢分析
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# プロジェクトパスの追加
sys.path.append('.')

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simple_age_calculation():
    """シンプルな年齢計算の実例"""
    logger.info("シンプルな年齢計算実例")
    
    try:
        # 修正済みデータを読み込み
        data = pd.read_pickle('enhanced_comprehensive_data_2020.pickle')
        logger.info(f"データ読み込み完了: {len(data):,}件")
        
        # 最初の100件をサンプルとして使用
        sample_data = data.head(100).copy()
        
        # レース日を設定
        sample_data['race_date'] = pd.to_datetime('2020-06-01')
        
        # サンプル生年月日を生成（現実的な分布）
        base_date = pd.to_datetime('2020-01-01')
        ages_in_days = np.random.choice([
            365 * 2 + 180,  # 2.5歳
            365 * 3 + 90,   # 3.25歳
            365 * 4 + 120,  # 4.3歳
            365 * 5 + 200,  # 5.5歳
            365 * 6 + 50,   # 6.1歳
            365 * 7 + 100   # 7.3歳
        ], size=len(sample_data))
        
        sample_data['birthday'] = sample_data['race_date'] - pd.to_timedelta(ages_in_days, unit='D')
        
        # 生後日数を手動計算
        sample_data['days_old'] = (sample_data['race_date'] - sample_data['birthday']).dt.days
        
        # 年齢を計算
        sample_data['age_years'] = sample_data['days_old'] / 365.25
        
        # 年齢カテゴリを設定
        sample_data['age_category'] = pd.cut(
            sample_data['age_years'],
            bins=[0, 3, 6, 20],
            labels=['若駒', '盛期', 'ベテラン'],
            include_lowest=True
        )
        
        print("\n年齢計算結果（最初の10頭）:")
        display_cols = ['horse_id', 'birthday', 'race_date', 'days_old', 'age_years', 'age_category']
        print(sample_data[display_cols].head(10).round(2).to_string(index=False))
        
        # 年齢分布統計
        print(f"\n年齢分布統計:")
        print(f"  サンプル数: {len(sample_data)}頭")
        print(f"  平均年齢: {sample_data['age_years'].mean():.2f}歳")
        print(f"  年齢範囲: {sample_data['age_years'].min():.1f} - {sample_data['age_years'].max():.1f}歳")
        
        # カテゴリ別分布
        category_counts = sample_data['age_category'].value_counts()
        print(f"\n年齢カテゴリ別分布:")
        for category, count in category_counts.items():
            percentage = (count / len(sample_data)) * 100
            print(f"  {category}: {count}頭 ({percentage:.1f}%)")
        
        return sample_data
        
    except Exception as e:
        logger.error(f"エラー: {e}")
        import traceback
        traceback.print_exc()
        return None

def practical_race_example():
    """実用的なレース例"""
    logger.info("実用的なレース例")
    
    # 具体的なレースのサンプル
    race_example = pd.DataFrame({
        'horse_id': ['A001', 'A002', 'A003', 'A004', 'A005', 'A006', 'A007', 'A008'],
        'horse_name': ['スピードスター', 'パワフル', 'エレガント', 'ダイナミック', 
                      'クイック', 'ストロング', 'グレース', 'マイティ'],
        'birthday': [
            '2021-03-15',  # 3歳
            '2020-04-20',  # 4歳
            '2019-05-10',  # 5歳
            '2018-02-28',  # 6歳
            '2017-07-15',  # 7歳
            '2021-01-25',  # 3歳
            '2020-06-08',  # 4歳
            '2019-09-12'   # 5歳
        ],
        'race_date': '2024-06-08'  # 今日のレース
    })
    
    # 日付を変換
    race_example['birthday'] = pd.to_datetime(race_example['birthday'])
    race_example['race_date'] = pd.to_datetime(race_example['race_date'])
    
    # 生後日数と年齢を計算
    race_example['days_old'] = (race_example['race_date'] - race_example['birthday']).dt.days
    race_example['age_years'] = race_example['days_old'] / 365.25
    
    # 年齢カテゴリ
    def categorize_age(age):
        if age <= 3:
            return '若駒'
        elif age <= 6:
            return '盛期'
        else:
            return 'ベテラン'
    
    race_example['age_category'] = race_example['age_years'].apply(categorize_age)
    
    print(f"\n実用例: 2024年6月8日のレース（8頭立て）")
    print("-" * 60)
    display_cols = ['horse_name', 'birthday', 'days_old', 'age_years', 'age_category']
    print(race_example[display_cols].round(2).to_string(index=False))
    
    # レース分析
    print(f"\nレース分析:")
    print(f"  出走頭数: {len(race_example)}頭")
    print(f"  平均年齢: {race_example['age_years'].mean():.2f}歳")
    print(f"  最年少: {race_example['age_years'].min():.1f}歳 ({race_example.loc[race_example['age_years'].idxmin(), 'horse_name']})")
    print(f"  最年長: {race_example['age_years'].max():.1f}歳 ({race_example.loc[race_example['age_years'].idxmax(), 'horse_name']})")
    
    category_dist = race_example['age_category'].value_counts()
    print(f"\n年齢構成:")
    for cat, count in category_dist.items():
        print(f"  {cat}: {count}頭")
    
    return race_example

def age_advantage_analysis():
    """年齢による有利不利の簡易分析"""
    logger.info("年齢有利不利分析")
    
    # サンプルデータで成績シミュレーション
    np.random.seed(42)
    
    ages = np.random.uniform(2.5, 8.0, 1000)  # 2.5-8歳の馬
    
    # 年齢による勝率の仮想モデル（盛期が有利）
    def calculate_win_probability(age):
        if age < 3:
            return 0.05  # 若駒は経験不足
        elif 3 <= age <= 6:
            return 0.12  # 盛期は高い勝率
        else:
            return 0.08  # ベテランは経験あるが体力低下
    
    win_probs = [calculate_win_probability(age) for age in ages]
    
    # データフレーム作成
    simulation_data = pd.DataFrame({
        'age': ages,
        'win_probability': win_probs,
        'age_category': ['若駒' if age < 3 else '盛期' if age <= 6 else 'ベテラン' for age in ages]
    })
    
    # 年齢カテゴリ別統計
    category_stats = simulation_data.groupby('age_category').agg({
        'age': ['count', 'mean'],
        'win_probability': 'mean'
    }).round(3)
    
    print(f"\n年齢による勝率分析（シミュレーション）:")
    print("-" * 50)
    
    for category in ['若駒', '盛期', 'ベテラン']:
        if category in category_stats.index:
            count = category_stats.loc[category, ('age', 'count')]
            avg_age = category_stats.loc[category, ('age', 'mean')]
            win_rate = category_stats.loc[category, ('win_probability', 'mean')]
            
            print(f"{category}:")
            print(f"  サンプル数: {count}")
            print(f"  平均年齢: {avg_age:.1f}歳")
            print(f"  期待勝率: {win_rate:.1%}")
            print()
    
    return simulation_data

def main():
    """メイン実行"""
    logger.info("シンプル年齢分析デモ開始")
    
    # 1. 基本的な年齢計算
    print("="*60)
    print("1. 基本的な年齢計算")
    print("="*60)
    sample_result = simple_age_calculation()
    
    # 2. 実用的なレース例
    print("\n" + "="*60)
    print("2. 実用的なレース例")
    print("="*60)
    race_result = practical_race_example()
    
    # 3. 年齢有利不利分析
    print("\n" + "="*60)
    print("3. 年齢有利不利分析")
    print("="*60)
    advantage_result = age_advantage_analysis()
    
    logger.info("デモ完了")

if __name__ == "__main__":
    main()