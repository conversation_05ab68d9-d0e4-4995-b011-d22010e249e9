# PowerShell Startup Script for Keiba AI System
# Execution Policy Check
$ExecutionPolicy = Get-ExecutionPolicy
if ($ExecutionPolicy -eq "Restricted") {
    Write-Host "WARNING: PowerShell execution policy is restricted" -ForegroundColor Yellow
    Write-Host "Please run the following command as administrator:" -ForegroundColor Yellow
    Write-Host "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Cyan
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Starting Keiba AI Prediction System..." -ForegroundColor Green
Write-Host ""

# Save current directory
$OriginalLocation = Get-Location

# Move to script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "Working Directory: $ScriptDir" -ForegroundColor Cyan

# Virtual environment activation
$VenvActivated = $false

if (Test-Path "venv\Scripts\Activate.ps1") {
    Write-Host "Virtual environment found, activating..." -ForegroundColor Green
    try {
        # Activate virtual environment and set environment variables
        . "venv\Scripts\Activate.ps1"
        $VenvActivated = $true
        Write-Host "Virtual environment activated successfully" -ForegroundColor Green
    } catch {
        Write-Host "Virtual environment activation error: $_" -ForegroundColor Red
    }
} elseif (Test-Path "activate_env.ps1") {
    Write-Host "Running activate_env.ps1..." -ForegroundColor Yellow
    try {
        . ".\activate_env.ps1"
        $VenvActivated = $true
        Write-Host "activate_env.ps1 executed successfully" -ForegroundColor Green
    } catch {
        Write-Host "activate_env.ps1 execution error: $_" -ForegroundColor Red
    }
} elseif (Test-Path "activate_env.bat") {
    Write-Host "Found activate_env.bat. Running batch version..." -ForegroundColor Yellow
    Write-Host "PowerShell cannot directly execute bat files" -ForegroundColor Yellow
    Write-Host "Please run start_keiba_ai.bat in Command Prompt" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    Set-Location $OriginalLocation
    exit 1
}

if (-not $VenvActivated) {
    Write-Host "Virtual environment setup files not found" -ForegroundColor Red
    Write-Host "One of the following files is required:" -ForegroundColor Yellow
    Write-Host "  - venv\Scripts\Activate.ps1" -ForegroundColor Yellow
    Write-Host "  - activate_env.ps1" -ForegroundColor Yellow
    Write-Host "  - activate_env.bat (for Command Prompt)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Please manually activate virtual environment and run again" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    Set-Location $OriginalLocation
    exit 1
}

# Python environment check
Write-Host ""
Write-Host "Checking Python environment..." -ForegroundColor Cyan
try {
    $PythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Python environment: $PythonVersion" -ForegroundColor Green
    } else {
        throw "Python execution failed"
    }
} catch {
    Write-Host "Python not found: $_" -ForegroundColor Red
    Write-Host "Please check if virtual environment is properly activated" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    Set-Location $OriginalLocation
    exit 1
}

# Main file existence check
if (-not (Test-Path "keiba_ai_main.py")) {
    Write-Host "keiba_ai_main.py not found" -ForegroundColor Red
    Write-Host "Please run from the project directory" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    Set-Location $OriginalLocation
    exit 1
}

Write-Host ""
Write-Host "Starting main system..." -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Gray
Write-Host ""

# Main program execution
try {
    $StartTime = Get-Date
    python keiba_ai_main.py
    $EndTime = Get-Date
    $Duration = $EndTime - $StartTime
    
    Write-Host ""
    Write-Host "=" * 50 -ForegroundColor Gray
    Write-Host "Program execution completed" -ForegroundColor Green
    Write-Host "Execution time: $($Duration.ToString('hh\:mm\:ss'))" -ForegroundColor Cyan
} catch {
    Write-Host ""
    Write-Host "=" * 50 -ForegroundColor Gray
    Write-Host "Error occurred: $_" -ForegroundColor Red
    Write-Host "Detailed error information:" -ForegroundColor Yellow
    Write-Host $_.Exception.Message -ForegroundColor Yellow
    if ($_.Exception.InnerException) {
        Write-Host "Inner error: $($_.Exception.InnerException.Message)" -ForegroundColor Yellow
    }
} finally {
    # Return to original directory
    Set-Location $OriginalLocation
}

Write-Host ""
Write-Host "Keiba AI Prediction System terminated" -ForegroundColor Blue
Read-Host "Press Enter to close window"