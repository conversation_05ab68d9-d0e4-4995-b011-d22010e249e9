# データリーケージ修正実装ガイド

## 1. 即時対応手順

### Step 1: バックアップの作成
```bash
# 現在の実装をバックアップ
cp core/features/calculators.py core/features/calculators_backup.py
cp core/features/manager.py core/features/manager_backup.py
```

### Step 2: 修正版の適用
```bash
# 修正版を本番環境に適用
cp core/features/fixed/calculators_fixed.py core/features/calculators.py
```

### Step 3: manager.pyの更新
以下の変更を`manager.py`に適用：

```python
# 変更前
from core.features.calculators import FeatureCalculators

# 変更後
from core.features.calculators import FeatureCalculatorsFixed as FeatureCalculators
```

## 2. 特徴量計算の更新

### 必須パラメータの追加
全ての特徴量計算で`race_date_column`を必須にする：

```python
# 変更前
def calculate_features(self, data: pd.DataFrame, 
                      feature_names: Optional[List[str]] = None,
                      dependencies: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
    # ...

# 変更後  
def calculate_features(self, data: pd.DataFrame,
                      feature_names: Optional[List[str]] = None,
                      dependencies: Optional[Dict[str, Any]] = None,
                      race_date_column: str = 'date') -> pd.DataFrame:
    """
    Parameters
    ----------
    race_date_column : str
        レース日カラム名（データリーケージ防止のため必須）
    """
    if race_date_column not in data.columns:
        raise ValueError(f"レース日カラム '{race_date_column}' が見つかりません。データリーケージを防ぐため必須です。")
```

## 3. 既存コードの修正例

### 例1: 特徴量作成スクリプト
```python
# 修正前
features_df = feature_manager.calculate_features(
    race_data,
    dependencies={'horse_results_df': past_results}
)

# 修正後
features_df = feature_manager.calculate_features(
    race_data,
    dependencies={'horse_results_df': past_results},
    race_date_column='date'  # 必ず指定
)
```

### 例2: 過去成績の集計
```python
# 修正前（問題あり）
def get_horse_stats(horse_id, results_df):
    horse_data = results_df[results_df['horse_id'] == horse_id]
    return {
        'win_rate': (horse_data['着順'] == 1).mean(),
        'avg_rank': horse_data['着順'].mean()
    }

# 修正後（正しい）
def get_horse_stats(horse_id, results_df, target_date):
    # 対象日より前のデータのみ使用
    past_data = results_df[
        (results_df['horse_id'] == horse_id) & 
        (results_df['日付'] < target_date)
    ]
    if len(past_data) == 0:
        return {'win_rate': 0.0, 'avg_rank': None}
    
    return {
        'win_rate': (past_data['着順'] == 1).mean(),
        'avg_rank': past_data['着順'].mean()
    }
```

## 4. テストの実装

### ユニットテスト例
```python
import unittest
from datetime import datetime
import pandas as pd

class TestDataLeakagePrevention(unittest.TestCase):
    
    def setUp(self):
        # テストデータの準備
        self.calculator = FeatureCalculatorsFixed()
        self.test_date = datetime(2024, 6, 1)
        
    def test_no_future_data_in_win_rate(self):
        """勝率計算で未来データが含まれないことを確認"""
        # 過去と未来のデータを含むDataFrameを作成
        results = pd.DataFrame([
            {'horse_id': 'A', '日付': '2024-05-01', '着順': 1},
            {'horse_id': 'A', '日付': '2024-07-01', '着順': 1},  # 未来
        ])
        
        # 対象レース
        target = pd.DataFrame([
            {'horse_id': 'A', 'date': self.test_date}
        ])
        
        # 勝率を計算
        win_rate = self.calculator.calculate_win_rate(
            target, results, race_date_column='date'
        )
        
        # 期待値: 過去1レースで1勝 = 100%
        self.assertEqual(win_rate.iloc[0], 1.0)
```

## 5. 検証手順

### データリーケージチェックスクリプト
```python
def validate_no_data_leakage(train_features, train_labels, validation_date):
    """
    訓練データにデータリーケージがないことを検証
    """
    issues = []
    
    # 1. 日付カラムの存在確認
    if 'date' not in train_features.columns:
        issues.append("日付カラムが見つかりません")
    
    # 2. 未来データの確認
    future_data = train_features[train_features['date'] >= validation_date]
    if len(future_data) > 0:
        issues.append(f"未来のデータが{len(future_data)}件含まれています")
    
    # 3. 特徴量の妥当性確認
    # 例: 勝率が異常に高い場合は要確認
    if 'win_rate' in train_features.columns:
        high_win_rate = train_features[train_features['win_rate'] > 0.8]
        if len(high_win_rate) > 0:
            issues.append("異常に高い勝率の馬が存在します")
    
    return issues
```

## 6. 移行計画

### Phase 1: 開発環境での検証（1週間）
- [ ] 修正版コードのテスト
- [ ] 既存モデルとの性能比較
- [ ] エッジケースの確認

### Phase 2: ステージング環境での検証（1週間）
- [ ] 大規模データでのテスト
- [ ] パフォーマンステスト
- [ ] 既存システムとの統合テスト

### Phase 3: 本番環境への適用（2日間）
- [ ] バックアップの作成
- [ ] 段階的なロールアウト
- [ ] モニタリングの強化

## 7. モニタリング

### 監視項目
```python
# ログ出力の追加
logger.info(f"特徴量計算: {len(data)}件のレースデータを処理")
logger.info(f"フィルタリング前: {len(horse_results_df)}件")
logger.info(f"フィルタリング後: {len(filtered_df)}件")
logger.warning(f"除外されたデータ: {len(horse_results_df) - len(filtered_df)}件")
```

### アラート設定
- 勝率が90%を超える馬が存在する場合
- フィルタリング後のデータが0件の場合
- 処理時間が通常の2倍を超える場合

## 8. よくある質問

### Q1: 修正後、モデルの精度が下がった
A: これは正常です。以前は未来のデータを使っていたため、不当に高い精度が出ていました。現在の精度が実運用時の真の性能です。

### Q2: 処理時間が増加した
A: 各レースごとに日付フィルタリングを行うため、処理時間は増加します。以下の最適化を検討してください：
- 事前に日付でソート
- インデックスの活用
- 並列処理の導入

### Q3: 新馬（過去データなし）の扱い
A: デフォルト値（勝率0%など）を設定し、別途「データ不足フラグ」を立てることを推奨します。

## 9. 長期的な改善提案

1. **時系列交差検証の導入**
   ```python
   from sklearn.model_selection import TimeSeriesSplit
   ```

2. **特徴量ストアの構築**
   - 日付ベースでの特徴量管理
   - Point-in-time correctnessの保証

3. **自動テストの強化**
   - CI/CDパイプラインでのデータリーケージチェック
   - 定期的な特徴量品質レポート

このガイドに従って実装を進めることで、データリーケージのない健全な機械学習システムを構築できます。
