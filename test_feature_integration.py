#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
機械学習における特徴量統合テスト

レース基本情報、レース詳細情報、馬の基本情報、馬の過去戦績、馬の過去コーナー順位が
特徴量として適切に反映されているかをテストする。
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# プロジェクトのモジュールをインポート
sys.path.append('.')
from core.features.manager import FeatureEngineeringManager
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
from core.utils.constants import ComprehensiveIntegratorConfig

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FeatureIntegrationTester:
    """特徴量統合テスト用クラス"""
    
    def __init__(self, test_year: str = "2020"):
        """
        初期化
        
        Parameters
        ----------
        test_year : str
            テスト対象年度
        """
        self.test_year = test_year
        self.test_results = {}
        
        # テスト設定
        self.test_config = ComprehensiveIntegratorConfig(
            use_pickle_source=True,  # pickleファイルを使用して高速化
            include_corner_features=True,  # コーナー特徴量を含める
            parallel=True,
            max_workers=2
        )
        
        # 特徴量エンジニアリングマネージャーを初期化
        self.feature_manager = FeatureEngineeringManager()
        
        # データ統合器を初期化
        self.data_integrator = ComprehensiveDataIntegrator(config=self.test_config.__dict__)
        
    def test_basic_race_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        レース基本情報特徴量のテスト
        
        Parameters
        ----------
        data : pd.DataFrame
            統合データ
            
        Returns
        -------
        Dict[str, Any]
            テスト結果
        """
        logger.info("レース基本情報特徴量のテストを開始...")
        
        results = {
            'test_name': 'レース基本情報特徴量',
            'status': 'PASS',
            'details': {},
            'missing_features': [],
            'unexpected_issues': []
        }
        
        # 期待される基本レース特徴量
        expected_race_features = [
            '距離',           # レース距離
            '開催',           # 開催場
            '馬場状態',       # 馬場状態  
            '天気',           # 天気
            '頭数',           # 出走頭数
        ]
        
        # 各特徴量の存在チェック
        for feature in expected_race_features:
            if feature in data.columns:
                non_null_count = data[feature].notna().sum()
                results['details'][feature] = {
                    'exists': True,
                    'non_null_count': int(non_null_count),
                    'coverage': float(non_null_count / len(data))
                }
                logger.info(f"✓ {feature}: {non_null_count}/{len(data)} ({non_null_count/len(data)*100:.1f}%)")
            else:
                results['missing_features'].append(feature)
                results['status'] = 'PARTIAL'
                logger.warning(f"✗ {feature}: 見つかりません")
        
        return results
    
    def test_horse_basic_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        馬の基本情報特徴量のテスト
        
        Parameters
        ----------
        data : pd.DataFrame
            統合データ
            
        Returns
        -------
        Dict[str, Any]
            テスト結果
        """
        logger.info("馬基本情報特徴量のテストを開始...")
        
        results = {
            'test_name': '馬基本情報特徴量',
            'status': 'PASS',
            'details': {},
            'missing_features': [],
            'unexpected_issues': []
        }
        
        # 期待される馬基本特徴量
        expected_horse_features = [
            '馬番',           # 馬番
            '枠番',           # 枠番
            '性別',           # 性別
            '年齢',           # 年齢
            '斤量',           # 斤量
            '騎手',           # 騎手名
            '調教師',         # 調教師名
            'father_id',      # 父馬ID（血統）
            'mother_father_id' # 母父馬ID（血統）
        ]
        
        # 各特徴量の存在チェック
        for feature in expected_horse_features:
            if feature in data.columns:
                non_null_count = data[feature].notna().sum()
                results['details'][feature] = {
                    'exists': True,
                    'non_null_count': int(non_null_count),
                    'coverage': float(non_null_count / len(data))
                }
                logger.info(f"✓ {feature}: {non_null_count}/{len(data)} ({non_null_count/len(data)*100:.1f}%)")
            else:
                results['missing_features'].append(feature)
                results['status'] = 'PARTIAL'
                logger.warning(f"✗ {feature}: 見つかりません")
        
        return results
    
    def test_horse_performance_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        馬の過去成績特徴量のテスト
        
        Parameters
        ----------
        data : pd.DataFrame
            統合データ
            
        Returns
        -------
        Dict[str, Any]
            テスト結果
        """
        logger.info("馬過去成績特徴量のテストを開始...")
        
        results = {
            'test_name': '馬過去成績特徴量',
            'status': 'PASS',
            'details': {},
            'missing_features': [],
            'unexpected_issues': []
        }
        
        # 期待される過去成績特徴量
        expected_performance_features = [
            'total_races',        # 総出走回数
            'win_rate',          # 勝率
            'place_rate',        # 連対率
            'show_rate',         # 複勝率
            'avg_rank',          # 平均着順
            'avg_prize',         # 平均賞金
            'last_race_rank',    # 前走着順
            'days_since_last'    # 前走からの間隔
        ]
        
        # パフォーマンス統計の列名パターンもチェック
        performance_patterns = [
            '_races_',  # 過去N戦の統計
            '_win_rate', 
            '_avg_rank',
            '_place_rate'
        ]
        
        # 基本的な過去成績特徴量をチェック
        for feature in expected_performance_features:
            found_columns = [col for col in data.columns if feature in col]
            if found_columns:
                # 最初に見つかった列について詳細をチェック
                col = found_columns[0]
                non_null_count = data[col].notna().sum()
                results['details'][feature] = {
                    'exists': True,
                    'actual_column': col,
                    'non_null_count': int(non_null_count),
                    'coverage': float(non_null_count / len(data)),
                    'all_matching_columns': found_columns
                }
                logger.info(f"✓ {feature} (as {col}): {non_null_count}/{len(data)} ({non_null_count/len(data)*100:.1f}%)")
            else:
                results['missing_features'].append(feature)
                results['status'] = 'PARTIAL'
                logger.warning(f"✗ {feature}: 見つかりません")
        
        # パターンマッチング
        for pattern in performance_patterns:
            matching_cols = [col for col in data.columns if pattern in col]
            if matching_cols:
                logger.info(f"✓ パターン '{pattern}' にマッチする列: {len(matching_cols)}個")
                results['details'][f'pattern_{pattern}'] = {
                    'matching_columns': matching_cols,
                    'count': len(matching_cols)
                }
            else:
                logger.warning(f"✗ パターン '{pattern}' にマッチする列が見つかりません")
        
        return results
    
    def test_corner_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        コーナー順位特徴量のテスト
        
        Parameters
        ----------
        data : pd.DataFrame
            統合データ
            
        Returns
        -------
        Dict[str, Any]
            テスト結果
        """
        logger.info("コーナー順位特徴量のテストを開始...")
        
        results = {
            'test_name': 'コーナー順位特徴量',
            'status': 'PASS',
            'details': {},
            'missing_features': [],
            'unexpected_issues': []
        }
        
        # コーナー関連の列パターン
        corner_patterns = [
            'corner_',      # コーナー通過順位
            'passing_',     # 通過順位
            'position_',    # ポジション
            'diff_',        # 差
            '1コーナー',
            '2コーナー',
            '3コーナー',
            '4コーナー'
        ]
        
        total_corner_features = 0
        
        for pattern in corner_patterns:
            matching_cols = [col for col in data.columns if pattern in col.lower()]
            if matching_cols:
                total_corner_features += len(matching_cols)
                sample_col = matching_cols[0]
                non_null_count = data[sample_col].notna().sum()
                
                results['details'][f'pattern_{pattern}'] = {
                    'matching_columns': matching_cols,
                    'count': len(matching_cols),
                    'sample_column': sample_col,
                    'sample_non_null_count': int(non_null_count),
                    'sample_coverage': float(non_null_count / len(data))
                }
                logger.info(f"✓ パターン '{pattern}': {len(matching_cols)}個の列")
            else:
                results['missing_features'].append(pattern)
                logger.warning(f"✗ パターン '{pattern}': 見つかりません")
        
        if total_corner_features == 0:
            results['status'] = 'FAIL'
            results['unexpected_issues'].append('コーナー特徴量が全く見つかりません')
        elif total_corner_features < 4:  # 最低4つのコーナー分は欲しい
            results['status'] = 'PARTIAL'
            results['unexpected_issues'].append(f'コーナー特徴量が少なすぎます: {total_corner_features}個')
        
        results['details']['total_corner_features'] = total_corner_features
        
        return results
    
    def test_age_features(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        年齢・生後日数特徴量のテスト
        
        Parameters
        ----------
        data : pd.DataFrame
            統合データ
            
        Returns
        -------
        Dict[str, Any]
            テスト結果
        """
        logger.info("年齢・生後日数特徴量のテストを開始...")
        
        results = {
            'test_name': '年齢・生後日数特徴量',
            'status': 'PASS',
            'details': {},
            'missing_features': [],
            'unexpected_issues': []
        }
        
        # 期待される年齢特徴量
        expected_age_features = [
            'days_old',           # 生後日数
            'age_years',          # 年齢（年単位）
            'age_months',         # 月齢
            'is_young',           # 若駒フラグ
            'is_prime',           # 盛期フラグ
            'is_veteran',         # ベテランフラグ
            'experience_density', # 経験密度
            'young_prospect',     # 若手有望フラグ
            'mature_period'       # 円熟期フラグ
        ]
        
        for feature in expected_age_features:
            if feature in data.columns:
                non_null_count = data[feature].notna().sum()
                results['details'][feature] = {
                    'exists': True,
                    'non_null_count': int(non_null_count),
                    'coverage': float(non_null_count / len(data))
                }
                
                # 特徴量の値の範囲チェック
                if feature == 'days_old':
                    valid_values = data[feature][(data[feature] > 0) & (data[feature] < 15000)].count()
                    results['details'][feature]['valid_range_count'] = int(valid_values)
                elif feature in ['is_young', 'is_prime', 'is_veteran', 'young_prospect', 'mature_period']:
                    binary_values = data[feature][data[feature].isin([0, 1])].count()
                    results['details'][feature]['binary_values_count'] = int(binary_values)
                
                logger.info(f"✓ {feature}: {non_null_count}/{len(data)} ({non_null_count/len(data)*100:.1f}%)")
            else:
                results['missing_features'].append(feature)
                results['status'] = 'PARTIAL'
                logger.warning(f"✗ {feature}: 見つかりません")
        
        return results
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """
        包括的な特徴量統合テストを実行
        
        Returns
        -------
        Dict[str, Any]
            全テスト結果
        """
        logger.info(f"=== 特徴量統合テスト開始（年度: {self.test_year}）===")
        
        test_summary = {
            'test_year': self.test_year,
            'overall_status': 'PASS',
            'data_info': {},
            'test_results': {},
            'recommendations': []
        }
        
        try:
            # 1. データ統合の実行
            logger.info("データ統合を実行中...")
            
            comprehensive_data = self.data_integrator.generate_comprehensive_table(
                year=self.test_year,
                include_race_info=True,
                include_horse_info=True,
                include_past_performance=True,
                use_pickle_source=True,  # 高速化のためpickleを使用
                parallel=True
            )
            
            if comprehensive_data.empty:
                test_summary['overall_status'] = 'FAIL'
                test_summary['data_info']['error'] = 'データ統合結果が空です'
                return test_summary
            
            # データ情報
            test_summary['data_info'] = {
                'total_rows': len(comprehensive_data),
                'total_columns': len(comprehensive_data.columns),
                'column_names': list(comprehensive_data.columns[:50]),  # 最初の50列のみ表示
                'memory_usage_mb': float(comprehensive_data.memory_usage(deep=True).sum() / 1024 / 1024)
            }
            
            logger.info(f"統合データ: {len(comprehensive_data)}行 × {len(comprehensive_data.columns)}列")
            
            # 2. 特徴量エンジニアリングの適用
            logger.info("特徴量エンジニアリングを適用中...")
            
            # 基本的な依存データを準備
            dependencies = {
                'horse_results_df': comprehensive_data,  # 過去成績計算用
                'race_date': comprehensive_data.get('date', comprehensive_data.get('日付'))
            }
            
            # 特徴量を計算
            enhanced_data = self.feature_manager.calculate_features(
                data=comprehensive_data,
                dependencies=dependencies
            )
            
            logger.info(f"特徴量エンジニアリング後: {len(enhanced_data)}行 × {len(enhanced_data.columns)}列")
            
            # 3. 個別テストの実行
            test_summary['test_results']['basic_race'] = self.test_basic_race_features(enhanced_data)
            test_summary['test_results']['horse_basic'] = self.test_horse_basic_features(enhanced_data)
            test_summary['test_results']['horse_performance'] = self.test_horse_performance_features(enhanced_data)
            test_summary['test_results']['corner_features'] = self.test_corner_features(enhanced_data)
            test_summary['test_results']['age_features'] = self.test_age_features(enhanced_data)
            
            # 4. 全体ステータスの決定
            failed_tests = [name for name, result in test_summary['test_results'].items() 
                          if result['status'] == 'FAIL']
            partial_tests = [name for name, result in test_summary['test_results'].items() 
                           if result['status'] == 'PARTIAL']
            
            if failed_tests:
                test_summary['overall_status'] = 'FAIL'
                test_summary['recommendations'].append(f"失敗したテスト: {', '.join(failed_tests)}")
            elif partial_tests:
                test_summary['overall_status'] = 'PARTIAL'
                test_summary['recommendations'].append(f"部分的に成功したテスト: {', '.join(partial_tests)}")
            
            # 5. 推奨事項の追加
            if test_summary['data_info']['total_rows'] < 1000:
                test_summary['recommendations'].append("データ量が少ない可能性があります。より多くのデータで再テストを検討してください。")
            
            missing_features_count = sum(len(result.get('missing_features', [])) 
                                       for result in test_summary['test_results'].values())
            if missing_features_count > 5:
                test_summary['recommendations'].append(f"多くの特徴量が見つかりません ({missing_features_count}個)。データ処理パイプラインを確認してください。")
            
        except Exception as e:
            logger.error(f"テスト実行中にエラー: {e}")
            test_summary['overall_status'] = 'ERROR'
            test_summary['error'] = str(e)
            import traceback
            test_summary['traceback'] = traceback.format_exc()
        
        return test_summary
    
    def save_test_report(self, test_results: Dict[str, Any], output_path: str = "feature_integration_test_report.txt"):
        """
        テスト結果をファイルに保存
        
        Parameters
        ----------
        test_results : Dict[str, Any]
            テスト結果
        output_path : str
            出力ファイルパス
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("=== 機械学習特徴量統合テストレポート ===\n\n")
                f.write(f"テスト日時: {pd.Timestamp.now()}\n")
                f.write(f"テスト年度: {test_results['test_year']}\n")
                f.write(f"全体ステータス: {test_results['overall_status']}\n\n")
                
                # データ情報
                f.write("=== データ情報 ===\n")
                data_info = test_results['data_info']
                f.write(f"総行数: {data_info.get('total_rows', 'N/A')}\n")
                f.write(f"総列数: {data_info.get('total_columns', 'N/A')}\n")
                f.write(f"メモリ使用量: {data_info.get('memory_usage_mb', 'N/A'):.2f} MB\n\n")
                
                # 個別テスト結果
                f.write("=== 個別テスト結果 ===\n")
                for test_name, result in test_results.get('test_results', {}).items():
                    f.write(f"\n--- {result['test_name']} ---\n")
                    f.write(f"ステータス: {result['status']}\n")
                    f.write(f"見つからない特徴量: {len(result['missing_features'])}個\n")
                    if result['missing_features']:
                        f.write(f"  {', '.join(result['missing_features'])}\n")
                    f.write(f"予期しない問題: {len(result['unexpected_issues'])}個\n")
                    if result['unexpected_issues']:
                        for issue in result['unexpected_issues']:
                            f.write(f"  - {issue}\n")
                
                # 推奨事項
                if test_results.get('recommendations'):
                    f.write("\n=== 推奨事項 ===\n")
                    for i, rec in enumerate(test_results['recommendations'], 1):
                        f.write(f"{i}. {rec}\n")
                
                # エラー情報
                if 'error' in test_results:
                    f.write(f"\n=== エラー情報 ===\n")
                    f.write(f"エラー: {test_results['error']}\n")
                    if 'traceback' in test_results:
                        f.write(f"詳細:\n{test_results['traceback']}\n")
            
            logger.info(f"テストレポートを保存しました: {output_path}")
            
        except Exception as e:
            logger.error(f"テストレポートの保存に失敗: {e}")


def main():
    """メイン関数"""
    # テスト実行
    tester = FeatureIntegrationTester(test_year="2020")
    test_results = tester.run_comprehensive_test()
    
    # 結果表示
    print(f"\n=== テスト結果サマリー ===")
    print(f"全体ステータス: {test_results['overall_status']}")
    print(f"データ行数: {test_results['data_info'].get('total_rows', 'N/A')}")
    print(f"データ列数: {test_results['data_info'].get('total_columns', 'N/A')}")
    
    # 個別テスト結果
    for test_name, result in test_results.get('test_results', {}).items():
        status_symbol = {
            'PASS': '✅',
            'PARTIAL': '⚠️',
            'FAIL': '❌'
        }.get(result['status'], '❓')
        print(f"{status_symbol} {result['test_name']}: {result['status']}")
    
    # テストレポート保存
    tester.save_test_report(test_results)
    
    return test_results


if __name__ == "__main__":
    main()