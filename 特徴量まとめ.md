# 特徴量仕様書 - 競馬AI予測システム

**実装済み特徴量の包括的仕様とシステムアーキテクチャ**

この文書では、競馬AI予測システムで実装・管理されている特徴量の詳細仕様、システムアーキテクチャ、拡張方法について説明します。

## 🎯 現在の学習済みモデル仕様

### 使用中モデル: `lgb_model_20250607_205540.pkl`
- **精度**: 99.21%
- **特徴量数**: 10個（基本特徴量のみ）
- **モデル**: LightGBM
- **訓練日**: 2025年6月7日

### 実際に使用されている基本特徴量（10個）

| No. | 特徴量名 | データ型 | 説明 | 重要度 |
|-----|----------|----------|------|--------|
| 1   | 単勝    | 数値     | 単勝オッズ | 高 |
| 2   | 複勝    | 数値     | 複勝オッズ | 高 |
| 3   | 枠番    | 数値     | 枠番（1-8） | 中 |
| 4   | 馬      | 数値     | 馬番 | 中 |
| 5   | 人気    | 数値     | 人気順位 | 高 |
| 6   | 馬体重  | 数値     | 馬体重（kg） | 中 |
| 7   | ハンデ(負担) | 数値 | 負担重量（kg） | 中 |
| 8   | 馬齢    | 数値     | 馬の年齢 | 中 |
| 9   | 賞金(万円) | 数値  | 獲得賞金（万円） | 高 |
| 10  | course_len | 数値 | コース距離（m） | 中 |

**注記**: 現在のモデルは基本特徴量のみで99.21%の高精度を達成していますが、より豊富な特徴量セットによりさらなる精度向上が期待されます。

---

## 🏗️ 特徴量エンジニアリング アーキテクチャ

### システム設計概要
```
特徴量管理システム
├── 7つの特徴量カテゴリ
├── 268行のYAML設定
├── 動的計算エンジン
└── 型安全な定義システム
```

### 1. 特徴量カテゴリ体系（7カテゴリ）

| カテゴリ | 英名 | 説明 | 実装状況 |
|---------|------|------|----------|
| **基本** | `basic` | 馬番、枠番、オッズ、人気 | ✅ 完全実装 |
| **過去成績** | `performance` | 勝率、連対率、平均着順 | 🔧 フレームワーク実装済み |
| **血統** | `pedigree` | 父・母・母父の産駒成績 | 🔧 フレームワーク実装済み |
| **騎手** | `jockey` | 騎手の実績統計 | 🔧 フレームワーク実装済み |
| **調教師** | `trainer` | 調教師の実績統計 | 🔧 フレームワーク実装済み |
| **レース条件** | `race_condition` | 距離、馬場、開催場 | 🔧 フレームワーク実装済み |
| **高度** | `advanced` | 交互作用、クラスタリング | ⚠️ 実験的 |

### 2. 特徴量データ型システム

```python
class FeatureType(Enum):
    NUMERICAL = "numerical"      # 数値特徴量（オッズ、着順など）
    CATEGORICAL = "categorical"  # カテゴリカル特徴量（開催場など）
    BINARY = "binary"           # バイナリ特徴量（性別など）
    ORDINAL = "ordinal"         # 順序特徴量（人気など）
    TEXT = "text"               # テキスト特徴量（実験的）
    DATETIME = "datetime"       # 日時特徴量（日付など）
```

### 3. 特徴量重要度・優先度

```python
class FeatureImportance(Enum):
    CRITICAL = "critical"       # 必須（基本特徴量）
    HIGH = "high"              # 高重要度（過去成績）
    MEDIUM = "medium"          # 中重要度（血統・騎手）
    LOW = "low"                # 低重要度（レース条件）
    EXPERIMENTAL = "experimental" # 実験的（高度特徴量）
```

---

## 📋 実装可能な特徴量グループ

### 1. 基本特徴量 (basic) ✅ 実装済み
**設定**: 有効 | **優先度**: 1

- ✅ 馬番、枠番
- ✅ オッズ（単勝、複勝）
- ✅ 人気順位
- ✅ 馬体重、負担重量
- ✅ 馬齢
- ✅ 獲得賞金
- ✅ コース距離

### 2. 過去成績特徴量 (performance) 🔧 フレームワーク実装済み
**設定**: 有効 | **優先度**: 2

#### 実装可能な統計量：
- `count` - 出走回数
- `mean` - 平均着順
- `std` - 着順標準偏差
- `min` - 最高着順
- `max` - 最低着順
- `median` - 中央着順
- `win_rate` - 勝率（1着率）
- `place_rate` - 連対率（1-2着率）
- `show_rate` - 複勝率（1-3着率）

#### 設定パラメータ：
```yaml
performance_features:
  lookback_races: 10        # 過去10レース分
  min_races: 3             # 最低3レース必要
  weight_decay: 0.9        # 古いレースの重み減衰
  exclude_current_race: true
```

### 3. 血統特徴量 (pedigree) 🔧 フレームワーク実装済み
**設定**: 有効 | **優先度**: 3

#### 実装可能な血統統計：
```yaml
pedigree_features:
  depth: 3                # 父、母父、母母父まで
  statistics:
    - offspring_count    # 産駒数
    - win_rate          # 産駒勝率
    - avg_prize         # 産駒平均賞金
    - grade_race_wins   # 重賞勝利数
```

### 4. 騎手特徴量 (jockey) 🔧 フレームワーク実装済み
**設定**: 有効 | **優先度**: 4

#### 実装可能な騎手統計：
```yaml
jockey_trainer_features:
  statistics_period: 365  # 過去1年間
  min_data_count: 10     # 最低10回のデータ
  statistics:
    - win_rate          # 勝率
    - place_rate        # 連対率
    - show_rate         # 複勝率
    - avg_popularity    # 平均人気
    - avg_odds          # 平均オッズ
```

### 5. 調教師特徴量 (trainer) 🔧 フレームワーク実装済み
**設定**: 有効 | **優先度**: 5

- 騎手特徴量と同様の統計量を調教師別に計算

### 6. レース条件特徴量 (race_condition) 🔧 フレームワーク実装済み
**設定**: 有効 | **優先度**: 6

#### 距離カテゴリ分類：
```yaml
distance_categories:
  short: [1000, 1400]     # 短距離
  mile: [1401, 1800]      # マイル
  middle: [1801, 2200]    # 中距離
  long: [2201, 4000]      # 長距離
```

#### 開催場グループ分類：
```yaml
track_groups:
  kanto: ['東京', '中山', '新潟', '福島']
  kansai: ['京都', '阪神', '中京', '小倉']
```

### 7. 高度な特徴量 (advanced) ⚠️ 実験的
**設定**: 無効 | **優先度**: 10

#### 交互作用特徴量：
- 馬番 × 枠番
- 人気 × 斤量  
- 距離 × 芝・ダート

#### 時系列特徴量：
- トレンド
- 季節性
- モメンタム

#### クラスタリング特徴量：
- 平均着順、勝率、平均賞金でのクラスタリング

---

## 🛠️ 実装済み機能

### 1. 特徴量計算フレームワーク
- `FeatureCalculators`クラス
- 各種統計量計算関数
- エンコーディング機能（性別、年齢抽出など）

### 2. 特徴量管理システム
- `FeatureEngineeringManager`クラス
- 設定ファイル（YAML）による管理
- 特徴量の依存関係管理
- キャッシュ機能

### 3. データ品質管理
```yaml
data_quality:
  missing_values:
    strategy: 'fill'
    fill_value: 0
  outliers:
    method: 'iqr'
    action: 'clip'
  type_validation:
    enabled: true
```

### 4. エンコーディング設定
```yaml
encoding:
  label_encode_columns:
    - 'jockey_id'
    - 'trainer_id'
  onehot_encode_columns:
    - '開催'
    - '芝・ダート'
```

---

## 📈 特徴量拡張の手順

### 1. 新しい特徴量を追加する場合

```python
# 1. 特徴量定義を作成
from core.features.definitions import FeatureDefinition, FeatureCategory, FeatureType

new_feature = FeatureDefinition(
    name='win_rate_last_5',
    category=FeatureCategory.PERFORMANCE,
    feature_type=FeatureType.NUMERICAL,
    description='過去5走の勝率',
    required_columns=['race_id', 'horse_id', '着順'],
    calculator=calculate_win_rate_last_5
)

# 2. 特徴量管理システムに追加
manager = FeatureEngineeringManager()
manager.add_custom_feature(new_feature)
```

### 2. 設定ファイルでの有効化
```yaml
# core/features/config.yaml
feature_groups:
  performance:
    enabled: true  # 過去成績特徴量を有効化
```

### 3. モデル再学習
```bash
python train_model.py  # 新しい特徴量でモデル再学習
```

---

## 🚀 特徴量拡張ロードマップ

### 📈 段階的実装計画

#### フェーズ1: 基本拡張（優先度: 高）
- **過去成績特徴量**: 勝率・連対率・複勝率計算
- **騎手統計**: 実績データの自動集計
- **距離適性**: コース別成績統計

#### フェーズ2: 高度特徴量（優先度: 中）
- **血統特徴量**: 父・母・母父の産駒統計
- **レース条件詳細**: 馬場状態・天候適性
- **交互作用**: 重要な特徴量組み合わせ

#### フェーズ3: 実験的特徴量（優先度: 低）
- **時系列分析**: 調子の波・トレンド分析
- **クラスタリング**: 馬のタイプ分類
- **テキスト分析**: レースコメント解析

### 🔧 特徴量追加方法

```python
# 新特徴量の定義例
from core.features.definitions import FeatureDefinition, FeatureCategory

new_feature = FeatureDefinition(
    name='win_rate_last_5',
    category=FeatureCategory.PERFORMANCE,
    description='過去5走の勝率',
    calculator=calculate_win_rate_last_5
)

# システムへの追加
manager = FeatureEngineeringManager()
manager.add_custom_feature(new_feature)
```

### 📊 期待される効果

- **予測精度向上**: 99.21% → 99.5%+ 目標
- **特徴量数拡大**: 10個 → 50-100個
- **専門知識反映**: 競馬専門家の知見を数値化
- **モデル堅牢性**: 多様な特徴量による安定化

現在のシステムは高い拡張性を持ち、体系的な特徴量管理により効率的な開発が可能です。