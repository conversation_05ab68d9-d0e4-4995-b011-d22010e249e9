#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
複数年データを使った最適モデル期間評価システム

異なる年度の組み合わせでモデルを学習し、
どの期間が最良のモデルを生成するかを評価する。
"""

import os
import sys
import json
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 機械学習関連
import lightgbm as lgb
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import joblib

# プロジェクトのモジュール
sys.path.append('.')
from core.features.manager import FeatureEngineeringManager
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
from core.utils.constants import ComprehensiveIntegratorConfig

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiYearModelEvaluator:
    """複数年データを使った最適モデル期間評価クラス"""
    
    def __init__(self, base_years: List[str] = None, test_year: str = "2023"):
        """
        初期化
        
        Parameters
        ----------
        base_years : List[str]
            学習に使用する基本年度リスト
        test_year : str
            テスト用年度（最新年度を推奨）
        """
        if base_years is None:
            base_years = ["2018", "2019", "2020", "2021", "2022"]
        
        self.base_years = base_years
        self.test_year = test_year
        self.available_years = self._check_available_years()
        
        # 結果保存用
        self.evaluation_results = {}
        self.best_combinations = {}
        
        # データ処理設定
        self.data_config = ComprehensiveIntegratorConfig(
            use_pickle_source=True,
            include_corner_features=True,
            parallel=True,
            max_workers=2  # 安定性重視
        )
        
        # 特徴量マネージャー
        self.feature_manager = FeatureEngineeringManager()
        
        # データ統合器
        self.data_integrator = ComprehensiveDataIntegrator(config=self.data_config.__dict__)
        
        # エンコーダー保存用
        self.label_encoders = {}
        self.scaler = StandardScaler()
        
        logger.info(f"利用可能年度: {self.available_years}")
        logger.info(f"テスト年度: {test_year}")
        
    def _check_available_years(self) -> List[str]:
        """利用可能な年度データをチェック"""
        available = []
        for year in self.base_years + [self.test_year]:
            race_info_path = f"output/race_info_{year}.pickle"
            race_results_path = f"output/race_results_{year}.pickle"
            
            if os.path.exists(race_info_path) and os.path.exists(race_results_path):
                available.append(year)
            else:
                logger.warning(f"年度 {year} のデータが見つかりません")
        
        return available
    
    def load_year_data(self, years: List[str], sample_rate: float = 1.0) -> pd.DataFrame:
        """
        指定年度のデータを読み込み・統合
        
        Parameters
        ----------
        years : List[str]
            読み込む年度のリスト
        sample_rate : float
            データサンプリング率（1.0=全データ）
            
        Returns
        -------
        pd.DataFrame
            統合されたデータ
        """
        logger.info(f"年度 {years} のデータを読み込み中...")
        
        all_data = []
        
        for year in years:
            if year not in self.available_years:
                logger.warning(f"年度 {year} はスキップします")
                continue
                
            try:
                # データ統合
                year_data = self.data_integrator.generate_comprehensive_table(
                    year=year,
                    include_race_info=True,
                    include_horse_info=True,
                    include_past_performance=True,
                    use_pickle_source=True,
                    parallel=True
                )
                
                if not year_data.empty:
                    # サンプリング
                    if sample_rate < 1.0:
                        n_samples = int(len(year_data) * sample_rate)
                        year_data = year_data.sample(n=n_samples, random_state=42)
                    
                    # 年度情報を追加
                    year_data['data_year'] = year
                    all_data.append(year_data)
                    logger.info(f"年度 {year}: {len(year_data):,}件")
                
            except Exception as e:
                logger.error(f"年度 {year} のデータ読み込みエラー: {e}")
                continue
        
        if not all_data:
            raise ValueError("読み込み可能なデータがありません")
        
        # 全年度データを結合
        combined_data = pd.concat(all_data, ignore_index=True)
        logger.info(f"統合データ: {len(combined_data):,}件")
        
        return combined_data
    
    def prepare_features(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
        """
        機械学習用の特徴量とターゲットを準備
        
        Parameters
        ----------
        data : pd.DataFrame
            元データ
            
        Returns
        -------
        Tuple[pd.DataFrame, pd.Series]
            特徴量データとターゲット
        """
        logger.info("特徴量エンジニアリングを実行中...")
        
        # 基本的な依存データを準備
        dependencies = {
            'horse_results_df': data,
            'race_date': data.get('date', data.get('日付'))
        }
        
        # 特徴量計算（時間短縮のため基本特徴量のみ）
        enhanced_data = self.feature_manager.calculate_features(
            data=data,
            feature_names=['馬番', '枠番', '斤量', '人気', '性別_数値', '年齢', '開催場'],
            dependencies=dependencies
        )
        
        logger.info(f"特徴量計算後: {len(enhanced_data)}行 × {len(enhanced_data.columns)}列")
        
        # ターゲット変数の作成（1着かどうか）
        if '着順' in enhanced_data.columns:
            target = (pd.to_numeric(enhanced_data['着順'], errors='coerce') == 1).astype(int)
        else:
            raise ValueError("着順カラムが見つかりません")
        
        # 特徴量として使用するカラムを選択
        feature_columns = [
            '馬番', '枠番', '斤量', '人気', '性別_数値', '年齢'
        ]
        
        # カテゴリカル特徴量の処理
        categorical_columns = ['開催場']
        for col in categorical_columns:
            if col in enhanced_data.columns:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    enhanced_data[f'{col}_encoded'] = self.label_encoders[col].fit_transform(
                        enhanced_data[col].fillna('UNKNOWN')
                    )
                else:
                    # 既存のエンコーダーを使用
                    try:
                        enhanced_data[f'{col}_encoded'] = self.label_encoders[col].transform(
                            enhanced_data[col].fillna('UNKNOWN')
                        )
                    except ValueError:
                        # 新しいカテゴリがある場合
                        enhanced_data[f'{col}_encoded'] = enhanced_data[col].map(
                            dict(zip(self.label_encoders[col].classes_, 
                                   self.label_encoders[col].transform(self.label_encoders[col].classes_)))
                        ).fillna(-1)
                
                feature_columns.append(f'{col}_encoded')
        
        # 特徴量データの準備
        features = enhanced_data[feature_columns].fillna(0)
        
        # 欠損値やエラーのある行を除去
        valid_mask = target.notna() & features.notna().all(axis=1)
        features = features[valid_mask]
        target = target[valid_mask]
        
        logger.info(f"最終データ: {len(features)}行 × {len(features.columns)}列")
        logger.info(f"ターゲット分布: 0={sum(target==0)}, 1={sum(target==1)} (勝率: {sum(target==1)/len(target):.3f})")
        
        return features, target
    
    def train_evaluate_model(self, train_years: List[str], test_years: List[str], 
                           sample_rate: float = 0.3) -> Dict[str, Any]:
        """
        指定年度でモデルを学習・評価
        
        Parameters
        ----------
        train_years : List[str]
            学習用年度
        test_years : List[str]  
            テスト用年度
        sample_rate : float
            データサンプリング率
            
        Returns
        -------
        Dict[str, Any]
            評価結果
        """
        try:
            logger.info(f"モデル学習開始: 学習年度={train_years}, テスト年度={test_years}")
            
            # 学習データの準備
            train_data = self.load_year_data(train_years, sample_rate=sample_rate)
            X_train, y_train = self.prepare_features(train_data)
            
            # テストデータの準備
            test_data = self.load_year_data(test_years, sample_rate=sample_rate)
            X_test, y_test = self.prepare_features(test_data)
            
            # 特徴量の標準化
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # LightGBMモデルの学習
            lgb_params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42
            }
            
            # データセット作成
            train_dataset = lgb.Dataset(X_train_scaled, label=y_train)
            
            # モデル学習
            model = lgb.train(
                lgb_params,
                train_dataset,
                num_boost_round=100,
                valid_sets=[train_dataset],
                callbacks=[lgb.early_stopping(stopping_rounds=10), lgb.log_evaluation(0)]
            )
            
            # 予測
            y_pred_proba = model.predict(X_test_scaled, num_iteration=model.best_iteration)
            y_pred = (y_pred_proba > 0.5).astype(int)
            
            # 評価指標の計算
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            f1 = f1_score(y_test, y_pred, zero_division=0)
            
            try:
                auc = roc_auc_score(y_test, y_pred_proba)
            except ValueError:
                auc = 0.5  # クラスが偏っている場合のフォールバック
            
            # 結果をまとめ
            result = {
                'train_years': train_years,
                'test_years': test_years,
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'train_win_rate': float(y_train.mean()),
                'test_win_rate': float(y_test.mean()),
                'accuracy': float(accuracy),
                'precision': float(precision),
                'recall': float(recall),
                'f1_score': float(f1),
                'auc_score': float(auc),
                'feature_importance': dict(zip(X_train.columns, model.feature_importance())),
                'model_params': lgb_params
            }
            
            logger.info(f"評価完了 - AUC: {auc:.3f}, F1: {f1:.3f}, Accuracy: {accuracy:.3f}")
            
            return result
            
        except Exception as e:
            logger.error(f"モデル学習・評価エラー: {e}")
            return {
                'train_years': train_years,
                'test_years': test_years,
                'error': str(e),
                'accuracy': 0.0,
                'auc_score': 0.0,
                'f1_score': 0.0
            }
    
    def run_comprehensive_evaluation(self, sample_rate: float = 0.2) -> Dict[str, Any]:
        """
        包括的なモデル評価を実行
        
        Parameters
        ----------
        sample_rate : float
            データサンプリング率（高速化のため）
            
        Returns
        -------
        Dict[str, Any]
            全評価結果
        """
        logger.info("=== 包括的なモデル評価を開始 ===")
        
        evaluation_summary = {
            'evaluation_time': datetime.now().isoformat(),
            'test_year': self.test_year,
            'sample_rate': sample_rate,
            'results': [],
            'best_models': {},
            'recommendations': []
        }
        
        # 評価する学習期間のパターン
        training_patterns = [
            # 単年学習
            (['2022'], '直前1年'),
            (['2021'], '2年前単独'),
            (['2020'], '3年前単独'),
            
            # 2年間学習
            (['2021', '2022'], '直前2年'),
            (['2020', '2021'], '2-3年前'),
            (['2019', '2020'], '3-4年前'),
            
            # 3年間学習
            (['2020', '2021', '2022'], '直前3年'),
            (['2019', '2020', '2021'], '2-4年前'),
            
            # 4年間学習
            (['2019', '2020', '2021', '2022'], '直前4年'),
            
            # 5年間学習
            (['2018', '2019', '2020', '2021', '2022'], '直前5年'),
        ]
        
        best_auc = 0
        best_f1 = 0
        best_accuracy = 0
        
        for train_years, description in training_patterns:
            # 利用可能な年度のみを使用
            available_train_years = [y for y in train_years if y in self.available_years]
            
            if not available_train_years:
                logger.warning(f"スキップ: {description} (データなし)")
                continue
            
            if self.test_year not in self.available_years:
                logger.warning(f"テスト年度 {self.test_year} のデータがありません")
                continue
            
            logger.info(f"\n--- {description} での学習・評価 ---")
            
            # モデル学習・評価
            result = self.train_evaluate_model(
                train_years=available_train_years,
                test_years=[self.test_year],
                sample_rate=sample_rate
            )
            
            result['description'] = description
            evaluation_summary['results'].append(result)
            
            # ベストモデルの更新
            if result.get('auc_score', 0) > best_auc:
                best_auc = result['auc_score']
                evaluation_summary['best_models']['best_auc'] = result
                
            if result.get('f1_score', 0) > best_f1:
                best_f1 = result['f1_score']
                evaluation_summary['best_models']['best_f1'] = result
                
            if result.get('accuracy', 0) > best_accuracy:
                best_accuracy = result['accuracy']
                evaluation_summary['best_models']['best_accuracy'] = result
        
        # 推奨事項の生成
        self._generate_recommendations(evaluation_summary)
        
        # 結果保存
        self.evaluation_results = evaluation_summary
        
        logger.info("=== 包括的評価完了 ===")
        return evaluation_summary
    
    def _generate_recommendations(self, summary: Dict[str, Any]):
        """評価結果に基づく推奨事項を生成"""
        results = summary['results']
        recommendations = []
        
        if not results:
            recommendations.append("評価可能なデータがありませんでした。")
            summary['recommendations'] = recommendations
            return
        
        # AUCスコア順にソート
        results_by_auc = sorted(results, key=lambda x: x.get('auc_score', 0), reverse=True)
        best_result = results_by_auc[0]
        
        recommendations.append(
            f"最高性能: {best_result['description']} "
            f"(AUC: {best_result['auc_score']:.3f}, F1: {best_result['f1_score']:.3f})"
        )
        
        # 学習期間の長さによる分析
        period_performance = {}
        for result in results:
            period_length = len(result['train_years'])
            if period_length not in period_performance:
                period_performance[period_length] = []
            period_performance[period_length].append(result['auc_score'])
        
        for length, scores in period_performance.items():
            avg_score = np.mean(scores)
            recommendations.append(
                f"{length}年間学習の平均AUC: {avg_score:.3f}"
            )
        
        # 最適な学習期間の推奨
        best_length = max(period_performance.keys(), 
                         key=lambda x: np.mean(period_performance[x]))
        recommendations.append(
            f"推奨学習期間: {best_length}年間 "
            f"(平均AUC: {np.mean(period_performance[best_length]):.3f})"
        )
        
        # データ量と性能の関係
        if len(results) > 1:
            train_sizes = [r['train_samples'] for r in results]
            aucs = [r['auc_score'] for r in results]
            correlation = np.corrcoef(train_sizes, aucs)[0, 1]
            
            if correlation > 0.3:
                recommendations.append("データ量が多いほど性能が向上する傾向があります。")
            elif correlation < -0.3:
                recommendations.append("データ量が少ない方が性能が良い場合があります（過学習の可能性）。")
            else:
                recommendations.append("データ量と性能の明確な相関は見られません。")
        
        summary['recommendations'] = recommendations
    
    def save_results(self, output_path: str = "multi_year_evaluation_results.json"):
        """評価結果をファイルに保存"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.evaluation_results, f, indent=2, ensure_ascii=False)
            logger.info(f"評価結果を保存しました: {output_path}")
        except Exception as e:
            logger.error(f"結果保存エラー: {e}")
    
    def generate_report(self, output_path: str = "multi_year_model_evaluation_report.txt"):
        """評価レポートを生成"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("=== 複数年データモデル評価レポート ===\n\n")
                f.write(f"評価日時: {self.evaluation_results.get('evaluation_time', 'N/A')}\n")
                f.write(f"テスト年度: {self.evaluation_results.get('test_year', 'N/A')}\n")
                f.write(f"サンプリング率: {self.evaluation_results.get('sample_rate', 'N/A')}\n\n")
                
                # ベストモデル
                f.write("=== ベストモデル ===\n")
                best_models = self.evaluation_results.get('best_models', {})
                for metric, model in best_models.items():
                    f.write(f"\n{metric.upper()}最優秀:\n")
                    f.write(f"  学習期間: {model['description']}\n")
                    f.write(f"  学習年度: {', '.join(model['train_years'])}\n")
                    f.write(f"  AUC: {model['auc_score']:.3f}\n")
                    f.write(f"  F1スコア: {model['f1_score']:.3f}\n")
                    f.write(f"  精度: {model['accuracy']:.3f}\n")
                    f.write(f"  学習サンプル数: {model['train_samples']:,}\n")
                
                # 全結果
                f.write("\n=== 全評価結果 ===\n")
                results = sorted(self.evaluation_results.get('results', []), 
                               key=lambda x: x.get('auc_score', 0), reverse=True)
                
                for i, result in enumerate(results, 1):
                    f.write(f"\n{i}. {result['description']}\n")
                    f.write(f"   学習年度: {', '.join(result['train_years'])}\n")
                    f.write(f"   AUC: {result.get('auc_score', 0):.3f}\n")
                    f.write(f"   F1: {result.get('f1_score', 0):.3f}\n")
                    f.write(f"   精度: {result.get('accuracy', 0):.3f}\n")
                    f.write(f"   学習データ: {result.get('train_samples', 0):,}件\n")
                    if 'error' in result:
                        f.write(f"   エラー: {result['error']}\n")
                
                # 推奨事項
                f.write("\n=== 推奨事項 ===\n")
                recommendations = self.evaluation_results.get('recommendations', [])
                for i, rec in enumerate(recommendations, 1):
                    f.write(f"{i}. {rec}\n")
            
            logger.info(f"評価レポートを保存しました: {output_path}")
            
        except Exception as e:
            logger.error(f"レポート生成エラー: {e}")


def main():
    """メイン関数"""
    # 評価システムの初期化
    evaluator = MultiYearModelEvaluator(
        base_years=["2018", "2019", "2020", "2021", "2022"],
        test_year="2023"
    )
    
    # 包括的評価の実行
    results = evaluator.run_comprehensive_evaluation(sample_rate=0.1)  # 高速化のため10%サンプル
    
    # 結果の表示
    print("\n=== 評価結果サマリー ===")
    best_models = results.get('best_models', {})
    
    if 'best_auc' in best_models:
        best = best_models['best_auc']
        print(f"最高AUCモデル: {best['description']}")
        print(f"  AUC: {best['auc_score']:.3f}")
        print(f"  F1: {best['f1_score']:.3f}")
        print(f"  学習年度: {', '.join(best['train_years'])}")
    
    print(f"\n推奨事項:")
    for i, rec in enumerate(results.get('recommendations', []), 1):
        print(f"  {i}. {rec}")
    
    # 結果保存
    evaluator.save_results()
    evaluator.generate_report()
    
    return results


if __name__ == "__main__":
    main()