#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
時系列交差検証システム

競馬データの時系列特性を考慮した交差検証を実装し、
モデルの汎化性能をより正確に評価する。
"""

import os
import sys
import json
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional, Iterator
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 機械学習関連
import lightgbm as lgb
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import joblib

# プロジェクトのモジュール
sys.path.append('.')
from core.features.manager import FeatureEngineeringManager
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
from core.utils.constants import ComprehensiveIntegratorConfig

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TimeSeriesValidator:
    """時系列交差検証クラス"""
    
    def __init__(self, years: List[str], validation_strategy: str = "expanding_window"):
        """
        初期化
        
        Parameters
        ----------
        years : List[str]
            使用する年度のリスト（時系列順）
        validation_strategy : str
            検証戦略
            - "expanding_window": 拡張ウィンドウ（累積学習）
            - "rolling_window": ローリングウィンドウ（固定期間学習）
            - "monthly_split": 月別分割検証
        """
        self.years = sorted(years)  # 時系列順にソート
        self.validation_strategy = validation_strategy
        self.validation_results = []
        
        # データ処理設定
        self.data_config = ComprehensiveIntegratorConfig(
            use_pickle_source=True,
            include_corner_features=False,  # 高速化のため
            parallel=True,
            max_workers=2
        )
        
        # 特徴量マネージャー
        self.feature_manager = FeatureEngineeringManager()
        
        # データ統合器
        self.data_integrator = ComprehensiveDataIntegrator(config=self.data_config.__dict__)
        
        # データキャッシュ
        self.year_data_cache = {}
        
        logger.info(f"時系列検証初期化: 年度={years}, 戦略={validation_strategy}")
    
    def load_and_cache_year_data(self, year: str, sample_rate: float = 0.2) -> pd.DataFrame:
        """
        年度データの読み込みとキャッシュ
        
        Parameters
        ----------
        year : str
            年度
        sample_rate : float
            サンプリング率
            
        Returns
        -------
        pd.DataFrame
            年度データ
        """
        if year in self.year_data_cache:
            return self.year_data_cache[year]
        
        logger.info(f"年度 {year} のデータを読み込み中...")
        
        try:
            # データ統合
            year_data = self.data_integrator.generate_comprehensive_table(
                year=year,
                include_race_info=True,
                include_horse_info=True,
                include_past_performance=True,
                use_pickle_source=True,
                parallel=True
            )
            
            if year_data.empty:
                logger.warning(f"年度 {year} のデータが空です")
                return pd.DataFrame()
            
            # サンプリング
            if sample_rate < 1.0:
                n_samples = int(len(year_data) * sample_rate)
                year_data = year_data.sample(n=n_samples, random_state=42)
            
            # 日付でソート（時系列順）
            if 'date' in year_data.columns:
                year_data = year_data.sort_values('date')
            
            # 年度と月の情報を追加
            if 'date' in year_data.columns:
                year_data['year'] = pd.to_datetime(year_data['date']).dt.year
                year_data['month'] = pd.to_datetime(year_data['date']).dt.month
                year_data['year_month'] = pd.to_datetime(year_data['date']).dt.strftime('%Y-%m')
            
            self.year_data_cache[year] = year_data
            logger.info(f"年度 {year}: {len(year_data):,}件をキャッシュ")
            
            return year_data
            
        except Exception as e:
            logger.error(f"年度 {year} のデータ読み込みエラー: {e}")
            return pd.DataFrame()
    
    def prepare_features_quick(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
        """
        高速な特徴量準備（基本特徴量のみ）
        
        Parameters
        ----------
        data : pd.DataFrame
            元データ
            
        Returns
        -------
        Tuple[pd.DataFrame, pd.Series]
            特徴量とターゲット
        """
        # ターゲット変数（1着かどうか）
        if '着順' not in data.columns:
            raise ValueError("着順カラムが見つかりません")
        
        target = (pd.to_numeric(data['着順'], errors='coerce') == 1).astype(int)
        
        # 基本特徴量のみを使用（高速化）
        feature_columns = []
        
        # 数値特徴量
        numeric_features = ['馬番', '枠番', '斤量', '人気']
        for col in numeric_features:
            if col in data.columns:
                feature_columns.append(col)
        
        # 性齢から年齢を抽出
        if '性齢' in data.columns:
            data['年齢_extracted'] = data['性齢'].str.extract(r'(\d+)').astype(float)
            feature_columns.append('年齢_extracted')
        
        # 開催場のエンコーディング
        if '開催' in data.columns:
            le = LabelEncoder()
            data['開催_encoded'] = le.fit_transform(data['開催'].fillna('UNKNOWN'))
            feature_columns.append('開催_encoded')
        
        # 特徴量データの準備
        features = data[feature_columns].fillna(0)
        
        # 有効データのフィルタリング
        valid_mask = target.notna() & features.notna().all(axis=1)
        features = features[valid_mask]
        target = target[valid_mask]
        
        return features, target
    
    def expanding_window_validation(self, sample_rate: float = 0.2) -> List[Dict[str, Any]]:
        """
        拡張ウィンドウ検証（累積学習）
        
        Parameters
        ----------
        sample_rate : float
            サンプリング率
            
        Returns
        -------
        List[Dict[str, Any]]
            検証結果のリスト
        """
        logger.info("拡張ウィンドウ検証を開始...")
        results = []
        
        for i in range(1, len(self.years)):
            train_years = self.years[:i]  # 最初から現在の年まで
            test_year = self.years[i]     # 次の年
            
            logger.info(f"学習: {train_years} → テスト: {test_year}")
            
            try:
                # 学習データの準備
                train_data_list = []
                for year in train_years:
                    year_data = self.load_and_cache_year_data(year, sample_rate)
                    if not year_data.empty:
                        train_data_list.append(year_data)
                
                if not train_data_list:
                    logger.warning(f"学習データが空です: {train_years}")
                    continue
                
                train_data = pd.concat(train_data_list, ignore_index=True)
                
                # テストデータの準備
                test_data = self.load_and_cache_year_data(test_year, sample_rate)
                if test_data.empty:
                    logger.warning(f"テストデータが空です: {test_year}")
                    continue
                
                # 特徴量準備
                X_train, y_train = self.prepare_features_quick(train_data)
                X_test, y_test = self.prepare_features_quick(test_data)
                
                # モデル学習・評価
                result = self._train_and_evaluate(
                    X_train, y_train, X_test, y_test,
                    train_years, [test_year]
                )
                
                result['fold_type'] = 'expanding_window'
                result['fold_number'] = i
                results.append(result)
                
            except Exception as e:
                logger.error(f"拡張ウィンドウ検証エラー (fold {i}): {e}")
                continue
        
        return results
    
    def rolling_window_validation(self, window_size: int = 2, sample_rate: float = 0.2) -> List[Dict[str, Any]]:
        """
        ローリングウィンドウ検証（固定期間学習）
        
        Parameters
        ----------
        window_size : int
            学習に使用する年数
        sample_rate : float
            サンプリング率
            
        Returns
        -------
        List[Dict[str, Any]]
            検証結果のリスト
        """
        logger.info(f"ローリングウィンドウ検証を開始... (ウィンドウサイズ: {window_size}年)")
        results = []
        
        for i in range(window_size, len(self.years)):
            train_years = self.years[i-window_size:i]  # 固定サイズのウィンドウ
            test_year = self.years[i]                   # 次の年
            
            logger.info(f"学習: {train_years} → テスト: {test_year}")
            
            try:
                # 学習データの準備
                train_data_list = []
                for year in train_years:
                    year_data = self.load_and_cache_year_data(year, sample_rate)
                    if not year_data.empty:
                        train_data_list.append(year_data)
                
                if not train_data_list:
                    logger.warning(f"学習データが空です: {train_years}")
                    continue
                
                train_data = pd.concat(train_data_list, ignore_index=True)
                
                # テストデータの準備
                test_data = self.load_and_cache_year_data(test_year, sample_rate)
                if test_data.empty:
                    logger.warning(f"テストデータが空です: {test_year}")
                    continue
                
                # 特徴量準備
                X_train, y_train = self.prepare_features_quick(train_data)
                X_test, y_test = self.prepare_features_quick(test_data)
                
                # モデル学習・評価
                result = self._train_and_evaluate(
                    X_train, y_train, X_test, y_test,
                    train_years, [test_year]
                )
                
                result['fold_type'] = 'rolling_window'
                result['window_size'] = window_size
                result['fold_number'] = i - window_size + 1
                results.append(result)
                
            except Exception as e:
                logger.error(f"ローリングウィンドウ検証エラー (fold {i}): {e}")
                continue
        
        return results
    
    def monthly_split_validation(self, year: str, sample_rate: float = 0.3) -> List[Dict[str, Any]]:
        """
        月別分割検証（年内での時系列検証）
        
        Parameters
        ----------
        year : str
            対象年度
        sample_rate : float
            サンプリング率
            
        Returns
        -------
        List[Dict[str, Any]]
            検証結果のリスト
        """
        logger.info(f"月別分割検証を開始... (年度: {year})")
        results = []
        
        # 年度データの読み込み
        year_data = self.load_and_cache_year_data(year, sample_rate)
        if year_data.empty:
            logger.warning(f"年度 {year} のデータが空です")
            return results
        
        # 月別にデータを分割
        months = sorted(year_data['month'].unique()) if 'month' in year_data.columns else range(1, 13)
        
        for i, test_month in enumerate(months[3:], 1):  # 最初の3ヶ月は学習データとして確保
            train_months = months[:months.index(test_month)]
            
            logger.info(f"学習: {year}年{train_months}月 → テスト: {year}年{test_month}月")
            
            try:
                # 学習・テストデータの分割
                train_mask = year_data['month'].isin(train_months)
                test_mask = year_data['month'] == test_month
                
                train_data = year_data[train_mask]
                test_data = year_data[test_mask]
                
                if len(train_data) == 0 or len(test_data) == 0:
                    logger.warning(f"データが不足: 学習={len(train_data)}, テスト={len(test_data)}")
                    continue
                
                # 特徴量準備
                X_train, y_train = self.prepare_features_quick(train_data)
                X_test, y_test = self.prepare_features_quick(test_data)
                
                # モデル学習・評価
                result = self._train_and_evaluate(
                    X_train, y_train, X_test, y_test,
                    [f"{year}-{m:02d}" for m in train_months],
                    [f"{year}-{test_month:02d}"]
                )
                
                result['fold_type'] = 'monthly_split'
                result['target_year'] = year
                result['test_month'] = test_month
                result['fold_number'] = i
                results.append(result)
                
            except Exception as e:
                logger.error(f"月別分割検証エラー (月 {test_month}): {e}")
                continue
        
        return results
    
    def _train_and_evaluate(self, X_train: pd.DataFrame, y_train: pd.Series,
                           X_test: pd.DataFrame, y_test: pd.Series,
                           train_periods: List[str], test_periods: List[str]) -> Dict[str, Any]:
        """
        モデルの学習と評価
        
        Parameters
        ----------
        X_train, y_train : 学習データ
        X_test, y_test : テストデータ
        train_periods, test_periods : 期間情報
        
        Returns
        -------
        Dict[str, Any]
            評価結果
        """
        try:
            # 特徴量の標準化
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # LightGBMモデルの設定
            lgb_params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42
            }
            
            # データセット作成
            train_dataset = lgb.Dataset(X_train_scaled, label=y_train)
            
            # モデル学習
            model = lgb.train(
                lgb_params,
                train_dataset,
                num_boost_round=50,  # 高速化のため削減
                valid_sets=[train_dataset],
                callbacks=[lgb.early_stopping(stopping_rounds=10), lgb.log_evaluation(0)]
            )
            
            # 予測
            y_pred_proba = model.predict(X_test_scaled, num_iteration=model.best_iteration)
            y_pred = (y_pred_proba > 0.5).astype(int)
            
            # 評価指標の計算
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            f1 = f1_score(y_test, y_pred, zero_division=0)
            
            try:
                auc = roc_auc_score(y_test, y_pred_proba)
            except ValueError:
                auc = 0.5
            
            # 結果をまとめ
            return {
                'train_periods': train_periods,
                'test_periods': test_periods,
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'train_win_rate': float(y_train.mean()),
                'test_win_rate': float(y_test.mean()),
                'accuracy': float(accuracy),
                'precision': float(precision),
                'recall': float(recall),
                'f1_score': float(f1),
                'auc_score': float(auc),
                'feature_names': list(X_train.columns)
            }
            
        except Exception as e:
            logger.error(f"モデル学習・評価エラー: {e}")
            return {
                'train_periods': train_periods,
                'test_periods': test_periods,
                'error': str(e),
                'accuracy': 0.0,
                'auc_score': 0.0,
                'f1_score': 0.0
            }
    
    def run_validation(self, sample_rate: float = 0.2) -> Dict[str, Any]:
        """
        選択された戦略で検証を実行
        
        Parameters
        ----------
        sample_rate : float
            サンプリング率
            
        Returns
        -------
        Dict[str, Any]
            検証結果
        """
        logger.info(f"時系列検証開始: 戦略={self.validation_strategy}")
        
        validation_summary = {
            'validation_time': datetime.now().isoformat(),
            'strategy': self.validation_strategy,
            'years': self.years,
            'sample_rate': sample_rate,
            'results': [],
            'summary_stats': {},
            'recommendations': []
        }
        
        # 戦略に応じた検証実行
        if self.validation_strategy == "expanding_window":
            results = self.expanding_window_validation(sample_rate)
        elif self.validation_strategy == "rolling_window":
            results = self.rolling_window_validation(window_size=2, sample_rate=sample_rate)
        elif self.validation_strategy == "monthly_split":
            # 最新年度での月別検証
            latest_year = self.years[-1]
            results = self.monthly_split_validation(latest_year, sample_rate)
        else:
            raise ValueError(f"未知の検証戦略: {self.validation_strategy}")
        
        validation_summary['results'] = results
        
        # 統計サマリーの計算
        if results:
            valid_results = [r for r in results if 'error' not in r]
            if valid_results:
                aucs = [r['auc_score'] for r in valid_results]
                f1s = [r['f1_score'] for r in valid_results]
                accuracies = [r['accuracy'] for r in valid_results]
                
                validation_summary['summary_stats'] = {
                    'mean_auc': float(np.mean(aucs)),
                    'std_auc': float(np.std(aucs)),
                    'mean_f1': float(np.mean(f1s)),
                    'std_f1': float(np.std(f1s)),
                    'mean_accuracy': float(np.mean(accuracies)),
                    'std_accuracy': float(np.std(accuracies)),
                    'total_folds': len(valid_results),
                    'error_folds': len(results) - len(valid_results)
                }
                
                # 推奨事項の生成
                self._generate_validation_recommendations(validation_summary)
        
        self.validation_results = validation_summary
        return validation_summary
    
    def _generate_validation_recommendations(self, summary: Dict[str, Any]):
        """検証結果に基づく推奨事項を生成"""
        stats = summary['summary_stats']
        recommendations = []
        
        # 性能の安定性評価
        if stats['std_auc'] < 0.05:
            recommendations.append("モデル性能が安定しています（AUC標準偏差 < 0.05）")
        elif stats['std_auc'] > 0.1:
            recommendations.append("モデル性能が不安定です（AUC標準偏差 > 0.1）")
        
        # 平均性能評価
        if stats['mean_auc'] > 0.6:
            recommendations.append(f"良好な予測性能です（平均AUC: {stats['mean_auc']:.3f}）")
        elif stats['mean_auc'] < 0.55:
            recommendations.append(f"予測性能の改善が必要です（平均AUC: {stats['mean_auc']:.3f}）")
        
        # エラー率評価
        if stats['error_folds'] > 0:
            error_rate = stats['error_folds'] / (stats['total_folds'] + stats['error_folds'])
            recommendations.append(f"エラー率: {error_rate:.1%} ({stats['error_folds']}/{stats['total_folds'] + stats['error_folds']})")
        
        # 戦略別の推奨
        if summary['strategy'] == 'expanding_window':
            if len(summary['results']) > 2:
                recent_aucs = [r['auc_score'] for r in summary['results'][-3:] if 'error' not in r]
                if recent_aucs and np.mean(recent_aucs) > stats['mean_auc']:
                    recommendations.append("学習データが増えるほど性能が向上しています")
        
        summary['recommendations'] = recommendations
    
    def save_results(self, output_path: str = "time_series_validation_results.json"):
        """検証結果をファイルに保存"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.validation_results, f, indent=2, ensure_ascii=False)
            logger.info(f"検証結果を保存しました: {output_path}")
        except Exception as e:
            logger.error(f"結果保存エラー: {e}")


def main():
    """メイン関数"""
    # 利用可能年度
    years = ["2020", "2021", "2022", "2023"]
    
    print("=== 時系列交差検証システム ===\n")
    
    # 1. 拡張ウィンドウ検証
    print("1. 拡張ウィンドウ検証...")
    validator_expanding = TimeSeriesValidator(years, "expanding_window")
    results_expanding = validator_expanding.run_validation(sample_rate=0.1)
    
    print(f"   平均AUC: {results_expanding['summary_stats'].get('mean_auc', 0):.3f}")
    print(f"   AUC標準偏差: {results_expanding['summary_stats'].get('std_auc', 0):.3f}")
    
    # 2. ローリングウィンドウ検証
    print("\n2. ローリングウィンドウ検証...")
    validator_rolling = TimeSeriesValidator(years, "rolling_window")
    results_rolling = validator_rolling.run_validation(sample_rate=0.1)
    
    print(f"   平均AUC: {results_rolling['summary_stats'].get('mean_auc', 0):.3f}")
    print(f"   AUC標準偏差: {results_rolling['summary_stats'].get('std_auc', 0):.3f}")
    
    # 3. 月別分割検証
    print("\n3. 月別分割検証 (2023年)...")
    validator_monthly = TimeSeriesValidator(["2023"], "monthly_split")
    results_monthly = validator_monthly.run_validation(sample_rate=0.2)
    
    print(f"   平均AUC: {results_monthly['summary_stats'].get('mean_auc', 0):.3f}")
    print(f"   AUC標準偏差: {results_monthly['summary_stats'].get('std_auc', 0):.3f}")
    
    # 結果保存
    validator_expanding.save_results("expanding_window_results.json")
    validator_rolling.save_results("rolling_window_results.json")
    validator_monthly.save_results("monthly_split_results.json")
    
    # 推奨事項表示
    print("\n=== 推奨事項 ===")
    for strategy, results in [
        ("拡張ウィンドウ", results_expanding),
        ("ローリングウィンドウ", results_rolling),
        ("月別分割", results_monthly)
    ]:
        print(f"\n{strategy}:")
        for rec in results.get('recommendations', []):
            print(f"  - {rec}")
    
    return {
        'expanding_window': results_expanding,
        'rolling_window': results_rolling,
        'monthly_split': results_monthly
    }


if __name__ == "__main__":
    main()