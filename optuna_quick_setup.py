#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna クイックセットアップ
高速でOptuna最適化システムをセットアップ
"""

import pandas as pd
import numpy as np
import optuna
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score
from sklearn.datasets import make_classification
import pickle
import json
from datetime import datetime
from pathlib import Path

def quick_optuna_demo():
    """クイックOptuna最適化デモ"""
    
    print("🚀 Optuna クイックセットアップ開始")
    
    # 1. 競馬風データ生成
    print("📊 競馬風データ生成中...")
    X, y = make_classification(
        n_samples=2000,
        n_features=15,
        n_informative=10,
        n_redundant=3,
        class_sep=0.8,
        random_state=42
    )
    
    # 競馬風特徴量名
    feature_names = [
        '枠番', '馬番', '斤量', 'course_len', '着順_last_5R_mean',
        '人気_last_5R_mean', 'オッズ_last_5R_mean', '上り_last_5R_mean',
        '体重_last_5R_mean', 'interval_days', 'race_class', 
        'ground_state', 'weather', 'track_direction', 'jockey_skill'
    ]
    
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"✅ データ生成完了: 訓練{X_train.shape}, テスト{X_test.shape}")
    
    # 2. Optuna最適化
    def objective(trial):
        params = {
            'objective': 'binary',
            'metric': 'auc',
            'boosting_type': 'gbdt',
            'num_leaves': trial.suggest_int('num_leaves', 10, 100),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
            'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
            'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
            'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 10.0, log=True),
            'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 10.0, log=True),
            'random_state': 42,
            'verbosity': -1
        }
        
        # 簡単な訓練・検証分割
        split_idx = int(len(X_train) * 0.8)
        X_tr, X_val = X_train[:split_idx], X_train[split_idx:]
        y_tr, y_val = y_train[:split_idx], y_train[split_idx:]
        
        train_data = lgb.Dataset(X_tr, label=y_tr)
        val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
        
        model = lgb.train(
            params, train_data, 
            valid_sets=[val_data],
            num_boost_round=50,
            callbacks=[lgb.early_stopping(stopping_rounds=10), lgb.log_evaluation(0)]
        )
        
        y_pred = model.predict(X_val, num_iteration=model.best_iteration)
        return roc_auc_score(y_val, y_pred)
    
    print("🔍 Optuna最適化実行中...")
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=10, timeout=120)  # 2分で最適化
    
    best_params = study.best_params
    best_score = study.best_value
    
    print(f"✅ 最適化完了: 最高AUC={best_score:.4f}")
    
    # 3. 結果保存
    results_dir = Path("quick_optuna_results")
    results_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 最適パラメータ保存
    params_file = results_dir / f"quick_best_params_{timestamp}.json"
    with open(params_file, 'w', encoding='utf-8') as f:
        json.dump({
            'best_params': best_params,
            'best_score': best_score,
            'n_trials': len(study.trials),
            'feature_names': feature_names
        }, f, indent=2, ensure_ascii=False)
    
    # 最終モデル訓練
    final_params = best_params.copy()
    final_params.update({
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'random_state': 42,
        'verbosity': -1
    })
    
    train_data = lgb.Dataset(X_train, label=y_train)
    final_model = lgb.train(final_params, train_data, num_boost_round=100)
    
    # テスト評価
    y_test_pred = final_model.predict(X_test)
    test_auc = roc_auc_score(y_test, y_test_pred)
    
    # モデル保存
    model_file = results_dir / f"quick_optimized_model_{timestamp}.pkl"
    with open(model_file, 'wb') as f:
        pickle.dump(final_model, f)
    
    print(f"📁 結果保存完了:")
    print(f"  パラメータ: {params_file}")
    print(f"  モデル: {model_file}")
    print(f"  テストAUC: {test_auc:.4f}")
    
    # 4. 簡単なレポート
    print(f"\n📊 Optuna最適化レポート:")
    print(f"  試行回数: {len(study.trials)}")
    print(f"  最高CV-AUC: {best_score:.4f}")
    print(f"  テストAUC: {test_auc:.4f}")
    print(f"  最適パラメータ:")
    for param, value in best_params.items():
        if isinstance(value, float):
            print(f"    {param}: {value:.4f}")
        else:
            print(f"    {param}: {value}")
    
    print(f"\n🎉 Optunaクイックセットアップ完了！")
    
    return {
        'best_params': best_params,
        'best_score': best_score,
        'test_auc': test_auc,
        'model_file': model_file,
        'params_file': params_file
    }

if __name__ == "__main__":
    quick_optuna_demo()