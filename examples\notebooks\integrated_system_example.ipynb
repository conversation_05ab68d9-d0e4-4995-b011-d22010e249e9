{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 競馬AI予測システム統合版 - 使用例\n", "\n", "このノートブックでは、統合された競馬AI予測システムの基本的な使用方法を説明します。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. システムのインポート"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# プロジェクトのルートディレクトリをパスに追加\n", "sys.path.append('../..')\n", "\n", "# 統合システムのインポート\n", "from keiba_ai_system.core.processors import RaceProcessor, HorseProcessor\n", "from keiba_ai_system.core.features import FeatureEngineeringManager\n", "from keiba_ai_system.core.scrapers import scrape_html_race, scrape_html_horse\n", "from keiba_ai_system.prediction import LiveRacePredictor\n", "from keiba_ai_system.core.utils import LocalPaths"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 基本的なデータ処理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# レースプロセッサーの初期化\n", "race_processor = RaceProcessor()\n", "\n", "# 2024年のレースデータを処理（最大10ファイル）\n", "race_info_df, race_results_df = race_processor.process_race_bin_files(\n", "    year=\"2024\",\n", "    parallel=True,\n", "    max_files=10\n", ")\n", "\n", "print(f\"レース情報: {len(race_info_df)}件\")\n", "print(f\"レース結果: {len(race_results_df)}件\")\n", "print(\"\\nレース情報の例:\")\n", "print(race_info_df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 馬データの処理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 馬プロセッサーの初期化\n", "horse_processor = HorseProcessor()\n", "\n", "# レース結果から馬IDを抽出\n", "if not race_results_df.empty and 'horse_id' in race_results_df.columns:\n", "    horse_ids = race_results_df['horse_id'].dropna().unique()[:20]  # 最初の20頭\n", "    \n", "    # 馬の基本情報を取得\n", "    horse_info_df = horse_processor.get_rawdata_horse_info(\n", "        horse_id_list=horse_ids.tolist(),\n", "        max_workers=2\n", "    )\n", "    \n", "    print(f\"馬の基本情報: {len(horse_info_df)}件\")\n", "    print(\"\\n馬の基本情報の例:\")\n", "    print(horse_info_df.head())\n", "else:\n", "    print(\"馬IDが見つかりませんでした\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 特徴量エンジニアリング"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 特徴量エンジニアリングマネージャーの初期化\n", "feature_manager = FeatureEngineeringManager()\n", "\n", "# 利用可能な特徴量を確認\n", "available_features = feature_manager.list_features(enabled_only=True)\n", "print(f\"利用可能な特徴量数: {len(available_features)}\")\n", "print(\"\\n特徴量の例:\")\n", "for feature in available_features[:10]:\n", "    print(f\"- {feature}\")\n", "\n", "# サンプルデータに特徴量を追加\n", "if not race_results_df.empty:\n", "    sample_data = race_results_df.head(10).copy()\n", "    \n", "    # 基本特徴量を計算\n", "    enhanced_data = feature_manager.calculate_features(\n", "        data=sample_data,\n", "        feature_names=available_features[:5]  # 最初の5つの特徴量\n", "    )\n", "    \n", "    print(f\"\\n特徴量追加後のデータ形状: {enhanced_data.shape}\")\n", "    print(\"\\n追加された特徴量:\")\n", "    new_columns = set(enhanced_data.columns) - set(sample_data.columns)\n", "    for col in new_columns:\n", "        print(f\"- {col}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 予測システムの使用"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ライブ予測システムの初期化\n", "predictor = LiveRacePredictor()\n", "\n", "print(\"予測システムが初期化されました\")\n", "print(f\"モデル: {'ロード済み' if predictor.model else '未ロード'}\")\n", "print(f\"スケーラー: {'ロード済み' if predictor.scaler else '未ロード'}\")\n", "\n", "# 注意: 実際の予測を行うには、学習済みモデルとスケーラーが必要です\n", "print(\"\\n注意: 実際の予測を行うには以下が必要です:\")\n", "print(\"1. 学習済みLightGBMモデル (.pkl ファイル)\")\n", "print(\"2. 学習済みStandardScaler (.pkl ファイル)\")\n", "print(\"3. 予測対象レースのHTMLデータ\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. データの保存と読み込み"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 処理したデータを保存\n", "output_dir = Path(\"../../output/integrated_example\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "if not race_info_df.empty:\n", "    race_info_df.to_csv(output_dir / \"race_info_sample.csv\", index=False, encoding='utf-8-sig')\n", "    print(f\"レース情報を保存: {output_dir / 'race_info_sample.csv'}\")\n", "\n", "if not race_results_df.empty:\n", "    race_results_df.to_csv(output_dir / \"race_results_sample.csv\", index=False, encoding='utf-8-sig')\n", "    print(f\"レース結果を保存: {output_dir / 'race_results_sample.csv'}\")\n", "\n", "if 'horse_info_df' in locals() and not horse_info_df.empty:\n", "    horse_info_df.to_csv(output_dir / \"horse_info_sample.csv\", index=False, encoding='utf-8-sig')\n", "    print(f\"馬情報を保存: {output_dir / 'horse_info_sample.csv'}\")\n", "\n", "print(\"\\nデータの保存が完了しました\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. システム情報の確認"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# システムの設定情報を確認\n", "print(\"=== 競馬AI予測システム統合版 ===\")\n", "print(f\"データディレクトリ: {LocalPaths.HTML_RACE_DIR}\")\n", "print(f\"馬データディレクトリ: {LocalPaths.HTML_HORSE_DIR}\")\n", "\n", "# 利用可能なデータファイル数を確認\n", "race_html_dir = Path(LocalPaths.HTML_RACE_DIR)\n", "if race_html_dir.exists():\n", "    race_files = list(race_html_dir.glob(\"**/*.bin\"))\n", "    print(f\"利用可能なレースファイル数: {len(race_files)}\")\n", "else:\n", "    print(\"レースデータディレクトリが見つかりません\")\n", "\n", "horse_html_dir = Path(LocalPaths.HTML_HORSE_DIR)\n", "if horse_html_dir.exists():\n", "    horse_files = list(horse_html_dir.glob(\"**/*.bin\"))\n", "    print(f\"利用可能な馬ファイル数: {len(horse_files)}\")\n", "else:\n", "    print(\"馬データディレクトリが見つかりません\")\n", "\n", "print(\"\\n統合システムの動作確認が完了しました！\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}