# 競馬AIシステム スクレイピング機能調査報告書

**調査日時**: 2025年6月10日  
**調査対象**: enhanced_live_predictor.py と HorseProcessor の最新馬戦績スクレイピング機能

## 🔍 調査概要

競馬AIシステムにおいて、enhanced_live_predictor.py と HorseProcessor が最新の馬戦績をリアルタイムでWebスクレイピングしているかどうかを詳細に調査しました。

## 📋 調査結果サマリー

### ❌ **最新馬戦績のリアルタイムスクレイピングは行われていない**

- **HorseProcessor**: 完全にローカルHTMLファイルのみを使用
- **enhanced_live_predictor**: 出馬表のみリアルタイム取得、過去戦績はローカルファイル依存

## 🔬 詳細調査結果

### 1. HorseProcessor の分析

#### データソース
- **ファイル**: `core/processors/horse_processor.py`
- **データ取得方式**: ローカルHTMLファイル解析のみ
- **ファイル形式**: `.bin` ファイル（HTMLコンテンツ）
- **ファイル場所**: `data/html/horse/horse_by_year/YYYY/YYYYXXXXXX.bin`

#### 処理フロー
```
1. HTMLファイルインデックス構築 (86,464件のファイル)
2. 馬IDから対応するローカルHTMLファイルを検索
3. HTMLファイルをBeautifulSoupで解析
4. 馬基本情報・過去戦績を抽出
```

#### 重要な発見
- **Webアクセスなし**: 一切のHTTP/HTTPSリクエストを行わない
- **ファイル依存**: 事前にダウンロード済みのHTMLファイルのみ使用
- **更新なし**: 最新戦績を取得する機能は存在しない

### 2. enhanced_live_predictor.py の分析

#### データ取得の二重構造

##### 🌐 リアルタイムスクレイピング部分
- **対象**: 出馬表のみ
- **方式**: Selenium または requests/BeautifulSoup
- **URL**: `https://race.netkeiba.com/race/shutuba.html?race_id={race_id}`
- **取得データ**: 最新の出馬情報（枠番、馬番、馬名、騎手等）

##### 💽 ローカルファイル部分
- **対象**: 馬の過去戦績・基本情報
- **方式**: HorseProcessor使用
- **データソース**: ローカルHTMLファイル
- **鮮度**: ファイル更新日に依存

#### 処理フロー詳細
```
1. 出馬表取得 (リアルタイム)
   ├── Selenium WebDriver or requests
   └── 最新の出馬情報を取得

2. 馬ID抽出
   └── 出馬表から馬IDを抽出

3. 過去戦績取得 (ローカル)
   ├── HorseProcessor.process_horse_results_for_ids()
   ├── ローカルHTMLファイル解析
   └── 最新戦績は取得されない

4. 特徴量計算・予測実行
```

### 3. データの鮮度分析

#### ローカルHTMLファイルの状況
- **総ファイル数**: 86,464件
- **年度範囲**: 1996年〜2024年
- **最新更新**: 2024年5月頃（ファイルタイムスタンプ確認）
- **2021年ファイル例**: 2021105522.bin（2024年5月21日更新）

#### データ鮮度まとめ
| データ種類 | 取得方式 | 鮮度 |
|-----------|---------|------|
| 出馬表 | リアルタイムWebスクレイピング | ✅ 最新 |
| 馬基本情報 | ローカルHTMLファイル | ⚠️ 2024年5月頃 |
| 馬過去戦績 | ローカルHTMLファイル | ⚠️ 2024年5月頃 |
| コーナー特徴量 | ローカルPickleファイル | ⚠️ 不明 |

## 🧩 システム構成の問題点

### 1. データ不整合の可能性
- **最新出馬表**: 2025年6月のレース情報
- **馬過去戦績**: 2024年5月以前のデータ
- **予測精度への影響**: 最新の馬の調子や成績が反映されない

### 2. 予測の信頼性
- 馬が2024年6月以降に出走した最新戦績は一切考慮されない
- 調教師変更、騎手変更、馬の成長等の最新情報が反映されない
- 長期間出走していない馬の復帰戦等の判断ができない

### 3. システム設計の課題
- リアルタイム部分とローカルファイル部分の二重構造
- データ更新の責任範囲が不明確
- 運用時のデータメンテナンスが困難

## 💡 改善提案

### 1. 短期的改善
```python
# 馬の最新戦績取得機能の追加例
def get_latest_horse_results(self, horse_id: str, since_date: str) -> pd.DataFrame:
    """指定日以降の最新戦績をWebスクレイピングで取得"""
    url = f"https://db.netkeiba.com/horse/{horse_id}"
    # リアルタイムスクレイピング実装
```

### 2. 中期的改善
- ローカルHTMLファイルの定期自動更新システム
- データ鮮度チェック機能
- 段階的スクレイピング（最新データのみ取得）

### 3. 長期的改善
- 完全リアルタイムスクレイピングシステムへの移行
- データベースベースの馬情報管理
- APIを活用したデータ取得システム

## 📊 テスト結果

### HorseProcessor テスト
```
HTMLファイルインデックス: 86,464件
テスト馬ID 2021105522: ✅ 存在確認
馬基本情報取得: ✅ 1件成功
馬過去戦績取得: ✅ 23件成功
リアルタイムスクレイピング: ❌ なし
```

### enhanced_live_predictor テスト
```
Selenium使用: False（テスト環境）
出馬表スクレイピング: ✅ 実装確認
過去戦績取得: ✅ HorseProcessor使用
最新戦績スクレイピング: ❌ なし
```

## 🚨 重要な注意事項

1. **予測精度への影響**: 最新の馬情報が反映されないため、予測精度が低下する可能性
2. **運用リスク**: データが古い状態での予測は投資判断に重大な影響を与える可能性
3. **システム改善の必要性**: リアルタイムデータ取得機能の実装が急務

## 📝 結論

enhanced_live_predictor.py は**出馬表のみリアルタイム取得**を行い、**馬の過去戦績は2024年5月頃の古いローカルデータ**を使用しています。最新の馬戦績をリアルタイムでスクレイピングする機能は実装されておらず、これが予測精度と信頼性に大きな影響を与える可能性があります。

**推奨アクション**: 馬の最新戦績を取得する機能の早急な実装が必要です。