================================================================================
AI予想根拠説明レポート
================================================================================

📊 最重要特徴量 (Top 5):
  順位                  特徴量        重要度
----------------------------------------
   1      人気_last_5R_mean     0.753
   2      着順_last_5R_mean     0.696
   3        interval_days     0.556
   4      上り_last_5R_mean     0.412
   5      斤量_last_5R_mean     0.406

🐎 個別予想根拠 (上位3頭):

• &#10003:
  有利要因: 斤量_last_5R_mean; 不利要因: 人気_last_5R_mean, 着順_last_5R_mean
  主な有利要因:
    - 斤量_last_5R_mean: +0.406
    - 賞金_last_10R_mean: +0.207
  主な不利要因:
    - 人気_last_5R_mean: -0.740
    - 着順_last_5R_mean: -0.716

• &#10003:
  有利要因: 斤量_last_5R_mean; 不利要因: 人気_last_5R_mean, 着順_last_5R_mean
  主な有利要因:
    - 斤量_last_5R_mean: +0.406
    - 賞金_all_R_mean: +0.193
  主な不利要因:
    - 人気_last_5R_mean: -0.740
    - 着順_last_5R_mean: -0.695

• &#10003:
  有利要因: 斤量_last_5R_mean; 不利要因: 人気_last_5R_mean, 着順_last_5R_mean
  主な有利要因:
    - 斤量_last_5R_mean: +0.406
    - 賞金_all_R_mean: +0.193
  主な不利要因:
    - 人気_last_5R_mean: -0.754
    - 着順_last_5R_mean: -0.695

🧠 モデル分析:
最も影響力のある要因: 人気_last_5R_mean
モデル複雑度: 26 個の有効特徴量
予想安定性: 普通 (偏差: 0.196)

⚖️ 判断パターン分析:
常に有利に働く要因: 枠番, 斤量, 距離
常に不利に働く要因: 馬番, 着順_last_5R_mean, 人気_last_5R_mean

================================================================================
予想根拠説明完了
================================================================================