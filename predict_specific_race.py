#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特定レースIDでの予想実行
"""

import sys
sys.path.append('.')
from enhanced_live_predictor import EnhancedLiveRacePredictor

def main():
    # 実際のレースIDで予想実行
    race_id = "202505021211"  # 2025年5月2日 11R
    
    print(f"レース予想開始: {race_id}")
    print("データリーケージ修正版モデルを使用")
    
    # 予想システム初期化（Selenium使用）
    predictor = EnhancedLiveRacePredictor(use_selenium=True)
    
    try:
        # 予想実行
        results, race_info = predictor.predict_race(race_id)
        
        if not results.empty:
            print(f"\n✅ レース{race_id}の予想が成功しました！")
            predictor.display_prediction_results(results, race_info)
        else:
            print(f"\n❌ レース{race_id}の予想に失敗しました")
            print("レースが存在しないか、データが取得できませんでした")
            
    except Exception as e:
        print(f"\n❌ エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()