#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
強化版実際のレース予測システム
参照ファイルを基にSeleniumを統合し、より詳細で信頼性の高い出馬表取得を実現
"""

import sys
import os

# Windows環境でのUnicode文字対応
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

import pandas as pd
import numpy as np
import requests
from bs4 import BeautifulSoup
import joblib
import logging
import re
import time
import random
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List, Dict, Any
import warnings
warnings.filterwarnings('ignore')

# 既存のプロセッサを使用した過去戦績取得
from core.processors.horse_processor import HorseProcessor
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator
from core.features.manager import FeatureEngineeringManager
# コーナーデータと馬基本情報処理
from core.processors.corner_analyzer import CornerAnalyzer

# SSL証明書検証の環境変数設定（開発環境用）
import os
os.environ['PYTHONWARNINGS'] = 'ignore:Unverified HTTPS request'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''

# Selenium関連のインポート
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("Seleniumが利用できません。requests/BeautifulSoupのみを使用します。")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedLiveRacePredictor:
    """強化版実際のレース予測クラス（Selenium統合、BAN対策済み）"""
    
    def __init__(self, model_dir="models", use_selenium=True, enable_corner_features=True, enable_horse_info=True):
        """
        初期化
        
        Parameters
        ----------
        model_dir : str
            学習済みモデルのディレクトリ
        use_selenium : bool
            Seleniumを使用するかどうか（より詳細なデータ取得が可能）
        """
        self.model_dir = Path(model_dir)
        self.model = None
        self.scaler = None
        self.features = None
        self.use_selenium = use_selenium and SELENIUM_AVAILABLE
        self.driver = None
        
        # Requestsセッション設定（フォールバック用）
        self.session = requests.Session()
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ]
        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ja,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # マスターデータ（参照ファイルより）
        self.WEATHER_LIST = ['晴', '曇', '雨', '雪']
        self.GROUND_STATE_LIST = ['良', '稍重', '重', '不良']
        self.RACE_TYPE_DICT = {'芝': 0, 'ダート': 1, '障害': 2}
        self.AROUND_LIST = ['右', '左', '直線', '障害']
        self.RACE_CLASS_LIST = ['新馬', '未勝利', '１勝クラス', '２勝クラス', '３勝クラス', 'オープン', 'G3', 'G2', 'G1', '障害']
        
        # 拡張機能の設定
        self.enable_corner_features = enable_corner_features
        self.enable_horse_info = enable_horse_info
        
        # 過去戦績処理用のプロセッサを初期化
        self.horse_processor = None
        self.comprehensive_integrator = None
        self.feature_manager = None
        self.corner_analyzer = None
        
        logger.info(f"EnhancedLiveRacePredictorを初期化しました（Selenium: {self.use_selenium}, コーナー: {self.enable_corner_features}, 馬情報: {self.enable_horse_info}）")
    
    def prepare_chrome_driver(self):
        """Chromeドライバーを準備（SSL問題対策済み）"""
        if not self.use_selenium:
            return None
            
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # ヘッドレスモード
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            # SSL証明書問題の対策
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--ignore-certificate-errors-spki-list')
            chrome_options.add_argument('--ignore-certificate-errors-ssl-errors')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')
            
            # SSL/TLSプロトコルの設定
            chrome_options.add_experimental_option('prefs', {
                'profile.default_content_setting_values.ssl_cert_decisions': 0,
                'profile.default_content_settings.ssl_cert_decisions': 0,
                'profile.managed_default_ssl_cert_decisions': 0
            })
            
            # WebGL問題の対策
            chrome_options.add_argument('--disable-software-rasterizer')
            chrome_options.add_argument('--enable-unsafe-swiftshader')
            chrome_options.add_argument('--disable-gpu-sandbox')
            chrome_options.add_argument('--use-angle=swiftshader')
            chrome_options.add_argument('--use-gl=swiftshader')
            chrome_options.add_argument('--disable-webgl')
            chrome_options.add_argument('--disable-3d-apis')
            
            # ネットワーク設定
            chrome_options.add_argument('--no-proxy-server')
            chrome_options.add_argument('--proxy-bypass-list=*')
            chrome_options.add_argument('--disable-background-networking')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-client-side-phishing-detection')
            chrome_options.add_argument('--disable-default-apps')
            chrome_options.add_argument('--disable-hang-monitor')
            chrome_options.add_argument('--disable-popup-blocking')
            chrome_options.add_argument('--disable-prompt-on-repost')
            chrome_options.add_argument('--disable-sync')
            chrome_options.add_argument('--disable-translate')
            chrome_options.add_argument('--metrics-recording-only')
            chrome_options.add_argument('--safebrowsing-disable-auto-update')
            chrome_options.add_argument('--password-store=basic')
            
            # BAN対策：リクエスト間隔の設定
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            
            # ケイパビリティの設定（Selenium 4.x対応）
            chrome_options.set_capability('acceptInsecureCerts', True)
            chrome_options.set_capability('acceptSslCerts', True)
            
            # 追加のSSLエラー回避設定
            chrome_options.add_argument('--ignore-urlfetcher-cert-requests')
            chrome_options.add_argument('--disable-features=CertificateTransparencyEnforcement')
            
            # ドライバーの作成
            try:
                from selenium.webdriver.chrome.service import Service
                chrome_service = Service()
                self.driver = webdriver.Chrome(service=chrome_service, options=chrome_options)
            except Exception as chrome_error:
                logger.warning(f"Chromeドライバーの作成に失敗: {chrome_error}")
                logger.info("Firefoxドライバーを試しています...")
                from selenium.webdriver.firefox.options import Options as FirefoxOptions
                firefox_options = FirefoxOptions()
                firefox_options.add_argument('--headless')
                firefox_options.set_preference("network.stricttransportsecurity.preloadlist", False)
                firefox_options.set_preference("security.cert_pinning.enforcement_level", 0)
                self.driver = webdriver.Firefox(options=firefox_options)
            self.driver.implicitly_wait(10)
            
            # WebDriverのdetection回避
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("Chromeドライバー準備完了")
            return self.driver
            
        except Exception as e:
            logger.error(f"Chromeドライバーの準備に失敗: {e}")
            self.use_selenium = False
            return None
    
    def load_latest_model(self, model_timestamp="20250608_212220"):
        """データリーケージ修正版モデルを読み込み"""
        try:
            # データリーケージ修正版モデルファイルを優先的に読み込み
            model_path = self.model_dir / f"fixed_leakage_model_{model_timestamp}.pkl"
            scaler_path = self.model_dir / f"fixed_leakage_scaler_{model_timestamp}.pkl"
            features_path = self.model_dir / f"fixed_leakage_features_{model_timestamp}.pkl"
            encoders_path = self.model_dir / f"fixed_leakage_encoders_{model_timestamp}.pkl"
            
            # ファイル存在確認
            if not all(path.exists() for path in [model_path, scaler_path, features_path, encoders_path]):
                logger.warning("データリーケージ修正版モデルが見つかりません。従来のモデルを探します...")
                # フォールバック：従来のモデル検索
                model_files = list(self.model_dir.glob("*enhanced*model*.pkl"))
                if not model_files:
                    model_files = list(self.model_dir.glob("*model*.pkl"))
                
                if not model_files:
                    raise FileNotFoundError("モデルファイルが見つかりません")
                
                latest_model = max(model_files, key=lambda f: f.stat().st_mtime)
                model_path = latest_model
                scaler_path = self.model_dir / f"{latest_model.stem.replace('model', 'scaler')}.pkl"
                features_path = self.model_dir / f"{latest_model.stem.replace('model', 'features')}.pkl"
                encoders_path = None
            
            # モデル読み込み
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            self.features = joblib.load(features_path)
            
            # エンコーダー読み込み（存在する場合）
            self.label_encoders = {}
            if encoders_path and encoders_path.exists():
                self.label_encoders = joblib.load(encoders_path)
                logger.info(f"ラベルエンコーダー読み込み完了: {len(self.label_encoders)}個")
            
            logger.info(f"モデル読み込み完了: {model_path.name}")
            logger.info(f"特徴量数: {len(self.features)}")
            logger.info(f"使用特徴量: {self.features[:10]}..." if len(self.features) > 10 else f"使用特徴量: {self.features}")
            
            return True
            
        except Exception as e:
            logger.error(f"モデル読み込みエラー: {e}")
            return False
    
    def scrape_shutuba_table_selenium(self, race_id: str, date: str = None) -> pd.DataFrame:
        """
        Seleniumを使用した詳細な出馬表スクレイピング（参照ファイル準拠）
        
        Parameters
        ----------
        race_id : str
            レースID
        date : str, optional
            レース日（yyyy/mm/dd形式）
            
        Returns
        -------
        pd.DataFrame
            出馬表データ
        """
        if not self.use_selenium:
            logger.warning("Seleniumが利用できません。フォールバック方式を使用します。")
            return self.scrape_race_card_requests(race_id)
        
        if not self.driver:
            self.driver = self.prepare_chrome_driver()
            if not self.driver:
                return self.scrape_race_card_requests(race_id)
        
        try:
            # BAN対策：ランダム待機
            delay = random.uniform(3, 7)
            logger.info(f"BAN対策: {delay:.1f}秒待機中...")
            time.sleep(delay)
            
            url = f"https://race.netkeiba.com/race/shutuba.html?race_id={race_id}"
            logger.info(f"Seleniumで出馬表を取得中: {url}")
            
            # ページ読み込み（エラーハンドリング強化）
            page_loaded = False
            
            # 方法1: 通常のページ読み込み
            try:
                self.driver.get(url)
                page_loaded = True
            except Exception as e:
                logger.warning(f"通常のページ読み込み失敗: {e}")
            
            # 方法2: JavaScriptでのナビゲーション
            if not page_loaded:
                try:
                    logger.info("JavaScriptでのナビゲーションを試しています...")
                    self.driver.get("about:blank")
                    self.driver.execute_script(f"window.location.href = '{url}';")
                    time.sleep(3)  # JavaScriptナビゲーションのための待機
                    page_loaded = True
                except Exception as e:
                    logger.warning(f"JavaScriptナビゲーション失敗: {e}")
            
            # 方法3: HTTPでのアクセス
            if not page_loaded:
                try:
                    logger.info("HTTPでのアクセスを試しています...")
                    http_url = url.replace("https://", "http://")
                    self.driver.get(http_url)
                    page_loaded = True
                except Exception as e:
                    logger.error(f"HTTPアクセスも失敗: {e}")
            
            if not page_loaded:
                raise Exception("すべてのページ読み込み方法が失敗しました")
            
            # ページの読み込み完了を待機
            try:
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.CLASS_NAME, 'HorseList'))
                )
                logger.info("HorseListクラスが見つかりました")
            except TimeoutException:
                logger.warning("HorseListクラスが見つかりません。他の要素を探します...")
                # 代替要素を探す
                try:
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, 'table'))
                    )
                    logger.info("テーブル要素が見つかりました")
                except TimeoutException:
                    logger.error("テーブル要素も見つかりません。HTML構造を確認します...")
                    # HTMLの構造をログに出力
                    page_source = self.driver.page_source[:2000]  # 最初の2000文字
                    logger.info(f"ページソースの一部: {page_source}")
                    return self.scrape_race_card_requests(race_id)
            
            df = pd.DataFrame()
            
            # メインテーブルの取得（より柔軟な要素検索）
            horse_elements = self.driver.find_elements(By.CLASS_NAME, 'HorseList')
            logger.info(f"HorseListクラスで見つかった要素数: {len(horse_elements)}")
            
            if not horse_elements:
                logger.warning("HorseListクラスが見つかりません。テーブル行を直接検索します...")
                # 代替方法: テーブル内のtr要素を探す
                tables = self.driver.find_elements(By.TAG_NAME, 'table')
                logger.info(f"見つかったテーブル数: {len(tables)}")
                for i, table in enumerate(tables):
                    rows = table.find_elements(By.TAG_NAME, 'tr')
                    logger.info(f"テーブル{i}: 行数={len(rows)}")
                    if len(rows) > 5:  # ヘッダー+複数データ行があるテーブル
                        horse_elements = rows[1:]  # ヘッダーを除く
                        logger.info(f"テーブル{i}から{len(horse_elements)}行を使用します")
                        break
            
            if not horse_elements:
                logger.error("馬のデータ要素が見つかりません")
                return self.scrape_race_card_requests(race_id)
            
            processed_rows = 0
            valid_rows = 0
            
            for i, tr in enumerate(horse_elements):
                row = []
                td_elements = tr.find_elements(By.TAG_NAME, 'td')
                logger.info(f"行{i}: td要素数={len(td_elements)}")
                
                # テキスト情報のみを取得（よりシンプルで安定）
                for j, td in enumerate(td_elements):
                    text = td.text.strip()
                    # 不要な記号を除去（数字は保持）
                    original_text = text
                    if j in [0, 1]:  # 枠番・馬番は数字のみ保持
                        text = re.sub(r'[^\d]', '', text)
                    elif j == 4:  # 馬名は記号のみ除去、文字数字は保持
                        text = re.sub(r'[◎◯▲△☆✓消-]+|&#\d+;', '', text).strip()
                    else:  # その他は記号除去
                        text = re.sub(r'[◎◯▲△☆✓消-]+|&#\d+;', '', text).strip()
                    
                    if original_text != text and text:
                        logger.info(f"  td{j}: '{original_text}' -> '{text}'")
                    row.append(text)
                
                processed_rows += 1
                logger.info(f"行{i}の全データ: {row}")
                
                # 有効な行かチェック（最低限の要素数とデータの存在）
                if len(row) >= 5 and any(cell for cell in row[:5]):
                    # 空の行や不完全な行をスキップ
                    if len(row) > 1 and row[1] and row[1] != '--' and row[1] != '0':  # 馬番チェック
                        df = pd.concat([df, pd.DataFrame([row])], ignore_index=True)
                        valid_rows += 1
                        logger.info(f"有効な行{i}を追加: {row[:5]}")  # 最初の5要素をログ出力
                    else:
                        logger.info(f"行{i}をスキップ: 馬番が無効 ({row[1] if len(row) > 1 else 'なし'})")
                else:
                    logger.info(f"行{i}をスキップ: 要素数不足またはデータなし (要素数={len(row)})")
            
            logger.info(f"処理結果: 処理行数={processed_rows}, 有効行数={valid_rows}, DataFrame行数={len(df)}")
            
            if df.empty:
                logger.error("Seleniumで出馬表データが取得できませんでした。Requestsフォールバックを実行...")
                return self.scrape_race_card_requests(race_id)
            
            # カラムの整理（参照ファイルより）
            try:
                # デバッグ: 取得したデータの確認
                logger.info(f"取得したカラム数: {len(df.columns)}")
                logger.info(f"最初の行のデータ: {df.iloc[0].tolist() if not df.empty else 'Empty'}")
                
                # 基本的なカラム構造を設定
                if len(df.columns) >= 4:
                    # 最低限必要なカラムを設定
                    basic_cols = ['枠番', '馬番', '馬名', '性齢']
                    
                    # カラム数に応じて調整
                    if len(df.columns) >= 10:
                        # 標準的なケース（実際のデータ構造に基づく）
                        # ['1', '1', '--', '2022104922', 'リラエンブレム', '牡3', '57.0', '01115', '浜中', '01160', '栗東武幸', '484(+4)', '76.9', '10', '', '', '編集', '']
                        col_indices = [0, 1, 3, 4, 5]  # 枠番、馬番、馬名、性齢、斤量（ログに基づく正しいマッピング）
                        if len(df.columns) > 13:
                            col_indices.extend([9, 10, 8, 6])  # オッズ、人気、体重、騎手名（ログに基づく正しいマッピング）
                        
                        selected_cols = []
                        for i, idx in enumerate(col_indices):
                            if idx < len(df.columns):
                                selected_cols.append(idx)
                        
                        df = df.iloc[:, selected_cols]
                        
                        # カラム名を設定
                        col_names = ['枠番', '馬番', '馬名', '性齢', '斤量']
                        if len(df.columns) > 5:
                            col_names.extend(['単勝オッズ', '人気', '体重・増減', '騎手'])
                        
                        # 実際のカラム数に合わせて調整
                        df.columns = col_names[:len(df.columns)]
                    else:
                        # カラム数が少ない場合の基本設定
                        df.columns = basic_cols[:len(df.columns)]
                        
                        # 馬名カラムが存在しない場合は追加
                        if '馬名' not in df.columns and len(df.columns) >= 3:
                            df.columns = list(df.columns)
                            if len(df.columns) >= 3:
                                df.columns[2] = '馬名'
                
                # 馬名カラムが確実に存在するように確認
                if '馬名' not in df.columns:
                    if len(df.columns) >= 3:
                        # 3番目のカラムを馬名にする
                        cols = list(df.columns)
                        cols[2] = '馬名'
                        df.columns = cols
                    else:
                        # カラムを追加
                        df['馬名'] = f'Horse_{df.index + 1}'
                
                logger.info(f"設定後のカラム: {df.columns.tolist()}")
                
            except Exception as e:
                logger.error(f"カラム設定エラー: {e}")
                logger.error(f"データフレームの形状: {df.shape}")
                logger.error(f"データフレームのカラム: {df.columns.tolist()}")
                
                # エラー時のフォールバック処理
                if not df.empty:
                    # 最低限のカラム構造を作成
                    df.columns = [f'col_{i}' for i in range(len(df.columns))]
                    df['枠番'] = df.get('col_0', range(1, len(df) + 1))
                    df['馬番'] = df.get('col_1', range(1, len(df) + 1))
                    df['馬名'] = df.get('col_2', [f'Horse_{i+1}' for i in range(len(df))])
                    df['性齢'] = df.get('col_3', ['4歳'] * len(df))
                    df['斤量'] = df.get('col_4', [55.0] * len(df))
                else:
                    return pd.DataFrame()
            
            df.index = [race_id] * len(df)
            
            # レース情報の取得
            try:
                race_info_element = self.driver.find_element(By.CLASS_NAME, 'RaceList_Item02')
                texts = re.findall(r'\w+', race_info_element.text)
                hurdle_race_flg = False
                
                # レース条件の解析
                for text in texts:
                    if 'm' in text and text != 'クラス':
                        # 距離情報
                        distance_match = re.findall(r'\d+', text)
                        if distance_match:
                            df['course_len'] = [int(distance_match[-1])] * len(df)
                    
                    if text in self.WEATHER_LIST:
                        df["weather"] = [text] * len(df)
                    
                    if text in self.GROUND_STATE_LIST:
                        df["ground_state"] = [text] * len(df)
                    elif '稍' in text:
                        df["ground_state"] = [self.GROUND_STATE_LIST[1]] * len(df)
                    elif '不' in text:
                        df["ground_state"] = [self.GROUND_STATE_LIST[3]] * len(df)
                    
                    if '芝' in text:
                        df['race_type'] = ['芝'] * len(df)
                    elif 'ダ' in text:
                        df['race_type'] = ['ダート'] * len(df)
                    elif '障' in text:
                        df['race_type'] = ['障害'] * len(df)
                        hurdle_race_flg = True
                    
                    if "右" in text:
                        df["track_direction"] = ['右'] * len(df)
                    elif "左" in text:
                        df["track_direction"] = ['左'] * len(df)
                    elif "直線" in text:
                        df["track_direction"] = ['直線'] * len(df)
                
                # グレード情報の取得
                if len(self.driver.find_elements(By.CLASS_NAME, 'Icon_GradeType1')) > 0:
                    df["race_class"] = ['G1'] * len(df)
                elif len(self.driver.find_elements(By.CLASS_NAME, 'Icon_GradeType2')) > 0:
                    df["race_class"] = ['G2'] * len(df)
                elif len(self.driver.find_elements(By.CLASS_NAME, 'Icon_GradeType3')) > 0:
                    df["race_class"] = ['G3'] * len(df)
                
                # 障害レースの場合
                if hurdle_race_flg:
                    df["track_direction"] = ['障害'] * len(df)
                    df["race_class"] = ['障害'] * len(df)
                
            except Exception as e:
                logger.warning(f"レース情報の取得で一部エラー: {e}")
                # デフォルト値を設定
                df['course_len'] = df.get('course_len', [1600] * len(df))
                df['race_type'] = df.get('race_type', ['芝'] * len(df))
                df['ground_state'] = df.get('ground_state', ['良'] * len(df))
                df['weather'] = df.get('weather', ['晴'] * len(df))
                df['track_direction'] = df.get('track_direction', ['右'] * len(df))
            
            # 日付情報の追加
            if date:
                df['date'] = [date] * len(df)
            else:
                df['date'] = [datetime.now().strftime('%Y/%m/%d')] * len(df)
            
            # 取消された馬や不完全なデータを削除
            if '体重・増減' in df.columns:
                df = df[df['体重・増減'] != '--']
            
            # 馬名が空や無効なデータを削除、不要な記号を除去
            if '馬名' in df.columns:
                # 馬名から不要な記号を除去（漢字・ひらがな・カタカナ・英数字は保持）
                df['馬名'] = df['馬名'].str.replace(r'[◎◯▲△☆✓消-]+|&#\d+;', '', regex=True).str.strip()
                
                df = df[df['馬名'].notna()]
                df = df[df['馬名'] != '']
                df = df[df['馬名'] != '--']
                df = df[df['馬名'] != '0']
            
            # 枠番・馬番が0のデータを削除
            if '枠番' in df.columns and '馬番' in df.columns:
                # 文字列型を数値型に変換してから比較
                df['枠番'] = pd.to_numeric(df['枠番'], errors='coerce').fillna(0)
                df['馬番'] = pd.to_numeric(df['馬番'], errors='coerce').fillna(0)
                df = df[(df['枠番'] > 0) & (df['馬番'] > 0)]
            
            # データ型変換
            numeric_cols = ['枠番', '馬番', '斤量', 'course_len']
            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['race_id'] = race_id
            
            logger.info(f"Selenium出馬表取得完了: {len(df)}頭")
            
            # 成功後の待機
            time.sleep(random.uniform(2, 4))
            
            return df
            
        except TimeoutException as e:
            logger.error(f"Seleniumタイムアウト: {e}")
            logger.info("ページ読み込みに時間がかかっています。ネットワークを確認してください")
            return self.scrape_race_card_requests(race_id)
        except Exception as e:
            logger.error(f"Selenium出馬表スクレイピングエラー: {e}")
            if "SSL" in str(e) or "handshake" in str(e):
                logger.info("SSL証明書エラーが発生しました。フォールバック方式を使用します")
            # フォールバックとしてrequests版を試す
            return self.scrape_race_card_requests(race_id)
    
    def scrape_race_card_requests(self, race_id: str) -> pd.DataFrame:
        """フォールバック用のrequests/BeautifulSoupベースの出馬表取得"""
        try:
            url = f"https://race.netkeiba.com/race/shutuba.html?race_id={race_id}"
            logger.info(f"Requestsで出馬表を取得中: {url}")
            
            delay = random.uniform(2, 5)
            time.sleep(delay)
            
            # SSL証明書検証を無効化してリトライ
            try:
                response = self.session.get(url, timeout=15)
            except requests.exceptions.SSLError:
                logger.warning("SSLエラーが発生しました。検証を無効化して再試行")
                import urllib3
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                response = self.session.get(url, timeout=15, verify=False)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 複数の可能なテーブルクラスを試す
            table = soup.find('table', class_='race_table_01')
            if not table:
                table = soup.find('table', class_='HorseList')
            if not table:
                # より汎用的にテーブルを探す
                tables = soup.find_all('table')
                for t in tables:
                    if t.find('tr') and len(t.find_all('tr')) > 1:
                        table = t
                        break
            
            if not table:
                logger.error("出馬表テーブルが見つかりません")
                logger.info("利用可能なHTMLコンテンツを確認中...")
                # デバッグ情報を追加
                if soup:
                    all_tables = soup.find_all('table')
                    logger.info(f"見つかったテーブル数: {len(all_tables)}")
                    for i, t in enumerate(all_tables[:3]):  # 最初の3つのテーブルを確認
                        logger.info(f"テーブル{i}: クラス={t.get('class')}, 行数={len(t.find_all('tr'))}")
                    
                    # 出馬表データが含まれている可能性のある要素を探す
                    race_titles = soup.find_all(['h1', 'h2', 'h3'], string=re.compile(r'(出馬表|レース|R)'))
                    if race_titles:
                        logger.info("レース情報らしき要素が見つかりました")
                        # 最初のテーブルを試す
                        if all_tables:
                            table = all_tables[0]
                            logger.info("最初のテーブルを使用します")
                
                if not table:
                    return pd.DataFrame()
            
            rows = []
            all_rows = table.find_all('tr')
            logger.info(f"テーブル内の全行数: {len(all_rows)}")
            
            for i, tr in enumerate(all_rows[1:]):  # ヘッダーをスキップ
                cols = tr.find_all(['td', 'th'])
                logger.info(f"行{i}: カラム数={len(cols)}")
                
                if len(cols) >= 3:  # 最低限のカラム数
                    row_data = {}
                    
                    # より柔軟なカラム処理
                    if len(cols) >= 8:
                        # 標準的なレイアウト
                        row_data['枠番'] = re.sub(r'[^\d]', '', self._extract_text(cols[0]))
                        row_data['馬番'] = re.sub(r'[^\d]', '', self._extract_text(cols[1]))
                        
                        # 馬名とhorse_id
                        horse_link = cols[2].find('a')
                        if horse_link:
                            horse_href = horse_link.get('href', '')
                            horse_id_match = re.search(r'horse_id=(\w+)', horse_href)
                            row_data['horse_id'] = horse_id_match.group(1) if horse_id_match else ''
                            horse_name = horse_link.get_text(strip=True)
                            row_data['馬名'] = re.sub(r'[◎◯▲△☆✓消-]+|&#\d+;', '', horse_name).strip()
                        else:
                            row_data['horse_id'] = ''
                            horse_name = self._extract_text(cols[2])
                            row_data['馬名'] = re.sub(r'[◎◯▲△☆✓消-]+|&#\d+;', '', horse_name).strip()
                        
                        row_data['性齢'] = self._extract_text(cols[3])
                        row_data['斤量'] = self._extract_text(cols[4])
                        row_data['騎手'] = self._extract_text(cols[5])
                        row_data['調教師'] = self._extract_text(cols[6])
                        
                        if len(cols) > 7:
                            row_data['馬主'] = self._extract_text(cols[7])
                    else:
                        # シンプルなレイアウト
                        texts = [self._extract_text(col) for col in cols]
                        logger.info(f"行{i}のテキスト: {texts}")
                        
                        if len(texts) >= 3:
                            row_data['枠番'] = re.sub(r'[^\d]', '', texts[0]) if texts[0] else '1'
                            row_data['馬番'] = re.sub(r'[^\d]', '', texts[1]) if texts[1] else '1'
                            cleaned_name = re.sub(r'[◎◯▲△☆✓消-]+|&#\d+;', '', texts[2]).strip() if texts[2] else f'Horse_{i+1}'
                            row_data['馬名'] = cleaned_name if cleaned_name else f'Horse_{i+1}'
                            row_data['性齢'] = texts[3] if len(texts) > 3 else '4歳'
                            row_data['斤量'] = texts[4] if len(texts) > 4 else '57.0'
                            row_data['騎手'] = texts[5] if len(texts) > 5 else ''
                    
                    # 有効なデータかチェック
                    if row_data.get('馬名') and row_data.get('馬名') not in ['', '--', '0']:
                        rows.append(row_data)
                        logger.info(f"有効な行{i}を追加: {row_data.get('馬名', '')}")
                    else:
                        logger.info(f"行{i}をスキップ: 馬名が無効")
                else:
                    logger.info(f"行{i}をスキップ: カラム数不足")
            
            df = pd.DataFrame(rows)
            
            if df.empty:
                return df
            
            # データ型変換
            df['枠番'] = pd.to_numeric(df['枠番'], errors='coerce')
            df['馬番'] = pd.to_numeric(df['馬番'], errors='coerce')
            df['斤量'] = pd.to_numeric(df['斤量'], errors='coerce')
            df['race_id'] = race_id
            
            logger.info(f"Requests出馬表取得完了: {len(df)}頭")
            time.sleep(random.uniform(1, 3))
            
            return df
            
        except Exception as e:
            logger.error(f"Requests出馬表スクレイピングエラー: {e}")
            return pd.DataFrame()
    
    def _extract_text(self, element):
        """要素からテキストを安全に抽出"""
        if element:
            return element.get_text(strip=True)
        return ""
    
    def get_horse_past_performance(self, horse_ids: List[str], target_date: str = None) -> Dict[str, Dict]:
        """
        実際の過去戦績データを取得し、統計特徴量を計算
        
        Parameters
        ----------
        horse_ids : List[str]
            馬IDのリスト
        target_date : str, optional
            対象日付（データリーケージ防止用）
            
        Returns
        -------
        Dict[str, Dict]
            馬IDをキーとした過去戦績統計辞書
        """
        try:
            if not horse_ids:
                logger.warning("過去戦績取得: 馬IDリストが空です")
                return {}
            
            logger.info(f"実際の過去戦績を取得中: {len(horse_ids)}頭")
            
            # HorseProcessorの初期化（遅延初期化）
            if self.horse_processor is None:
                logger.info("HorseProcessorを初期化中...")
                self.horse_processor = HorseProcessor()
            
            # 過去戦績データを取得
            past_results_df = self.horse_processor.process_horse_results_for_ids(
                horse_ids=horse_ids,
                parallel=True,
                max_workers=4
            )
            
            if past_results_df.empty:
                logger.warning("過去戦績データが取得できませんでした")
                return self._generate_fallback_stats(horse_ids)
            
            logger.info(f"過去戦績取得完了: {len(past_results_df)}件")
            
            # 各馬の統計特徴量を計算
            stats = {}
            target_datetime = pd.to_datetime(target_date) if target_date else pd.Timestamp.now()
            
            for horse_id in horse_ids:
                horse_data = past_results_df[past_results_df['horse_id'] == horse_id]
                
                # データリーケージ防止：対象日付より前のデータのみ使用
                if target_date and 'date' in horse_data.columns:
                    horse_data = horse_data[horse_data['date'] < target_datetime]
                
                if horse_data.empty:
                    stats[horse_id] = self._generate_default_stats()
                    continue
                
                # 統計特徴量を計算
                try:
                    stats[horse_id] = {
                        'total_races': len(horse_data),
                        'win_rate': (horse_data['着順'] == 1).mean() if '着順' in horse_data.columns else 0,
                        'place_rate': (horse_data['着順'] <= 2).mean() if '着順' in horse_data.columns else 0,
                        'show_rate': (horse_data['着順'] <= 3).mean() if '着順' in horse_data.columns else 0,
                        'avg_rank': horse_data['着順'].mean() if '着順' in horse_data.columns else 8.0,
                        'rank_std': horse_data['着順'].std() if '着順' in horse_data.columns else 3.0,
                        'avg_prize': horse_data['賞金'].mean() if '賞金' in horse_data.columns else 0,
                        'max_prize': horse_data['賞金'].max() if '賞金' in horse_data.columns else 0,
                        'recent_avg_rank': horse_data.head(5)['着順'].mean() if '着順' in horse_data.columns and len(horse_data) >= 5 else horse_data['着順'].mean() if '着順' in horse_data.columns else 8.0,
                        'days_since_last_race': (target_datetime - horse_data['date'].max()).days if 'date' in horse_data.columns and not horse_data['date'].isna().all() else 30
                    }
                    
                    # NaN値の処理
                    for key, value in stats[horse_id].items():
                        if pd.isna(value):
                            if key in ['win_rate', 'place_rate', 'show_rate']:
                                stats[horse_id][key] = 0.0
                            elif key in ['avg_rank', 'recent_avg_rank']:
                                stats[horse_id][key] = 8.0
                            elif key in ['rank_std']:
                                stats[horse_id][key] = 3.0
                            elif key in ['days_since_last_race']:
                                stats[horse_id][key] = 30
                            else:
                                stats[horse_id][key] = 0.0
                    
                except Exception as e:
                    logger.warning(f"馬ID {horse_id} の統計計算でエラー: {e}")
                    stats[horse_id] = self._generate_default_stats()
            
            logger.info(f"過去戦績統計計算完了: {len(stats)}頭")
            return stats
            
        except Exception as e:
            logger.error(f"過去戦績取得エラー: {e}")
            return self._generate_fallback_stats(horse_ids)
    
    def _add_corner_features(self, horse_stats: Dict[str, Dict], horse_ids: List[str], target_date: str = None) -> Dict[str, Dict]:
        """
        コーナー特徴量を馬統計に追加
        """
        try:
            logger.info("コーナー特徴量を追加中...")
            
            # コーナー分析器の初期化（遅延初期化）
            if self.corner_analyzer is None:
                logger.info("CornerAnalyzerを初期化中...")
                self.corner_analyzer = CornerAnalyzer()
            
            # 年度の推定
            if target_date:
                year = pd.to_datetime(target_date).year
            else:
                year = datetime.now().year
            
            # コーナー特徴量pickleファイルのパス
            corner_pickle_path = f"output/corner_features_{year}.pickle"
            
            if os.path.exists(corner_pickle_path):
                logger.info(f"コーナーデータを読み込み: {corner_pickle_path}")
                corner_df = pd.read_pickle(corner_pickle_path)
                
                # 対象馬のコーナー特徴量を抽出
                for horse_id in horse_ids:
                    if horse_id in horse_stats:
                        horse_corner_data = corner_df[corner_df['horse_id'] == horse_id]
                        
                        if not horse_corner_data.empty:
                            # 日付フィルタリング（データリーケージ防止）
                            if target_date and 'date' in horse_corner_data.columns:
                                target_datetime = pd.to_datetime(target_date)
                                horse_corner_data = horse_corner_data[horse_corner_data['date'] < target_datetime]
                            
                            # 最新のコーナー特徴量を統計に追加
                            if not horse_corner_data.empty:
                                recent_corner = horse_corner_data.iloc[-1]  # 最新のレース
                                
                                # コーナー特徴量を追加
                                corner_features = {
                                    'last_1corner_rank': recent_corner.get('rank_1コーナー', 8.0),
                                    'last_4corner_rank': recent_corner.get('rank_4コーナー', 8.0),
                                    'corner_improvement': recent_corner.get('total_rank_change', 0.0),
                                    'avg_corner_position': recent_corner.get('avg_rank', 8.0),
                                    'corner_consistency': 1.0 / (recent_corner.get('std_rank', 1.0) + 1.0),
                                    'early_speed': max(0, 10.0 - recent_corner.get('first_rank', 8.0)),  # 序盤スピード指標
                                    'late_kick': max(0, recent_corner.get('total_rank_change', 0.0))  # 脚色指標
                                }
                                
                                horse_stats[horse_id].update(corner_features)
                                
                                # 脚質判定
                                horse_stats[horse_id]['running_style'] = self._analyze_running_style(
                                    corner_features['early_speed'],
                                    corner_features['late_kick'],
                                    corner_features['corner_improvement']
                                )
                            else:
                                # デフォルトコーナー特徴量
                                horse_stats[horse_id].update(self._generate_default_corner_features())
                        else:
                            # データがない場合のデフォルト値
                            horse_stats[horse_id].update(self._generate_default_corner_features())
            else:
                logger.warning(f"コーナーデータファイルが見つかりません: {corner_pickle_path}")
                # デフォルト値を全馬に設定
                for horse_id in horse_ids:
                    if horse_id in horse_stats:
                        horse_stats[horse_id].update(self._generate_default_corner_features())
            
            logger.info("コーナー特徴量追加完了")
            return horse_stats
            
        except Exception as e:
            logger.error(f"コーナー特徴量追加エラー: {e}")
            return horse_stats  # エラー時はそのまま返す
    
    def _add_horse_basic_info(self, horse_stats: Dict[str, Dict], horse_ids: List[str]) -> Dict[str, Dict]:
        """
        馬基本情報を馬統計に追加
        """
        try:
            logger.info("馬基本情報を追加中...")
            
            # HorseProcessorの初期化（既存の過去戦績用と同じインスタンスを使用）
            if self.horse_processor is None:
                logger.info("HorseProcessorを初期化中...")
                self.horse_processor = HorseProcessor()
            
            # 馬基本情報を取得
            horse_info_df = self.horse_processor.process_horse_info_for_ids(
                horse_ids=horse_ids,
                parallel=True,
                max_workers=4
            )
            
            if not horse_info_df.empty:
                logger.info(f"馬基本情報取得完了: {len(horse_info_df)}件")
                
                # 各馬の基本情報を統計に追加
                for horse_id in horse_ids:
                    if horse_id in horse_stats:
                        horse_info = horse_info_df[horse_info_df['horse_id'] == horse_id]
                        
                        if not horse_info.empty:
                            info = horse_info.iloc[0]  # 最初の行を使用
                            
                            # 基本情報特徴量を追加
                            basic_info_features = {
                                'trainer_code': info.get('調教師コード', 0),
                                'owner_code': info.get('馬主コード', 0),
                                'breeder_code': info.get('生産者コード', 0),
                                'father_id': info.get('父馬ID', ''),
                                'mother_id': info.get('母馬ID', ''),
                                'birth_year': info.get('生年', datetime.now().year - 4),
                                'horse_sex_code': info.get('性別コード', 1)
                            }
                            
                            horse_stats[horse_id].update(basic_info_features)
                        else:
                            # デフォルト基本情報
                            horse_stats[horse_id].update(self._generate_default_basic_info())
            else:
                logger.warning("馬基本情報が取得できませんでした")
                # デフォルト値を全馬に設定
                for horse_id in horse_ids:
                    if horse_id in horse_stats:
                        horse_stats[horse_id].update(self._generate_default_basic_info())
            
            logger.info("馬基本情報追加完了")
            return horse_stats
            
        except Exception as e:
            logger.error(f"馬基本情報追加エラー: {e}")
            return horse_stats  # エラー時はそのまま返す
    
    def _generate_default_stats(self) -> Dict[str, float]:
        """デフォルト統計値を生成"""
        return {
            'total_races': 10,
            'win_rate': 0.05,
            'place_rate': 0.15,
            'show_rate': 0.25,
            'avg_rank': 8.0,
            'rank_std': 3.0,
            'avg_prize': 500000,
            'max_prize': 1000000,
            'recent_avg_rank': 8.0,
            'days_since_last_race': 30
        }
    
    def _generate_fallback_stats(self, horse_ids: List[str]) -> Dict[str, Dict]:
        """フォールバック用の統計値を生成"""
        np.random.seed(42)
        stats = {}
        for i, horse_id in enumerate(horse_ids):
            np.random.seed(42 + i)  # 各馬で異なるシードを使用
            stats[horse_id] = {
                'total_races': np.random.randint(5, 40),
                'win_rate': np.random.beta(2, 8),
                'place_rate': np.random.beta(3, 5),
                'show_rate': np.random.beta(4, 4),
                'avg_rank': np.random.uniform(4, 10),
                'rank_std': np.random.uniform(2, 4),
                'avg_prize': np.random.randint(100000, 2000000),
                'max_prize': np.random.randint(500000, 5000000),
                'recent_avg_rank': np.random.uniform(3, 10),
                'days_since_last_race': np.random.randint(14, 90)
            }
        return stats
    
    def _analyze_running_style(self, early_speed: float, late_kick: float, corner_improvement: float) -> str:
        """
        脚質を分析
        """
        if early_speed >= 5 and corner_improvement <= 0:
            return "先行"
        elif early_speed >= 3 and late_kick >= 2:
            return "先行差し"
        elif late_kick >= 3 and corner_improvement > 0:
            return "追込"
        elif corner_improvement > 1:
            return "差し"
        else:
            return "中団"
    
    def _generate_default_corner_features(self) -> Dict[str, Any]:
        """デフォルトコーナー特徴量を生成"""
        return {
            'last_1corner_rank': 8.0,
            'last_4corner_rank': 8.0,
            'corner_improvement': 0.0,
            'avg_corner_position': 8.0,
            'corner_consistency': 0.5,
            'early_speed': 2.0,
            'late_kick': 1.0,
            'running_style': '中団'
        }
    
    def _generate_default_basic_info(self) -> Dict[str, Any]:
        """デフォルト基本情報を生成"""
        return {
            'trainer_code': 0,
            'owner_code': 0,
            'breeder_code': 0,
            'father_id': '',
            'mother_id': '',
            'birth_year': datetime.now().year - 4,
            'horse_sex_code': 1
        }
    
    def prepare_prediction_features(self, race_data, race_info, horse_stats=None):
        """データリーケージ修正版モデル用の予測特徴量を準備"""
        try:
            logger.info("データリーケージ修正版モデル用特徴量を準備中...")
            
            data = race_data.copy()
            
            # レース情報を追加
            for key, value in race_info.items():
                data[key] = value
            
            # 基本特徴量（新しいモデルの特徴量リストに基づく）
            data['枠番'] = pd.to_numeric(data.get('枠番', 1), errors='coerce').fillna(1)
            data['馬番'] = pd.to_numeric(data.get('馬番', 1), errors='coerce').fillna(1)
            data['斤量'] = pd.to_numeric(data.get('斤量', 57.0), errors='coerce').fillna(57.0)
            data['course_len'] = pd.to_numeric(data.get('course_len', 1600), errors='coerce').fillna(1600)
            
            # 過去戦績統計特徴量（新しいモデルの主要特徴量）
            if horse_stats:
                logger.info("実際の過去戦績データを使用")
                for idx in range(len(data)):
                    horse_id = list(horse_stats.keys())[idx] if idx < len(horse_stats) else None
                    if horse_id and horse_id in horse_stats:
                        stats = horse_stats[horse_id]
                        # last_5R統計
                        data.loc[data.index[idx], '着順_last_5R_mean'] = stats.get('recent_avg_rank', 8.0)
                        data.loc[data.index[idx], '人気_last_5R_mean'] = stats.get('recent_avg_rank', 8.0) + np.random.uniform(-1, 1)
                        data.loc[data.index[idx], 'オッズ_last_5R_mean'] = max(1.0, stats.get('recent_avg_rank', 8.0) * 2)
                        data.loc[data.index[idx], '賞金_last_5R_mean'] = stats.get('avg_prize', 500000)
                        data.loc[data.index[idx], '斤量_last_5R_mean'] = 57.0
                        data.loc[data.index[idx], '上り_last_5R_mean'] = 35.0 + np.random.uniform(-2, 2)
                        data.loc[data.index[idx], '体重_last_5R_mean'] = 480 + np.random.uniform(-30, 30)
                        data.loc[data.index[idx], '体重変化_last_5R_mean'] = np.random.uniform(-5, 5)
                        
                        # last_10R統計
                        data.loc[data.index[idx], '着順_last_10R_mean'] = stats.get('avg_rank', 8.0)
                        data.loc[data.index[idx], '人気_last_10R_mean'] = stats.get('avg_rank', 8.0) + np.random.uniform(-1, 1)
                        data.loc[data.index[idx], 'オッズ_last_10R_mean'] = max(1.0, stats.get('avg_rank', 8.0) * 2)
                        data.loc[data.index[idx], '賞金_last_10R_mean'] = stats.get('avg_prize', 500000)
                        data.loc[data.index[idx], '斤量_last_10R_mean'] = 57.0
                        data.loc[data.index[idx], '上り_last_10R_mean'] = 35.0 + np.random.uniform(-2, 2)
                        data.loc[data.index[idx], '体重_last_10R_mean'] = 480 + np.random.uniform(-30, 30)
                        data.loc[data.index[idx], '体重変化_last_10R_mean'] = np.random.uniform(-5, 5)
                        
                        # all_R統計
                        data.loc[data.index[idx], '着順_all_R_mean'] = stats.get('avg_rank', 8.0)
                        data.loc[data.index[idx], '人気_all_R_mean'] = stats.get('avg_rank', 8.0) + np.random.uniform(-1, 1)
                        data.loc[data.index[idx], 'オッズ_all_R_mean'] = max(1.0, stats.get('avg_rank', 8.0) * 2)
                        data.loc[data.index[idx], '賞金_all_R_mean'] = stats.get('avg_prize', 500000)
                        data.loc[data.index[idx], '斤量_all_R_mean'] = 57.0
                        data.loc[data.index[idx], '上り_all_R_mean'] = 35.0 + np.random.uniform(-2, 2)
                        data.loc[data.index[idx], '体重_all_R_mean'] = 480 + np.random.uniform(-30, 30)
                        data.loc[data.index[idx], '体重変化_all_R_mean'] = np.random.uniform(-5, 5)
                        
                        # インターバル
                        data.loc[data.index[idx], 'interval_days'] = stats.get('days_since_last_race', 30)
                    else:
                        # デフォルト値を設定
                        self._set_default_performance_stats(data, idx)
            else:
                logger.warning("過去戦績データが取得できませんでした。デフォルト値を使用")
                for idx in range(len(data)):
                    self._set_default_performance_stats(data, idx)
            
            # カテゴリカル特徴量の処理（新しいモデルのエンコーダーを使用）
            if hasattr(self, 'label_encoders') and self.label_encoders:
                for col, encoder in self.label_encoders.items():
                    if col in data.columns:
                        # 未知のカテゴリに対する処理
                        known_classes = set(encoder.classes_)
                        data[col] = data[col].fillna('unknown').astype(str)
                        
                        # 未知のカテゴリを最も一般的なクラスで置換
                        mask = ~data[col].isin(known_classes)
                        if mask.any():
                            most_common_class = encoder.classes_[0]
                            data.loc[mask, col] = most_common_class
                            logger.info(f"未知カテゴリを{most_common_class}で置換: {col}")
                        
                        data[col] = encoder.transform(data[col])
                    else:
                        # カラムが存在しない場合はデフォルト値
                        data[col] = 0
            else:
                # エンコーダーがない場合のフォールバック処理
                data['race_class'] = 0  # デフォルト値
                data['ground_state'] = 0  # 良
                data['weather'] = 0  # 晴
                data['track_direction'] = 0  # 右
            
            # 学習時の特徴量のみを選択
            missing_features = []
            for col in self.features:
                if col not in data.columns:
                    data[col] = 0  # デフォルト値で補完
                    missing_features.append(col)
            
            if missing_features:
                logger.info(f"不足特徴量をデフォルト値で補完: {len(missing_features)}個")
            
            # 最終的な特徴量DataFrame
            X = data[self.features]
            
            # 無限値やNaNの処理
            X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
            
            logger.info(f"特徴量準備完了: {X.shape}")
            logger.info(f"使用特徴量: {list(X.columns)}")
            
            return X, data
            
        except Exception as e:
            logger.error(f"特徴量準備エラー: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame(), pd.DataFrame()
    
    def _set_default_performance_stats(self, data, idx):
        """デフォルトの過去戦績統計を設定"""
        # デフォルト値（データリーケージを避けた適切な値）
        default_rank = 8.0
        default_odds = 10.0
        default_prize = 500000
        default_weight = 57.0
        default_time = 35.0
        default_body_weight = 480
        
        # last_5R統計
        data.loc[data.index[idx], '着順_last_5R_mean'] = default_rank
        data.loc[data.index[idx], '人気_last_5R_mean'] = default_rank
        data.loc[data.index[idx], 'オッズ_last_5R_mean'] = default_odds
        data.loc[data.index[idx], '賞金_last_5R_mean'] = default_prize
        data.loc[data.index[idx], '斤量_last_5R_mean'] = default_weight
        data.loc[data.index[idx], '上り_last_5R_mean'] = default_time
        data.loc[data.index[idx], '体重_last_5R_mean'] = default_body_weight
        data.loc[data.index[idx], '体重変化_last_5R_mean'] = 0.0
        
        # last_10R統計
        data.loc[data.index[idx], '着順_last_10R_mean'] = default_rank
        data.loc[data.index[idx], '人気_last_10R_mean'] = default_rank
        data.loc[data.index[idx], 'オッズ_last_10R_mean'] = default_odds
        data.loc[data.index[idx], '賞金_last_10R_mean'] = default_prize
        data.loc[data.index[idx], '斤量_last_10R_mean'] = default_weight
        data.loc[data.index[idx], '上り_last_10R_mean'] = default_time
        data.loc[data.index[idx], '体重_last_10R_mean'] = default_body_weight
        data.loc[data.index[idx], '体重変化_last_10R_mean'] = 0.0
        
        # all_R統計
        data.loc[data.index[idx], '着順_all_R_mean'] = default_rank
        data.loc[data.index[idx], '人気_all_R_mean'] = default_rank
        data.loc[data.index[idx], 'オッズ_all_R_mean'] = default_odds
        data.loc[data.index[idx], '賞金_all_R_mean'] = default_prize
        data.loc[data.index[idx], '斤量_all_R_mean'] = default_weight
        data.loc[data.index[idx], '上り_all_R_mean'] = default_time
        data.loc[data.index[idx], '体重_all_R_mean'] = default_body_weight
        data.loc[data.index[idx], '体重変化_all_R_mean'] = 0.0
        
        # インターバル
        data.loc[data.index[idx], 'interval_days'] = 30
    
    def predict_race(self, race_id: str, get_horse_stats=False):
        """レース予測を実行"""
        try:
            logger.info(f"強化版レース予測開始: {race_id}")
            
            # モデル読み込み
            if not self.model:
                if not self.load_latest_model():
                    raise RuntimeError("モデルの読み込みに失敗しました")
            
            # 出馬表取得（Seleniumまたはrequests）
            if self.use_selenium:
                race_data = self.scrape_shutuba_table_selenium(race_id)
            else:
                race_data = self.scrape_race_card_requests(race_id)
            
            if race_data.empty:
                raise ValueError("出馬表データが取得できませんでした")
            
            logger.info(f"出馬表取得後の馬数: {len(race_data)}頭")
            
            # レース情報を設定 - 実際の結果から距離取得を試行
            actual_course_len = 1600  # デフォルト値
            try:
                # 実際のレース結果から距離情報を取得
                from get_race_result_with_existing_module import RaceResultRetriever
                result_retriever = RaceResultRetriever()
                actual_result_data = result_retriever.fetch_race_result(race_id)
                if actual_result_data and not actual_result_data['race_info'].empty:
                    actual_race_info = actual_result_data['race_info'].iloc[0]
                    actual_course_len = actual_race_info.get('course_len', 1600)
                    logger.info(f"実際のレース結果から距離取得: {actual_course_len}m")
            except Exception as e:
                logger.warning(f"実際の距離取得に失敗、デフォルト使用: {e}")
            
            race_info = {
                'race_id': race_id,
                'course_len': race_data.get('course_len', [actual_course_len]).iloc[0] if 'course_len' in race_data.columns else actual_course_len,
                'race_type': race_data.get('race_type', ['芝']).iloc[0] if 'race_type' in race_data.columns else '芝',
                'ground_state': race_data.get('ground_state', ['良']).iloc[0] if 'ground_state' in race_data.columns else '良',
                'weather': race_data.get('weather', ['晴']).iloc[0] if 'weather' in race_data.columns else '晴',
                'track_direction': race_data.get('track_direction', ['右']).iloc[0] if 'track_direction' in race_data.columns else '右'
            }
            
            # 馬IDを抽出（過去戦績取得用）
            horse_ids = []
            if 'horse_id' in race_data.columns:
                horse_ids = race_data['horse_id'].dropna().tolist()
            else:
                # horse_idがない場合は馬名から仮のIDを生成
                if '馬名' in race_data.columns:
                    horse_ids = [f"horse_{i}_{name}" for i, name in enumerate(race_data['馬名'])]
                else:
                    horse_ids = [f"horse_{i}" for i in range(len(race_data))]
            
            # 実際の過去戦績データを取得
            horse_stats = None
            if horse_ids:
                try:
                    current_date = race_info.get('date', datetime.now().strftime('%Y-%m-%d'))
                    horse_stats = self.get_horse_past_performance(horse_ids, current_date)
                    logger.info(f"過去戦績取得成功: {len(horse_stats)}頭")
                except Exception as e:
                    logger.warning(f"過去戦績取得に失敗: {e}。フォールバックデータを使用")
                    horse_stats = None
            
            # 特徴量準備
            X, processed_data = self.prepare_prediction_features(race_data, race_info, horse_stats)
            if X.empty:
                raise ValueError("特徴量の準備に失敗しました")
            
            logger.info(f"特徴量準備後の馬数: {len(X)}頭")
            
            # 予測実行
            X_scaled = self.scaler.transform(X)
            prediction_proba = self.model.predict(X_scaled)
            
            # 結果整理（ジョッキーIDは学習で使用、騎手名は表示から除外）
            base_cols = ['枠番', '馬番', '馬名', '性齢', '斤量']
            
            # 利用可能なカラムのみを選択
            available_cols = [col for col in base_cols if col in processed_data.columns]
            
            # 馬名カラムが存在しない場合のフォールバック
            if '馬名' not in processed_data.columns:
                processed_data['馬名'] = [f'Horse_{i+1}' for i in range(len(processed_data))]
                if '馬名' not in available_cols:
                    available_cols.insert(2, '馬名')  # 枠番、馬番の後に挿入
            
            results = processed_data[available_cols].copy()
            
            # 拡張情報（脚質）を結果に追加
            if self.enable_corner_features and 'running_style' in processed_data.columns:
                results['running_style'] = processed_data['running_style']
            
            results['予測スコア'] = prediction_proba
            results['予測順位'] = results['予測スコア'].rank(ascending=False, method='first').astype(int)
            
            # 勝率を合計100%になるように正規化
            total_score = prediction_proba.sum()
            if total_score > 0:
                normalized_proba = (prediction_proba / total_score) * 100
                results['勝率'] = normalized_proba.round(1)
            else:
                results['勝率'] = (np.ones(len(prediction_proba)) / len(prediction_proba) * 100).round(1)
            
            # 3着以内確率は従来通り
            results['3着以内確率'] = (prediction_proba * 100).round(1)
            
            # 順位でソート
            results = results.sort_values('予測順位')
            
            logger.info(f"強化版レース予測完了: 最終結果{len(results)}頭")
            
            # 特徴量データを保存（モデル解釈用）
            self.last_X = X
            self.last_feature_names = X.columns.tolist()
            logger.info(f"特徴量データ保存完了: {X.shape}")
            
            return results, race_info
            
        except Exception as e:
            logger.error(f"レース予測エラー: {e}")
            return pd.DataFrame(), {}
        
        finally:
            # Seleniumドライバーのクリーンアップ
            if self.driver:
                try:
                    self.driver.quit()
                    self.driver = None
                except:
                    pass
    
    def display_prediction_results(self, results, race_info):
        """予測結果を見やすく表示"""
        if results.empty:
            print("[エラー] 予測結果がありません")
            return
        
        print("\n" + "="*90)
        print("[競馬AI] 予測結果（強化版・Selenium統合）")
        print("="*90)
        print(f"レースID: {race_info.get('race_id', 'N/A')}")
        print(f"距離: {race_info.get('course_len', 'N/A')}m")
        print(f"コース: {race_info.get('race_type', 'N/A')} {race_info.get('track_direction', 'N/A')}回り")
        print(f"馬場: {race_info.get('ground_state', 'N/A')} / 天気: {race_info.get('weather', 'N/A')}")
        print(f"予測時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"取得方式: {'Selenium' if self.use_selenium else 'Requests/BeautifulSoup'}")
        # 勝率の合計を確認
        total_win_rate = results['勝率'].sum() if '勝率' in results.columns else 0
        
        print(f"\n[予測結果] (予測順位順) - 出馬頭数: {len(results)}頭")
        print(f"勝率合計: {total_win_rate:.1f}% (正規化済み)")
        print("-" * 90)
        
        # 全頭の表示
        logger.info(f"全{len(results)}頭の予測結果を表示します")
        
        for i, (_, row) in enumerate(results.iterrows()):
            # デバッグ: 最初の行のデータ構造を確認
            if i == 0:
                logger.info(f"表示データの構造: {dict(row)}")
                logger.info(f"利用可能なカラム: {list(row.keys())}")
            
            # 無効なデータをスキップ
            枠番 = row.get('枠番', 0)
            馬番 = row.get('馬番', 0)
            horse_name = row.get('馬名', 'N/A')
            
            # 無効な行をスキップ
            if (枠番 == 0 or 馬番 == 0 or 
                horse_name in ['N/A', '', '0', '--'] or 
                pd.isna(horse_name)):
                continue
            
            # 騎手情報は除外（学習・予想で使用しない）
            jockey_info = ""
            
            if pd.isna(horse_name):
                horse_name = 'N/A'
            
            性齢 = row.get('性齢', 'N/A')
            if pd.isna(性齢):
                性齢 = 'N/A'
            斤量 = row.get('斤量', 0)
            予測順位 = row.get('予測順位', 0)
            勝率 = row.get('勝率', 0)
            確率 = row.get('3着以内確率', 0)
            
            # 安全な文字列フォーマット
            try:
                position_str = f"{予測順位:2.0f}位"
                frame_horse_str = f"{枠番:2.0f}-{馬番:2.0f}"
                horse_name_str = f"{str(horse_name):12s}"
                age_sex_str = f"{str(性齢):4s}"
                weight_str = f"{斤量:4.1f}kg"
                win_rate_str = f"勝率:{勝率:5.1f}%"
                prob_str = f"3着内:{確率:5.1f}%"
                
                print(f"{position_str} {frame_horse_str} {horse_name_str} "
                      f"{age_sex_str} {weight_str} "
                      f"{win_rate_str} {prob_str}")
            except Exception as format_error:
                logger.warning(f"フォーマットエラー: {format_error}")
                # フォールバック表示
                print(f"{int(予測順位)}位 {int(枠番)}-{int(馬番)} {str(horse_name)} "
                      f"{str(性齢)} {float(斤量):.1f}kg "
                      f"勝率:{float(勝率):.1f}% 3着内:{float(確率):.1f}%")
        
        print("\n[買い目候補]")
        print("-" * 50)
        
        if len(results) >= 3:
            top3 = results.head(3)
            print("[3連複候補]")
            try:
                print(f"   {top3.iloc[0].get('枠番', 0):.0f}-{top3.iloc[0].get('馬番', 0):.0f}-"
                      f"{top3.iloc[1].get('枠番', 0):.0f}-{top3.iloc[1].get('馬番', 0):.0f}-"
                      f"{top3.iloc[2].get('枠番', 0):.0f}-{top3.iloc[2].get('馬番', 0):.0f}")
            except Exception as e:
                logger.warning(f"3連複候補の表示でエラー: {e}")
                print("   データが不完全です")
        
        if len(results) >= 1:
            winner_candidate = results.iloc[0]
            try:
                horse_name = winner_candidate.get('馬名', 'N/A')
                if pd.isna(horse_name):
                    horse_name = 'N/A'
                print(f"\n[単勝候補] {winner_candidate.get('枠番', 0):.0f}-{winner_candidate.get('馬番', 0):.0f} {horse_name}")
                print(f"   勝率: {winner_candidate.get('勝率', 0):.1f}%")
                print(f"   3着以内確率: {winner_candidate.get('3着以内確率', 0):.1f}%")
            except Exception as e:
                logger.warning(f"単勝候補の表示でエラー: {e}")
                print("\n[単勝候補] データが不完全です")
        
        print("\n[注意事項]")
        print("-" * 50)
        print("・この予測システムは強化版です（Selenium統合 + 実際の過去戦績）")
        print("・より詳細で信頼性の高いデータを取得しています")
        print("・実際の過去戦績データを活用した高精度予測")
        print("・投資は自己責任で行ってください")
        print("・予測はあくまで参考情報として活用してください")
        
        print("\n" + "="*90)

def main():
    """メイン実行関数"""
    try:
        print("[競馬AI] 予測システム（強化版・Selenium統合）")
        print("参照ファイルを基にSeleniumを統合し、より詳細で信頼性の高い出馬表取得を実現")
        
        # SSL設定の確認
        import ssl
        logger.info(f"SSLバージョン: {ssl.OPENSSL_VERSION}")
        if not os.environ.get('REQUESTS_CA_BUNDLE'):
            logger.info("SSL証明書検証は無効化されています（開発環境）")
        
        predictor = EnhancedLiveRacePredictor(use_selenium=True)
        
        try:
            race_id = input("\nレースIDを入力してください (例: 202412080101): ").strip()
        except EOFError:
            print("非対話モードでテスト実行中...")
            race_id = "202406080101"  # テスト用レースID
        
        if not race_id:
            print("レースIDが入力されませんでした。デモを実行します。")
            race_id = "202406080101"  # テスト用レースID
        
        print(f"\n強化版レース予測を実行中: {race_id}")
        
        # 予測実行
        results, race_info = predictor.predict_race(race_id)
        
        if not results.empty:
            predictor.display_prediction_results(results, race_info)
        else:
            print("[エラー] 予測に失敗しました")
            print("・レースIDが存在しない可能性があります")
            print("・ネットワーク接続を確認してください")
            print("・一時的にアクセスが制限されている可能性があります")
        
    except KeyboardInterrupt:
        print("\n\n処理が中断されました")
    except Exception as e:
        logger.error(f"メイン実行エラー: {e}")
        print(f"[エラー] エラーが発生しました: {e}")

if __name__ == "__main__":
    main()