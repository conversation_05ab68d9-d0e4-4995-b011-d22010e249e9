#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
レース当日の日付と馬の生年月日から生後日数を計算するシンプルなデモ
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# プロジェクトパスの追加
sys.path.append('.')

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from core.features.calculators import FeatureCalculators

def create_sample_data():
    """サンプルデータの作成"""
    logger.info("レース当日と生年月日による生後日数計算デモ")
    
    # サンプルデータの作成
    sample_data = pd.DataFrame({
        'horse_id': ['ディープインパクト', 'オルフェーヴル', 'ダイワスカーレット', 'ウオッカ', 'ジェンティルドンナ'],
        'race_date': [  # レース当日の日付
            '2023-10-29',  # 天皇賞（秋）
            '2023-11-26',  # ジャパンカップ
            '2023-12-24',  # 有馬記念
            '2024-01-21',  # 京都記念
            '2024-02-18'   # フェブラリーS
        ],
        'birthday': [  # 馬の生年月日
            '2020-03-15',   # 3歳馬
            '2019-04-22',   # 4歳馬
            '2018-05-10',   # 5歳馬
            '2017-02-28',   # 6歳馬
            '2016-06-05'    # 7歳馬
        ]
    })
    
    logger.info(f"サンプルデータ: {len(sample_data)}頭")
    print("\n対象馬とレース情報:")
    print(sample_data.to_string(index=False))
    
    return sample_data

def calculate_days_old_basic(data):
    """基本的な生後日数計算"""
    logger.info("基本的な生後日数計算...")
    
    result_data = data.copy()
    
    # 日付文字列をdatetimeに変換
    result_data['race_date_dt'] = pd.to_datetime(result_data['race_date'])
    result_data['birthday_dt'] = pd.to_datetime(result_data['birthday'])
    
    # 生後日数を計算
    result_data['days_old'] = (result_data['race_date_dt'] - result_data['birthday_dt']).dt.days
    
    # 年齢も計算
    result_data['age_years'] = result_data['days_old'] / 365.25
    
    print("\n基本計算結果:")
    display_cols = ['horse_id', 'race_date', 'birthday', 'days_old', 'age_years']
    print(result_data[display_cols].round(2).to_string(index=False))
    
    return result_data

def calculate_with_feature_calculator(data):
    """FeatureCalculatorを使った計算"""
    logger.info("FeatureCalculatorを使った生後日数計算...")
    
    calc = FeatureCalculators()
    result_data = data.copy()
    
    # 日付をdatetimeに変換
    result_data['race_date_dt'] = pd.to_datetime(result_data['race_date'])
    result_data['birthday_dt'] = pd.to_datetime(result_data['birthday'])
    
    # FeatureCalculatorで生後日数を計算
    result_data['days_old_calc'] = calc.calculate_days_old(
        result_data, 
        date_column='race_date_dt', 
        birthday_column='birthday_dt'
    )
    
    # FeatureCalculatorで年齢を計算
    result_data['age_years_calc'] = calc.calculate_age_years(
        result_data,
        date_column='race_date_dt',
        birthday_column='birthday_dt'
    )
    
    # FeatureCalculatorで月齢を計算
    result_data['age_months_calc'] = calc.calculate_age_months(
        result_data,
        date_column='race_date_dt',
        birthday_column='birthday_dt'
    )
    
    print("\nFeatureCalculator計算結果:")
    display_cols = ['horse_id', 'race_date', 'days_old_calc', 'age_years_calc', 'age_months_calc']
    print(result_data[display_cols].round(2).to_string(index=False))
    
    return result_data

def calculate_age_categories(data):
    """年齢カテゴリの計算"""
    logger.info("年齢カテゴリの計算...")
    
    calc = FeatureCalculators()
    result_data = data.copy()
    
    # 年齢カテゴリを計算
    result_data['is_young'] = calc.calculate_age_category_young(
        result_data, 'race_date_dt', 'birthday_dt', threshold=3.0
    )
    
    result_data['is_prime'] = calc.calculate_age_category_prime(
        result_data, 'race_date_dt', 'birthday_dt', min_threshold=3.0, max_threshold=6.0
    )
    
    result_data['is_veteran'] = calc.calculate_age_category_veteran(
        result_data, 'race_date_dt', 'birthday_dt', threshold=6.0
    )
    
    print("\n年齢カテゴリ分類:")
    category_cols = ['horse_id', 'age_years_calc', 'is_young', 'is_prime', 'is_veteran']
    category_data = result_data[category_cols].copy()
    
    # カテゴリ名を追加
    def get_age_category(row):
        if row['is_young']:
            return '若駒(3歳以下)'
        elif row['is_prime']:
            return '盛期(4-6歳)'
        elif row['is_veteran']:
            return 'ベテラン(7歳以上)'
        else:
            return 'その他'
    
    category_data['age_category'] = category_data.apply(get_age_category, axis=1)
    
    print(category_data[['horse_id', 'age_years_calc', 'age_category']].round(2).to_string(index=False))
    
    return result_data

def practical_example():
    """実用的な例: 特定のレース日での計算"""
    logger.info("実用的な例: 2024年ダービーでの計算...")
    
    # 2024年日本ダービー（5月26日）のサンプル
    derby_data = pd.DataFrame({
        'horse_id': ['イクイノックス', 'ドウデュース', 'アスクビクターモア', 'ダノンベルーガ', 'ジオグリフ'],
        'race_date': ['2024-05-26'] * 5,  # 全頭同じレース日
        'birthday': [
            '2021-04-15',  # 3歳馬
            '2021-03-20',  # 3歳馬
            '2021-05-10',  # 3歳馬
            '2021-02-28',  # 3歳馬
            '2021-04-08'   # 3歳馬
        ]
    })
    
    print("\n2024年ダービー出走馬サンプル:")
    print(derby_data.to_string(index=False))
    
    # 生後日数計算
    calc = FeatureCalculators()
    derby_data['race_date_dt'] = pd.to_datetime(derby_data['race_date'])
    derby_data['birthday_dt'] = pd.to_datetime(derby_data['birthday'])
    
    derby_data['days_old'] = calc.calculate_days_old(
        derby_data, 'race_date_dt', 'birthday_dt'
    )
    
    derby_data['age_years'] = calc.calculate_age_years(
        derby_data, 'race_date_dt', 'birthday_dt'
    )
    
    print("\nダービー当日の生後日数:")
    derby_display = ['horse_id', 'birthday', 'days_old', 'age_years']
    print(derby_data[derby_display].round(2).to_string(index=False))
    
    # 統計
    print(f"\n生後日数統計:")
    print(f"  平均: {derby_data['days_old'].mean():.1f}日")
    print(f"  最小: {derby_data['days_old'].min():.0f}日")
    print(f"  最大: {derby_data['days_old'].max():.0f}日")
    print(f"  範囲: {derby_data['days_old'].max() - derby_data['days_old'].min():.0f}日")
    
    return derby_data

def main():
    """メイン実行関数"""
    logger.info("生後日数計算デモ開始")
    
    # 1. サンプルデータの作成
    sample_data = create_sample_data()
    
    # 2. 基本計算
    basic_result = calculate_days_old_basic(sample_data)
    
    # 3. FeatureCalculatorによる計算
    calc_result = calculate_with_feature_calculator(sample_data)
    
    # 4. 年齢カテゴリの計算
    category_result = calculate_age_categories(calc_result)
    
    # 5. 実用的な例
    practical_example()
    
    # 6. 計算方法の検証
    logger.info("計算方法の検証:")
    if 'days_old' in basic_result.columns and 'days_old_calc' in calc_result.columns:
        basic_days = basic_result['days_old'].values
        calc_days = calc_result['days_old_calc'].values
        
        differences = np.abs(basic_days - calc_days)
        max_diff = np.max(differences)
        
        if max_diff == 0:
            print("OK: 基本計算とFeatureCalculatorの結果が完全に一致")
        else:
            print(f"WARNING: 最大差異: {max_diff}日")
    
    logger.info("生後日数計算デモ完了")

if __name__ == "__main__":
    main()