#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
競馬AI予測システム - コアモジュール

データ処理、スクレイピング、特徴量エンジニアリングの中核機能を提供します。

サブモジュール:
- processors: データ処理モジュール
- scrapers: スクレイピングモジュール  
- features: 特徴量エンジニアリング
- utils: ユーティリティ
"""

# 主要クラスのインポート
from core.processors.race_processor import RaceProcessor
from core.processors.horse_processor import HorseProcessor
from core.processors.comprehensive_integrator import ComprehensiveDataIntegrator

from core.scrapers.scraper import (
    scrape_html_race,
    scrape_html_horse,
    scrape_html_ped
)

from core.features.manager import FeatureEngineeringManager
from core.features.definitions import (
    FeatureDefinition,
    FeatureCategory,
    FeatureType,
    FeatureImportance
)

from core.utils.constants import LocalPaths, UrlPaths

__all__ = [
    # Processors
    'RaceProcessor',
    'HorseProcessor', 
    'DataMerger',
    'ComprehensiveDataIntegrator',
    
    # Scrapers
    'scrape_html_race',
    'scrape_html_horse',
    'scrape_html_ped',
    
    # Features
    'FeatureEngineeringManager',
    'FeatureDefinition',
    'FeatureCategory',
    'FeatureType',
    'FeatureImportance',
    
    # Utils
    'LocalPaths',
    'UrlPaths'
]
