#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TensorFlow Ranking + Optuna 競馬予想システム
ランキング学習とハイパーパラメータ最適化を組み合わせた高精度予想システム
"""

import sys
sys.path.append('.')

import pandas as pd
import numpy as np
import tensorflow as tf
import tensorflow_ranking as tfr
import optuna
from sklearn.model_selection import train_test_split, GroupKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import ndcg_score, roc_auc_score
from sklearn.datasets import make_classification
import pickle
import logging
import warnings
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import json

# 警告を抑制
warnings.filterwarnings('ignore')
tf.compat.v1.logging.set_verbosity(tf.compat.v1.logging.ERROR)

class TensorFlowRankingOptunaSystem:
    """TensorFlow Ranking + Optunaシステム"""
    
    def __init__(self, 
                 n_trials: int = 20,
                 random_seed: int = 42,
                 max_epochs: int = 50):
        """
        初期化
        
        Parameters
        ----------
        n_trials : int
            Optuna最適化試行回数
        random_seed : int
            乱数シード
        max_epochs : int
            最大エポック数
        """
        self.n_trials = n_trials
        self.random_seed = random_seed
        self.max_epochs = max_epochs
        self.output_dir = Path("tfr_optuna_results")
        self.output_dir.mkdir(exist_ok=True)
        
        # ログ設定
        self.logger = self._setup_logging()
        
        # GPU設定
        self._setup_gpu()
        
        # データ
        self.X_train = None
        self.y_train = None
        self.groups_train = None
        self.X_test = None
        self.y_test = None
        self.groups_test = None
        self.feature_names = None
        
        # 最適化結果
        self.best_params = None
        self.best_score = None
        self.best_model = None
        self.optimization_history = []
        
    def _setup_logging(self) -> logging.Logger:
        """ログ設定"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _setup_gpu(self):
        """GPU設定の最適化"""
        try:
            # GPU使用量の制限
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                self.logger.info(f"GPU設定完了: {len(gpus)}個のGPU")
            else:
                self.logger.info("GPU未検出 - CPUを使用")
        except Exception as e:
            self.logger.warning(f"GPU設定エラー: {e}")
    
    def generate_horse_racing_ranking_data(self) -> bool:
        """競馬風ランキングデータの生成"""
        
        try:
            self.logger.info("競馬風ランキングデータを生成中...")
            
            # 基本パラメータ
            n_races = 500  # レース数
            horses_per_race = 18  # 1レースあたりの馬数
            n_features = 15  # 特徴量数
            
            np.random.seed(self.random_seed)
            
            # 特徴量名
            self.feature_names = [
                '枠番', '馬番', '斤量', 'course_len', '着順_last_5R_mean',
                '人気_last_5R_mean', 'オッズ_last_5R_mean', '上り_last_5R_mean',
                '体重_last_5R_mean', 'interval_days', 'race_class',
                'ground_state', 'weather', 'jockey_skill', 'trainer_skill'
            ]
            
            all_features = []
            all_labels = []
            all_groups = []
            
            for race_id in range(n_races):
                # レースごとのデータ生成
                race_features = np.random.randn(horses_per_race, n_features)
                
                # 競馬特有の特徴量調整
                race_features[:, 0] = np.random.randint(1, 9, horses_per_race)  # 枠番
                race_features[:, 1] = np.arange(1, horses_per_race + 1)  # 馬番
                race_features[:, 2] = np.random.uniform(52, 58, horses_per_race)  # 斤量
                race_features[:, 3] = np.random.choice([1200, 1400, 1600, 1800, 2000, 2400])  # 距離
                
                # 着順、人気、オッズの調整
                for i in range(4, 8):
                    if '着順' in self.feature_names[i]:
                        race_features[:, i] = np.random.uniform(1, 18, horses_per_race)
                    elif '人気' in self.feature_names[i]:
                        race_features[:, i] = np.random.uniform(1, 18, horses_per_race)
                    elif 'オッズ' in self.feature_names[i]:
                        race_features[:, i] = np.random.lognormal(1, 1, horses_per_race)
                
                # ランキングラベル生成（強い馬ほど高スコア）
                # 人気や過去成績を基にした確率的スコア
                base_scores = np.zeros(horses_per_race)
                for i in range(horses_per_race):
                    # 人気度による影響（人気が高いほど強い）
                    popularity_effect = 1.0 / (race_features[i, 5] + 1)  # 人気
                    past_rank_effect = 1.0 / (race_features[i, 4] + 1)  # 過去着順
                    random_factor = np.random.normal(0, 0.2)
                    
                    base_scores[i] = popularity_effect + past_rank_effect + random_factor
                
                # スコアを0-1に正規化
                base_scores = (base_scores - base_scores.min()) / (base_scores.max() - base_scores.min() + 1e-8)
                
                # 実際の着順を生成（スコアが高いほど上位）
                ranking_labels = np.argsort(-base_scores) + 1  # 1位から18位
                relevance_scores = 1.0 / ranking_labels  # 着順の逆数（1位=1.0, 2位=0.5, ...）
                
                all_features.append(race_features)
                all_labels.append(relevance_scores)
                all_groups.append([race_id] * horses_per_race)
            
            # データの結合
            X = np.vstack(all_features)
            y = np.concatenate(all_labels)
            groups = np.concatenate(all_groups)
            
            # 訓練・テスト分割（レース単位で分割）
            unique_groups = np.unique(groups)
            train_groups, test_groups = train_test_split(
                unique_groups, test_size=0.2, random_state=self.random_seed
            )
            
            train_mask = np.isin(groups, train_groups)
            test_mask = np.isin(groups, test_groups)
            
            self.X_train = X[train_mask]
            self.y_train = y[train_mask]
            self.groups_train = groups[train_mask]
            
            self.X_test = X[test_mask]
            self.y_test = y[test_mask]
            self.groups_test = groups[test_mask]
            
            self.logger.info(f"ランキングデータ生成完了:")
            self.logger.info(f"  訓練データ: {self.X_train.shape}, {len(np.unique(self.groups_train))}レース")
            self.logger.info(f"  テストデータ: {self.X_test.shape}, {len(np.unique(self.groups_test))}レース")
            self.logger.info(f"  特徴量数: {len(self.feature_names)}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"データ生成エラー: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_tf_ranking_model(self, params: Dict[str, Any]) -> tf.keras.Model:
        """TensorFlow Rankingモデルの作成"""
        
        # 入力層
        input_layer = tf.keras.Input(shape=(len(self.feature_names),), name='features')
        
        # 隠れ層
        x = input_layer
        for i in range(params['n_hidden_layers']):
            x = tf.keras.layers.Dense(
                params[f'hidden_size_{i}'],
                activation=params['activation'],
                kernel_regularizer=tf.keras.regularizers.l2(params['l2_reg'])
            )(x)
            
            if params['use_dropout']:
                x = tf.keras.layers.Dropout(params['dropout_rate'])(x)
            
            if params['use_batch_norm']:
                x = tf.keras.layers.BatchNormalization()(x)
        
        # 出力層（ランキングスコア）
        output = tf.keras.layers.Dense(1, activation='linear', name='ranking_score')(x)
        
        model = tf.keras.Model(inputs=input_layer, outputs=output)
        
        # オプティマイザー
        if params['optimizer'] == 'adam':
            optimizer = tf.keras.optimizers.Adam(learning_rate=params['learning_rate'])
        elif params['optimizer'] == 'sgd':
            optimizer = tf.keras.optimizers.SGD(learning_rate=params['learning_rate'])
        else:
            optimizer = tf.keras.optimizers.RMSprop(learning_rate=params['learning_rate'])
        
        # TensorFlow Rankingの損失関数
        loss_fn = tfr.keras.losses.get(
            params['ranking_loss'],
            reduction=tf.keras.losses.Reduction.AUTO
        )
        
        # メトリクス
        metrics = [
            tfr.keras.metrics.get('ndcg', topk=5),
            tfr.keras.metrics.get('ndcg', topk=10),
            tfr.keras.metrics.get('mrr')
        ]
        
        model.compile(
            optimizer=optimizer,
            loss=loss_fn,
            metrics=metrics
        )
        
        return model
    
    def prepare_tf_ranking_data(self, X: np.ndarray, y: np.ndarray, groups: np.ndarray) -> tf.data.Dataset:
        """TensorFlow Ranking用のデータ準備"""
        
        # グループごとにデータを整理
        unique_groups = np.unique(groups)
        group_features = []
        group_labels = []
        
        for group in unique_groups:
            group_mask = groups == group
            group_X = X[group_mask]
            group_y = y[group_mask]
            
            # パディング（固定長にする）
            max_size = 18  # 最大馬数
            current_size = len(group_X)
            
            if current_size < max_size:
                # パディング
                pad_size = max_size - current_size
                pad_features = np.zeros((pad_size, group_X.shape[1]))
                pad_labels = np.zeros(pad_size)
                
                group_X = np.vstack([group_X, pad_features])
                group_y = np.concatenate([group_y, pad_labels])
            elif current_size > max_size:
                # 切り詰め
                group_X = group_X[:max_size]
                group_y = group_y[:max_size]
            
            group_features.append(group_X)
            group_labels.append(group_y)
        
        # テンソルに変換
        features_tensor = tf.constant(np.array(group_features), dtype=tf.float32)
        labels_tensor = tf.constant(np.array(group_labels), dtype=tf.float32)
        
        # データセット作成
        dataset = tf.data.Dataset.from_tensor_slices((features_tensor, labels_tensor))
        return dataset
    
    def objective(self, trial) -> float:
        """Optuna最適化の目的関数"""
        
        try:
            # ハイパーパラメータの提案
            params = {
                'n_hidden_layers': trial.suggest_int('n_hidden_layers', 1, 4),
                'activation': trial.suggest_categorical('activation', ['relu', 'tanh', 'elu']),
                'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-1, log=True),
                'l2_reg': trial.suggest_float('l2_reg', 1e-8, 1e-2, log=True),
                'use_dropout': trial.suggest_categorical('use_dropout', [True, False]),
                'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5) if trial.suggest_categorical('use_dropout_temp', [True, False]) else 0.2,
                'use_batch_norm': trial.suggest_categorical('use_batch_norm', [True, False]),
                'optimizer': trial.suggest_categorical('optimizer', ['adam', 'sgd', 'rmsprop']),
                'ranking_loss': trial.suggest_categorical('ranking_loss', [
                    'pairwise_hinge_loss',
                    'pairwise_logistic_loss', 
                    'pairwise_soft_zero_one_loss'
                ]),
                'batch_size': trial.suggest_categorical('batch_size', [8, 16, 32])
            }
            
            # 隠れ層のサイズを動的に決定
            for i in range(params['n_hidden_layers']):
                params[f'hidden_size_{i}'] = trial.suggest_categorical(f'hidden_size_{i}', [32, 64, 128, 256])
            
            # クロスバリデーション
            group_kfold = GroupKFold(n_splits=3)
            cv_scores = []
            
            for fold, (train_idx, val_idx) in enumerate(group_kfold.split(self.X_train, self.y_train, self.groups_train)):
                try:
                    # 分割
                    X_fold_train = self.X_train[train_idx]
                    y_fold_train = self.y_train[train_idx]
                    groups_fold_train = self.groups_train[train_idx]
                    
                    X_fold_val = self.X_train[val_idx]
                    y_fold_val = self.y_train[val_idx]
                    groups_fold_val = self.groups_train[val_idx]
                    
                    # データ準備
                    train_dataset = self.prepare_tf_ranking_data(X_fold_train, y_fold_train, groups_fold_train)
                    val_dataset = self.prepare_tf_ranking_data(X_fold_val, y_fold_val, groups_fold_val)
                    
                    train_dataset = train_dataset.batch(params['batch_size']).prefetch(tf.data.AUTOTUNE)
                    val_dataset = val_dataset.batch(params['batch_size']).prefetch(tf.data.AUTOTUNE)
                    
                    # モデル作成
                    model = self.create_tf_ranking_model(params)
                    
                    # 早期終了
                    early_stopping = tf.keras.callbacks.EarlyStopping(
                        monitor='val_loss',
                        patience=5,
                        restore_best_weights=True
                    )
                    
                    # 訓練
                    history = model.fit(
                        train_dataset,
                        epochs=20,  # 高速化のため短縮
                        validation_data=val_dataset,
                        callbacks=[early_stopping],
                        verbose=0
                    )
                    
                    # 評価（NDCG@5を使用）
                    val_results = model.evaluate(val_dataset, verbose=0)
                    ndcg_score = val_results[1]  # NDCG@5
                    
                    cv_scores.append(ndcg_score)
                    
                    # メモリクリア
                    del model
                    tf.keras.backend.clear_session()
                    
                except Exception as fold_e:
                    self.logger.warning(f"Fold {fold} エラー: {fold_e}")
                    cv_scores.append(0.0)
            
            # 平均スコア
            mean_score = np.mean(cv_scores)
            
            # 履歴記録
            self.optimization_history.append({
                'trial_number': trial.number,
                'params': params,
                'cv_score': mean_score,
                'cv_std': np.std(cv_scores)
            })
            
            return mean_score
            
        except Exception as e:
            self.logger.error(f"目的関数エラー: {e}")
            return 0.0
    
    def optimize_hyperparameters(self) -> bool:
        """ハイパーパラメータ最適化の実行"""
        
        try:
            self.logger.info(f"TensorFlow Ranking Optuna最適化開始: {self.n_trials}回試行")
            
            # Optunaスタディ作成
            study = optuna.create_study(
                direction='maximize',
                sampler=optuna.samplers.TPESampler(seed=self.random_seed),
                pruner=optuna.pruners.MedianPruner(n_startup_trials=3)
            )
            
            # 最適化実行
            study.optimize(
                self.objective,
                n_trials=self.n_trials,
                timeout=1200,  # 20分でタイムアウト
                show_progress_bar=True
            )
            
            # 最適化結果の保存
            self.best_params = study.best_params
            self.best_score = study.best_value
            
            self.logger.info(f"最適化完了:")
            self.logger.info(f"  最高NDCG@5: {self.best_score:.4f}")
            self.logger.info(f"  最適パラメータ: {self.best_params}")
            
            # 結果保存
            self._save_optimization_results(study)
            
            return True
            
        except Exception as e:
            self.logger.error(f"最適化エラー: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def train_final_model(self) -> bool:
        """最適パラメータで最終モデル訓練"""
        
        try:
            if self.best_params is None:
                self.logger.error("最適パラメータが見つかりません")
                return False
            
            self.logger.info("最適パラメータで最終モデルを訓練中...")
            
            # 隠れ層サイズの補完
            for i in range(self.best_params['n_hidden_layers']):
                if f'hidden_size_{i}' not in self.best_params:
                    self.best_params[f'hidden_size_{i}'] = 128
            
            # データ準備
            train_dataset = self.prepare_tf_ranking_data(self.X_train, self.y_train, self.groups_train)
            test_dataset = self.prepare_tf_ranking_data(self.X_test, self.y_test, self.groups_test)
            
            train_dataset = train_dataset.batch(self.best_params['batch_size']).prefetch(tf.data.AUTOTUNE)
            test_dataset = test_dataset.batch(self.best_params['batch_size']).prefetch(tf.data.AUTOTUNE)
            
            # 最終モデル作成
            self.best_model = self.create_tf_ranking_model(self.best_params)
            
            # コールバック
            callbacks = [
                tf.keras.callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    restore_best_weights=True
                ),
                tf.keras.callbacks.ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=5
                )
            ]
            
            # 訓練
            history = self.best_model.fit(
                train_dataset,
                epochs=self.max_epochs,
                validation_data=test_dataset,
                callbacks=callbacks,
                verbose=1
            )
            
            # テスト評価
            test_results = self.best_model.evaluate(test_dataset, verbose=0)
            test_ndcg5 = test_results[1]
            test_ndcg10 = test_results[2]
            test_mrr = test_results[3]
            
            self.logger.info(f"最終モデル性能:")
            self.logger.info(f"  テストNDCG@5: {test_ndcg5:.4f}")
            self.logger.info(f"  テストNDCG@10: {test_ndcg10:.4f}")
            self.logger.info(f"  テストMRR: {test_mrr:.4f}")
            
            # モデル保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            model_file = self.output_dir / f"tfr_optuna_model_{timestamp}"
            
            self.best_model.save(model_file)
            
            # パラメータとメタデータ保存
            metadata = {
                'best_params': self.best_params,
                'best_cv_score': self.best_score,
                'test_ndcg5': float(test_ndcg5),
                'test_ndcg10': float(test_ndcg10),
                'test_mrr': float(test_mrr),
                'feature_names': self.feature_names,
                'model_path': str(model_file)
            }
            
            metadata_file = self.output_dir / f"tfr_optuna_metadata_{timestamp}.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"最終モデル保存完了:")
            self.logger.info(f"  モデル: {model_file}")
            self.logger.info(f"  メタデータ: {metadata_file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"最終モデル訓練エラー: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _save_optimization_results(self, study):
        """最適化結果の保存"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 最適パラメータの保存
        best_params_file = self.output_dir / f"tfr_best_params_{timestamp}.json"
        with open(best_params_file, 'w', encoding='utf-8') as f:
            json.dump({
                'best_params': self.best_params,
                'best_score': self.best_score,
                'n_trials': len(study.trials)
            }, f, indent=2, ensure_ascii=False)
        
        # 最適化履歴の保存
        history_file = self.output_dir / f"tfr_optimization_history_{timestamp}.json"
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(self.optimization_history, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"最適化結果保存完了:")
        self.logger.info(f"  最適パラメータ: {best_params_file}")
        self.logger.info(f"  最適化履歴: {history_file}")
    
    def generate_report(self) -> str:
        """総合レポート生成"""
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("TensorFlow Ranking + Optuna 競馬予想システム レポート")
        report_lines.append("=" * 80)
        
        # 基本情報
        report_lines.append(f"\n📊 システム概要:")
        report_lines.append(f"  アルゴリズム: TensorFlow Ranking")
        report_lines.append(f"  最適化手法: Optuna")
        report_lines.append(f"  試行回数: {len(self.optimization_history)}")
        report_lines.append(f"  特徴量数: {len(self.feature_names) if self.feature_names else 'N/A'}")
        
        # データ情報
        if self.X_train is not None:
            report_lines.append(f"\n📈 データ情報:")
            report_lines.append(f"  訓練レース数: {len(np.unique(self.groups_train))}")
            report_lines.append(f"  テストレース数: {len(np.unique(self.groups_test))}")
            report_lines.append(f"  総馬数: {len(self.X_train) + len(self.X_test)}")
        
        # 最適化結果
        if self.best_score is not None:
            report_lines.append(f"\n🎯 最適化結果:")
            report_lines.append(f"  最高NDCG@5: {self.best_score:.4f}")
            
            if self.best_params:
                report_lines.append(f"  最適パラメータ:")
                key_params = ['n_hidden_layers', 'learning_rate', 'ranking_loss', 'optimizer']
                for param in key_params:
                    if param in self.best_params:
                        value = self.best_params[param]
                        if isinstance(value, float):
                            report_lines.append(f"    {param}: {value:.4f}")
                        else:
                            report_lines.append(f"    {param}: {value}")
        
        # 最適化推移
        scores = [h['cv_score'] for h in self.optimization_history]
        if scores:
            report_lines.append(f"\n📈 最適化推移:")
            report_lines.append(f"  初期スコア: {scores[0]:.4f}")
            report_lines.append(f"  最終スコア: {scores[-1]:.4f}")
            report_lines.append(f"  改善幅: {max(scores) - scores[0]:+.4f}")
            report_lines.append(f"  平均スコア: {np.mean(scores):.4f}")
        
        # 上位試行
        sorted_history = sorted(self.optimization_history, 
                              key=lambda x: x['cv_score'], reverse=True)
        
        if sorted_history:
            report_lines.append(f"\n🏆 上位3試行:")
            report_lines.append(f"{'順位':>4} {'試行':>6} {'NDCG@5':>8} {'学習率':>10} {'損失関数'}")
            report_lines.append("-" * 60)
            
            for i, trial in enumerate(sorted_history[:3]):
                rank = i + 1
                trial_num = trial['trial_number']
                score = trial['cv_score']
                lr = trial['params'].get('learning_rate', 0)
                loss = trial['params'].get('ranking_loss', 'N/A')
                
                report_lines.append(f"{rank:>4} {trial_num:>6} {score:>8.4f} {lr:>10.6f} {loss}")
        
        # TensorFlow Rankingの特徴
        report_lines.append(f"\n🔧 TensorFlow Ranking 特徴:")
        report_lines.append(f"  • ランキング学習に特化")
        report_lines.append(f"  • NDCG, MRR, MAP等の指標")
        report_lines.append(f"  • ペアワイズ・リストワイズ損失")
        report_lines.append(f"  • GPU加速対応")
        
        # 競馬予想への応用
        report_lines.append(f"\n🏇 競馬予想への応用:")
        report_lines.append(f"  • 馬の相対的順位予測")
        report_lines.append(f"  • レース全体の着順分布")
        report_lines.append(f"  • 複数の評価指標で性能測定")
        report_lines.append(f"  • 実際のレースに近い学習方式")
        
        report_lines.append(f"\n" + "=" * 80)
        report_lines.append("TensorFlow Ranking + Optuna システム完了")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)

def main():
    """メイン実行関数"""
    
    print("=" * 80)
    print("TensorFlow Ranking + Optuna 競馬予想システム")
    print("=" * 80)
    
    # システム設定
    system = TensorFlowRankingOptunaSystem(
        n_trials=10,  # デバッグのため少なく設定
        random_seed=42,
        max_epochs=30
    )
    
    try:
        # 1. データ生成
        print("\n📊 競馬風ランキングデータ生成中...")
        if not system.generate_horse_racing_ranking_data():
            print("❌ データ生成に失敗しました。")
            return
        
        print("✅ データ生成完了")
        print(f"  訓練レース数: {len(np.unique(system.groups_train))}")
        print(f"  テストレース数: {len(np.unique(system.groups_test))}")
        
        # 2. ハイパーパラメータ最適化
        print("\n🔍 ハイパーパラメータ最適化中...")
        if not system.optimize_hyperparameters():
            print("❌ 最適化に失敗しました。")
            return
        
        print("✅ 最適化完了")
        print(f"  最高NDCG@5: {system.best_score:.4f}")
        
        # 3. 最終モデル訓練
        print("\n🎯 最終モデル訓練中...")
        if not system.train_final_model():
            print("❌ 最終モデル訓練に失敗しました。")
            return
        
        print("✅ 最終モデル訓練完了")
        
        # 4. レポート生成
        print("\n📄 総合レポート生成中...")
        report = system.generate_report()
        
        # レポート表示
        print("\n" + report)
        
        # レポート保存
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = system.output_dir / f"tfr_optuna_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📁 レポート保存: {report_file}")
        print("\n🎉 TensorFlow Ranking + Optuna システム完了！")
        
    except KeyboardInterrupt:
        print("\n⚠️ ユーザーによって中断されました。")
    except Exception as e:
        print(f"\n❌ エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()