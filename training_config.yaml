# 競馬AI学習設定ファイル
training_years:
  - "2020"

# データ分割設定
test_size: 0.2
random_state: 42

# 出力設定
output_dir: "models"

# LightGBM パラメータ
lgb_params:
  objective: "binary"
  metric: "binary_logloss"
  boosting_type: "gbdt"
  num_leaves: 31
  learning_rate: 0.05
  feature_fraction: 0.9
  bagging_fraction: 0.8
  bagging_freq: 5
  verbose: 0

# データリーケージ防止設定
leakage_risk_columns:
  - "人気"
  - "単勝"
  - "オッズ"
  - "着差"
  - "タイム"
  - "ﾀｲﾑ指数"
  - "通過"
  - "上り"

# 安全なカテゴリカル特徴量
safe_categorical_cols:
  - "場名"
  - "距離"
  - "コース"
  - "馬場状態"
  - "天気"
  - "性"
  - "調教師"
  - "騎手"