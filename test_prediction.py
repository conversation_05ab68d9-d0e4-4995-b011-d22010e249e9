#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
学習済みモデルのテストスクリプト
"""

import pandas as pd
import numpy as np
import joblib
import logging
from pathlib import Path

# ログ設定
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_trained_model():
    """学習済みモデルのテスト"""
    
    # 最新のモデルファイルを読み込み
    model_path = "models/lgb_model_20250607_205540.pkl"
    scaler_path = "models/scaler_20250607_205540.pkl"
    features_path = "models/features_20250607_205540.pkl"
    
    logger.info("学習済みモデルを読み込み中...")
    model = joblib.load(model_path)
    scaler = joblib.load(scaler_path)
    feature_columns = joblib.load(features_path)
    
    logger.info(f"モデル: {type(model)}")
    logger.info(f"特徴量数: {len(feature_columns)}")
    logger.info(f"特徴量: {feature_columns}")
    
    # テストデータの作成（サンプルデータ）
    logger.info("テストデータを作成中...")
    
    # 保存された特徴量名を使用してテストデータを作成
    logger.info(f"学習時の特徴量: {feature_columns}")
    
    # 学習時と同じ特徴量でテストデータを作成
    test_data = pd.DataFrame()
    for feature in feature_columns:
        if feature == '単勝':
            test_data[feature] = [1.5, 3.2, 8.1, 12.5, 25.0]
        elif feature == '複勝':
            test_data[feature] = [1.1, 1.8, 3.5, 5.2, 8.9]
        elif feature == '枠番':
            test_data[feature] = [1, 4, 6, 2, 8]
        elif feature == '馬':
            test_data[feature] = [1, 5, 7, 3, 9]
        elif feature == '人気':
            test_data[feature] = [1, 2, 5, 8, 12]
        elif feature == '馬体重':
            test_data[feature] = [450, 480, 465, 520, 445]
        elif feature == 'ハンデ(負担)':
            test_data[feature] = [54, 56, 58, 55, 52]
        elif feature == '馬齢':
            test_data[feature] = [4, 5, 3, 6, 4]
        elif feature == '賞金(万円)':
            test_data[feature] = [8500, 4200, 1200, 800, 350]
        elif feature == 'course_len':
            test_data[feature] = [2000, 1600, 1200, 2400, 1800]
        else:
            # その他の特徴量はデフォルト値
            test_data[feature] = [0.0, 0.0, 0.0, 0.0, 0.0]
    
    logger.info(f"テストデータ形状: {test_data.shape}")
    
    # 特徴量を正規化
    test_data_scaled = scaler.transform(test_data)
    
    # 予測実行
    predictions = model.predict(test_data_scaled)
    probabilities = predictions  # LightGBMの予測確率
    
    # 結果表示
    logger.info("予測結果:")
    for i, (prob, row) in enumerate(zip(probabilities, test_data.itertuples())):
        prediction = "1-3着圏内" if prob > 0.5 else "4着以下"
        logger.info(f"馬{i+1}: 確率={prob:.3f}, 予測={prediction}, 人気={row.人気}位")
    
    # 人気順と予測の比較
    test_data['prediction_prob'] = probabilities
    test_data['prediction'] = (probabilities > 0.5).astype(int)
    
    # 人気順でソート
    test_data_sorted = test_data.sort_values('人気')
    logger.info("\n人気順での予測結果:")
    for _, row in test_data_sorted.iterrows():
        prediction_text = "1-3着圏内" if row['prediction'] == 1 else "4着以下"
        logger.info(f"人気{int(row['人気'])}位: 確率={row['prediction_prob']:.3f}, 予測={prediction_text}")
    
    return test_data

def evaluate_model_performance():
    """モデルの詳細評価"""
    
    logger.info("モデルの性能評価を実行中...")
    
    # 実際の2020年データでの評価
    try:
        race_results = pd.read_pickle("output/race_results_2020.pickle")
        race_info = pd.read_pickle("output/race_info_2020.pickle")
        
        # データをマージ
        data = pd.merge(race_results, race_info, on='race_id', how='left')
        
        # 着順データのクリーニング
        data = data.dropna(subset=['着順'])
        data['着順'] = pd.to_numeric(data['着順'], errors='coerce')
        data = data.dropna(subset=['着順'])
        
        # ターゲット作成
        data['target'] = (data['着順'] <= 3).astype(int)
        
        # 人気別の実際の的中率
        logger.info("\n人気別の実際の1-3着率:")
        popularity_stats = data.groupby('人気')['target'].agg(['count', 'mean']).head(10)
        for pop, (count, rate) in popularity_stats.iterrows():
            if count >= 100:  # 十分なサンプル数がある場合のみ
                logger.info(f"人気{int(pop)}位: {rate:.3f} ({count}件)")
        
        logger.info(f"\n全体の1-3着率: {data['target'].mean():.3f}")
        
    except Exception as e:
        logger.error(f"性能評価でエラー: {e}")

if __name__ == "__main__":
    test_trained_model()
    evaluate_model_performance()