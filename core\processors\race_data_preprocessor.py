# core/processors/race_data_preprocessor.py
import logging
import numpy as np
import pandas as pd
from core.utils.constants import Master, RaceInfoCols, ResultsCols

class RaceDataPreprocessor:
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def preprocess_data(self, race_info_df: pd.DataFrame, race_results_df: pd.DataFrame) -> pd.DataFrame:
        """レースデータの前処理を行い、結果を返す"""
        if race_info_df.empty or race_results_df.empty:
            self.logger.warning("前処理するデータがありません。")
            return pd.DataFrame()

        processed_info_df = self._preprocess_race_info(race_info_df.copy())
        processed_results_df = self._preprocess_results(race_results_df.copy())

        if 'race_id' in processed_results_df.columns and 'race_id' in processed_info_df.columns:
            # processed_info_df の race_id をインデックスに設定（まだの場合）
            if processed_info_df.index.name != 'race_id':
                 processed_info_df = processed_info_df.set_index('race_id')
            # processed_results_df の race_id をインデックスに設定（まだの場合）
            if processed_results_df.index.name != 'race_id':
                processed_results_df = processed_results_df.set_index('race_id')

            # インデックスベースでマージ
            merged_df = processed_results_df.join(processed_info_df, how='left', lsuffix='_result', rsuffix='_info')
            # マージ後に race_id カラムが重複する場合があるので、適切に処理
            if 'race_id_info' in merged_df.columns and 'race_id_result' in merged_df.columns:
                merged_df['race_id'] = merged_df['race_id_result'].fillna(merged_df['race_id_info'])
                merged_df = merged_df.drop(columns=['race_id_result', 'race_id_info'])
            elif 'race_id_result' in merged_df.columns: #片方だけ存在する場合
                 merged_df = merged_df.rename(columns={'race_id_result': 'race_id'})
            elif 'race_id_info' in merged_df.columns:
                 merged_df = merged_df.rename(columns={'race_id_info': 'race_id'})

            # マージ後にインデックスをリセットする場合
            # merged_df = merged_df.reset_index()
            return merged_df
        else:
            self.logger.error("結合に必要なカラム(race_id)がありません。")
            return pd.DataFrame()

    def _preprocess_race_info(self, df: pd.DataFrame) -> pd.DataFrame:
        """レース情報の前処理"""
        if RaceInfoCols.DISTANCE in df.columns:
            df[RaceInfoCols.DISTANCE] = pd.to_numeric(df[RaceInfoCols.DISTANCE], errors='coerce')
            df[RaceInfoCols.DISTANCE] = (df[RaceInfoCols.DISTANCE] // 100).astype('Int64')
        if RaceInfoCols.DATE in df.columns:
            try:
                df[RaceInfoCols.DATE] = pd.to_datetime(df[RaceInfoCols.DATE], format="%Y年%m月%d日", errors='coerce')
            except:
                try: df[RaceInfoCols.DATE] = pd.to_datetime(df[RaceInfoCols.DATE], errors='coerce')
                except Exception: pass
        if RaceInfoCols.VENUE in df.columns:
            df[RaceInfoCols.VENUE] = df[RaceInfoCols.VENUE].astype(pd.StringDtype())
        else:
            df[RaceInfoCols.VENUE] = pd.Series([pd.NA] * len(df), dtype=pd.StringDtype())
        venue_code_map = {'01':'札幌','02':'函館','03':'福島','04':'新潟','05':'東京','06':'中山','07':'中京','08':'京都','09':'阪神','10':'小倉'}
        if 'race_id' in df.columns:
            venue_from_id = df['race_id'].fillna('').astype(str).str[4:6].map(venue_code_map)
            df[RaceInfoCols.VENUE] = venue_from_id.fillna(df[RaceInfoCols.VENUE])
        if 'data_intro_text' in df.columns:
            venues_keywords = ["札幌","函館","福島","新潟","東京","中山","中京","京都","阪神","小倉"]
            def find_venue_in_text(text):
                if pd.isna(text): return pd.NA
                for v_kw in venues_keywords:
                    if v_kw in str(text): return v_kw
                return pd.NA
            venue_is_na = df[RaceInfoCols.VENUE].isna()
            if venue_is_na.any():
                extracted = df.loc[venue_is_na, 'data_intro_text'].apply(find_venue_in_text)
                df.loc[venue_is_na, RaceInfoCols.VENUE] = df.loc[venue_is_na, RaceInfoCols.VENUE].fillna(extracted)
        if RaceInfoCols.WEATHER in df.columns:
            df[RaceInfoCols.WEATHER] = pd.Categorical(df[RaceInfoCols.WEATHER].astype(str), Master.WEATHER_LIST)
        if RaceInfoCols.GROUND_STATE in df.columns:
            df[RaceInfoCols.GROUND_STATE] = pd.Categorical(df[RaceInfoCols.GROUND_STATE].astype(str), Master.GROUND_STATE_LIST)
        return df

    def _preprocess_results(self, df: pd.DataFrame) -> pd.DataFrame:
        """レース結果の前処理"""
        df = self._preprocess_rank(df)
        if ResultsCols.SEX_AGE in df.columns:
            sex_age_str = df[ResultsCols.SEX_AGE].astype(str)
            extract_result = sex_age_str.str.extract(r'^([^\d]+)(\d+)$', expand=True)
            if not extract_result.empty:
                df["性"] = extract_result[0]
                df["年齢"] = pd.to_numeric(extract_result[1], errors='coerce').astype('Int64')
                valid_sexes = Master.SEX_LIST
                if "性" in df.columns: df.loc[~df["性"].isin(valid_sexes), "性"] = pd.NA
            else:
                df["性"], df["年齢"] = pd.NA, pd.NA
        for col in [ResultsCols.TANSHO_ODDS, ResultsCols.KINRYO, ResultsCols.WAKUBAN, ResultsCols.UMABAN]:
            if col in df.columns:
                if col in [ResultsCols.WAKUBAN, ResultsCols.UMABAN]:
                    df[col] = pd.to_numeric(df[col], errors='coerce').astype('Int64')
                else:
                    df[col] = pd.to_numeric(df[col], errors='coerce').astype(float) # KINRYOもfloatで良いか確認
        if ResultsCols.RANK_DIFF in df.columns:
            df[ResultsCols.RANK_DIFF] = pd.to_numeric(df[ResultsCols.RANK_DIFF], errors='coerce')
            df.loc[df[ResultsCols.RANK] == 1, ResultsCols.RANK_DIFF] = 0
        if ResultsCols.TIME in df.columns:
            try:
                baseformat = '%M:%S.%f'
                basetime = pd.to_datetime("00:00.0", format=baseformat)
                to_dt = lambda x: pd.to_datetime(df[ResultsCols.TIME], format=x, errors='coerce')
                datetime_s = to_dt(baseformat)
                for fmt_add in ['%M.%S.%f', '%M:%S:%f']:
                    datetime_s = datetime_s.fillna(to_dt(fmt_add))
                df['time_seconds'] = (datetime_s - basetime).dt.total_seconds()
            except Exception as e:
                self.logger.warning(f"タイムの変換中にエラー: {e}")
        if 'race_id' in df.columns:
            df['n_horses'] = df.groupby('race_id')['race_id'].transform('count')
        return df

    def _preprocess_rank(self, df: pd.DataFrame) -> pd.DataFrame:
        """着順の前処理"""
        if ResultsCols.RANK in df.columns:
            df = df.copy()
            df[ResultsCols.RANK] = pd.to_numeric(df[ResultsCols.RANK], errors='coerce')
            df.dropna(subset=[ResultsCols.RANK], inplace=True)
            df[ResultsCols.RANK] = df[ResultsCols.RANK].astype(int)
            df['rank_binary'] = np.where((df[ResultsCols.RANK] > 0) & (df[ResultsCols.RANK] < 4), 1, 0)
        return df
