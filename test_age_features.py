#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
年齢・生後日数特徴量のテストスクリプト
"""

import pandas as pd
import numpy as np
import sys
import logging
from datetime import datetime, timedelta

# プロジェクトのパスを追加
sys.path.append('.')

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """テスト用データを作成"""
    # テスト用の馬データ
    test_data = pd.DataFrame({
        'horse_id': ['horse_001', 'horse_002', 'horse_003', 'horse_004', 'horse_005'],
        'date': [
            datetime(2023, 6, 1),   # レース日
            datetime(2023, 6, 15),
            datetime(2023, 7, 1),
            datetime(2023, 7, 15),
            datetime(2023, 8, 1)
        ],
        'birthday': [
            datetime(2020, 3, 15),  # 生年月日（3歳馬）
            datetime(2019, 4, 20),  # 4歳馬
            datetime(2018, 5, 10),  # 5歳馬
            datetime(2017, 2, 28),  # 6歳馬
            datetime(2015, 6, 5)    # 8歳馬
        ],
        'total_races': [5, 12, 25, 35, 50],  # 総出走回数
        'win_rate': [0.2, 0.15, 0.08, 0.06, 0.04]  # 勝率
    })
    
    logger.info(f"テストデータを作成: {len(test_data)}件")
    return test_data

def test_age_calculators():
    """年齢計算関数のテスト"""
    logger.info("=== 年齢計算関数のテスト開始 ===")
    
    try:
        from core.features.calculators import FeatureCalculators
        calc = FeatureCalculators()
        
        # テストデータ作成
        test_data = create_test_data()
        
        # 生後日数計算テスト
        logger.info("1. 生後日数計算テスト")
        days_old = calc.calculate_days_old(test_data)
        test_data['days_old_test'] = days_old
        logger.info(f"生後日数: {days_old.tolist()}")
        
        # 年齢計算テスト
        logger.info("2. 年齢（年単位）計算テスト")
        age_years = calc.calculate_age_years(test_data)
        test_data['age_years_test'] = age_years
        logger.info(f"年齢（年）: {age_years.round(2).tolist()}")
        
        # 月齢計算テスト
        logger.info("3. 月齢計算テスト")
        age_months = calc.calculate_age_months(test_data)
        test_data['age_months_test'] = age_months
        logger.info(f"月齢: {age_months.round(1).tolist()}")
        
        # 年齢カテゴリテスト
        logger.info("4. 年齢カテゴリテスト")
        is_young = calc.calculate_age_category_young(test_data)
        is_prime = calc.calculate_age_category_prime(test_data)
        is_veteran = calc.calculate_age_category_veteran(test_data)
        
        test_data['is_young_test'] = is_young
        test_data['is_prime_test'] = is_prime
        test_data['is_veteran_test'] = is_veteran
        
        logger.info(f"若駒フラグ: {is_young.tolist()}")
        logger.info(f"盛期フラグ: {is_prime.tolist()}")
        logger.info(f"ベテランフラグ: {is_veteran.tolist()}")
        
        # 交互作用特徴量テスト
        logger.info("5. 交互作用特徴量テスト")
        experience_density = calc.calculate_experience_density(test_data)
        young_prospect = calc.calculate_young_prospect(test_data)
        mature_period = calc.calculate_mature_period(test_data)
        
        test_data['experience_density_test'] = experience_density
        test_data['young_prospect_test'] = young_prospect
        test_data['mature_period_test'] = mature_period
        
        logger.info(f"経験密度: {experience_density.round(2).tolist()}")
        logger.info(f"若手有望: {young_prospect.tolist()}")
        logger.info(f"円熟期: {mature_period.tolist()}")
        
        # 結果表示
        logger.info("=== テスト結果一覧 ===")
        print(test_data[['horse_id', 'age_years_test', 'is_young_test', 'is_prime_test', 
                        'is_veteran_test', 'experience_density_test', 'young_prospect_test', 
                        'mature_period_test']].to_string(index=False))
        
        logger.info("年齢計算関数のテスト完了")
        return True
        
    except Exception as e:
        logger.error(f"年齢計算関数のテストでエラー: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_age_feature_definitions():
    """年齢特徴量定義のテスト"""
    logger.info("=== 年齢特徴量定義のテスト開始 ===")
    
    try:
        from core.features.definitions import create_age_features, create_age_feature_group
        
        # 年齢特徴量の作成
        age_features = create_age_features()
        logger.info(f"年齢特徴量数: {len(age_features)}個")
        
        # 各特徴量の詳細を表示
        for feature in age_features:
            logger.info(f"  - {feature.name}: {feature.description} ({feature.feature_type.value})")
        
        # 年齢特徴量グループの作成
        age_group = create_age_feature_group()
        logger.info(f"年齢特徴量グループ: {age_group.name}")
        logger.info(f"グループ内特徴量数: {len(age_group.features)}個")
        
        logger.info("年齢特徴量定義のテスト完了")
        return True
        
    except Exception as e:
        logger.error(f"年齢特徴量定義のテストでエラー: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_manager():
    """FeatureManagerでの年齢特徴量テスト"""
    logger.info("=== FeatureManagerでの年齢特徴量テスト開始 ===")
    
    try:
        from core.features.manager import FeatureEngineeringManager
        
        # FeatureManagerを初期化（core/features/config.yamlを使用）
        manager = FeatureEngineeringManager(config_path="core/features/config.yaml")
        
        # 登録されている特徴量を確認
        feature_names = list(manager.feature_definitions.keys())
        logger.info(f"登録済み特徴量数: {len(feature_names)}個")
        
        # 年齢関連特徴量のみ抽出
        age_features = [name for name in feature_names if 'age' in name or 'young' in name or 'veteran' in name or 'prime' in name or 'days_old' in name or 'experience' in name or 'mature' in name]
        
        if age_features:
            logger.info(f"年齢関連特徴量: {len(age_features)}個")
            for feature_name in age_features:
                logger.info(f"  - {feature_name}")
        else:
            logger.warning("年齢関連特徴量が見つかりません")
        
        # テストデータで特徴量計算
        test_data = create_test_data()
        
        # 年齢特徴量を計算
        if age_features:
            logger.info("年齢特徴量を計算中...")
            
            for feature_name in age_features[:3]:  # 最初の3つをテスト
                try:
                    feature_def = manager.feature_definitions[feature_name]
                    result = feature_def.calculator(test_data, **feature_def.parameters)
                    test_data[f"{feature_name}_computed"] = result
                    logger.info(f"{feature_name}: {result.round(2).tolist() if hasattr(result, 'round') else result.tolist()}")
                except Exception as e:
                    logger.warning(f"{feature_name}の計算でエラー: {e}")
        
        logger.info("FeatureManagerでの年齢特徴量テスト完了")
        return True
        
    except Exception as e:
        logger.error(f"FeatureManagerでのテストでエラー: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """メイン実行関数"""
    logger.info("🐎 年齢・生後日数特徴量テスト開始")
    
    results = []
    
    # 1. 年齢計算関数のテスト
    result1 = test_age_calculators()
    results.append(("年齢計算関数", result1))
    
    # 2. 年齢特徴量定義のテスト
    result2 = test_age_feature_definitions()
    results.append(("年齢特徴量定義", result2))
    
    # 3. FeatureManagerでのテスト
    result3 = test_feature_manager()
    results.append(("FeatureManager", result3))
    
    # 結果サマリー
    logger.info("=== テスト結果サマリー ===")
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        logger.info("🎉 全てのテストが成功しました！年齢特徴量の統合が完了です。")
    else:
        logger.error("❌ 一部のテストが失敗しました。")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)